<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-PROPOSTE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0154</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaRequestDTO">
			<!-- Totali 263 car (150 + 92) -->
			<!-- 92 car (new input 122 car) -->
			<field-map attributeName="codAgenziaGest"    length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="codSubagenzia"     length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codAgenzia"        length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="numeroProposta"    length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenza"    length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="codiceCliente"     length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- field-map attributeName="nominativoCliente" length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/-->			
            <field-map attributeName="cognomeCliente"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="nomeCliente"      length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="importoPremi"      length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>

		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaResponseDTO">
		    <!-- Totali 26500 car (150 + 24526 + 1824) --> 
		    <!-- 24404 + 122 = 24526 -->
		    <!-- Campi di input 122 car  -->
			<field-map attributeName="codAgenziaGest"    length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="codSubagenzia"     length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codAgenzia"        length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="numeroProposta"    length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenza"    length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
            <field-map attributeName="codiceCliente"     length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- field-map attributeName="nominativoCliente" length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/-->			
            <field-map attributeName="cognomeCliente"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="nomeCliente"      length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="importoPremi"      length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <!-- Proposte -->
            <!-- 24404 car  -->	
            <field-map attributeName="listaRapporti"						length="24404">
				<nested-mapping className="it.sistinf.common.albedo.utility.BaseAlbedoListaElementiDTO" iterations="0" blockLength="24404">
					<field-map attributeName="numElementi" 					length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0" />
			      	<field-map attributeName="flAltri" 						length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      	<field-map attributeName="listaDaCommarea" 				length="24400">
						<nested-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoDTO" iterations="200" blockLength="122">
							<field-map attributeName="agenziaProposta" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroProposta"   	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDecorrenza"       length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="categoria"  		    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="prodotto"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descProdotto"         length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="contraente"    	    length="30" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoPremi"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="causaleAttivazione"  	length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numCategoria"  	    length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    