<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0043</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioRequestDTO">
			<!-- Totali 171 car (150 + 21) -->
			<!-- 34 car -->
		    <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataCreazione"  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<field-map attributeName="flTipoTariffa"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioResponseDTO">
		    <!-- Totali 10109 car (150 + 9438 + 1824) --> 
		    <!-- 9404 + 34 = 9438 car -->
		    <!-- Campi di input 34 car -->
		   <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataCreazione"  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<field-map attributeName="flTipoTariffa"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			
			<field-map attributeName="numUTPresenti"  length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="flAltri"        length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="unitaTecniche" length="9400" >
            	<nested-mapping className="it.sistinf.albedoweb.polizza.dto.UTPolizzaDettaglioDTO" iterations="100" blockLength="94">
					<field-map attributeName="codiceUT"              length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCodiceUT"    	     length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="flgAttiva"          	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="premiEmessi"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premiPagati"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="prestazione"   		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="flTipoPrestazione"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				</nested-mapping>
			</field-map>  
	</output-mapping>
     </rule>
</rules>