<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>FASE-BATCH-PRIMARIE-ELIMINA-RIVALUTAZ-INDICIZ</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE056</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
 	<logApp>true</logApp>
	<logAppServDesc>ELIMINA RIVALUTAZIONE-INDICIZZAZIONE</logAppServDesc>
	<areaFunzionale>RICHIESTA FASI BATCH-FASI PRIMARIE</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Lunghezza: 150 + 3 + 1824 = 1977  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchRivalutazIndicizRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 3 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchRivalutazIndicizResponseDTO"> 
      <!-- output :3  car -->      
      	   <field-map attributeName="codiceSocieta"   	length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
   </output-mapping> 
 </rule> 
</rules> 