<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-RISERVA-MATEMATICA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE006</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	   	<!-- Totali BWLSE006  (150 + 394 + 1824 = 2369 ) -->

		<input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaRequestDTO" nomeKeyERR="erroriFaseBatch">
		<!-- input: 3 car -->
			<field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaResponseDTO">
		    <!-- output: 3 + 391 car = 394 Totale -->
		   <field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    
		    <!-- Campi di output 391 car -->
		    <field-map attributeName="numElementiProdotti" 		  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriProdotti"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaProdotti" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiUT" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriUT"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaUT" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiAgenzie" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriAgenzie"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaAgenzie" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="6">
		   			<field-map attributeName="codice" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiFondi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriFondi"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaFondi" length="50" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="5">
		   			<field-map attributeName="codice" 			length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiTest" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriTest"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaTest" length="5" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="5" blockLength="1">
		   			<field-map attributeName="codice" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="tipoRiserva" 			 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRiserva" 				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataFondo"			 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="passo1" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="passo2" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoProdotto"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="categoria" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDa" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataA" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>									
			<field-map attributeName="flgTest" 					length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="flgEsistenza" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    
