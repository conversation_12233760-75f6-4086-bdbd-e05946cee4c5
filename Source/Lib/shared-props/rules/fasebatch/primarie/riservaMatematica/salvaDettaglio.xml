<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-DETTAGLIO-RISERVA-MATEMATICA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE007</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA RISERVA MATEMATICA</logAppServDesc>
		<areaFunzionale>RICHIESTA FASI BATCH-FASI PRIMARIE</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	   	<!-- Totali 150 + 590 + 1824 = 2564 car -->

		<input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaRequestDTO" nomeKeyERR="erroriFaseBatch">
			<!-- input 369 -->
			<field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				
			<field-map attributeName="listaProdotti" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaUT" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaAgenzie" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="6">
		   			<field-map attributeName="codice" 			length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaFondi" length="50" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="5">
		   			<field-map attributeName="codice" 			length="05"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaTest" length="5" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="5" blockLength="1">
		   			<field-map attributeName="codice" 			length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="tipoRiserva" 			 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRiserva" 				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRiserva" nomeKeyERR="erroriFaseBatch"/>
			<field-map attributeName="dataFondo"			 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataFondo" nomeKeyERR="erroriFaseBatch"/>
			<field-map attributeName="passo1" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="passo2" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoProdotto"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="categoria" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDa" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dallaData" nomeKeyERR="erroriFaseBatch"/>
			<field-map attributeName="dataA" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="allaData" nomeKeyERR="erroriFaseBatch"/>									
			<field-map attributeName="flgTest" 					length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="flgEsistenza" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaResponseDTO">
		    <!-- output :  369 + 220 = 590  -->
		    <field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				
			<field-map attributeName="listaProdotti" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaUT" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaAgenzie" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="6">
		   			<field-map attributeName="codice" 			length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaFondi" length="50" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="5">
		   			<field-map attributeName="codice" 			length="05"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaTest" length="5" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="5" blockLength="1">
		   			<field-map attributeName="codice" 			length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="tipoRiserva" 			 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRiserva" 				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataFondo"			 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="passo1" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="passo2" 					length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoProdotto"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoria" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDa" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataA" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>									
			<field-map attributeName="flgTest" 					length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgEsistenza" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- 220 car -->
			<field-map attributeName="listaErroriProdotti" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaErroriUT" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="listaErroriAgenzie" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="listaErroriFondi" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaErroriTest" length="20" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="5" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="tipoRiservaCdErr"     			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRiservaCdErr"     			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataFondoCdErr"     				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="passo1CdErr"     					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>			
			<field-map attributeName="passo2CdErr"     					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoProdottoCdErr"     			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="categoriaCdErr"     				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDaCdErr"						length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataACdErr"     					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flgTestCdErr"            			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
						
		</output-mapping>
	</rule>
</rules>
    
