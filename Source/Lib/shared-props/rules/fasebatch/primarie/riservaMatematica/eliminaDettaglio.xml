<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ANNULLA-RISERVA-MATEMATICA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE058</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>ELIMINA RISERVA MATEMATICA</logAppServDesc>
		<areaFunzionale>RICHIESTA FASI BATCH-FASI PRIMARIE</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	   	<!-- Totali 150 + 3 + 1824 = 1977 car -->

		<input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaRequestDTO" nomeKeyERR="erroriFaseBatch">
			<!-- input 3 -->
			<field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.riservaMatematica.dto.FaseBatchRiservaMatematicaResponseDTO">
		    <!-- output :  3  -->
		    <field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				
		</output-mapping>
	</rule>
</rules>
    
