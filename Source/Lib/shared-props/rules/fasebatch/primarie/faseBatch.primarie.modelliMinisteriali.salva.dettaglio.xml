<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-DETTAGLIO-MODELLI-MINISTERIALI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE009</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>SALVA MODELLI MINISTERIALI</logAppServDesc>
	<areaFunzionale>RICHIESTA FASI BATCH-FASI PRIMARIE</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE013 Lunghezza: 150 + 84 + 1824 = 2054  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.modelliMinisteriali.dto.FaseBatchModelliMinisterialiRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 60 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
		   <field-map attributeName="elencoModelliMinisteriali" length="9" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="9" blockLength="1"> 
			         
			          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
		   <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		valida="true" natura="Data" nomeAttributoERR="dallaData" nomeKeyERR="erroriFaseBatch" />
	       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		valida="true" natura="Data" nomeAttributoERR="allaData" nomeKeyERR="erroriFaseBatch" />
	       <field-map attributeName="flgTest"		 			length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="annoPerTrimestre"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"  nomeAttributoERR="annoPerTrimestre" nomeKeyERR="erroriFaseBatch"/>
	       <field-map attributeName="trimestre"   			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="1" numDecimali="0" separatoreMigliaia="false"  nomeAttributoERR="trimestre" nomeKeyERR="erroriFaseBatch"/>
	       <field-map attributeName="annoPerMese"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"  nomeAttributoERR="annoPerMese" nomeKeyERR="erroriFaseBatch"/>
	       <field-map attributeName="mese"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
	       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.modelliMinisteriali.dto.FaseBatchModelliMinisterialiResponseDTO"> 
      <!-- output :60 + 24 = 84 car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
		   <field-map attributeName="elencoModelliMinisteriali" length="9" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="9" blockLength="1"> 
			          
			          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
		   <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="flgTest"		 			length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="annoPerTrimestre"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
	       <field-map attributeName="trimestre"   			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="1" numDecimali="0" separatoreMigliaia="false"/>
	       <field-map attributeName="annoPerMese"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
	       <field-map attributeName="mese"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
	       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       
	       <field-map attributeName="elencoModelliMinisterialiErrori" length="36" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="9" blockLength="4"> 
			          <field-map attributeName="codice"  	    		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0" />
			   </nested-mapping>
	       </field-map> 
	       
	       <field-map attributeName="annoPerTrimestreCdErr"   				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="trimestreCdErr"   	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="annoPerMeseCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="meseCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="categoriaCdErr"   				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataDaCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataACdErr"   				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="flgTestCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
   </output-mapping> 
 </rule> 
</rules> 