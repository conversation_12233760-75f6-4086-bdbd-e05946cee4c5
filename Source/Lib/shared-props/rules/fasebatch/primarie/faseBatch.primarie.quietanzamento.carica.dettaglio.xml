<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>FASE-BATCH-PRIMARIE-DETTAGLIO-QUIETANZAMENTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE004</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	   	<!-- Totali 2437 car (150 + 463 + 1824 = 2437 )) -->

	   	<!-- input: 01 car -->
		<input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchQuietanziamentoRequestDTO">
			<!-- 01 car -->
			<field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchQuietanziamentoResponseDTO">
		    <!-- Campi di output 463 car -->
		    <field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="listaProdotti" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="12">
		            <field-map attributeName="indice"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaUT" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="12">
		            <field-map attributeName="indice"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="listaAgenzie" length="80" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="08">
		            <field-map attributeName="indice"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="codice" 			length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="listaFondi" length="70" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="07">
		            <field-map attributeName="indice"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="codice" 			length="05"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="listaCollettive" length="08" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="04" blockLength="02">
		            <field-map attributeName="indice"			length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="codice" 			length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="polizzaCategoriaInizio" 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaAgenziaInizio" 	length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaCollettivaInizio" 	length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaNumInizio" 		length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			

			<field-map attributeName="polizzaCategoriaFine" 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaAgenziaFine" 		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaCollettivaFine" 	length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaNumFine" 			length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			

			<field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDa" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataA" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>									
			<field-map attributeName="test" 					length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="flagEsistenza" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>									
		</output-mapping>
	</rule>
</rules>
    
