<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>FASE-BATCH-PRIMARIE-DETTAGLIO-RIVALUTAZ-INDICIZ</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE002</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	   	<!-- Totali 2437 car (150 + 408 + 1824 = 2382 ) -->

		<!-- 3 car -->
		<input-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchRivalutazIndicizRequestDTO" nomeKeyERR="erroriFaseBatch">

			<field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.primarie.dto.FaseBatchRivalutazIndicizResponseDTO">
		    <!-- output: 3 + 405 car = 408 Totale -->
		   <field-map attributeName="codiceSocieta"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    
		    <!-- Campi di output 405 car -->
		    <field-map attributeName="numElementiProdotti" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriProdotti"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaProdotti" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiUT" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriUT"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaUT" length="100" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="10">
		   			<field-map attributeName="codice" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiAgenzie" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriAgenzie"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaAgenzie" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="6">
		   			<field-map attributeName="codice" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiFondi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriFondi"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaFondi" length="50" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="10" blockLength="5">
		   			<field-map attributeName="codice" 			length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiCollettive" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriCollettive"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="listaCollettive" length="4" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="4" blockLength="1">
		   			<field-map attributeName="codice" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<field-map attributeName="polizzaCategoriaInizio" 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaAgenziaInizio" 	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaCollettivaInizio" 	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaNumInizio" 		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			

			<field-map attributeName="polizzaCategoriaFine" 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaAgenziaFine" 		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaCollettivaFine" 	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="polizzaNumFine" 			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			

			<field-map attributeName="rivalutazione" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoria" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDa" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataA" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>									
			<field-map attributeName="flgTest" 					length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="flgEsistenza" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    
