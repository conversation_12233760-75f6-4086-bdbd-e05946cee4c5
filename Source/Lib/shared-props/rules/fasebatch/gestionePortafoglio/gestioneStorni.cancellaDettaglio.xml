<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CANCELLA-DETTAGLIO-GESTIONE-STORNI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE079</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.GestioneStorniRequestDTO">
			<!-- Totali 175 car (150 + (I[3+27+196+87+62+80]+O[112]= 567)) -->
			<field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="oggFiltroProdotti" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroProdotti" iterations="10" blockLength="12">
            		<field-map attributeName="prodottoIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="prodotto" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>	
			<field-map attributeName="oggFiltroUnitaTecnica" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroUnitaTecnica" iterations="10" blockLength="12">
            		<field-map attributeName="unitaTecnicaIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="unitaTecnica" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggFiltroAgenzie" length="80" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroAgenzie" iterations="10" blockLength="8">
            		<field-map attributeName="agenziaIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggFiltroFondi" length="70" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroFondi" iterations="10" blockLength="7">
            		<field-map attributeName="fondoIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="fondo" length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInizio"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Data" nomeAttributoERR="dataInizio" nomeKeyERR="errorePortafoglioGestioneStorni"/>
			<field-map attributeName="dataFine"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataFine" nomeKeyERR="errorePortafoglioGestioneStorni"/>
			<field-map attributeName="test"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagEsistenza"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.GestioneStorniResponseDTO">
		    <!-- Totali 2011 car (150 + 47 + 1824) --> 
		    <!-- Campi di output 47 car -->
		    <field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="oggFiltroProdotti" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroProdotti" iterations="10" blockLength="12">
            		<field-map attributeName="prodottoIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="prodotto" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>	
			<field-map attributeName="oggFiltroUnitaTecnica" length="120" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroUnitaTecnica" iterations="10" blockLength="12">
            		<field-map attributeName="unitaTecnicaIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="unitaTecnica" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggFiltroAgenzie" length="80" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroAgenzie" iterations="10" blockLength="8">
            		<field-map attributeName="agenziaIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggFiltroFondi" length="70" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.FiltroFondi" iterations="10" blockLength="7">
            		<field-map attributeName="fondoIndice"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="fondo" length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
				</nested-mapping>
			</field-map>
			<field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInizio"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Data"/>
			<field-map attributeName="dataFine"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="test"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagEsistenza"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="oggProdottiArrayErrori" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.ErroriFiltri" iterations="10" blockLength="4">
            		<field-map attributeName="prodottoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggUnitaTecnicaArrayErrori" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.ErroriFiltri" iterations="10" blockLength="4">
            		<field-map attributeName="unitaTecnicaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggAgenziaArrayErrori" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.ErroriFiltri" iterations="10" blockLength="4">
            		<field-map attributeName="agenziaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggFondoArrayErrori" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.ErroriFiltri" iterations="10" blockLength="4">
            		<field-map attributeName="fondoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="categoriaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataInizioCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataFineCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="testCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		</output-mapping>
	</rule>
</rules>
    
