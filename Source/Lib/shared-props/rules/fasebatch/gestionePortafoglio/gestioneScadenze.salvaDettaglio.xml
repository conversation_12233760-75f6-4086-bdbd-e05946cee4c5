<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-DETTAGLIO-GESTIONE-SCADENZE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSE047</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.GestioneScadenzeRequestDTO">
			<!-- Totali 175 car (150 + (I[38]+O[36]= 224)) -->
			<!-- 38 car -->
			<field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="oggDettaglioGestioneScadenza" length="38" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.DettaglioGestioneScadenza" iterations="0" blockLength="38">
            		<field-map attributeName="flagControlloScadenza"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="dataControlloScadenza" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Data" nomeAttributoERR="dataControlloScadenza" nomeKeyERR="errorePortafoglioGestioneScadenze"/>
		            <field-map attributeName="flagConclusioneTemporanee"     length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataConclusioneTemporanee"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true"  natura="Data" nomeAttributoERR="dataConclusioneTemporanee" nomeKeyERR="errorePortafoglioGestioneScadenze" />
					<field-map attributeName="testConclusioneTemporanee"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagDifferimentoAutomatico"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDifferimentoAutomatico"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true"  natura="Data" nomeAttributoERR="dataDifferimentoAutomatico" nomeKeyERR="errorePortafoglioGestioneScadenze" />
					<field-map attributeName="testDifferimentoAutomatico"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagEsistenza"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.GestioneScadenzeResponseDTO">
		    <!-- Totali  car (150 + 224 + 1824) --> 
		    <!-- Campi di output 36 car -->
		    <field-map attributeName="societaSelezionata"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
		    <field-map attributeName="oggDettaglioGestioneScadenza" length="39" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.gestionePortafoglio.dto.DettaglioGestioneScadenza" iterations="0" blockLength="39">
            		<field-map attributeName="flagControlloScadenza"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="dataControlloScadenza" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  natura="Data"/>
		            <field-map attributeName="flagConclusioneTemporanee"     length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataConclusioneTemporanee"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  natura="Data"/>
					<field-map attributeName="testConclusioneTemporanee"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagDifferimentoAutomatico"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDifferimentoAutomatico"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  natura="Data"/>
					<field-map attributeName="testDifferimentoAutomatico"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagEsistenza"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flagControlloScadenzaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataControlloScadenzaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagConclusioneTemporaneeCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataConclusioneTemporaneeCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="testConclusioneTemporaneeCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagDifferimentoAutomaticoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDifferimentoAutomaticoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="testDifferimentoAutomaticoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="categoriaCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    
