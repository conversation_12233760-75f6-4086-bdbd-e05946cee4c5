<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CARICA-DETTAGLIO-BOLLATI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE012</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE012 Lunghezza: 150 + 38 + 1824 = 2012  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.bollati.dto.FaseBatchBollatiRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 3 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.bollati.dto.FaseBatchBollatiResponseDTO"> 
      <!-- output :3 + 35 = 38 car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
		   <field-map attributeName="elencoBollati" length="11" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="11" blockLength="1"> 
			          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
		   <field-map attributeName="flgDettaglioPur" 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   </output-mapping> 
 </rule> 
</rules> 