<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CARICA-POLIZZE-IN-SCADENZA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE024</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Lunghezza: 150 + 102 + 1824 = 2076  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.polizzeInScadenza.dto.FaseBatchPolizzeInScadenzaRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 3 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.dto.FaseBatchResponseDTO"> 
      <!-- output :3 + 99 = 102 car -->      
      	   	<field-map attributeName="codiceSocieta" 		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
      	   	<field-map attributeName="numElementiAgenzie" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriAgenzie"     	length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/> 
		  	 <field-map attributeName="listaAgenzie" 	length="72" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="12" blockLength="6"> 
			          <field-map attributeName="codice" length="6" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       	</field-map> 
	      	<field-map attributeName="categoria"   		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       	<field-map attributeName="dataDa"       	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       	<field-map attributeName="dataA" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       	<field-map attributeName="flgEsistenza"   	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  	</output-mapping> 
 </rule> 
</rules> 