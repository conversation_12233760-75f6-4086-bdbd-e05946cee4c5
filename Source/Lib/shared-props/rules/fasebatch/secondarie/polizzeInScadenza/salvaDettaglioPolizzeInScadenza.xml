<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-POLIZZE-IN-SCADENZA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE025</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
 	 <logApp>true</logApp>
	 <logAppServDesc>SALVA POLIZZE IN SCADENZA</logAppServDesc>
	 <areaFunzionale>RICHIESTA FASI BATCH-FASI SECONDARIE</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Lunghezza: 150 + 165 + 1824 = 2139  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.polizzeInScadenza.dto.FaseBatchPolizzeInScadenzaRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 101 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
	   <field-map attributeName="numElementiAgenzie" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
	   <field-map attributeName="listaAgenzie" length="72" > 
		    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="12" blockLength="6"> 
		          <field-map attributeName="codice"  	length="6" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
       </field-map> 
       <field-map attributeName="categoria"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="dataDa"       			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="dallaDataCdErr" nomeKeyERR="erroriFaseBatch"/>
       <field-map attributeName="dataA" 				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="allaDataCdErr" nomeKeyERR="erroriFaseBatch" />
       <field-map attributeName="flgEsistenza"   		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.dto.FaseBatchResponseDTO"> 
      <!-- output :101 + 64 = 165 car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
      	   <field-map attributeName="numElementiAgenzie" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/> 
		   <field-map attributeName="listaAgenzie" length="72" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="12" blockLength="6"> 
			          <field-map attributeName="codice"  	    		length="6" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
	       <field-map attributeName="categoria"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="dataA" 				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="flgEsistenza"   		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- errori 64 -->	       
	       <field-map attributeName="listaErroriAgenzie" length="48" >
            	<nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="12" blockLength="4">
		   			<field-map attributeName="codice"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
	       <field-map attributeName="categoriaCdErr"   		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataDaCdErr"   		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataACdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="flgEsistenzaCdErr"   	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
   </output-mapping> 
 </rule> 
</rules> 