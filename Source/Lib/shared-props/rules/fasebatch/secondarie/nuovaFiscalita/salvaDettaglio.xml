<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-DETTAGLIO-NUOVA-FISCALITA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE039</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	 <logApp>true</logApp>
	 <logAppServDesc>SALVA NUOVA FISCALITA</logAppServDesc>
	 <areaFunzionale>RICHIESTA FASI BATCH-FASI SECONDARIE</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE039 Lunghezza: 150 + 52 + 1824 = 2026  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.nuovaFiscalita.dto.FaseBatchNuovaFiscalitaRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 32 car --> 
	    <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
		   <field-map attributeName="elencoNuovaFiscalita" length="6" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="2" blockLength="3"> 
			          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
			          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
	       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		valida="true" natura="Data" nomeAttributoERR="dallaData" nomeKeyERR="erroriFaseBatch" />
	       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		valida="true" natura="Data" nomeAttributoERR="allaData" nomeKeyERR="erroriFaseBatch" />
	       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.nuovaFiscalita.dto.FaseBatchNuovaFiscalitaResponseDTO"> 
      <!-- output :32 + 20 = 52 car -->      
      	    <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
		   <field-map attributeName="elencoNuovaFiscalita" length="6" > 
			    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="2" blockLength="3"> 
			          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
			          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
			   </nested-mapping>
	       </field-map> 
	       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	       		natura="Data" />
	       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       <field-map attributeName="funzCdErr"   				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="categoriaCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataDaCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="dataACdErr"   				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	       <field-map attributeName="flgTestCdErr"   			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
   </output-mapping> 
 </rule> 
</rules> 