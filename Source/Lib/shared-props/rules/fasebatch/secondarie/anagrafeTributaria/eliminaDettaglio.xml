<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ANNULLA-ANAGRAFE-TRIB</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE073</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE073 Lunghezza: 150 + 43 + 1824 = 2017  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.anagrafeTributaria.dto.FaseBatchAnagrafeTributariaRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 43 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
	   <field-map attributeName="elencoAnagrafeTributaria" length="6" > 
		    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="2" blockLength="3"> 
		          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
       </field-map> 
	   <field-map attributeName="partitaIva" 				length="11"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="dallaData" nomeKeyERR="erroriFaseBatch"/>
       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="allaData" nomeKeyERR="erroriFaseBatch" />
       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.anagrafeTributaria.dto.FaseBatchAnagrafeTributariaResponseDTO"> 
      <!-- output :43  car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
	   <field-map attributeName="elencoAnagrafeTributaria" length="6" > 
		    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="2" blockLength="3"> 
		          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
       </field-map> 
	   <field-map attributeName="partitaIva" 				length="11"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		natura="Data" />
       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		natura="Data" />
       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
   </output-mapping> 
 </rule> 
</rules> 