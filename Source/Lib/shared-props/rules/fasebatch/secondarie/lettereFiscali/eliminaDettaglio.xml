<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CANCELLA-DETTAGLIO-LETTERE-FISCALI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE064</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
 	 <logApp>true</logApp>
	 <logAppServDesc>ELIMINA LETTERE FISCALI</logAppServDesc>
	 <areaFunzionale>RICHIESTA FASI BATCH-FASI SECONDARIE</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE061 Lunghezza: 150 + 60 + 1824 = 2034  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.lettereFiscali.dto.FaseBatchLettereFiscaliRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 60 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.lettereFiscali.dto.FaseBatchLettereFiscaliResponseDTO"> 
      <!-- output :60  car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
   </output-mapping> 
 </rule> 
</rules> 