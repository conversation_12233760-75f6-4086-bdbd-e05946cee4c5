<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-DETTAGLIO-RIASSICURAZIONE</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>BWLSE062</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	 <logApp>true</logApp>
	 <logAppServDesc>ELIMINA RIASSICURAZIONE</logAppServDesc>
	 <areaFunzionale>RICHIESTA FASI BATCH-FASI SECONDARIE</areaFunzionale>
	 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: BWLSE062 Lunghezza: 150 + 48 + 1824 = 2034  -->  
  <input-mapping className="it.sistinf.albedoweb.fasebatch.riassicurazione.dto.FaseBatchRiassicurazioneRequestDTO" nomeKeyERR="erroriFaseBatch"> 
   <!-- input : 48 car --> 
	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
	   <field-map attributeName="elencoRiass" length="18" > 
		    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="6" blockLength="3"> 
		          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
       </field-map> 
	   <field-map attributeName="tipoPremiCeduti" 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="dallaData" nomeKeyERR="erroriFaseBatch"/>
       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="allaData" nomeKeyERR="erroriFaseBatch"/>
       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="indiceFz"   				length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fasebatch.secondarie.riassicurazione.dto.FaseBatchRiassicurazioneResponseDTO"> 
      <!-- output :48  car -->      
      	   <field-map attributeName="codiceSocieta"   		length="3"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 
	   <field-map attributeName="elencoRiass" length="18" > 
		    <nested-mapping className="it.sistinf.albedoweb.fasebatch.ElementoFaseBatchDTO" iterations="6" blockLength="3"> 
		          <field-map attributeName="indice"       			length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		          <field-map attributeName="codice"  	    		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
       </field-map> 
	   <field-map attributeName="tipoPremiCeduti" 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="categoria"   				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="dataDa"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="dataDa" nomeKeyERR="erroreFaseBatch"/>
       <field-map attributeName="dataA" 					length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="dataA" nomeKeyERR="erroreFaseBatch"/>
       <field-map attributeName="flgTest"		 			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="flgEsistenza"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       <field-map attributeName="indiceFz"   				length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   </output-mapping> 
 </rule> 
</rules> 