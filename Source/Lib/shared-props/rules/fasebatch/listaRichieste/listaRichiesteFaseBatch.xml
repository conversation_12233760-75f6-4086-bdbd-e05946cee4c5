<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>LISTA_RICHIESTE_FASE_BATCH</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>BWLSL102</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Lunghezza: 150 + 7492 + 1824 = 9466  -->        
		<input-mapping className="it.sistinf.albedoweb.fasebatch.listaRichieste.dto.ListaRichiesteFaseBatchRequestDTO" nomeKeyERR="erroriFaseBatch">
			<!-- input : 3 car -->
            <field-map attributeName="codiceSocieta"		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>            
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fasebatch.listaRichieste.dto.ListaRichiesteFaseBatchResponseDTO">
		    <!-- output : 7492 car -->  
            <field-map attributeName="codiceSocieta"		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>                  
			<field-map attributeName="numElementiTrovati"  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   	length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>		    
			<field-map attributeName="altreFasi"     	   	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
			<field-map attributeName="listaRichiesteFaseBatch"     length="7400">
				<nested-mapping className="it.sistinf.albedoweb.fasebatch.listaRichieste.dto.ListaRichiesteFaseBatchElementoDTO" iterations="100" blockLength="74">
					<field-map attributeName="codice"   		length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="descrizione"  	length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="dataDa"     		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataA"     		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="flgTest"			length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="categoria"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="programma"       	length="8"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codFase1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
			<field-map attributeName="descFase1"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
			<field-map attributeName="codFase2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
			<field-map attributeName="descFase2"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    