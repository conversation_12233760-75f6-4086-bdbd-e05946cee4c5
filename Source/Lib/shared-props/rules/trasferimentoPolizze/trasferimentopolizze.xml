<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
	<rule> 
		<id>TRASFERIMENTO-POLIZZE</id> 
	 	<initialProgram>WNDISPC1</initialProgram> 
	  	<initialTransaction>SBEX</initialTransaction> 
	    <program>VWLSE464</program>     
	    <transaction>SBEX</transaction> 
	  	<connectorId>A05TAREEX</connectorId> 
	 	<logApp>true</logApp>
	  	<logAppServDesc>TRASFERIMENTO-POLIZZE</logAppServDesc>
	  	<areaFunzionale>TRASFERIMENTO POLIZZE</areaFunzionale>
  
	    <multipleTransaction>true</multipleTransaction> 
	    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
	    <pageRequestField/> 
	    <limitPage>99</limitPage> 
	    <moreDataField/> 
	    <moreDataEndValue>1</moreDataEndValue> 
	    <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
	    <pastedFields> 
	    	<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
	    </pastedFields>      
	  	<!-- Definizione commarea: VWCSE464 Lunghezza: 150 + 29784 + 1824 = 31758  -->  
	  	<input-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.TrasferimentoPolizzeRequestDTO" nomeKeyERR="erroriGestioneLivelloUno">
	    <!-- input : 29740 car -->
	    	<field-map attributeName="inputTrasferimentoPolizze" length="40" > 
				<nested-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.InputTrasferimentoPolizze"> 
		   			<field-map attributeName="tipoRichiesta"   		length="3"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
					<field-map attributeName="dataTrasferimento"    length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		   				valida="true" natura="Data" nomeAttributoERR="dataInizioValidita" nomeKeyERR="erroriGestioneLivelloUno"/>
		   			<field-map attributeName="agenziaDestinazione"   length="6"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="subAgenteDestinazione"   			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="collocatoreDestinazione" 			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="numeroPolizzeDaTrasferire"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		   		</nested-mapping>
		    </field-map> 
		    <field-map attributeName="polizzeDaTrasferire" length="29700" > 
				<nested-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.PolizzaDaTrasferire" iterations="1100" blockLength="27"> 
					<field-map attributeName="codiceSocieta"       	length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="categoria"       		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="agenzia"  			length="6" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="numeroCollettivo"     length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0" />
				    <field-map attributeName="numeroPolizza"  		length="7" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0" />
				    <field-map attributeName="esito"  				length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				</nested-mapping>
		    </field-map>   		
	  	</input-mapping> 
	  	<output-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.TrasferimentoPolizzeResponseDTO"> 
			<!-- output :29740 + 44 = 29784 car -->      
	      	<field-map attributeName="inputTrasferimentoPolizze" length="40" > 
				<nested-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.InputTrasferimentoPolizze"> 
			   		<field-map attributeName="tipoRichiesta"   		length="3"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
					<field-map attributeName="dataTrasferimento"    length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
			   			valida="true" natura="Data" nomeAttributoERR="dataInizioValidita" nomeKeyERR="erroriGestioneLivelloUno"/>
			   		<field-map attributeName="agenziaDestinazione"   length="6"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			   		<field-map attributeName="subAgenteDestinazione"   			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			   		<field-map attributeName="collocatoreDestinazione" 			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			   		<field-map attributeName="numeroPolizzeDaTrasferire"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		   		</nested-mapping>
		    </field-map> 
		    <field-map attributeName="polizzeDaTrasferire" length="29700" > 
				<nested-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.PolizzaDaTrasferire" iterations="1100" blockLength="27"> 
					<field-map attributeName="codiceSocieta"       	length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="categoria"       		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="agenzia"  			length="6" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				    <field-map attributeName="numeroCollettivo"     length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0" />
				    <field-map attributeName="numeroPolizza"  		length="7" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0" />
				    <field-map attributeName="esito"  				length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				   </nested-mapping>
		    </field-map>
		     
		    <field-map attributeName="transferimentoPolizzeError" length="44" > 
				<nested-mapping className="it.sistinf.albedoweb.trasferimentopolizze.dto.TrasferimentoPolizzeError"> 
					<field-map attributeName="tipoRichiestaErr"              length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	     	 		<field-map attributeName="dataTrasferimentoErr"          length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="agenziaDestinazioneErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="subAgenteErr"               	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="collocatoreErr"               	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="numeroPolizzeDaTrasferireErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="codiceSocietaErr"              length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="categoriaErr"                	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     		<field-map attributeName="agenziaErr"            		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 		<field-map attributeName="numeroCollettivoErr"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 		<field-map attributeName="numeroPolizzaErr"              length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				 </nested-mapping>
		     </field-map>		 
	   </output-mapping> 
 	</rule> 
</rules> 