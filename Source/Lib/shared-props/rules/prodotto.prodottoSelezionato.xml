<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PRODOTTO-SELEZIONATO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0034</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoSelezionatoRequestDTO">
		<!-- Totali 165 car (150 + 61) -->		
			<field-map attributeName="prodotto" length="61">
				<nested-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoDTO" iterations="0" blockLength="61">
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceProdotto" 	length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoOldProdotto"  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoSelezionatoResponseDTO">
		<!-- Totali 2038 car (150 + 64 + 1824) -->		
			<field-map attributeName="prodotto" length="61">
				<nested-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoDTO" iterations="0" blockLength="61">
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceProdotto" 	length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoOldProdotto"  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>				
				</nested-mapping>
			</field-map>
			<field-map attributeName="flNewCommarea" length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"  length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
		</output-mapping>
	</rule>
</rules>