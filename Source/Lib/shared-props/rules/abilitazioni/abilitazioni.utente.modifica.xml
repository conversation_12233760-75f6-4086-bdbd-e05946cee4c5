<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>MODIFICA-UTENTE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>WSDB0043</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
    <logAppServDesc>MODIFICA UTENTE</logAppServDesc>
    <areaFunzionale>ABILITAZIONI-UTENTI</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
      <input-mapping className="it.sistinf.albedoweb.abilitazioni.utente.dto.UtenteRequestDTO" nomeKeyERR="erroriSalvaUtente">
      	<!-- input : 96 car -->
        <field-map attributeName="username" 			length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="descrizione" 			length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="codProfilo" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="codAgenzia" 			length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="codSubagenzia" 		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="usernameAlbedo"  		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="codCollocatore" 		length="7" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="codGruppo" 			length="3" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
    	<field-map attributeName="password" 			length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.abilitazioni.utente.dto.UtenteResponseDTO">
      	<!-- output :  121 car -->
      	<field-map attributeName="username" 			length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="descrizione" 			length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="codProfilo" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="codAgenzia" 			length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="codSubagenzia" 		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="usernameAlbedo"  		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="codCollocatore" 		length="7" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="codGruppo" 			length="3" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="password" 			length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="usernameCdErr" 		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="descrizioneCdErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
      	<field-map attributeName="codProfiloCdErr" 		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="codAgenziaCdErr" 		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
      	<field-map attributeName="codSubagenziaCdErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="usernameAlbedoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="codCollocatoreCdErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="codGruppoCdErr" 		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
     	<field-map attributeName="errPassword" 			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
      </output-mapping>
  </rule>
</rules>
