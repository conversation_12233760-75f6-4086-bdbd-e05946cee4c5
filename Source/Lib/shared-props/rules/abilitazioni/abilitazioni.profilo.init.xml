<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-NUOVO-PROFILO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSDB0000</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.abilitazioni.profilo.dto.ProfiloRequestDTO">
			<!-- Totali 169 car (150 + 10) -->
			<!-- 10 car -->
			<field-map attributeName="dataInizio"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " valida="false" natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.abilitazioni.profilo.dto.ProfiloResponseDTO">
		    <!-- Totali 1984 car (150 + 10 + 1824) --> 
		    <!-- 20 car -->
			<field-map attributeName="dataInizio"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " valida="false" natura="Data"/>			
			<field-map attributeName="dataFine"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " valida="false" natura="Data"/>
 		</output-mapping>
	</rule>
</rules>