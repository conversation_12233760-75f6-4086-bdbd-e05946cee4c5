<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>INIT-AZIONE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>WSDB0030</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <input-mapping className="it.sistinf.albedoweb.abilitazioni.azione.dto.AzioneRequestDTO">
      <field-map attributeName="codAzione" 			length="30" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.abilitazioni.azione.dto.AzioneResponseDTO">
      <field-map attributeName="codAzione" 			length="30" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
      <field-map attributeName="codContesto" 		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
      <field-map attributeName="codAmbito" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
      <field-map attributeName="descrizioneAzione" 	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
    </output-mapping>
  </rule>
</rules>
