<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ABIL-APRI-POLIZZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSAUTH02</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.abilitazioni.dto.AbilitazioniApriPolizzaRequestDTO">
			<!-- Totali 174 car (150 + 26) -->
			<!-- 26 car -->
			<field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codProdotto"     length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="statoProdotto"   length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="causaleProdotto" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.abilitazioni.dto.AbilitazioniApriPolizzaResponseDTO">
		    <!-- Totali 7303 car (150 + 5329 + 1824) --> 
		    <!-- 5329 car -->
			<field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codProdotto"     length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="statoProdotto"   length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="causaleProdotto" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="elencoAbilitazioni" length="5300" >
            	<nested-mapping className="it.sistinf.common.dto.AzioneDTO" iterations="100" blockLength="53">
            		<field-map attributeName="codSoc"   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="contesto" length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="ambito"   length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="azione"   length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
            		<field-map attributeName="fenomeno" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
            	</nested-mapping>
            </field-map>
 		</output-mapping>
	</rule>
</rules>