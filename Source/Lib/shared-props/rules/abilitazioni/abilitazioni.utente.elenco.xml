<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-UTENTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WDBL0041</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.abilitazioni.utente.dto.UtenteRequestDTO" nomeKeyERR="listaUtentiErrori">
			<!-- input : 8 car -->
			<field-map attributeName="usernameRicerca"  length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.abilitazioni.utente.dto.ListaUtentiResponseDTO">
		    <!-- output : 28817 car -->
		    <field-map attributeName="usernameRicerca"    length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
		    <field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaUtenti" length="28800" >
				<nested-mapping className="it.sistinf.albedoweb.abilitazioni.utente.dto.UtenteResponseDTO" iterations="300" blockLength="96">
					<field-map attributeName="username" 			length="8"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      	<field-map attributeName="codProfilo" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="descrizione" 			length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="codGruppo" 			length="3" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="codAgenzia" 			length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      	<field-map attributeName="codSubagenzia" 		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			        <field-map attributeName="usernameAlbedo"  		length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			        <field-map attributeName="codCollocatore" 		length="7" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
				</nested-mapping>
			</field-map>
			<field-map attributeName="usernameRicercaCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
 		</output-mapping>
	</rule>
</rules>