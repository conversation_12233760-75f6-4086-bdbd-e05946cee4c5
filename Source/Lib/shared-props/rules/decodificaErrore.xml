<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DECODIFICA-ERRORE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0008</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.errore.decodifica.dto.DecodificaErroreRequestDTO">
			<!-- Totali 550 car (150 + 400) -->
			<!-- 400 car -->
            <field-map attributeName="elencoErrori" length="400" >
            	<nested-mapping className="it.sistinf.common.utility.CodiciErroreDTO" iterations="100" blockLength="4">
            		<field-map attributeName="codiceErrore" length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
			<field-map attributeName="tipoTabellaMesgErrori" length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="  " offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.errore.decodifica.dto.DecodificaErroreResponseDTO">
		    <!-- Totali 10374 car (150 + 8400 + 1824) --> 
		    <!-- 8400 car -->
            <field-map attributeName="decodificaCodiciErrore" length="8400" >
            	<nested-mapping className="it.sistinf.common.utility.DecodificaCodiciErroreDTO" iterations="100" blockLength="84">
             		<field-map attributeName="codiceErrore" 	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            		<field-map attributeName="descrizioneErrore" length="80" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""     offset="" padding=" "/>             		
            	</nested-mapping>
            </field-map>
		</output-mapping>
	</rule>
</rules>
    