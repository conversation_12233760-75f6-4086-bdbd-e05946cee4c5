<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-POSIZIONI-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0038</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UnitaTecnicaDettaglioRequestDTO">
			<!-- Totali 171 car (150 + 21) -->
			<!-- 21 car -->
		    <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UnitaTecnicaDettaglioResponseDTO">
		    <!-- Totali 31879 car (150 + 29905 + 1824) --> 
		    <!-- 21025 car -->
		    <!-- Campi di input 25 car -->
		    <field-map attributeName="categoria"             length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"  	     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"     		 length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"         length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numPosizioniPresenti"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="flAltri"     		     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
            <field-map attributeName="posizioniUT" length="29880" >
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="360" blockLength="83">
            		<field-map attributeName="categoriaUT"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="posizione"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceUT"              length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCodiceUT"          length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="decorrenzaUT"    	     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="durataAnniUT"          length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="durataMesiUT"          length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="premioNetto"           length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="prestazione"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statoPolizzaLista"     length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="variazionePolizza"     length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
				</nested-mapping>
			</field-map>            
	</output-mapping>
     </rule>
</rules>