<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-DATA-CALENDARIO-NAV</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE095</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA DATA CALENDARIO NAV</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
     <multipleTransaction>true</multipleTransaction>
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSE094 Lunghezza: 150 + 24 + 1824 = 1998  -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.calendariNav.dto.CalendariNavRequestDTO" nomeKeyERR="erroriCalendariNav"> 
   <!-- input : 24 car --> 
   		<field-map attributeName="codiceCalendarioNav"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   		<field-map attributeName="annoCalendarioNav"  			length="4"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		<field-map attributeName="dataNav"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.calendariNav.dto.CalendariNavResponseDTO"> 
      <!-- output :24 car -->      
        <field-map attributeName="codiceCalendarioNav"  		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   		<field-map attributeName="annoCalendarioNav"  			length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		<field-map attributeName="dataNav"       				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
   </output-mapping> 
 </rule> 
</rules> 