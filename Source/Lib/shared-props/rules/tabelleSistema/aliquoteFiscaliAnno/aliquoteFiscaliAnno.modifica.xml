<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-ALIQUOTE-FISCALI-ANNO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE104</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA ALIQUOTE FISCALI ANNO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE GESTIONALI</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE104 Lunghezza: 150 + (589 + 232 = 812) + 1824 = 2795 -->
        
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoRequestDTO" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno">
    	<!-- input : 589 car --> 
    	<field-map attributeName="anno" 				length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="visentini" 			length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="visentini" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
      	<field-map attributeName="acconto" 				length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="acconto" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
       	<field-map attributeName="rendite" 				length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="rendite" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
        <field-map attributeName="renditeFip" 			length="5" 	precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="renditeFip" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		<field-map attributeName="datiIrpefOr" length="120" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefOrDTO" iterations="6" blockLength="20"> 
		          <field-map attributeName="irpefInd"         	    length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="irpefS"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="datiIrpefOr[].irpefS" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		          <field-map attributeName="irpefP"        			length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="datiIrpefOr[].irpefP" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		   </nested-mapping>
		</field-map>  
		<field-map attributeName="datiIrpefSep" length="120" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefSepDTO" iterations="6" blockLength="20"> 
		          <field-map attributeName="separInd"         	    length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="separS"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="datiIrpefSep[].separS" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		          <field-map attributeName="separP"        			length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="datiIrpefSep[].separP" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		   </nested-mapping>
		</field-map>
		<field-map attributeName="impSostAgev" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="impSostAgev" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>    
		<field-map attributeName="impSostStand" 		length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="impSostStand" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		<field-map attributeName="impMonti" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="impMonti" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		<field-map attributeName="imposta26" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="imposta26" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		<field-map attributeName="generale" 			length="14" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="generale" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		<field-map attributeName="pensionati" 			length="14" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="dipendenti" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="dipendenti" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
        <field-map attributeName="autonomi" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="autonomi" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
        <field-map attributeName="impDeducib" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="impDeducib" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
        <field-map attributeName="detrazVita" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="detrazVita" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
      	<field-map attributeName="scaglioni" length="216" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.ScaglioniDTO" iterations="9" blockLength="24"> 
		          <field-map attributeName="scaReddInd"         	length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="scaRed"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="scaglioni[].scaRed" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		          <field-map attributeName="impDetr"        		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="5" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="scaglioni[].impDetr" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/>
		   </nested-mapping>
		</field-map>   
		<field-map attributeName="tassoInteresseRitLiq" length="5" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="tassoInteresseRitLiq" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno"/> 
  	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoResponseDTO"> 
      	
      	<!-- output : 589 (stessi dati input) + 232 car  = 821 -->      
   		<field-map attributeName="anno" 				length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="visentini" 			length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"  />
      	<field-map attributeName="acconto" 				length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
       	<field-map attributeName="rendite" 				length="5"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
        <field-map attributeName="renditeFip" 			length="5" 	precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="datiIrpefOr" length="120" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefOrDTO" iterations="6" blockLength="20"> 
		          <field-map attributeName="irpefInd"         	    length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="irpefS"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		          <field-map attributeName="irpefP"        			length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		   </nested-mapping>
		</field-map>   
		<field-map attributeName="datiIrpefSep" length="120" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefSepDTO" iterations="6" blockLength="20"> 
		          <field-map attributeName="separInd"         	    length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="separS"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		          <field-map attributeName="separP"        			length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		   </nested-mapping>
		</field-map>
		<field-map attributeName="impSostAgev" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>    
		<field-map attributeName="impSostStand" 		length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="impMonti" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="imposta26" 			length="5" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="generale" 			length="14" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="pensionati" 			length="14" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="dipendenti" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
        <field-map attributeName="autonomi" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
        <field-map attributeName="impDeducib" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
        <field-map attributeName="detrazVita" 			length="14" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
      	<field-map attributeName="scaglioni" length="216" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.ScaglioniDTO" iterations="9" blockLength="24"> 
		          <field-map attributeName="scaReddInd"         	length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="0" />
		          <field-map attributeName="scaRed"         		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		          <field-map attributeName="impDetr"        		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="5" numDecimali="2" separatoreMigliaia="true"/>
		   </nested-mapping>
		</field-map>
		<field-map attributeName="tassoInteresseRitLiq" length="5" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
 		
 		<!--  variabili di errore -->
   		<field-map attributeName="annoErr" 				length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="visentiniErr" 		length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="accontoErr" 			length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
       	<field-map attributeName="renditeErr" 			length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="renditeFipErr" 		length="4" 	precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="datiIrpefOrErr" length="48" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefOrErrDTO" iterations="6" blockLength="8"> 
		          <field-map attributeName="irpefSErr"         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="irpefPErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=" " />
		   </nested-mapping>
		</field-map>  
		<field-map attributeName="datiIrpefSepErr" length="48" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.DatiIrpefSepErrDTO" iterations="6" blockLength="8"> 
		          <field-map attributeName="separSErr"         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="separPErr"        		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=" " />
		   </nested-mapping>
		</field-map>
		<field-map attributeName="impSostAgevErr" 		length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />    
		<field-map attributeName="impSostStandErr" 		length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="impMontiErr"	 		length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="imposta26Err"	 		length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="generaleErr" 			length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="pensionatiErr" 		length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="dipendentiErr" 		length="4" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="autonomiErr" 			length="4" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="impDeducibErr" 		length="4" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="detrazVitaErr" 		length="4" precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
      	<field-map attributeName="scaglioniErr" length="72" > 
		    <nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.ScaglioniErrDTO" iterations="9" blockLength="8"> 
		          <field-map attributeName="scaRedErr"         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="impDetrErr"        		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
		</field-map>  
		<field-map attributeName="tassoInteresseRitLiqErr" 	length="4" precision="0" numericScale="0" align="right"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" /> 
   </output-mapping> 
  </rule>
</rules>
