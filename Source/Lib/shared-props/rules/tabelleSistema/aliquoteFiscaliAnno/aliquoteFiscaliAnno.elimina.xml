<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-ALIQUOTE-FISCALI-ANNO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE105</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA ALIQUOTE FISCALI ANNO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE GESTIONALI</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: DWLSE105 Lunghezza: 150 + 4 + 1824 = 1978  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoRequestDTO" nomeKeyERR="erroriSalvaAliquoteFiscaliAnno">
        
        <!-- input :  4  car -->
      	<field-map attributeName="anno"	length="4"	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>      
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoResponseDTO">
      
		<field-map attributeName="anno"	length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>      
    </output-mapping>
  </rule>
</rules>
