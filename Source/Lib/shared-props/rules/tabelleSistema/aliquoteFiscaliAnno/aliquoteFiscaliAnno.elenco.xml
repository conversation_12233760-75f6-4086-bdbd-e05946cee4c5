<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-ALIQUOTE-FISCALI-ANNO</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLALQ</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  
  <!-- Definizione commarea: DWLSLALQ Lunghezza: 150 + 405 + 1824 = 2379  -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoRequestDTO" nomeKeyERR="erroriElencoAliquoteFiscaliAnno"> 
	
	<!-- input : car -->   
	<field-map attributeName="annoRicerca" 		length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
 
  </input-mapping> 
  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.ListaAliquoteFiscaliAnnoResponseDTO"> 
	<!-- output : 150 + 405 + 1824   = 2379-->
	 <field-map attributeName="numElementiTrovati" 	length="4" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/> 
   	<field-map attributeName="flLimite"         	length="1" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
    <field-map attributeName="listaAliquoteFiscaliAnno" length="400" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.aliquoteFiscaliAnno.dto.AliquoteFiscaliAnnoResponseDTO" iterations="100" blockLength="4">            
			<field-map attributeName="annoRicerca" 		length="4" precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		    
		</nested-mapping>
	</field-map>     
   </output-mapping> 
 </rule> 
</rules> 