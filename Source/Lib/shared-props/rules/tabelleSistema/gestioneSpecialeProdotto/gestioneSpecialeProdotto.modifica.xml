<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-GESTIONE-SPECIALE-PRODOTTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE155</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA GESTIONE SPECIALE PRODOTTO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE DI SISTEMA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>    
    <!-- Definizione commarea: DWLSE155 Lunghezza: 150 + 73 [ 65 + 08 ] + 1824 = 2044 -->
        
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.gestioneSpecialeProdotto.dto.GestioneSpecialeProdottoRequestDTO" nomeKeyERR="erroriSalvaGestioneSpecialeProdotto">
		<!-- input : 65 car -->   
		<field-map attributeName="societaSelezionata"	length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		<field-map attributeName="codice" 				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrizione" 			length="60"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.gestioneSpecialeProdotto.dto.GestioneSpecialeProdottoResponseDTO">
        <!-- output : 65 (stessi dati input) + 08 = 73 car -->   
        <field-map attributeName="societaSelezionata"	length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		<field-map attributeName="codice" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrizione" 		length="60"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

		<!-- mappatura area output:  08 car --> 
		<field-map attributeName="codiceErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="descrizioneErr" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
   	</output-mapping>  	
    		
  </rule>
</rules>
