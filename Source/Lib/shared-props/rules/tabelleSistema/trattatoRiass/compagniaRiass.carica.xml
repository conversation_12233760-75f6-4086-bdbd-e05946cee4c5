<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>CARICA-DETTAGLIO-COMPAGNIA-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE110</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSE110 Lunghezza: 150 + 237 [ 14 + 223 ] + 1824 = 2211 -->  
	<input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassRequestDTO" nomeKeyERR="erroriAggiungiCompagniaRiass">
		<!-- input : 14 car -->   
		<field-map attributeName="categoria" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"		length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	</input-mapping>

    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassResponseDTO">

		<!-- stessi campi di input : 14 car -->   
		<field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
		<!-- campi di output specifici : 223 car  -->
		<field-map attributeName="tipoTrattatoDettaglio"	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="tipoIntestatario"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="intestatario" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrIntestatario" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		

		<field-map attributeName="codProvvigione" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="facoltativo" 				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="bollettino" 				length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
		<field-map attributeName="capMaxQotPerc"			length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="capMaxQotImp"				length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>				
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>		    
		
		<field-map attributeName="percVia" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formVia" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotVia" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				
		<field-map attributeName="percSan" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formSan" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotSan" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	   
		<field-map attributeName="percPrf" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formPrf" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotPrf" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percSpr" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formSpr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotSpr" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percAlt" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formAlt" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotAlt" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

		<field-map attributeName="percPrm" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formPrm" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotPrm" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	</output-mapping>
  </rule>
</rules>
