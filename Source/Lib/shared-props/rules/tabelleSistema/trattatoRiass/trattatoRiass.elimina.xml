<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-TRATTATO-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE109</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA TRATTATO DI RIASSICURAZIONE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: DWLSE109 Lunghezza: 150 + 31 + 1824 = 2025  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassRequestDTO" nomeKeyERR="erroriSalvaTrattatoRiass">
        
        <!-- input :  31  car -->
		<field-map attributeName="categoria" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataInizioVal"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
		<field-map attributeName="dataFineVal"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassResponseDTO">
      
		<!-- stessi campi di input :  31  car -->
		<field-map attributeName="categoria" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataInizioVal"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
		<field-map attributeName="dataFineVal"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
		
    </output-mapping>
  </rule>
</rules>
