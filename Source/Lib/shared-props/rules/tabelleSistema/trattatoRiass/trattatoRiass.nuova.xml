<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>NUOVO-TRATTATO-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE107</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA TRATTATO DI RIASSICURAZIONE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSE107 Lunghezza: 150 + 300 [ 200 + 100 ] + 1824 = 2274 -->      
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassRequestDTO" nomeKeyERR="erroriSalvaTrattatoRiass">
		<!-- input : 200 car -->   
		<field-map attributeName="categoria" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
		<field-map attributeName="descrTrattatoRiass" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="tipoTrattato"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="dataInizioVal"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataInizioVal" nomeKeyERR="erroriSalvaTrattatoRiass"/>
		<field-map attributeName="dataFineVal" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataFineVal" nomeKeyERR="erroriSalvaTrattatoRiass"/>
		<field-map attributeName="trattatoInfortuni" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="trattatoAltre" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="tipoCessione" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="tipoRiass" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
		<field-map attributeName="tipoTrattato_2" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="protezConserv" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    
		<field-map attributeName="tipoPienoConserv" 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="pienoConservImp" 		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="8" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="pienoConserv" nomeKeyERR="erroriSalvaTrattatoRiass" />
		<field-map attributeName="pienoConservPerc"		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="pienoConserv" nomeKeyERR="erroriSalvaTrattatoRiass" />
		<field-map attributeName="pienoConservForm"		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="pienoConserv" nomeKeyERR="erroriSalvaTrattatoRiass" />		
		<field-map attributeName="capMinimoRiass" 		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="capMinimoRiass" nomeKeyERR="erroriSalvaTrattatoRiass" />
		<field-map attributeName="aliquotaCessione" 	length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="aliquotaCessione" nomeKeyERR="erroriSalvaTrattatoRiass" />
		<field-map attributeName="limiteMassimo" 		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="limiteMassimo" nomeKeyERR="erroriSalvaTrattatoRiass" />
		<field-map attributeName="rischioComune" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoEccedenza" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="ricalcoloProporz" 	length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codProvvigione" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="calcoloCapitaleRiass" length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoDefinito" 	length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoInEuro" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassResponseDTO">
      	
      	<!-- output : 200 (stessi dati input) + 100 car -->   
		<field-map attributeName="categoria" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
		<field-map attributeName="descrTrattatoRiass" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="tipoTrattato"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="dataInizioVal"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		<field-map attributeName="dataFineVal" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		<field-map attributeName="trattatoInfortuni" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="trattatoAltre" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="tipoCessione" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="tipoRiass" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
		<field-map attributeName="tipoTrattato_2" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="protezConserv" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    
		<field-map attributeName="tipoPienoConserv" 	length="01"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="pienoConservImp" 		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="8" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="pienoConservPerc"		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="pienoConservForm"		length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="capMinimoRiass" 		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="aliquotaCessione" 	length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="limiteMassimo" 		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="rischioComune" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoEccedenza" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="ricalcoloProporz" 	length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codProvvigione" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="calcoloCapitaleRiass" length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoDefinito" 	length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="trattatoInEuro" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
   		
   		<!-- mappatura area output: 100 car -->
		<field-map attributeName="codTrattatoRiassErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>   		   		
		<field-map attributeName="descrTrattatoRiassErr" 	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		<field-map attributeName="tipoTrattatoErr"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		<field-map attributeName="dataInizioValErr"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="dataFineValErr" 			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="trattatoInfortuniErr" 	length="04"	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		<field-map attributeName="trattatoAltreErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="tipoCessioneErr" 			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="tipoRiassErr" 			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>						
		<field-map attributeName="tipoTrattato_2Err" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="protezConservErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="tipoPienoConservErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="pienoConservImpErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="pienoConservPercErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="pienoConservFormErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="capMinimoRiassErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="aliquotaCessioneErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="limiteMassimoErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="rischioComuneErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="trattatoEccedenzaErr" 	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="ricalcoloProporzErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="codProvvigioneErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="calcoloCapitaleRiassErr" 	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="trattatoDefinitoErr" 		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="trattatoInEuroErr" 		length="04"	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
   	</output-mapping>
	
  </rule>
</rules>
