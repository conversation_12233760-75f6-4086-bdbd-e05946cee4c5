<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-TRATTATO-RIASS</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLRIA</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  
  <!-- Definizione commarea: DWLSLRIA Lunghezza: 150 + 21056 [51 + 21005] + 1824 =  ??? -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassRequestDTO" nomeKeyERR="erroriElencoTrattatoRiass"> 
	
	<!-- input : 51 car -->	
	<field-map attributeName="categoria"			 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
	<field-map attributeName="codTrattatoRiassRicerca" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="descrTrattatoRiassRicerca" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.ListaTrattatoRiassResponseDTO"> 
	<!-- output : 51 (stessi dati input) + 21005  car =  21056 -->
	<field-map attributeName="categoria"			 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
	<field-map attributeName="codTrattatoRiassRicerca" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="descrTrattatoRiassRicerca" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       
    <field-map attributeName="numElementiTrovati" 	length="04" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/> 
   	<field-map attributeName="flAltri"         		length="01" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
    <field-map attributeName="listaTrattatoRiass" length="21000" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassResponseDTO" iterations="300" blockLength="70">            
			<field-map attributeName="codTrattatoRiass" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
			<field-map attributeName="descrTrattatoRiass" 			length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	   
		  	<field-map attributeName="dataInizioVal" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>  	
			<field-map attributeName="dataFineVal"					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>  	
		</nested-mapping>
	</field-map>     
   </output-mapping> 
 </rule> 
</rules> 