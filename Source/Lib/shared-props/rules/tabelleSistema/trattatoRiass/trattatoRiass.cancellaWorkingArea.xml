<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>CANCELLA-WORKING-AREA-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>WSER0035</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: WVCOMM35 Lunghezza: 150 + 2 + 1824 = 1976  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassRequestDTO" nomeKeyERR="erroriSalvaTrattatoRiass">
        
        <!-- input :  2  car -->
		<field-map attributeName="esito" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.TrattatoRiassResponseDTO">
      
		<!-- stessi campi di input :  2  car -->
		<field-map attributeName="esito" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		

    </output-mapping>
  </rule>
</rules>
