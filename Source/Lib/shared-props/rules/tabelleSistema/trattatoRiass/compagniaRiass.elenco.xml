<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-COMPAGNIA-RIASS</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLCRI</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  
  <!-- Definizione commarea: DWLSLRIA Lunghezza: 150 + 6816 [11 + 6805] + 1824 =  8790 -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassRequestDTO" nomeKeyERR="listaCompagniaRiass"> 
	
	<!-- input : 11 car -->	
	<field-map attributeName="categoria"			 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
	<field-map attributeName="codTrattatoRiass" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<!-- <field-map attributeName="dataInizioVal" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  -->
  </input-mapping> 
  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.ListaCompagniaRiassResponseDTO"> 
	<!-- output : 11 (stessi dati input) + 6805  car =  6816 -->
	<field-map attributeName="categoria"			 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
	<field-map attributeName="codTrattatoRiass" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    <!-- <field-map attributeName="dataInizioVal" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> -->
       
    <field-map attributeName="numElementiTrovati" 	length="04" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/> 
   	<field-map attributeName="flAltri"         		length="01" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
    <field-map attributeName="listaCompagniaRiass" length="6800" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassResponseDTO" iterations="100" blockLength="68">            
			<field-map attributeName="progressivo" 				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
			<field-map attributeName="tipoTrattatoDettaglio"	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	   
		  	<field-map attributeName="intestatario" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  	
			<field-map attributeName="descrIntestatario"		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  	
			<field-map attributeName="percentualeImporto"		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>			
		</nested-mapping>
	</field-map>     
   </output-mapping> 
 </rule> 
</rules> 