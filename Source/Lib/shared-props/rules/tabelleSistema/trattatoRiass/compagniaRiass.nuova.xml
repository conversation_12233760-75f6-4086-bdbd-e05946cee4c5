<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>NUOVO-COMPAGNIA-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE111</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA COMPAGNIA RIASSICURATRICE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSE111 Lunghezza: 150 + 349 [ 237 + 112 ] + 1824 = 2324 -->
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassRequestDTO" nomeKeyERR="erroriAggiungiCompagniaRiass">

		<!-- input : 237 car -->   
		<field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"				length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		
		<field-map attributeName="tipoTrattatoDettaglio"	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="tipoIntestatario"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="intestatario" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrIntestatario" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		

		<field-map attributeName="codProvvigione" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="facoltativo" 				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="bollettino" 				length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
		<field-map attributeName="capMaxQotPerc"			length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="capMaxQot" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="capMaxQotImp"				length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="capMaxQot" nomeKeyERR="erroriAggiungiCompagniaRiass"/>				
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formula" nomeKeyERR="erroriAggiungiCompagniaRiass"/>		    
		
		<field-map attributeName="percVia" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percVia" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formVia" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formVia" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotVia" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				
		<field-map attributeName="percSan" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percSan" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formSan" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formSan" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotSan" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	   
		<field-map attributeName="percPrf" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percPrf" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formPrf" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formPrf" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotPrf" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percSpr" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percSpr" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formSpr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formSpr" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotSpr" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percAlt" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percAlt" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formAlt" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formAlt" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotAlt" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

		<field-map attributeName="percPrm" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="percPrm" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="formPrm" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="formPrm" nomeKeyERR="erroriAggiungiCompagniaRiass"/>
		<field-map attributeName="quotPrm" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassResponseDTO">
      	
      	<!-- output : 237 (stessi dati input) + 112 car -->   
   		<field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="tipoTrattatoDettaglio"	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="tipoIntestatario"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="intestatario" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrIntestatario" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		

		<field-map attributeName="codProvvigione" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="facoltativo" 				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="bollettino" 				length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
		<field-map attributeName="capMaxQotPerc"			length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="capMaxQotImp" 			length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>				
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>		    
		
		<field-map attributeName="percVia" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formVia" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotVia" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				
		<field-map attributeName="percSan" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formSan" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotSan" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	   
		<field-map attributeName="percPrf" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formPrf" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotPrf" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percSpr" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formSpr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotSpr" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		<field-map attributeName="percAlt" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formAlt" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotAlt" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

		<field-map attributeName="percPrm" 					length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="formPrm" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="quotPrm" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		
   		<!-- mappatura area output: 112 car -->
		<field-map attributeName="tipoTrattatoDettaglioErr"		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		                
		<field-map attributeName="tipoIntestatarioErr"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		                
		<field-map attributeName="intestatarioErr" 				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="descrIntestatarioErr" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		                

		<field-map attributeName="codProvvigioneErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="facoltativoErr" 				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="bollettinoErr" 				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>						
		<field-map attributeName="capMaxQotPercErr"				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="capMaxQotImpErr" 				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		<field-map attributeName="formulaErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		                

		<field-map attributeName="percViaErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formViaErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="quotViaErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        

		<field-map attributeName="percSanErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formSanErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="quotSanErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        

		<field-map attributeName="percPrfErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formPrfErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="quotPrfErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        

		<field-map attributeName="percSprErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formSprErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="quotSprErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        

		<field-map attributeName="percAltErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formAltErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="quotAltErr" 					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        

		<field-map attributeName="percPrmErr"					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="formPrmErr"					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        
		<field-map attributeName="quotPrmErr"					length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	</output-mapping> 
	
  </rule>
</rules>
