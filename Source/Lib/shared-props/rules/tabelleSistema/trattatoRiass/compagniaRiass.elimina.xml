<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-COMPAGNIA-RIASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE113</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA COMPAGNIA RIASSICURATRICE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: DWLSE113 Lunghezza: 150 + 14 + 1824 = 1988  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassRequestDTO" nomeKeyERR="erroriAggiungiCompagniaRiass">
        
		<!-- input : 14 car -->   
		<field-map attributeName="categoria" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"		length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		      
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatoriass.dto.CompagniaRiassResponseDTO">
      
		<!-- stessi campi di input :  14  car -->   
		<field-map attributeName="categoria" 		length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTrattatoRiass"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="progressivo"		length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
    </output-mapping>
  </rule>
</rules>
