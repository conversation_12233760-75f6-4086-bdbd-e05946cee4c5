<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-PIANO-CONTO</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLLPC</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  
  <!-- Definizione commarea: DWLSLLPC Lunghezza: 150 + 6208 [03 + 6205] + 1824 =  8182 -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoRequestDTO" nomeKeyERR="erroriElencoPianoConto"> 
	
	<!-- input : 03 car -->
	<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="codTipoPagamentoRicerca" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
  </input-mapping> 
  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.ListaPianoContoResponseDTO"> 
	<!-- output : 03 (stessi dati input) + 6205  car =  6208 -->
	<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="codTipoPagamentoRicerca" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
       
    <field-map attributeName="numElementiTrovati" 			length="04" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/> 
   	<field-map attributeName="flAltri"         				length="01" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
    <field-map attributeName="listaPianoConto" length="6200" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoResponseDTO" iterations="100" blockLength="62">            
			<field-map attributeName="codTipoPagamento" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
			<field-map attributeName="descTipoPagamento" 			length="60"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	   
		</nested-mapping>
	</field-map>     
   </output-mapping> 
 </rule> 
</rules> 