<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELENCO-TIPO-PAGAMENTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSLTPG</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSLTPG Lunghezza: 150 + 126 [01 + 125] + 1824 =  2100  -->  
	<input-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoRequestDTO">

		<!-- input : 01 car -->
		<field-map attributeName="categoria" 		length="01" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/>
	</input-mapping>

    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.ListaPianoContoResponseDTO">

		<!-- input : 01 car -->
		<field-map attributeName="categoria" 		length="01" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/>
  			
		<!-- campi di output specifici : 125 car  -->
		<field-map attributeName="numElementiTrovati" 			length="04" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/>
		<field-map attributeName="flAltri"         				length="01" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		

		<field-map attributeName="listaTipoPagamento" length="120" >
			<nested-mapping className="it.sistinf.albedoweb.domini.dto.ElementoDominioDTO" iterations="10" blockLength="12">			
				<field-map attributeName="codice" 				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
				<field-map attributeName="descrizione" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			</nested-mapping>
		</field-map>
	
	</output-mapping>
  </rule>
</rules>
