<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-PIANO-CONTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE062</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA PIANO CONTO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: DWLSE062 Lunghezza: 150 + 03 + 1824 = 1984  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoRequestDTO" nomeKeyERR="erroriSalvaPianoConto">
        
        <!-- input :  03  car -->
        <field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTipoPagamento" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		      
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoResponseDTO">
      
		<!-- stessi campi di input :  03  car -->
        <field-map attributeName="categoria" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		<field-map attributeName="codTipoPagamento" 		length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
    </output-mapping>
  </rule>
</rules>
