<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-PIANO-CONTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE061</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA PIANO CONTO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE061 Lunghezza: 150 + 1329 [ 877 + 452 ] + 1824 = 3303  -->  
    
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoRequestDTO" nomeKeyERR="erroriSalvaPianoConto">

		<!-- input : 887 car -->
		<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTipoPagamento" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descTipoPagamento" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="listaRighePianoConto" length="869" >
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.RigaPianoContoDTO" iterations="11" blockLength="79">			
				<field-map attributeName="descrVoce" 				length="19"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 

				<field-map attributeName="listaCodiciConto" length="60" >
					<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.CodiceContoDTO" iterations="05" blockLength="12">
						<field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    			<field-map attributeName="codiceConto" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    					<field-map attributeName="flagConto" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>				   
			</nested-mapping>
		</field-map>
		<field-map attributeName="codSocieta"				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codOperazione" 			length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoResponseDTO">
      	
      	<!-- output : 887 (stessi dati input) + 452 car -->
      	<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTipoPagamento" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descTipoPagamento" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="listaRighePianoConto" length="869" >
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.RigaPianoContoDTO" iterations="11" blockLength="79">			
				<field-map attributeName="descrVoce" 				length="19"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 

				<field-map attributeName="listaCodiciConto" length="60" >
					<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.CodiceContoDTO" iterations="05" blockLength="12">
						<field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    			<field-map attributeName="codiceConto" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    					<field-map attributeName="flagConto" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>				   
			</nested-mapping>
		</field-map>
		<field-map attributeName="codSocieta"				length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codOperazione" 			length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		
   		<!-- mappatura area output:  496 car -->
		<field-map attributeName="codTipoPagamentoErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="listaRighePianoContoErr" length="484" >
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.RigaPianoContoDTO" iterations="11" blockLength="44">			
				<field-map attributeName="descrVoceErr" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				<field-map attributeName="listaCodiciContoErr" length="40" >
					<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.CodiceContoDTO" iterations="05" blockLength="08">
		    			<field-map attributeName="codiceContoErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
    					<field-map attributeName="flagContoErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					</nested-mapping>
				</field-map>				   
			</nested-mapping>
		</field-map>
		<field-map attributeName="codSocietaErr"			length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="codOperazioneErr" 		length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		
   	</output-mapping> 
    		
  </rule>
</rules>
