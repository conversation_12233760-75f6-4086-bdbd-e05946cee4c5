<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELENCO-DESCRIZIONI-VOCI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE059</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSE059 Lunghezza: 150 + 881 [03 + 878] + 1824 =  2855  -->
	<input-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoRequestDTO">

		<!-- input : 03 car -->
		<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTipoPagamento" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
	</input-mapping>

    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.PianoContoResponseDTO">

		<!-- input : 03 car -->
		<field-map attributeName="categoria"				 	length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codTipoPagamento" 			length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			
		<!-- campi di output specifici : 878 car  -->
		<field-map attributeName="codTipoPagamentoErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		

		<field-map attributeName="listaRighePianoConto" length="869" >
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.RigaPianoContoDTO" iterations="11" blockLength="79">
				<field-map attributeName="descrVoce" 				length="19"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
				<field-map attributeName="listaCodiciConto" length="60" >
					<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.liquidazione.dto.CodiceContoDTO" iterations="05" blockLength="12">
		    			<field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    			<field-map attributeName="codiceConto" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    					<field-map attributeName="flagConto" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>				   
			</nested-mapping>			
		</field-map>
		<field-map attributeName="codSocieta"			 	length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="codOperazione" 			length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		    				
	</output-mapping>
  </rule>
</rules>
