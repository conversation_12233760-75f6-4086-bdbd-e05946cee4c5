<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-TRATTATO-COASS</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLCOA</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  
  <!-- Definizione commarea: DWLSLCOA Lunghezza: 150 + 7055 [50 + 7055] + 1824 =  9029 -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassRequestDTO" nomeKeyERR="erroriElencoTrattatoCoass"> 
	
	<!-- input : 50 car -->	
	<field-map attributeName="codTrattatoCoassRicerca" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="descrTrattatoCoassRicerca" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.ListaTrattatoCoassResponseDTO"> 
	<!-- output : 50 (stessi dati input) + 7005  car =  7055 -->
	<field-map attributeName="codTrattatoCoassRicerca" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	<field-map attributeName="descrTrattatoCoassRicerca" 	length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       
    <field-map attributeName="numElementiTrovati" 	length="04" 	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	 offset="" padding=""/> 
   	<field-map attributeName="flAltri"         		length="01" 	precision="0" 	numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
    <field-map attributeName="listaTrattatoCoass" length="7000" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassResponseDTO" iterations="100" blockLength="70">            
			<field-map attributeName="codTrattatoCoass" 			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
			<field-map attributeName="descrTrattatoCoass" 			length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	   
		  	<field-map attributeName="dataStipula" 					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		  		natura="Data" />  	
			<field-map attributeName="dataScadenza"					length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />  	
		</nested-mapping>
	</field-map>     
   </output-mapping> 
 </rule> 
</rules> 