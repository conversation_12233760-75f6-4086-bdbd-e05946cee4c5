<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-TRATTATO-COASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE074</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA TRATTATO DI COASSICURAZIONE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>

    <!-- Definizione commarea: DWLSE074 Lunghezza: 150 + 10 + 1824 = 1984  -->  
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassRequestDTO" nomeKeyERR="erroriSalvaTrattatoCoass">
        
        <!-- input :  20  car -->
		<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipula" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>	      
    </input-mapping>
     
    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassResponseDTO">
      
		<!-- stessi campi di input :  20  car -->
		<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipula" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>	
    </output-mapping>
  </rule>
</rules>
