<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>CARICA-DETTAGLIO-TRATTATO-COASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE071</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    <!-- Definizione commarea: DWLSE071 Lunghezza: 150 + 181 [20 + 161] + 1824 =  215<field-map attributeName="descrTrattatoCoass" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>5  -->  
	<input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassRequestDTO" nomeKeyERR="erroriSalvaTrattatoCoass">
		<!-- input : 20 car -->   
		<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipulaInput" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>	
	</input-mapping>

    <output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassResponseDTO">

		<!-- stessi campi di input : 20 car -->   
		<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipulaInput" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>		
		<!-- campi di output specifici : 161 car  -->
		<field-map attributeName="descrTrattatoCoass" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipula" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>		
		<field-map attributeName="dataScadenza"				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
		<field-map attributeName="tipoDelega" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percPremio" 				length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>						
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percRistoro" 		        length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>						
		<field-map attributeName="formulaRistoro" 			length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="listaPercentualeSocieta" length="90" > 
		<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.PercentualeSocietaDTO" iterations="10" blockLength="09">
		    <field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="societa" 				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
			<field-map attributeName="percentuale" 			length="05"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="true"  />	   
		</nested-mapping>
	</field-map>    				
	</output-mapping>
  </rule>
</rules>
