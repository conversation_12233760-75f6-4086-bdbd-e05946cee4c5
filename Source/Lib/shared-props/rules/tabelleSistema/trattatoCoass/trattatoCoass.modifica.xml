<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-TRATTATO-COASS</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE073</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA TRATTATO DI COASSICURAZIONE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE073 Lunghezza: 150 + 279 [ 171 + 108 ] + 1824 = 2253  -->  
    
    <input-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassRequestDTO" nomeKeyERR="erroriSalvaTrattatoCoass">

		<!-- input : 171 car -->
		<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrTrattatoCoass" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipula" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			valida="true" natura="Data"  nomeAttributoERR="dataStipula" nomeKeyERR="erroriSalvaTrattatoCoass"/>		
		<field-map attributeName="dataScadenza"				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			valida="true" natura="Data"  nomeAttributoERR="dataScadenza" nomeKeyERR="erroriSalvaTrattatoCoass"/>
		<field-map attributeName="tipoDelega" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percPremio" 				length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"  nomeAttributoERR="percPremio" nomeKeyERR="erroriSalvaTrattatoCoass"/>						
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percRistoro" 				length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"  nomeAttributoERR="percRistoro" nomeKeyERR="erroriSalvaTrattatoCoass"/>						
		<field-map attributeName="formulaRistoro" 			length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="listaPercentualeSocieta" length="90" > 
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.PercentualeSocietaDTO" iterations="10" blockLength="09">            
				<field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				<field-map attributeName="societa" 				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
				<field-map attributeName="percentuale" 			length="05"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					valida="true" natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="true"  nomeAttributoERR="listaPercentualeSocieta[].percentuale" nomeKeyERR="erroriSalvaTrattatoCoass"/>	   
			</nested-mapping>
		</field-map>

  	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.TrattatoCoassResponseDTO">
      	
    	<!-- output : 171 (stessi dati input) + 108 car -->
      	<field-map attributeName="codTrattatoCoass" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="descrTrattatoCoass" 		length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="dataStipula" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>		
		<field-map attributeName="dataScadenza"				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
		<field-map attributeName="tipoDelega" 				length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percPremio" 				length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>						
		<field-map attributeName="formula" 					length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="percRistoro" 				length="06"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="true"/>						
		<field-map attributeName="formulaRistoro" 			length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		<field-map attributeName="listaPercentualeSocieta"  length="90" > 
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.PercentualeSocietaDTO" iterations="10" blockLength="09">            
				<field-map attributeName="indiceRiga" 			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				<field-map attributeName="societa" 				length="03"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
				<field-map attributeName="percentuale" 			length="05"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="true"/>	   
			</nested-mapping>	      	
      	</field-map>	
   		
   		<!-- mappatura area output:  108 car -->
		<field-map attributeName="codTrattatoCoassErr" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="descrTrattatoCoassErr" 	length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="dataStipulaErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
		<field-map attributeName="dataScadenzaErr"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="tipoDelegaErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="percPremioErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>						
		<field-map attributeName="formulaErr" 				length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		<field-map attributeName="percRistoroErr" 			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>						
		<field-map attributeName="formulaRistoroErr" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>

		<field-map attributeName="listaPercentualeSocietaErr" length="80" > 
			<nested-mapping className="it.sistinf.albedoweb.tabelleSistema.trattatocoass.dto.PercentualeSocietaDTO" iterations="10" blockLength="08">            
				<field-map attributeName="societaErr" 				length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>		
				<field-map attributeName="percentualeErr" 			length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>	   
			</nested-mapping>	      	
      	</field-map>	
      	
   	</output-mapping> 
    		
  </rule>
</rules>
