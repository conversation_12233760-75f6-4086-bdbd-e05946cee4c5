<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>NUOVO-MANAGEMENT-FEE</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE163</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSE069 Lunghezza: 150 + 70 + 1824 = 2044 -->  
  <input-mapping className="it.sistinf.albedoweb.tabelleSistema.managementFee.dto.ManagementFeeRequestDTO" nomeKeyERR="erroriSalvaManagementFee"> 
	   	<!-- input: 50 car --> 
	   	<field-map attributeName="codFondo" 	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	   	<field-map attributeName="descFondo" 	  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.tabelleSistema.managementFee.dto.ManagementFeeResponseDTO"> 
	    <!-- output: 70  car -->      
	   	<field-map attributeName="codFondo" 	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	   	<field-map attributeName="descFondo" 	  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
		<field-map attributeName="dataCompetenza"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
			natura="Data"/>
		<field-map attributeName="dataMgFee"  	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
			natura="Data"/>
   </output-mapping> 
 </rule> 
</rules> 