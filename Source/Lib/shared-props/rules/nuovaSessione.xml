<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>NUOVA-SESSIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE240</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.sessione.dto.NuovaSessioneRequestDTO">
		<!-- Totali 151 car (150 + 1) -->		
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="filler" length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.sessione.dto.NuovaSessioneResponseDTO">
		<!-- Totali  car (150 + 1) -->		
			<field-map attributeName="filler" length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</output-mapping>
	</rule>
</rules>