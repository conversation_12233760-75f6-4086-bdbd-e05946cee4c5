<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>PREVENTIVO-A-SCADENZA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>VWLSE511</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>STAMPA PREVENTIVO A SCADENZA</logAppServDesc>
	<areaFunzionale>VITA POLIZZA STAMPA PREVENTIVO A SCADENZA</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: VWLSE511 Lunghezza: 150 + 984 + 1824 = 2958 -->  
  		<input-mapping className="it.sistinf.albedoweb.stampe.preventivoascadenza.dto.PreventivoScadenzaRequestDTO" nomeKeyERR="erroriPreventivoScadenza"> 
  		 <!-- input : 22 car --> 
	    	<field-map attributeName="categoria"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	    
	    	<field-map attributeName="agenzia" 				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="numeroCollettiva"   	length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="numeroPolizza"   		length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="flagUnit"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	
 	 	</input-mapping> 
		<output-mapping className="it.sistinf.albedoweb.stampe.preventivoascadenza.dto.PreventivoScadenzaResponseDTO"> 
	    <!-- output : 998 (976 + 22) car -->      
	    	<field-map attributeName="categoria"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	    
	    	<field-map attributeName="agenzia" 				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="numeroCollettiva"   	length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="numeroPolizza"   		length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="flagUnit"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	
	    	<field-map attributeName="dataStorno"      	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data" /> 
			<field-map attributeName="capitaleMaturato"          length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />   
	    	<field-map attributeName="dataValorizzazione"      	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data" />
	    	<field-map attributeName="bonusScadenza"          length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />   
	    	<field-map attributeName="nominativo" 			length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="nome"   				length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="codiceFiscale"   		length="16"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="tipoPersona"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="indirizzo"   			length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="localita"   			length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="cap"   				length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="provincia"   			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="stato"   				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	
	    	<field-map attributeName="luogoNascita"   		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="provinciaNascita"   	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="dataNascita"      	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data" />
	    	<field-map attributeName="sesso"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="flagCommerciale" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	
	    	<field-map attributeName="importoNetto"         length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="imposteRedditi"       length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="importoLiquidazione"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="capitale"          	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="rendita"          	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="renditaAnnua"        	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			
			<field-map attributeName="numElementiTrovati" 	length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagLimite" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	
			<field-map attributeName="dettagliImposte" length="650" > 
					<nested-mapping className="it.sistinf.albedoweb.stampe.preventivoascadenza.dto.DettaglioImposte" iterations="10" blockLength="65">            
						<field-map attributeName="tipo"				length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="descrizione"		length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="imposta"          length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					   	<field-map attributeName="imponibile"       length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					    <field-map attributeName="aliquota"         length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					    	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false" />
					</nested-mapping>
			   </field-map> 
		       
		</output-mapping> 
 </rule> 
</rules> 