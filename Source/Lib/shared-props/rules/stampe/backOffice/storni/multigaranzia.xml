<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DATI-MULTIGARANZIA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE507</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiRequestDTO">
			<!-- INPUT 27 car -->
            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiResponseDTO">
		
		 	<!-- TOTALI 27 + 6942 = 6969 -->
			<!-- INPUT 27 car -->			
            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- Dati output 227 + 3284 + 76 + 202 + 5 + 3120 + 14 + 14 = 6942 -->
			<!-- Dati output 227 + 3284 + 76 + 216 + 5 + 3120 + 14 + 14 = 6956 -->
			 <field-map attributeName="oggDatiContraente" length="227" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.DatiContraente" iterations="0" blockLength="227">
				    <field-map attributeName="contraente"  length="130"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="indirizzo"  length="60"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="cap"  length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="citta"      length="30"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="provincia" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    </nested-mapping>
			</field-map>
		    <field-map attributeName="oggDatiContratto" length="3284" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.DatiContratto" iterations="0" blockLength="3284">
				    <field-map attributeName="assicurato"  length="130"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    <field-map attributeName="numElementiBV"  		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
					<field-map attributeName="elencoBeneficiariVita"	length="1300" >
						<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.Beneficiari" iterations="10" blockLength="130">
							<field-map attributeName="beneficiario"  length="130"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="estensioneBV"  		length="150"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numElementiBM"  		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
					<field-map attributeName="elencoBeneficiariMorte"	length="1300" >
						<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.Beneficiari" iterations="10" blockLength="130">
							<field-map attributeName="beneficiario"  length="130"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="estensioneBM"  		length="150"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDecorrenza"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		            <field-map attributeName="dataScadenza"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		            <field-map attributeName="durataContratto"      length="11"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="durataPagamento" length="10"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="frazionamento"     length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="stato"  length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipologiaVP"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataVP"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="enteVP"  length="130"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importoVincolo"  length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="2" />
			    </nested-mapping>
			</field-map>
			
			<field-map attributeName="oggRiepilogoMovimenti" length="76" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.RiepilogoMovimenti" iterations="0" blockLength="76">
				    <field-map attributeName="totalePremiVersati"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="ultimoPremioIncassato"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		            <field-map attributeName="dataUltimoPremio"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Data"/>
		            <field-map attributeName="totaliRiscattiParzialiLordi"      length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		            <field-map attributeName="dataUltimoPrestito"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="prestitoRimanente"  length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="2" />
	   			</nested-mapping>
			</field-map>
	
		    
		    <field-map attributeName="lineaInvestimentoI" length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dtPrestI" 		  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="valQI" 			  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
			<field-map attributeName="numQI" 			  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
			<field-map attributeName="controvaloreI" 	  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="lineaInvestimentoF" length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dtPrestF" 		  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="valQF" 			  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
			<field-map attributeName="numQF" 			  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
			<field-map attributeName="controvaloreF" 	  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			
			<field-map attributeName="prestazioneIni"     length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="prestazioneRiv"     length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="prestazioneRivAl"     length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>	
			<field-map attributeName="dtPrest"            length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>

			<field-map attributeName="numeroElementiSt" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flagLimiteSt"     length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoStRiv" length="3120">
				<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.Rivalutazione" iterations="60" blockLength="52">
					<field-map attributeName="dataStRiv"          length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>
					<field-map attributeName="stRivCapRend"       length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
					<field-map attributeName="stRivPremio"        length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
					<field-map attributeName="stRivTasso"         length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="2"/>
			</nested-mapping>
			</field-map>
			<field-map attributeName="importoLordoRiscatto"     length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="controvaloreRenditaImmediata"     length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
		</output-mapping>
	</rule>
</rules>
    