<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>RISCATTO-PRODOTTI-INDEX-LINKED</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE324</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiRequestDTO">
			<!-- Totali 227 car (150 + 77) -->
			<!-- 77 car -->
			<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataRichiestaRiscatto"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Data"/>
            <field-map attributeName="dataRicevimentoRichiesta" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataContabile"            length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataStorno"            length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataDisinvestimento"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiResponseDTO">
		    <!-- Totali 2011 car (150 + 47 + 1824) --> 
		    <!-- Campi di output 47 car -->
		    
		    <field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataRichiestaRiscatto"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Data"/>
            <field-map attributeName="dataRicevimentoRichiesta" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataContabile"            length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataStorno"            length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="dataDisinvestimento"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		    
		    <field-map attributeName="oggDatiContraente" length="97" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.DatiContraente" iterations="0" blockLength="97">
				    <field-map attributeName="contraente"  length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="indirizzo"  length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="cap"  length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="citta"      length="30"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="provincia" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            
			    </nested-mapping>
			</field-map>
		    
		    <field-map attributeName="oggDatiContratto" length="112" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.DatiContratto" iterations="0" blockLength="112">
				    <field-map attributeName="assicurato"  length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDecorrenza"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		            <field-map attributeName="dataScadenza"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		            <field-map attributeName="durataContratto"      length="11"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="durataPagamento" length="10"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="frazionamento"     length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="stato"  length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    </nested-mapping>
			</field-map>
			
			<field-map attributeName="oggRiepilogoMovimenti" length="52" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.RiepilogoMovimenti" iterations="0" blockLength="52">
				    <field-map attributeName="totalePremiVersati"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="ultimoPremioIncassato"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		            <field-map attributeName="dataUltimoPremio"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"  valida="false" natura="Data"/>
		            <field-map attributeName="totaliRiscattiParzialiLordi"      length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
	   			</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggValoriPrestazioni" length="48" >
            	<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.ValoriPrestazioni" iterations="0" blockLength="48">
				    <field-map attributeName="dataValorizzazione"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"  valida="false" natura="Data"/>
					<field-map attributeName="valoreIndice"  length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" numInteri="5" numDecimali="3" separatoreMigliaia="true"/>
		            <field-map attributeName="valoreNominale"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"  natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		            <field-map attributeName="valoreMercato"      length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
	   			</nested-mapping>
			</field-map>
			
			<field-map attributeName="numElementiTrovati"  		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
			<field-map attributeName="flAltri"   length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					
   			<field-map attributeName="oggElencoCedoleLiquidateList"	length="3120" >
				<nested-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.CedolaLiquidata" iterations="60" blockLength="52">
					<field-map attributeName="dataCedola"  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="cedolaLorda"   length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""   natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="cedolaNetta"      length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="cedolaTasso"      length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  natura="Numerico" segnato="false" numInteri="8" numDecimali="03" separatoreMigliaia="true" />
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
