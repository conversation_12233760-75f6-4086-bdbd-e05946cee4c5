<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>RECESSO-PRE-GESTIONE-SEPARATA-TRADIZIONALE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE401</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiRequestDTO">
			<!-- Totali 179 car (150 + 29) -->
			<!-- 29 car -->
			<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="flagUnit"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flagRiscatto"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.stampe.backoffice.storni.dto.StampeStorniRiscattiResponseDTO">
		    <!-- Totali 2252 car (150 + 29+250 + 1824) --> 
		    <!-- Campi di output 29+250 car -->
		    
		    <field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="flagUnit"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flagRiscatto"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            
            <field-map attributeName="descrizioneFunzione"      length="20"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
		    <field-map attributeName="capitaleMaturato"      length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            <field-map attributeName="risMaturato"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="tipoGestioneSinistro"      length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="importoLordoOperazione"      length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            <field-map attributeName="indennita"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="oneriProporzionali"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="utili"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    
		    <field-map attributeName="segnoOnopfi"      length="1"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="oneriFissi"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="importoNettoOperazione"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    
		    <field-map attributeName="dataPagamento"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		    <field-map attributeName="interessiPrestiti"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="segnoPrestiti"      length="1"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="prestiti"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="ratei"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="sospensioni"      length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="descrizioneOnopfi"      length="20"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataDisinvestimento"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
		    <field-map attributeName="cumuloPremi"  length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="importoLordoRiscatto"      length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
		    <field-map attributeName="flagErrore"      length="1"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
