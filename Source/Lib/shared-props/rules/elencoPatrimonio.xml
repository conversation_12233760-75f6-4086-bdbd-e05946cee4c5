<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-PATRIMONIO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0046</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.patrimonio.dto.PatrimonioRicercaRequestDTO">
			<!-- Totali 163 car (150 + 13) -->
			<!-- 13 car -->
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.patrimonio.dto.PatrimonioRicercaResponseDTO">
		    <!-- Totali 14733 car (150 + 12759 + 1824) --> 
		    <!-- 12759 car -->
		    <!-- Campi di input 13 car -->
            <field-map attributeName="codiceCliente"       length="13" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <!-- 12746 car -->		    
			<field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoPatrimonio"    length="12700" >
				<nested-mapping className="it.sistinf.albedoweb.patrimonio.dto.PatrimonioDTO" iterations="100" blockLength="127">
					<field-map attributeName="prodotto"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descProdotto"         length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDecorrenza"       length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="categoria"  		    length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenziaProposta" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCollettiva"     length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statoPolizza"   	    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="causalePolizza"   	length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premiVersati"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscattoLordo"  length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importoDaLiquidare"   length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
            <field-map attributeName="totPremiVersati"     length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totValRiscattoLordo" length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totImpDaLiquidare"   length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    