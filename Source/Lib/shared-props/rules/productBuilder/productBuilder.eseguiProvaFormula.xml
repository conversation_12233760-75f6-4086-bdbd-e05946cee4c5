<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-ESEGUI-PROVA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0035</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.EseguiProvaFormulaProductBuilderRequestDTO" nomeKeyERR="erroriProvaFormula">
			<!-- Totali 168 car (150 + 18) -->
			<!-- 18 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    							    					
			<!-- dati polizza -->
		    <field-map attributeName="categoriaPolizza" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"       length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"    length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>			
 			<!-- variabili -->
 			<field-map attributeName="elencoVariabiliProva" length="2402" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ElencoVariabiliProvaFormulaDTO" iterations="0" blockLength="2402">
		 			<field-map attributeName="elencoVariabili" length="2400" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="100" blockLength="24">
							<field-map attributeName="nomeVariabile"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreVariabile" length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="compilata" length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.EseguiProvaFormulaProductBuilderResponseDTO">
		    <!-- Totali 9077 car (150 + 5982 + 1824) --> 
		    <!-- 5982 car -->
			<!-- formula - 18 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    							    					
			<!-- dati polizza - 25 car -->
		    <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>			
 			<!-- variabili - 2400 car + 2 car -->
 			<field-map attributeName="elencoVariabiliProva" length="2402" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ElencoVariabiliProvaFormulaDTO" iterations="0" blockLength="2402">
		 			<field-map attributeName="elencoVariabili" length="2400" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="100" blockLength="24">
							<field-map attributeName="nomeVariabile"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreVariabile"  length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="compilata" length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
				</nested-mapping>
			</field-map>
  			<!-- DATI OUTPUT -->
  			<!-- elenco risultati formula - 3105 car + 8 car -->
  			<field-map attributeName="elencoRisultatiFormula" length="3105" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.RisultatiFormulaDTO" iterations="115" blockLength="27">
					<field-map attributeName="costante"    length="4" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione" length="23" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="risultatiDebug" length="8" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		     			
			<!-- ERRORI CAMPI - 424 car -->
			<field-map attributeName="formulaCdErr" length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<!-- Errori polizza - 420 car-->
			<field-map attributeName="categoriaCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="agenziaCdErr"    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="collettivaCdErr" length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="testaCdErr"      length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="posizioneCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
 			<field-map attributeName="elencoErroriVariabili" length="400" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="100" blockLength="4">
					<field-map attributeName="codErroreVariabile"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>

 		</output-mapping>
	</rule>
</rules>