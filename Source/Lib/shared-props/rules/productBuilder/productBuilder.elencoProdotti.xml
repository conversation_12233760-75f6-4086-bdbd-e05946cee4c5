<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-ELENCO-PRODOTTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0002</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.dto.ProductBuilderListaRequestDTO">
			<!-- Totali 186 car (150 + 3) -->
			<!-- 3 car -->
            <field-map attributeName="codSoc"   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.dto.ProductBuilderListaResponseDTO">
		    <!-- Totali 3678 car (150 + 21307 + 1824) --> 
		    <!-- 21307 car -->
		    <!-- Campi di input 3 car -->
            <field-map attributeName="codSoc"   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
            <!-- Elenco -->
            <!-- 21304 car -->		    
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaComponenti" length="21300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="300" blockLength="71">
					<field-map attributeName="categoria"    length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codice"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  	length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"  length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>