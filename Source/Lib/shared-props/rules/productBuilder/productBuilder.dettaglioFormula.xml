<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-DETTAGLIO-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0021</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderRequestDTO" nomeKeyERR="erroriSalvaFormulaPD">
			<!-- Totali 168 car (150 + 18) -->
			<!-- 18 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codiceFormula" nomeKeyERR="erroriSalvaFormulaPD"/>		    							    					
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderResponseDTO">
		    <!-- Totali 9079 car (150 + 7105 + 1824) --> 
		    <!-- 7105 car -->
		    <!-- Campi di input 18 car -->
            <field-map attributeName="codSoc"              length="3"     precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"           length="1"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"            length="10"    precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"       length="4"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>		    					
            <!-- Dettaglio formula -->
            <!-- 7087 car -->		    
			<field-map attributeName="progrStorico"        length="4"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="descrizioneFormula"  length="50"    precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="dataCert"    	       length="8"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataComp"            length="8"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="segue"               length="1"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="compilata"           length="2"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="dataVar"    	       length="8"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="userVar"             length="6"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente1"           length="3500"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="sorgente2"           length="3500"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
 		</output-mapping>
	</rule>
</rules>