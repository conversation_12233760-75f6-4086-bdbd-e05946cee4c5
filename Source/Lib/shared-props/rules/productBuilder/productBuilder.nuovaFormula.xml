<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-NUOVA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0046</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA FORMULA</logAppServDesc>
		<areaFunzionale>PRODUCT BUILDER-SELEZIONE PRODOTTO</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderRequestDTO" nomeKeyERR="erroriSalvaFormulaPD" validateScript="false">
			<!-- Totali 7218 car (150 + 7068) -->
			<!-- 7068 car -->
            <field-map attributeName="codSoc"          			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   			length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codiceFormula" nomeKeyERR="erroriSalvaFormulaPD"/>		    							    					
			<field-map attributeName="descrizioneFormula"		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente1"       			length="3500" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente2"       			length="3500" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderResponseDTO">
		    <!-- Totali 9096 car (150 + 7122 + 1824) --> 
		    <!-- 7122 car -->
		    <!-- Campi di input 7068 car -->
            <field-map attributeName="codSoc"          			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   			length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>		    							    					
			<field-map attributeName="descrizioneFormula"		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente1"       			length="3500" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente2"       			length="3500" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	    					
            <!-- 54 car -->	
            <field-map attributeName="codiceFormulaCdErr"   	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>                        	    
            <field-map attributeName="descrFormulaObbligatoria" length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		
 		</output-mapping>
	</rule>
</rules>