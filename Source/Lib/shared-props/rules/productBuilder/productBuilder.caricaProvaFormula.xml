<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-CARICA-PROVA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0033</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderRequestDTO" nomeKeyERR="erroriProvaFormula">
			<!-- Totali 168 car (150 + 18) -->
			<!-- 18 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"   length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    							    					
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.CaricaProvaFormulaProductBuilderResponseDTO">
		    <!-- Totali 10083 car (150 + 8109 + 1824) --> 
		    <!-- 8109 car -->
		    <!-- Campi di input 18 car -->
            <field-map attributeName="codSoc"              length="3"     precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"           length="1"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"            length="10"    precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"       length="4"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>		    					
            <!-- Dettaglio formula -->
            <!-- 7087 car -->		    
			<field-map attributeName="progrStorico"        length="4"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="descrizioneFormula"  length="50"    precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="dataCert"    	       length="8"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataComp"            length="8"     precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="segue"               length="1"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="compilata"           length="2"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="dataVar"    	       length="8"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="userVar"             length="6"     precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sorgente1"           length="3500"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="sorgente2"           length="3500"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
 			<!-- Variabili -->
 			<field-map attributeName="elencoVariabiliProva" length="1004" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ElencoVariabiliProvaFormulaDTO" iterations="0" blockLength="1004">
		 			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
		 			<field-map attributeName="elencoVariabili" length="1000" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="100" blockLength="10">
							<field-map attributeName="nomeVariabile"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
 			
 		</output-mapping>
	</rule>
</rules>