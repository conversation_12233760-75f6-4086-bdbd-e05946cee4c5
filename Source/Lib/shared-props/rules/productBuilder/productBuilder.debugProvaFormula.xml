<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-DEBUG-PROVA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0037</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DebugFormulaProductBuilderRequestDTO" nomeKeyERR="erroriProvaFormula">
			<!-- Totali 162 car (150 + 12) -->
			<!-- 12 car -->			
			<field-map attributeName="risultatiDebug" 		length="8" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numElementiTotali" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>		     			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DebugFormulaProductBuilderResponseDTO">
		    <!-- Totali 13390 car (150 + 11416 + 1824) --> 
		    <!-- 11416 car -->		    
			<field-map attributeName="risultatiDebug" 			length="8" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		<!-- Variabili -->
			<field-map attributeName="numElementiTotali" 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
 			<field-map attributeName="elencoVariabiliDebug" 	length="11404" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ElencoVariabiliProvaFormulaDTO" iterations="0" blockLength="11404">
		 			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
		 			<field-map attributeName="elencoVariabili" length="11400" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DebugFormulaDTO" iterations="300" blockLength="38" iterateAllRows="true">
							<field-map attributeName="costante"  length="5" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comando"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valore"    length="23" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
 			
 		</output-mapping>
	</rule>
</rules>