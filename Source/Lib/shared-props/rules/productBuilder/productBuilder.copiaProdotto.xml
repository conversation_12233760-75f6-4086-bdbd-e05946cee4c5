<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-COPIA-PRODOTTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0028</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.CopiaProdottoProductBuilderRequestDTO" nomeKeyERR="erroriCopiaPD">
			<!-- Totali 12448 car (150 + 12298) -->
			<!-- 12298 car -->
			<field-map attributeName="codSoc" 		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codSocNew" 	length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati del vecchio prodotto -->
			<field-map attributeName="componentePB" length="21" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
				</nested-mapping>
			</field-map>
			<!-- Dati del nuovo prodotto -->
			<field-map attributeName="codiceNuovo"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descrizioneNuovo" length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaNew"     length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- UT da copiare o collegare -->
			<field-map attributeName="elencoGaranzie" length="12200" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaDTO" iterations="200" blockLength="61">
					<field-map attributeName="codice"  		   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceNew"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="descrizioneNew"  length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="flDuplica"       length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.CopiaProdottoProductBuilderResponseDTO">
			<!-- Totali 17890 car (150 + 15916 + 1824) -->
		    <!-- 15916 car -->
 			<field-map attributeName="codSoc" 		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
 			<field-map attributeName="codSocNew" 	length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="componentePB" length="21" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceNuovo"    	   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descrizioneNuovo"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaNew"     	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- UT da copiare o collegare -->
			<field-map attributeName="elencoGaranzie" length="12200" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaDTO" iterations="200" blockLength="61">
					<field-map attributeName="codice"  		   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceNew"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="descrizioneNew"  length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="flDuplica"       length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceProdottoNuovo" length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="elencoNewCodGaranzie" length="2000" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaDTO" iterations="200" blockLength="10">
					<field-map attributeName="codiceMacchinaNew"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codProdottoCdErr"  			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="descrizioneProdottoCdErr"  	length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="elencoErroriUT" length="1600" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.ErroreListaUTDTO" iterations="200" blockLength="8">
					<field-map attributeName="codiceUtCdErr"  		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="descrizioneUtCdErr"  	length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			
		</output-mapping>
	</rule>
</rules>