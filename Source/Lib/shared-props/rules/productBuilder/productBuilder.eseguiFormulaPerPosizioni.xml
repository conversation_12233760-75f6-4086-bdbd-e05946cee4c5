<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-ESEGUI-FORMULA-PER-POSIZIONI</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>MWLSE015</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.EseguiFormulaPerPosizioniRequestDTO">			
			<!-- input - 15924  car (25 + 28 + 15650 + 221)-->
			
			<!-- dati polizza 25 -->
            <field-map attributeName="codSoc"   		length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoria"    	length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="agenziaPolizza"   length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"       length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"    length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<!-- dati richiamo formula 28 -->
			<field-map attributeName="codiceFormula"    length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>			
			<field-map attributeName="facoltativa"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRichiamo" 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffetto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDisinv" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="totalePosizioni" 	length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<!-- variabili 15650 -->
			<field-map attributeName="posizioniSimpleList" length="15650">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleDTO" iterations="25" blockLength="626">		    					
					<field-map attributeName="numeroPosizione" 		length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numVariabiliSimple" 	length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="elencoVariabiliSimple" length="620">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="20" blockLength="31">
							<field-map attributeName="nomeVariabile"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoVariabile"   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreVariabile" length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="filler0" 			length="221" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.EseguiFormulaPerPosizioniResponseDTO">
		    <!-- Totali 24026 car (25 + 28 + 15650 + 221 + 8282) --> 
		    <!-- dati polizza 25 -->
            <field-map attributeName="codSoc"   		length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoria"    	length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="agenziaPolizza"   length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"       length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"    length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<!-- dati richiamo formula 28 -->
			<field-map attributeName="codiceFormula"    length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="facoltativa"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRichiamo" 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffetto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDisinv" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="totalePosizioni" 	length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<!-- variabili 15650 -->
			<field-map attributeName="posizioniSimpleList" length="15650">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleDTO" iterations="25" blockLength="626">		    					
					<field-map attributeName="numeroPosizione" 		length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numVariabiliSimple" 	length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="elencoVariabiliSimple" length="620">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="20" blockLength="31">
							<field-map attributeName="nomeVariabile"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoVariabile"   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreVariabile" length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>		    
				</nested-mapping>
			</field-map>
			<field-map attributeName="filler0" 			length="221" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   
  			<!-- DATI OUTPUT -->
  			<!-- 2 + 12600 = 12602 car -->
  			<field-map attributeName="totalePosizioniOut" 		length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>  			
  			<field-map attributeName="eseguiFormulaResultList" 	length="12600">
  				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleResultDTO" iterations="25" blockLength="504">
  					<field-map attributeName="numeroPosizione" 			length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
  					<field-map attributeName="elencoRisultatiFormula" 	length="500" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.RisultatiFormulaDTO" iterations="25" blockLength="20">
							<!-- risultato formula -->
							<field-map attributeName="descrizione" 		length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
  				</nested-mapping>
  			</field-map>
  			
 		</output-mapping>
	</rule>
</rules>