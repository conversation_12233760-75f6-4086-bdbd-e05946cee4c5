<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-COPIA-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0015</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaProductBuilderRequestDTO" nomeKeyERR="erroriCopiaPD">
			<!-- Totali 239 car (150 + 89) -->
			<!-- 89 car -->
			<field-map attributeName="codSoc" 				  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="codSocNew" 			  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceMacchinaProdOld"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceMacchinaProdNew"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- UT da copiare o collegare -->
			<field-map attributeName="garanzia" length="62" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaDTO" iterations="0" blockLength="62">
					<field-map attributeName="categoriaNew"    length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codice"  		   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceNew"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="descrizioneNew"  length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="flDuplica"  	   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaProductBuilderResponseDTO">
		    <!-- Totali 2089 car (150 + 115 + 1824) --> 
		    <!-- 115 car -->
			<field-map attributeName="codSoc"                 length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="codSocNew" 			  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceMacchinaProdOld"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceMacchinaProdNew"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<!-- UT da copiare o collegare -->
			<field-map attributeName="garanzia" length="72" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.CopiaGaranziaDTO" iterations="0" blockLength="72">
					<field-map attributeName="categoriaNew"    length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codice"  		   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceNew"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="descrizioneNew"  length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="flDuplica"  	   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="codiceMacchinaNew"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="collegaCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="categoriaCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="codiceUTCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="descrizioneUTCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>