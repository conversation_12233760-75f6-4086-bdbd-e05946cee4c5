<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-DETTAGLIO-PRODOTTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0005</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.DettaglioProdottoProductBuilderRequestDTO" nomeKeyERR="erroriSalvaProdottoPD">
			<!-- Totali 174 car (150 + 24) -->
			<!-- 24 car -->
			<field-map attributeName="codSoc"               	length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB"         	length="21">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"	length="10"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
					<field-map attributeName="categoria"		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.DettaglioProdottoProductBuilderResponseDTO">
		  <!-- old Totali 13393 car (150 + 11419 + 1824) --> 
		  <!-- old 11419 car -->
		  <!-- Totali 13396 car (150 + 11422 + 1824) --> 
		  <!-- 11422 car -->
		  
		  
		  <!-- Campi di input 24 car -->
 			<field-map attributeName="codSoc"					length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB"     	    length="21">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="codiceMacchina"	length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="categoria"		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<!-- Prodotto 208 car -->
			<field-map attributeName="numerazionePolizza"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flStandard"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="descrProdotto"					length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flDifferimento"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flObbligatorio"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numAnniDiff"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="percDifferita"					length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="tipoProdotto"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoStampato"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flTassazioneSeparata"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flRiemissionePUR"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="tipoRamo"							length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL0"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flAttivazComb"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL1"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL2"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL3"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL4"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL5"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL6"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL7"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL8"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL9"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codProdotto"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flLiqCedola"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codCedola"						length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flConsenso1"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso2"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso3"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso4"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso5"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso6"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="periodicita1"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita2"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita3"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita4"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita5"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita6"						length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classifProdotto"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flReimpiego"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="gestioneSpeciale"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			
			<!-- Premi 135 car -->
			<field-map attributeName="coperturaPrimoVers"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="premioMinimoVersAnnuale"			length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersSemestrale"		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersQuadrimestrale"	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersTrimestrale"		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersBimestrale"		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersMensile"			length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersOccasionale"		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="flCalcoloDurata"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="etaPrevistaScadenza1"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza2"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza3"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza4"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza5"				length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="rateazione"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="durataGaranzie"					length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="flRiemissioneAutom"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="residuoMinRiscParz"				length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="flValuta"							length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="S"/>
			<field-map attributeName="flRid"							length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="codRid"							length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			
			<!-- 128 char -->
			<field-map attributeName="opzioneGestionale1"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale2"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale3"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale4"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale5"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale6"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale7"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale8"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale9"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale10"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="tipoRivalutazione"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="reimpiegoProp"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="diffScad"							length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="dsAutomatico"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniDs"							length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="percDiff"							length="07"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="cambioRegFisc"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="prorogaEmissPur"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniProroga"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="rinnovoTacito"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniRinnovo"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMinM"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""   natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMaxM"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""   natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMinF"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""   natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMaxF"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""   natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			
			<!--old 118 char -->
			<!--121 char -->
			<field-map attributeName="mesiDurata"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="durataFissa1"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa2"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa3"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa4"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa5"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa6"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />			
			<field-map attributeName="periodoPrima1"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodoPrima2"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodoPrima3"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodoPrima4"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodoPrima5"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodoPrima6"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>			
			<field-map attributeName="durataMinima"						length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataMassima"					length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />			
			<field-map attributeName="flFatca"          				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="classeDiPremio1"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio2"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio3"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio4"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio5"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="opzioniScadenza"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="dataDecorrenzaSAIDAC2"			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="data"/>
			<field-map attributeName="tipoColl"					        length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="numMinTesta"					    length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="flAmlCollREG5"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classREG5"					    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flMifidII"					    length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>			
			<field-map attributeName="percBonusMaturita"				length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="percBonusLaureaSenzaMaturita"		length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="percBonusLaureaConMaturita"		length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="flGestioneTranching"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flCoesistenzaLineeInvestimento"	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flModelloCosti"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<!-- Figure anagrafiche 5300 car -->
			<field-map attributeName="presenzaFigAnagrafiche" length="5300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.PresenzaFigureAnagraficheDTO" iterations="100" blockLength="53">
					<field-map attributeName="descrizioneRuolo"			length="50"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="codiceRuolo"				length="01"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>		    		
					<field-map attributeName="flObblig"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="numMaxFig"				length="01"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="1" numDecimali="0" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			
			<!-- atti di portafoglio 5506 = 2203 + 3303 -->
			<field-map attributeName="elencoFunzioniStorno" length="2203" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="2203">
					<field-map attributeName="numFunzioniStornoTrovate"		length="03"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="dettaglioFunzioniStorno"		length="2200">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="40" blockLength="55">
							<field-map attributeName="flFunzioneSelez"		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
							<field-map attributeName="codiceFunzione"		length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
							<field-map attributeName="descrizFunzione"		length="50"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoAltreFunzioni" length="3303" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="3303">
					<field-map attributeName="numAltreFunzioniTrovate"		length="03"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="dettaglioAltreFunzioni" 		length="3300">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="60" blockLength="55">
							<field-map attributeName="flFunzioneSelez"		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
							<field-map attributeName="codiceFunzione"		length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
							<field-map attributeName="descrizFunzione"		length="50"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>