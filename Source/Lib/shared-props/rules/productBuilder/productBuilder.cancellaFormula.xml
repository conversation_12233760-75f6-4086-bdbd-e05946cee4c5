<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-CANCELLA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0047</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>CANCELLA FORMULA</logAppServDesc>
		<areaFunzionale>PRODUCT BUILDER-SELEZIONE PRODOTTO</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderRequestDTO" nomeKeyERR="erroriSalvaFormulaPD">
			<!-- Totali 168 car (150 + 18) -->
			<!-- 18 car -->
            <field-map attributeName="codSoc"          		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    							    					
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.DettaglioFormulaProductBuilderResponseDTO">
		    <!-- Totali 1992 car (150 + 18 + 1824) --> 
		    <!-- 18 car -->
		    <!-- Campi di input 18 car -->
            <field-map attributeName="codSoc"          		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="codiceUT"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormula"		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    							    					
 		</output-mapping>
	</rule>
</rules>