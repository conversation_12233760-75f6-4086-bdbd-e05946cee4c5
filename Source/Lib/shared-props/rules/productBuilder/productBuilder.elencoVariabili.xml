<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-ELENCO-VARIABILI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0022</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ProductBuilderVariabiliRequestDTO">
			<!-- Totali 153 car (150 + 13) -->
			<!-- 13 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="keyUltimaVariabileElenco"   length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ProductBuilderVariabiliResponseDTO">
		    <!-- Totali 31991 car (150 + 30017 + 1824) --> 
		    <!-- 16238 car -->
		    <!-- Campi di input 13 car -->
            <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="keyUltimaVariabileElenco"      length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			
			<!-- Elenco -->
            <!-- 30004 car -->
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoVariabili" length="30000" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.ElencoVariabiliDTO" iterations="500" blockLength="60">
					<field-map attributeName="codice"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"  length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>