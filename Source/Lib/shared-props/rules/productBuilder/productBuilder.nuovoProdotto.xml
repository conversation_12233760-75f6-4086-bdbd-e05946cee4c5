<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-NUOVO-PRODOTTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0007</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA INSERIMENTO PRODOTTO</logAppServDesc>
		<areaFunzionale>PRODUCT BUILDER-SELEZIONE PRODOTTO</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.DettaglioProdottoProductBuilderRequestDTO" nomeKeyERR="erroriSalvaProdottoPD">		
			<!--old Totali 16263 car (150 + 14289 (11413 + 2876) + 1824) -->
			<!--old Campi di input: 24 + 208 + 135 + 246 + 5300 + 5500 = 11413 car -->
			<!-- Totali 16278 car (150 + 14304 (11416 + 2888) + 1824) -->
			<!-- Campi di input: 24 + 208 + 135 + 249 + 5300 + 5500 = 11416 car -->
			<!-- 24 car -->
 			<field-map attributeName="codSoc"                  length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB"            length="21">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="categoria"       length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- Prodotto 208 car -->
			<field-map attributeName="numerazionePolizza"             length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flStandard"                     length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="descrProdotto"                  length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flDifferimento"                 length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flObbligatorio"                 length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numAnniDiff"                    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numAnniDiff" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="percDifferita"                  length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="percDifferita" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="tipoProdotto"                   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoStampato"                   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flTassazioneSeparata"           length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flRiemissionePUR"               length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="tipoRamo"                       length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL0"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flAttivazComb"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL1"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL2"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL3"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL4"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL5"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL6"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL7"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL8"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL9"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codProdotto"                    length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flLiqCedola"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codCedola"                      length="06" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flConsenso1"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso2"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso3"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso4"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso5"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso6"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="periodicita1"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita2"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita3"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita4"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita5"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita6"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classifProdotto"        		  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flReimpiego"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="gestioneSpeciale"               length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>			
			 <!-- Premi  135 car -->
			<field-map attributeName="coperturaPrimoVers"             length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="coperturaPrimoVers" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersAnnuale"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersAnnuale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersSemestrale"     length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersSemestrale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersQuadrimestrale" length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersQuadrimestrale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersTrimestrale"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersTrimestrale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersBimestrale"     length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersBimestrale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersMensile"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersMensile" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="premioMinimoVersOccasionale"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioMinimoVersOccasionale" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="flCalcoloDurata"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="etaPrevistaScadenza1"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPrevistaScadenza1" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPrevistaScadenza2"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPrevistaScadenza2" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPrevistaScadenza3"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPrevistaScadenza3" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPrevistaScadenza4"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPrevistaScadenza4" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPrevistaScadenza5"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPrevistaScadenza5" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="rateazione"                     length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="rateazione" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataGaranzie"                 length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataGaranzie" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="flRiemissioneAutom"             length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="residuoMinRiscParz"             length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="residuoMinRiscParz" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="flValuta"                       length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="S"/>
			<field-map attributeName="flRid"                       	  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="codRid"                         length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			
			<!--old Nuovi Campi Product Builder 246 car -->
			<!-- Nuovi Campi Product Builder 249 car -->
			<field-map attributeName="opzioneGestionale1"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale2"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale3"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale4"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale5"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale6"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale7"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale8"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale9"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale10"            length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="tipoRivalutazione"              length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="reimpiegoProp"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="diffScad"              		  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="dsAutomatico"              	  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniDs"              			  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="anniDs" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="percDiff"              		  length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="true" nomeAttributoERR="percDiff" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="cambioRegFisc"              	  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="prorogaEmissPur"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniProroga"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="anniProroga" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="rinnovoTacito"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniRinnovo"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="anniRinnovo" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPensMinM"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPensMinM" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPensMaxM"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPensMaxM" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPensMinF"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPensMinF" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="etaPensMaxF"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="etaPensMaxF" nomeKeyERR="erroriSalvaProdottoPD"/>
			
			<field-map attributeName="mesiDurata"              	      length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>			
			<field-map attributeName="durataFissa1"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa1" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataFissa2"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa2" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataFissa3"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa3" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataFissa4"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa4" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataFissa5"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa5" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataFissa6"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataFissa6" nomeKeyERR="erroriSalvaProdottoPD"/>
			
			
			<field-map attributeName="periodoPrima1"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima1" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="periodoPrima2"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima2" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="periodoPrima3"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima3" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="periodoPrima4"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima4" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="periodoPrima5"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima5" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="periodoPrima6"                  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="periodoPrima6" nomeKeyERR="erroriSalvaProdottoPD"/>
				
			<field-map attributeName="durataMinima"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataMinima" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="durataMassima"                  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="durataMassima" nomeKeyERR="erroriSalvaProdottoPD"/>
				
			<field-map attributeName="flFatca"                        length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="classeDiPremio1"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio2"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio3"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio4"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio5"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="opzioniScadenza"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="dataDecorrenzaSAIDAC2"          length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  valida="true" natura="Data" nomeAttributoERR="dataDecorrenzaSAIDAC2" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="tipoColl"					      length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" nomeAttributoERR="tipoColl" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="numMinTesta"					  length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numMinTesta" nomeKeyERR="erroriSalvaProdottoPD" />
			<field-map attributeName="flAmlCollREG5"	              length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" nomeAttributoERR="amlCollREG5" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="classREG5"					  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" nomeAttributoERR="classREG5" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="flMifidII"					  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="percBonusMaturita"			  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="percBonusMaturita" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="percBonusLaureaSenzaMaturita"	  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="percBonusLaureaSenzaMaturita" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="percBonusLaureaConMaturita"	  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="percBonusLaureaConMaturita" nomeKeyERR="erroriSalvaProdottoPD"/>
			<field-map attributeName="flGestioneTranching"			  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flCoesistenzaLineeInvestimento" length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flModelloCosti"				  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<!-- Nuovi Campi Product Builder -->
			
			<!-- Figure anagrafiche 5300 car -->
			<field-map attributeName="presenzaFigAnagrafiche" length="5300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.PresenzaFigureAnagraficheDTO" iterations="100" blockLength="53">
					<field-map attributeName="descrizioneRuolo"    	  length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="codiceRuolo"            length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>		    		
					<field-map attributeName="flObblig"               length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="numMaxFig"              length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" valida="true" natura="Numerico"  segnato="false"  numInteri="1" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="listaAnagraficheErr[].numMaxFig" nomeKeyERR="erroriSalvaProdottoPD" />
				</nested-mapping>
			</field-map>
			<!-- atti di portafoglio 5500 -->
			<field-map attributeName="elencoFunzioniStorno" length="2200" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="2200">
					<field-map attributeName="dettaglioFunzioniStorno" length="2200">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="40" blockLength="55">
							<field-map attributeName="flFunzioneSelez"        length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
							<field-map attributeName="codiceFunzione"         length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>							
							<field-map attributeName="descrizFunzione"        length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoAltreFunzioni" length="3300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="3300">
					<field-map attributeName="dettaglioAltreFunzioni" length="3300">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="60" blockLength="55">
							<field-map attributeName="flFunzioneSelez"      length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
							<field-map attributeName="codiceFunzione"       length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
							<field-map attributeName="descrizFunzione"      length="50"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.DettaglioProdottoProductBuilderResponseDTO">			
			<!--old Totali 16263 car (150 + 14289 (11413 + 2876 err) + 1824) -->
			<!--old Campi di input: 24 + 208 + 135 + 246 + 5300 + 5500 = 11413 car -->
			<!-- Totali 16287 car (150 + 14304 (11416 + 2888 err) + 1824) -->
			<!-- Campi di input: 24 + 208 + 135 + 249 + 5300 + 5500 = 11416 car -->
			
			<!--  24 car -->
 			<field-map attributeName="codSoc"                  length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB"            length="21">
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="categoria"       length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- Prodotto 208 car -->
			<field-map attributeName="numerazionePolizza"             length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flStandard"                     length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="descrProdotto"                  length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flDifferimento"                 length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flObbligatorio"                 length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numAnniDiff"                    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="percDifferita"                  length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="tipoProdotto"                   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoStampato"                   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flTassazioneSeparata"           length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flRiemissionePUR"               length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="tipoRamo"                       length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL0"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flAttivazComb"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL1"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL2"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL3"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL4"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL5"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL6"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL7"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL8"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="tipoRaggrupUL9"                 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codProdotto"                    length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flLiqCedola"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="codCedola"                      length="06" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flConsenso1"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso2"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso3"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso4"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso5"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flConsenso6"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="periodicita1"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita2"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita3"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita4"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita5"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="periodicita6"                   length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classifProdotto"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flReimpiego"                    length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="gestioneSpeciale"               length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>			
			 <!-- Premi  135 car -->
			<field-map attributeName="coperturaPrimoVers"             length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="premioMinimoVersAnnuale"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersSemestrale"     length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersQuadrimestrale" length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersTrimestrale"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersBimestrale"     length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersMensile"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="premioMinimoVersOccasionale"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="flCalcoloDurata"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="etaPrevistaScadenza1"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza2"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza3"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza4"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="etaPrevistaScadenza5"           length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="rateazione"                     length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="durataGaranzie"                 length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="flRiemissioneAutom"             length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="residuoMinRiscParz"             length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="flValuta"                       length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="S"/>
			<field-map attributeName="flRid"                       	  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="codRid"                         length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			
			<!--old Nuovi Campi Product Builder  246 car-->
			<!--Nuovi Campi Product Builder  249 car-->
			<field-map attributeName="opzioneGestionale1"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale2"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale3"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale4"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale5"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale6"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale7"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale8"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale9"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="opzioneGestionale10"            length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="tipoRivalutazione"              length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="reimpiegoProp"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="diffScad"                       length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="dsAutomatico"                   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniDs"                         length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="percDiff"                       length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="cambioRegFisc"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="prorogaEmissPur"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniProroga"                    length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="rinnovoTacito"                  length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="anniRinnovo"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMinM"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMaxM"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMinF"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="etaPensMaxF"              	  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			
			<field-map attributeName="mesiDurata"              	      length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>			
			<field-map attributeName="durataFissa1"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa2"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa3"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa4"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa5"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataFissa6"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			
			<field-map attributeName="periodoPrima1"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="periodoPrima2"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="periodoPrima3"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="periodoPrima4"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="periodoPrima5"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			<field-map attributeName="periodoPrima6"              	  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
			
			<field-map attributeName="durataMinima"                   length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="durataMassima"                  length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
				
			<field-map attributeName="flFatca"                        length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>	
			<field-map attributeName="classeDiPremio1"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio2"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio3"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio4"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classeDiPremio5"                length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="opzioniScadenza"                length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="dataDecorrenzaSAIDAC2"          length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Data"/>			
			<field-map attributeName="tipoColl"					      length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="numMinTesta"					  length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="flAmlCollREG5"				  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="classREG5"					  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flMifidII"					  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="percBonusMaturita"			  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="percBonusLaureaSenzaMaturita"	  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="percBonusLaureaConMaturita"	  length="06"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""  natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="flGestioneTranching"			  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flCoesistenzaLineeInvestimento" length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="flModelloCosti"				  length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<!-- Nuovi Campi Product Builder -->
			
			<!-- Figure anagrafiche 5300 car -->
			<field-map attributeName="presenzaFigAnagrafiche" length="5300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.PresenzaFigureAnagraficheDTO" iterations="100" blockLength="53">
					<field-map attributeName="descrizioneRuolo"    	  length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="codiceRuolo"            length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>		    		
					<field-map attributeName="flObblig"               length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="numMaxFig"              length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="" natura="Numerico"  segnato="false"  numInteri="1" numDecimali="0" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<!-- atti di portafoglio 5500 -->
			<field-map attributeName="elencoFunzioniStorno" length="2200" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="2200">
					<field-map attributeName="dettaglioFunzioniStorno" length="2200">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="40" blockLength="55">
							<field-map attributeName="flFunzioneSelez"        length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
							<field-map attributeName="codiceFunzione"         length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
							<field-map attributeName="descrizFunzione"        length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoAltreFunzioni" length="3300" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ElencoFunzioniPortafoglioDTO" iterations="0" blockLength="3300">
					<field-map attributeName="dettaglioAltreFunzioni" length="3300">
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.DettaglioFunzioniDTO" iterations="60" blockLength="55">
							<field-map attributeName="flFunzioneSelez"      length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
							<field-map attributeName="codiceFunzione"       length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
							<field-map attributeName="descrizFunzione"      length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
			<!--old output errori Tot: 2876 car -->
			<!-- output errori Tot: 2888 car -->
			
			<!-- Errore prodotti 168 -->
			<field-map attributeName="codiceCdErr"                         length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="numerazionePolizzaCdErr"             length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flStandardCdErr"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descrProdottoCdErr"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flDifferimentoCdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flObbligatorioCdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="numAnniDiffCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percDifferitaCdErr"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoProdottoCdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoStampatoCdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flTassazioneSeparataCdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flRiemissionePURCdErr"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRamoCdErr"                       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL0CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flAttivazCombCdErr"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL1CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL2CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL3CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL4CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL5CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL6CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL7CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL8CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRaggrupUL9CdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flLiqCedolaCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codCedolaCdErr"                      length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codProdottoCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso1CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso2CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso3CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso4CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso5CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flConsenso6CdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita1CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita2CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita3CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita4CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita5CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicita6CdErr"                   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classifProdottoCdErr"                length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flReimpiegoCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="gestioneSpecialeCdErr"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <!-- Errore Premi  84 car -->
			<field-map attributeName="coperturaPrimoVersCdErr"             length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersAnnualeCdErr"        length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersSemestraleCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersQuadrimestraleCdErr" length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersTrimestraleCdErr"    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersBimestraleCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersMensileCdErr"        length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioMinimoVersOccasionaleCdErr"    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flCalcoloDurataCdErr"                length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPrevistaScadenza1CdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPrevistaScadenza2CdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPrevistaScadenza3CdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPrevistaScadenza4CdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPrevistaScadenza5CdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rateazioneCdErr"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataGaranzieCdErr"                 length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flRiemissioneAutomCdErr"             length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="residuoMinRiscParzCdErr"             length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flValutaCdErr"                       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flRidCdErr"                          length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codRidCdErr"                         length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<!--old Errore Nuovi Campi Product Builder: 224 car -->
			<!-- Errore Nuovi Campi Product Builder: 236 car -->
			<field-map attributeName="opzioneGestionale1Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale2Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale3Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale4Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale5Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale6Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale7Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale8Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale9Err"               length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioneGestionale10Err"              length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRivalutazioneErr"                length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="reimpiegoPropErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="diffScadErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dsAutomaticoErr"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anniDsErr"                           length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percDiffErr"                         length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="cambioRegFiscErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="prorogaEmissPurErr"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anniProrogaErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rinnovoTacitoErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anniRinnovoErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPensMinMErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPensMaxMErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPensMinFErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="etaPensMaxFErr"              	       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="mesiDurataErr"                       length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>	
			
			<field-map attributeName="durataFissa1Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataFissa2Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataFissa3Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataFissa4Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataFissa5Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataFissa6Err"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="periodoPrima1Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodoPrima2Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodoPrima3Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodoPrima4Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodoPrima5Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodoPrima6Err"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="durataMinimaErr"                     length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataMassimaErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flFatcaErr"                          length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classeDiPremio1Err"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classeDiPremio2Err"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classeDiPremio3Err"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classeDiPremio4Err"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classeDiPremio5Err"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="opzioniScadenzaErr"                  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenzaSAIDAC2Err"            length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCollErr"                         length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="numMinTestaErr"                        length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flAmlCollREG5Err"                      length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="classREG5Err"                        length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flMifidIIErr"                        length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percBonusMaturitaErr"				   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percBonusLaureaSenzaMaturitaErr"	   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percBonusLaureaConMaturitaErr"	   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flGestioneTranchingErr"			   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flCoesistenzaLineeInvestimentoErr"   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flModelloCostiErr"				   length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<!-- Nuovi Campi Product Builder -->
			
			<!-- errori liste 2400 car -->
			<field-map attributeName="erroreFigAnagrafiche" length="1600" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ErroreFigureAnagraficheDTO" iterations="100" blockLength="16">
					<field-map attributeName="descrizioneRuoloCdErr"       length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="codiceRuoloCdErr"            length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="flObbligCdErr"               length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="numMaxFigCdErr"              length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<!-- atti di portafoglio -->
			<field-map attributeName="erroreFunzioniStorno" length="320" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ErroreFunzioniPortafoglioDTO" iterations="40" blockLength="8">
					<field-map attributeName="codiceFunzioneCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="descrizFunzioneCdErr"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="erroreAltreFunzioni" length="480" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ErroreFunzioniPortafoglioDTO" iterations="60" blockLength="8">
					<field-map attributeName="codiceFunzioneCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="descrizFunzioneCdErr"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>