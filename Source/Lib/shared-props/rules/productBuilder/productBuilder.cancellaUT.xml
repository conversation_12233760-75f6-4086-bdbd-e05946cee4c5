<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-CANCELLA-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0016</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>CANCELLA UT</logAppServDesc>
		<areaFunzionale>PRODUCT BUILDER-SELEZIONE PRODOTTO</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.DettaglioUTProductBuilderRequestDTO" nomeKeyERR="erroriSalvaUTPD">
			<!-- Totali 184 car (150 + 34) -->
		    <!-- Campi di input 34 car -->
 			<field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB" length="11" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="11">
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<!-- codice prodotto -->
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceUT"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>					
			<field-map attributeName="codiceUTMacchina"  length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>					
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.garanzia.dto.DettaglioUTProductBuilderResponseDTO">
		    <!-- Totali 1978 car (150 + 38 + 1824) --> 
		    <!-- 34 input + 4 output = 38 car -->
		    <!-- Chiave UT 34 car -->
		    <field-map attributeName="codSoc"          length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB" length="11" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="11">
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<!-- codice prodotto -->
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceUT"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>					
			<field-map attributeName="codiceUTMacchina"  length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
		    <field-map attributeName="codiceTariffaCdErr"              	   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
