<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-CANCELLA-PRODOTTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0010</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>ELIMINA PRODOTTO</logAppServDesc>
		<areaFunzionale>PRODUCT BUILDER-SELEZIONE PRODOTTO</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.CancellaProdottoProductBuilderRequestDTO" nomeKeyERR="erroriSalvaProdottoPD">
			<!-- Totali 174 car (150 + 24) -->
			<!-- 24 car -->
			<field-map attributeName="codSoc" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB" length="21" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.prodotto.dto.CancellaProdottoProductBuilderResponseDTO">
		    <!-- Totali 3926 car (150 + 28 + 1824) --> 
		    <!-- 28 car -->
		    <!-- Campi di input 24 car -->
 			<field-map attributeName="codSoc" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="componentePB" length="21" >
				<nested-mapping className="it.sistinf.albedoweb.productBuilder.dto.ComponenteProductBuilderDTO" iterations="0" blockLength="21">
					<field-map attributeName="codice"    	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceMacchina"  length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
					<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- output 4 car -->
			<field-map attributeName="codiceCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>