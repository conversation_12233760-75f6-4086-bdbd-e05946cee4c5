<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PD-COPIA-FORMULA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSPB0031</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.CopiaFormulaProductBuilderRequestDTO" nomeKeyERR="erroriCopiaPD">
			<!-- Totali 236 car (150 + 86) -->
			<!-- 86 car -->
			<field-map attributeName="codSoc" 			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="codSocNew" 		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTOld"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormulaOld" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="categoriaNew"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="codiceUTNew"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormulaNew" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codiceFormulaCdErr" nomeKeyERR="erroriCopiaPD"/>
			<field-map attributeName="descrFormulaNew" 	length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.CopiaFormulaProductBuilderResponseDTO">
		    <!-- Totali 2114 car (150 + 140 + 1824) --> 
		    <!-- 140 car -->
			<field-map attributeName="codSoc" 			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="codSocNew" 		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="  "/>
			<field-map attributeName="categoria"       	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTOld"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormulaOld" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="categoriaNew"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="codiceUTNew"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceFormulaNew" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="descrFormulaNew" 	length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFormulaCdErr"  length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
			<field-map attributeName="descrFormulaObbligatoria" length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		
		</output-mapping>
	</rule>
</rules>