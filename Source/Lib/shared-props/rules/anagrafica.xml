<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>RICERCA-ANAGRAFICA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0003</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>INQUIRY ANAGRAFICA</logAppServDesc>
		<areaFunzionale>ANAGRAFICA</areaFunzionale>
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.anagrafica.dto.AnagraficaRicercaRequestDTO" nomeKeyERR="erroriAnagraficaCliente" >
			<!-- Totali 384 car (150 + 234) -->
			<!-- 234 car -->
			<field-map attributeName="cognome"   		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="nome"      		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
			<field-map attributeName="luogoDiResidenza" length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataDiNascita"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="luogoDiNascita"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceFiscale"    length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" valida="true" segnato="false" numInteri="13" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="errCodiceCliente" nomeKeyERR="erroriAnagraficaCliente"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.anagrafica.dto.AnagraficaRicercaResponseDTO">
		    <!-- Totali 29122 car = 150 + 27148 + 1824 --> 
		    <!-- input + output 27148 car -->
		    <!-- Campi di input 234 car -->
			<field-map attributeName="cognome"          length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="nome"             length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="luogoDiResidenza" length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataDiNascita"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="luogoDiNascita"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceFiscale"    length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="13" numDecimali="0" separatoreMigliaia="false"/>
            <!-- Figure anagrafiche -->
            <!-- output lenght 26914-->		    
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="figureAnagrafiche" length="26910" >
				<nested-mapping className="it.sistinf.common.albedo.dto.FiguraAnagraficaDTO" iterations="90" blockLength="299">
					<field-map attributeName="codiceCliente"  		length="13" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFiscale" 		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognome"   			length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="nome"      			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="indirizzoDiResidenza" length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoDiResidenza"     length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoDiNascita"  		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provinciaDiNascita" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipologia" 	        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>