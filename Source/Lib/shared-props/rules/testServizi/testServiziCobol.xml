<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PROGRAMMA-GENERICO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
		<program>DUMMYPROG</program>
		<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<multipleTransaction>true</multipleTransaction>
		<pageRequestField/>
		<limitPage>99</limitPage>
		<moreDataField/>
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />
		<pastedFields>
		  <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
		</pastedFields>
		<!-- Definizione commarea:  -->  
		  <input-mapping className="it.sistinf.albedoweb.cobtest.dto.CobTestRequestDTO">
		  <!-- input :  10  car -->
		  	<field-map attributeName="inputCommarea" length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
		 </input-mapping>
		  <output-mapping className="it.sistinf.albedoweb.cobtest.dto.CobTestResponseDTO">
		  <!-- output :  10  car -->      
			<field-map attributeName="outputCommarea" length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />      
		 </output-mapping>

	</rule>
</rules>