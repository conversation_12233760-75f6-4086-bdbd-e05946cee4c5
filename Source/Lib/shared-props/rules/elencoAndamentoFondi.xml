<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ANDAMENTO-FONDI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSERD008</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.fondi.dto.AndamentoFondiRequestDTO">
			<!-- Totali 171 car (150 + 21) -->
			<!-- 21 car -->
		    <field-map attributeName="codFondo"  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="dataDa"    length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Data"/>
            <field-map attributeName="dataA"     length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
            	natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fondi.dto.AndamentoFondiResponseDTO">
		    <!-- Totali 3103 car (150 + 7770 + 1824) --> 
		    <!-- 7770 car -->
		    <!-- Campi di input 23 car -->
			<field-map attributeName="codFondo"  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="dataDa"    length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Data"/>
            <field-map attributeName="dataA"     length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data"/>
			<!-- Campi di output -->
			<field-map attributeName="numeroElemTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="flAltri"      length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="dataMin"      length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
				natura="Data"/>            
			<field-map attributeName="valoreMin"    length="11"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>            
			<field-map attributeName="dataMax"      length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
				natura="Data"/>            
			<field-map attributeName="valoreMax"    length="11"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>            
			<field-map attributeName="variazionePercentuale"    length="9"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>            
            <field-map attributeName="elencoValoriFondi" length="7686" >
            	<nested-mapping className="it.sistinf.albedoweb.fondi.dto.ValoreFondoDTO" iterations="366" blockLength="21">
            		<field-map attributeName="dataFondo"     length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="valoreFondo"   length="11" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
	</output-mapping>
     </rule>
</rules>
    