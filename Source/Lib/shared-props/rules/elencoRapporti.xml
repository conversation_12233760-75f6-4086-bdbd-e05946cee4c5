<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-RAPPORTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0030</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaRequestDTO">
			<!-- Totali 263 car (150 + 13) -->
			<!-- 13 car -->
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaResponseDTO">
		    <!-- Totali 24391 car (150 + 26617 + 1824) --> 
		    <!-- 26617 car -->
		    <!-- Campi di input 13 car -->
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <!-- Proposte -->
            <!-- 22404 car -->		    
			<field-map attributeName="listaRapporti"						length="26604">
				<nested-mapping className="it.sistinf.common.albedo.utility.BaseAlbedoListaElementiDTO" iterations="0" blockLength="26604">
					<field-map attributeName="numElementi" 					length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="000" offset="" padding="0" />
			      	<field-map attributeName="flAltri" 						length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      	<field-map attributeName="listaDaCommarea" 				length="26600">
						<nested-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoDTO" iterations="200" blockLength="133">
							<field-map attributeName="codSocieta"  		    length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="000" offset="" padding="0" />
							<field-map attributeName="categoria"  		    length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="agenziaProposta" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroCollettiva"     length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroProposta"   	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="prodotto"             length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descProdotto"         length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDecorrenza"       length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="data" />					
							<field-map attributeName="numeroUT"             length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoProposta"        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroPolizzaColl"   	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoPolizza"   	    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="causalePolizza"   	length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="ruoli"   				length="21" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    