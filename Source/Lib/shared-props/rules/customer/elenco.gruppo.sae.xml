<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-SOTTOGRUPPO-SAE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>PWLSLSAE</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.domini.dto.ElencoDominiRequestDTO">
			<!-- Totali 20 car -->
			<!-- 20 car -->
			<field-map attributeName="codice1" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codice2" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.domini.dto.ElencoDominiResponseDTO">
		    <!-- Totali 25219 car (150 + 23245 + 1824) --> 
	            <!-- 23245 car -->
				<field-map attributeName="numElementi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
				<field-map attributeName="flAltri"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
				<field-map attributeName="elementiDominio" length="23240">
					<nested-mapping className="it.sistinf.albedoweb.domini.dto.ElementoDominioDTO" iterations="280" blockLength="83">
						<field-map attributeName="codice"  	   length="03" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="descrizione" length="80" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>
		</output-mapping>
	</rule>
</rules>
    