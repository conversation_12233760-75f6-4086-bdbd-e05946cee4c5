<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INIZIALIZZAZIONE-CUSTOMER</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>PWLSE000</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.customer.dto.CustomerRicercaRequestDTO" nomeKeyERR="erroriSalvaCustomer">
			<!-- input: 04 car -->
			<field-map attributeName="utilizzatore"   	length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ruolo"   			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.customer.dto.CustomerRicercaResponseDTO">
			<!-- output : 04 (stessi dati input) -->   
		    <!-- Totali 1978 car = 150 + 04 + 1824 --> 
			<field-map attributeName="utilizzatore"   	length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ruolo"   			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>            
		</output-mapping>
	</rule>
</rules>
    