<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ATTIVAZIONE-ONLINE-PROPOSTA-COLLETTIVA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE617</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>ATTIVAZIONE-ONLINE-PROPOSTA-COLLETTIVA</logAppServDesc>
		<areaFunzionale>PROPOSTA</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportiBaseRequestDTO">
			<!-- Totali 165 car (150 + 15) -->			
			<!-- Input 15 car -->
		    <field-map attributeName="categoria"       			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaProposta" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numCategoria"      		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportiBaseResponseDTO">
		    <!-- Totali 1990 car (150 + 15 + 1824) --> 
		    <!-- Output: stessi caratteri di Input 15 car -->
		    <field-map attributeName="categoria"               	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaProposta"         	length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numCategoria"      		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>            			
		</output-mapping>
     </rule>
</rules>
    