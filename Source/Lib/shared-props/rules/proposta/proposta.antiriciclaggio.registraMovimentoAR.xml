<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-MOVIMENTO-AR</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0146</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.MovimentoAntiriciclaggioRequestDTO">
			<!-- Totali 5872 car (150 + 5722) -->
			<!-- input 5722 car-->
			<field-map attributeName="tipoEvento"  		     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoEvento"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="progrMovimento"   	 length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="statoMovimento"  		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrStatoMovimento"	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoChiave"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoChiave"  	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="chiaveDinamica"  		 length="45" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaProposta"  	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroProposta"  		 length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaPolizza"  		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroPolizza"         length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataEffetto"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				valida="true" natura="Data"  nomeAttributoERR="dataEffetto" nomeKeyERR="erroriAntiriciclaggio"/>
			<field-map attributeName="dataOperazione"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				valida="true" natura="Data"  nomeAttributoERR="dataOperazione" nomeKeyERR="erroriAntiriciclaggio"/>
			<field-map attributeName="operazioneFrazionata"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="coordinate"  			 length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numAssegno"  		 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="modPagamento"  	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="valuta"        	     length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="importoAR" 			 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoAR" nomeKeyERR="erroriAntiriciclaggio"/>
		    <field-map attributeName="elencoSoggettiMovimento" length="5463">
				<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.ElencoSoggettiMovimentoDTO" iterations="0" blockLength="5463">        
					<field-map attributeName="numElementiTrovati"   length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="soggettiMovimento" length="5460">      
						<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.SoggettoMovimentoARDTO" iterations="20" blockLength="273">
							<field-map attributeName="tipoSoggetto"  		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
							<field-map attributeName="dettaglioSoggetto" length="194" > 
								<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.AnagraficaPercipienteDTO" iterations="0" blockLength="194"> 
									<field-map attributeName="codiceFiscale"            length="16" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<!-- field-map attributeName="nominativo"               length="60" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/ -->
									<field-map attributeName="cognome"      			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
									<field-map attributeName="nome"      				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
									<field-map attributeName="indirizzoDiResidenza"     length="35" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="luogoDiResidenza"         length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="capResidenza"             length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiResidenza"     length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="stato"                    length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="sesso"                    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="dataDiNascita"            length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
									    valida="true" natura="Data"   nomeAttributoERR="soggettiMovimento[].dettaglioSoggetto.dataDiNascita" nomeKeyERR="erroriAntiriciclaggio" />
									<field-map attributeName="luogoDiNascita"           length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiNascita"       length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
								</nested-mapping>
							</field-map>
							<field-map attributeName="datiAntiriciclaggio" length="78">
								<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="78">
									<field-map attributeName="numeroDocumento"    	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="tipoDocumento"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
									<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
										 valida="true" natura="Data"   nomeAttributoERR="soggettiMovimento[].datiAntiriciclaggio.dataRilascio" nomeKeyERR="erroriAntiriciclaggio"  />
						       		<field-map attributeName="localitaRilascio"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCab"               length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="gruppo"               length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="sottoGruppo"          length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="enteRilascio"     	length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								</nested-mapping>
							</field-map>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.MovimentoAntiriciclaggioResponseDTO">
		    <!-- Totali 9468 car (150 + 7494 + 1824)  -->  
		    <!-- Campi Output + input 5722 + 1772 = 7494 car-->
			<!-- input 5722 car -->
			<field-map attributeName="tipoEvento"  		     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoEvento"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="progrMovimento"   	 length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="statoMovimento"  		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrStatoMovimento"	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoChiave"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoChiave"  	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="chiaveDinamica"  		 length="45" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaProposta"  	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroProposta"  		 length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaPolizza"  		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroPolizza"         length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataEffetto"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
			 natura="Data" />
			<field-map attributeName="dataOperazione"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
			 natura="Data" />
			<field-map attributeName="operazioneFrazionata"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="coordinate"  			 length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numAssegno"  		 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="modPagamento"  	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="valuta"        	     length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="importoAR" 			 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="elencoSoggettiMovimento" length="5463">
				<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.ElencoSoggettiMovimentoDTO" iterations="0" blockLength="5463">         
					<field-map attributeName="numElementiTrovati"   length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="soggettiMovimento" length="5460">
						<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.SoggettoMovimentoARDTO" iterations="20" blockLength="273"> 
							<field-map attributeName="tipoSoggetto"  		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
							<field-map attributeName="dettaglioSoggetto" length="194" >
								<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.AnagraficaPercipienteDTO" iterations="0" blockLength="194">
									<field-map attributeName="codiceFiscale"            length="16" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<!-- <field-map attributeName="nominativo"               length="60" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/> -->
									<field-map attributeName="cognome"   				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>  
									<field-map attributeName="nome"      				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>  
									<field-map attributeName="indirizzoDiResidenza"     length="35" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="luogoDiResidenza"         length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="capResidenza"             length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiResidenza"     length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="stato"                    length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="sesso"                    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="dataDiNascita"            length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
									  natura="Data"  />
									<field-map attributeName="luogoDiNascita"           length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiNascita"       length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
								</nested-mapping>
							</field-map>
							<field-map attributeName="datiAntiriciclaggio" length="78">
								<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="78">
									<field-map attributeName="numeroDocumento"    	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="tipoDocumento"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
									<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
									  natura="Data" />
						       		<field-map attributeName="localitaRilascio"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCab"               length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="gruppo"               length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="sottoGruppo"          length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="enteRilascio"     	length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								</nested-mapping>
							</field-map>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<!-- Output 1772 --> 
			<field-map attributeName="tipoEventoCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="progrMovimentoCdErr"   	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="statoMovimentoCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="tipoChiaveCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="chiaveDinamicaCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="agenziaPropostaCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="numeroPropostaCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="agenziaPolizzaCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="numeroPolizzaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataEffettoCdErr"          length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataOperazioneCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="operazioneFrazionataCdErr" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="codiceIbanCdErr"  	     length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="siglaInternazCdErr"  	     length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="codiceControlloCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="codiceCinCdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="codiceAbiCdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="codiceCabCdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="contoCorrenteCdErr"  	     length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="numAssegnoCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="modPagamentoCdErr"  	     length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="valutaCdErr"        	     length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="importoARCdErr" 			 length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
		    <field-map attributeName="erroreSoggettiMovimento"   length="1680">
				<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.ErroreSoggettiMovimentoDTO" iterations="20" blockLength="84">
					<field-map attributeName="tipoSoggettoCdErr"  		  length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
					<field-map attributeName="erroreDettaglioSoggetto" length="48" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.ErrorePercipienteDTO" iterations="0" blockLength="48">
							<field-map attributeName="codiceFiscaleCdErr"            length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<!-- field-map attributeName="nominativoCdErr"               length="8" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/ -->
							<field-map attributeName="cognomeCdErr"   				 length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="nomeCdErr"      				 length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="indirizzoDiResidenzaCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="luogoDiResidenzaCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="capResidenzaCdErr"             length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="provinciaDiResidenzaCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="statoCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="sessoCdErr"                    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="dataDiNascitaCdErr"            length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="luogoDiNascitaCdErr"           length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="provinciaDiNascitaCdErr"       length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="erroreDatiAntiriciclaggio" length="32">
						<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.ErroreDatiAntiriciclaggioDTO" iterations="0" blockLength="32">
							<field-map attributeName="numeroDocumentoCdErr"    	 length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="tipoDocumentoCdErr"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="dataRilascioCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
					   		<field-map attributeName="localitaRilascioCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="numCabCdErr"               length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="gruppoCdErr"               length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="sottoGruppoCdErr"          length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
							<field-map attributeName="enteRilascioCdErr"     	 length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    