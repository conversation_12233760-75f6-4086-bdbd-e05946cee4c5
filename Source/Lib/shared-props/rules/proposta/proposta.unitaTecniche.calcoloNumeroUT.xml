<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLO-NUMERO-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0165</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.UnitaTecnicheRequestDTO">
		<!-- Totali 151 car (150 + 1) -->
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="filler" length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.UnitaTecnicheResponseDTO">
		<!-- Totali 1989 car (150 + 4 + 1824) -->		
			<field-map attributeName="numeroUT" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>