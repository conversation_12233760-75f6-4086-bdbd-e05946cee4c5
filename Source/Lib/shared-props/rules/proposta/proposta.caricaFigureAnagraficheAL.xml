<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-FIGURE-ANAGRAFICHE-AL</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0036</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaDettaglioRequestDTO">
			<!-- Totali 163 car (150 + 13) -->
			<!-- 13 car -->
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaDettaglioResponseDTO">
		    <!-- Totali 3064 car (150 + 1090 + 1824) --> 
		    <!-- Campi di output 1090 car -->
            <field-map attributeName="codiceCliente"    length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="elencoAnagrafica"  length="882" >
            	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="3" blockLength="294">
					<field-map attributeName="tipoRelazione"    	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="indiceFigura"    	    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		            <field-map attributeName="codiceCliente"    	length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		       		<field-map attributeName="nominativo"           length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
		       		<field-map attributeName="titolo"               length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
		       		<field-map attributeName="flConsenso1"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="flConsenso2"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="flConsenso3"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="flConsenso4"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="flConsenso5"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="flConsenso6"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiResidenza"     length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
					<field-map attributeName="capResidenza"         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="stato"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiNascita"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provinciaDiNascita"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="sesso"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceProfessione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descProfessione"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceStatoCivile"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="descStatoCivile"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="numFigli"             length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0"/>
			   	</nested-mapping>
            </field-map>
		    <!-- Dati Domicilio 120 car -->
		    <field-map attributeName="pressoDomicilio"      length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
		    <field-map attributeName="indirizzoDomicilio"   length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="localitaDomicilio"    length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
			<field-map attributeName="codiceLocalitaDomicilio"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
			<field-map attributeName="capDomicilio"         length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="provDomicilio"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="statoDomicilio"       length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <!-- Dati Antiriciclaggio 75 car -->
			<field-map attributeName="datiAntiriciclaggio"  length="75" >
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="75">
					<field-map attributeName="numeroDocumento"    	length="12" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		       		<field-map attributeName="tipoDocumento"        length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
       				<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
       				<field-map attributeName="localitaRilascio"     length="20"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="enteRilascio"     	length="20"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>       		
       				<field-map attributeName="numCab"            	length="5"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		       		<field-map attributeName="gruppo"           	length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
       				<field-map attributeName="sottoGruppo"          length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>