<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-PROPOSTA-PARZ-FIN-COLLETTIVA-FULL-JAVA</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE579</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA PROPOSTA PARZ FIN COLLETTIVA FULL JAVA</logAppServDesc>
		<areaFunzionale>ASSUNZIONE PROPOSTA</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRequestDTO">
			<!-- Totali 246 car (150 + 96) -->
			<!-- 96 car -->
            <field-map attributeName="codiceClienteContraente"  length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="flSalvaParziale"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flOnline"  			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codCliContraente1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliContraente2"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliRapprLeg1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliRapprLeg2"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliTitolare1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliBenefMorte1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliBenefVita1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliEsecutore1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliAziendaAss1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaResponseDTO">
		    <!-- Totali 2087 car (150 + 113 + 1824) --> 
		    <!-- Campi di output 14 + 17 + 82 car -->
		    <field-map attributeName="codiceClienteContraente"  length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="flSalvaParziale"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            
            <field-map attributeName="flOnline"  			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codCliContraente1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliContraente2"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliRapprLeg1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliRapprLeg2"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliTitolare1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliBenefMorte1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliBenefVita1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliEsecutore1"		length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCliAziendaAss1"	length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            
		    <field-map attributeName="categoria"                 length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numCategoria"  		     length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenzia"  		         length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            <field-map attributeName="numeroColl"     	         length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="propostaModificata" 		 length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		</output-mapping>
	</rule>
</rules>
    