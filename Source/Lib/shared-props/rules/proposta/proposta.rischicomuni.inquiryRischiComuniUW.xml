<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INQUIRY-RISCHI-COMUNI-UW</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE657</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    <!-- Lunghezza input: 191 car -->
		<input-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RischioComuneRequestDTO">
			<!-- input: 191 car -->
            <field-map attributeName="flagAssicurato"    			length="01" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flagRipartenza" 				length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userId" 						length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanza" 						length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoArea" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizza" 				length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizione" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPagina" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		
			<field-map attributeName="flagRipartenzaDreadDesease"	length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdDreadDesease" 			length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaDreadDesease" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaDreadDesease" 		length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaDreadDesease" 	length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneDreadDesease" 		length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaDreadDesease" 		length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flagRipartenzaInfortuni" 		length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdInfortuni" 				length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaInfortuni" 			length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaInfortuni" 		length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flagRipartenzaLtc" 			length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdLtc" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaLtc" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaLtc" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaLtc" 			length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneLtc" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaLtc" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flagRipartenzaPrPe" 			length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdPrPe" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaPrPe" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaPrPe" 				length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaPrPe" 			length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizionePrPe" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaPrPe" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RischioComuneResponseDTO">
            <!--  output : 191 + 55 + 6145 car = 6391 car -->
            <field-map attributeName="flagAssicurato"    			length="01" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flagRipartenza" 				length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userId" 						length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanza" 						length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoArea" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizza" 				length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizione" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPagina" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		
			<field-map attributeName="flagRipartenzaDreadDesease"	length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdDreadDesease" 			length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaDreadDesease" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaDreadDesease" 		length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaDreadDesease" 	length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneDreadDesease" 		length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaDreadDesease" 		length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flagRipartenzaInfortuni" 		length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdInfortuni" 				length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaInfortuni" 			length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaInfortuni" 		length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaInfortuni" 			length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="flagRipartenzaLtc" 			length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdLtc" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaLtc" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaLtc" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaLtc" 			length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizioneLtc" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaLtc" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						
			<field-map attributeName="flagRipartenzaPrPe" 			length="01" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="userIdPrPe" 					length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="istanzaPrPe" 					length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoAreaPrPe" 				length="08" precision="0" numericScale="0" align="left"		mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numeroPolizzaPrPe" 			length="09" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="posizionePrPe" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPaginaPrPe" 				length="04" precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
            <field-map attributeName="nominativoAssicuratoO"    	length="55" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>            
			
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaRapporti" length="1225" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RapportoRischioComune" iterations="5" blockLength="245">
					<field-map attributeName="tipoEntita" 	    length="15" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numPolizza" 	 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="posizione" 	    length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="prodotto" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descProdotto" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codiceUT" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descrizioneUT" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="sommaRischio" 	length="17" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="10" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="tipoGaranzia" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />																																
					<field-map attributeName="sovraSanMille" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSanPerc" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProf" 		length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProfInf" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSportivo" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   		</nested-mapping>
			</field-map>		
			
			<field-map attributeName="numElementiTrovatiDreadDesease" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltriDreadDesease"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaRapportiDreadDesease" length="1225" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RapportoRischioComune" iterations="5" blockLength="245">
					<field-map attributeName="tipoEntita" 	    length="15" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numPolizza" 	 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="posizione" 	    length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="prodotto" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descProdotto" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codiceUT" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descrizioneUT" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="sommaRischio" 	length="17" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="10" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="tipoGaranzia" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />																																
					<field-map attributeName="sovraSanMille" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSanPerc" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProf" 		length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProfInf" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSportivo" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   		</nested-mapping>
			</field-map>		
			
			<field-map attributeName="numElementiTrovatiInfortuni"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltriInfortuni"     		length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaRapportiInfortuni" length="1225" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RapportoRischioComune" iterations="5" blockLength="245">
					<field-map attributeName="tipoEntita" 	    length="15" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numPolizza" 	 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="posizione" 	    length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="prodotto" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descProdotto" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codiceUT" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descrizioneUT" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="sommaRischio" 	length="17" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="10" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="tipoGaranzia" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />																																
					<field-map attributeName="sovraSanMille" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSanPerc" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProf" 		length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProfInf" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSportivo" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   		</nested-mapping>
			</field-map>		
			
			<field-map attributeName="numElementiTrovatiLtc"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltriLtc"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaRapportiLtc" length="1225" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RapportoRischioComune" iterations="5" blockLength="245">
					<field-map attributeName="tipoEntita" 	    length="15" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numPolizza" 	 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="posizione" 	    length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="prodotto" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descProdotto" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codiceUT" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descrizioneUT" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="sommaRischio" 	length="17" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="10" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="tipoGaranzia" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />																																
					<field-map attributeName="sovraSanMille" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSanPerc" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProf" 		length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProfInf" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSportivo" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   		</nested-mapping>
			</field-map>		 
			
			<field-map attributeName="numElementiTrovatiPrPe" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltriPrPe"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaRapportiPrPe" length="1225" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.rischicomuni.dto.RapportoRischioComune" iterations="5" blockLength="245">
					<field-map attributeName="tipoEntita" 	    length="15" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numPolizza" 	 	length="07" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="posizione" 	    length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="prodotto" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descProdotto" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codiceUT" 	    length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descrizioneUT" 	length="60" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="sommaRischio" 	length="17" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="10" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="tipoGaranzia" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />																																
					<field-map attributeName="sovraSanMille" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSanPerc" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProf" 		length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraProfInf" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="sovraSportivo" 	length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   		</nested-mapping>
			</field-map>		 																															
		</output-mapping>
	</rule>
</rules>
    