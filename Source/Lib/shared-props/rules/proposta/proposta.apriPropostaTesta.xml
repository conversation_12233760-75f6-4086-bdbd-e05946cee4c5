<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>APRI-PROPOSTA-TESTA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE584</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>INQUIRY PROPOSTA</logAppServDesc>
		<areaFunzionale>PROPOSTA</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportiBaseRequestDTO" nomeKeyERR="erroriRapportiVita">
			<!-- Totali 173 car (150 + 23) -->
			<!-- 23 car -->
		    <field-map attributeName="categoria"       			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"    			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
            	valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroColl" nomeKeyERR="erroriRapportiVita"/>
            <field-map attributeName="numeroProposta"      		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
            	valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroColl" nomeKeyERR="erroriRapportiVita"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.rapporti.dto.RapportiResponseDTO">
		    <!-- Totali 2405 car (150 + 431 + 1824) --> 
		    <!--  output 408 + 23 car -->
		    <field-map attributeName="categoria"               	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"            	length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta"         	length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta"      		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            	
			<field-map attributeName="statoColl"               	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoCategoria"           	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceProdotto"          	length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="prodotto"      			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="descProdotto"  			length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="agenziaGest"          	length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descAgenziaGest"      	length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="subagente"            	length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descSubagente"        	length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="promotore"            	length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descPromotore"        	length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	<field-map attributeName="numeroTeste"  		   	length="7" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
           	<field-map attributeName="contrCognome"        		length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="contrNome"        		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="aziendaCognome"        	length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="aziendaNome"        		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataDecorrenza" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="data"/>
			<field-map attributeName="dataEmissione" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="data"/>
			<field-map attributeName="dataProposta" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="data"/>
		</output-mapping>
     </rule>
</rules>
    