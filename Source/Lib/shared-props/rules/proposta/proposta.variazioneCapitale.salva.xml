<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-VARIAZIONE-CAPITALE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE606</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DatiTecniciUTRequestDTO">
			<!-- Totali 198 car (150 + 48) -->
			<!-- 48 car -->
			<field-map attributeName="flConferma"  	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="filler"  		length="9" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataDecorrenzaInput"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data" nomeAttributoERR="dataDecorrenza" nomeKeyERR="erroriDatiTecnici" />
            <field-map attributeName="premioNettoInput"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioNettoInput" nomeKeyERR="erroriDatiTecnici"/>
			 <field-map attributeName="premioIngrInput"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="capitale" nomeKeyERR="erroriDatiTecnici"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DatiTecniciUTResponseDTO">
		    <!-- Totali 2166 car (150 + 192 + 1824) --> 
			
			<!-- 48 car -->
			<field-map attributeName="flConferma"  	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="filler"  		length="9" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataDecorrenzaInput"	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data" />
            <field-map attributeName="premioNettoInput"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioIngrInput"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
          
            <!-- Dati Tecnici 116 car-->
            <field-map attributeName="codiceTariffa"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descrizioneTariffa"    length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="premioIngr"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="dataDecorrenza"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data" />
            <field-map attributeName="dataScadenza"			 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="premioNetto"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capitale"              length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
              	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
            <field-map attributeName="provvigioni"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
              	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
              
             <!-- ERRORI 28 -->	
             <field-map attributeName="codiceTariffaCdErr"   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="premioIngrCdErr"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="dataDecorrenzaCdErr"	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="dataScadenzaCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="premioNettoCdErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="capitaleCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			 <field-map attributeName="provvigioniCdErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    