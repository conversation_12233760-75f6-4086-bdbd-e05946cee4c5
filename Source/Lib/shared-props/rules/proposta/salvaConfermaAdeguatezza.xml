<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-CONFERMA-ADEGUATEZZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE466</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.RispostaQuestionarioRequestDTO">
			<!-- Totali 670 car (150 + 520) -->
			<!-- 520 car -->
           	<field-map attributeName="codQuestionario" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="progrStrutturaQuest"    	length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="progSel"    				length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codiceInadeguatezza"  	length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descrInadeguatezza"  		length="500" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.RispostaQuestionarioResponseDTO">
		    <!-- Totali 2494 car (150 + 520 + 1824) -->
		    <!-- 520 car -->
		    <field-map attributeName="codQuestionario" 			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="progrStrutturaQuest"    	length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="progSel"    				length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codiceInadeguatezza"  	length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descrInadeguatezza"  		length="500" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		</output-mapping>
	</rule>
</rules>