<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLO-ASSICURATO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0014</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheRequestDTO">
			<!-- Totali 449 car (150 + 299) -->
			<!-- 329 car -->
			<field-map attributeName="flCoincidente"    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="S" offset="" padding=""/>
            <field-map attributeName="datiAnagrafiche"  length="328" >
            	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="328">
            		<field-map attributeName="indiceFigura"         length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="codiceCliente"        length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="cognome"           	length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="nome"           		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="titolo"               length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="flConsenso1"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso2"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso3"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso4"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso5"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso6"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiResidenza"     length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
					<field-map attributeName="capResidenza"         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="capNascita"           length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding="0"/>
					<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="stato"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiNascita"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provinciaDiNascita"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="sesso"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceProfessione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descProfessione"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceStatoCivile"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="descStatoCivile"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="numFigli"             length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheResponseDTO">
		    <!-- Totali 2399 car (150 + 425 + 1824) --> 
		    <!-- 425 car -->
		    <!-- Campi di input 329 car -->
			<field-map attributeName="flCoincidente"   length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="S" offset="" padding=""/>
            <field-map attributeName="datiAnagrafiche" length="328" >
            	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="328">
            		<field-map attributeName="indiceFigura"         length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="codiceCliente"        length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="cognome"           	length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="nome"           		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="titolo"               length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            		<field-map attributeName="flConsenso1"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso2"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso3"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso4"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso5"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="flConsenso6"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            		<field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiResidenza"     length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
					<field-map attributeName="capResidenza"         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="capNascita"           length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding="0"/>
					<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="stato"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiNascita"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provinciaDiNascita"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="sesso"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceProfessione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descProfessione"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceStatoCivile"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
					<field-map attributeName="descStatoCivile"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="numFigli"             length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
            <!-- Errori Anagrafiche -->
            <!-- 96 car -->
            <field-map attributeName="erroriFigureAnagrafiche" length="96">            
            	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.ErroriFigureAnagraficheDTO" iterations="1" blockLength="96">            	
            		<field-map attributeName="nominativoCdErr"     		 length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="cognomeCdErr"     		 length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="nomeCdErr"     		 	 length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="titoloCdErr"               length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso1CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso2CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso3CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso4CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso5CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="flConsenso6CdErr"          length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            		<field-map attributeName="indirizzoDiResidenzaCdErr" length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="luogoDiResidenzaCdErr"     length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="capResidenzaCdErr"         length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="provinciaDiResidenzaCdErr" length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="statoCdErr"                length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="dataDiNascitaCdErr" 	     length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="luogoDiNascitaCdErr"       length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="provinciaDiNascitaCdErr"   length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="codiceFiscaleCdErr"        length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="sessoCdErr"                length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="codiceProfessioneCdErr" 	 length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="tipologiaCdErr"            length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="descStatoCivileCdErr"      length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
					<field-map attributeName="numFigliCdErr"             length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0"  offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
		</output-mapping>
	</rule>
</rules>
    