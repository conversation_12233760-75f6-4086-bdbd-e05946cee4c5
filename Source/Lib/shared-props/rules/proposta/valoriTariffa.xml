<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VALORI-TARIFFA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSERD076</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DominioImportoRequestDTO">
			<!-- Totali 173 car (150 + 32) -->
			<!-- 32 car -->
			<field-map attributeName="numCategoria"   length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenzia" 		  length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCollettiva"  length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codiceProdotto" length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DominioImportoResponseDTO">
		    <!-- Totali 26011 car (150 + 24037 + 1824) --> 
		    	
		    	<field-map attributeName="numCategoria"   length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   				<field-map attributeName="agenzia" 		  length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	<field-map attributeName="numCollettiva"  length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="numeroPolizza"  length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="codiceProdotto" length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    	
	            <!-- 24005 car -->		    
				<field-map attributeName="numElementi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
				<field-map attributeName="flAltri"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
				<field-map attributeName="elementiDominio" length="24000">
					<nested-mapping className="it.sistinf.albedoweb.domini.dto.ElementoDominioDTO" iterations="400" blockLength="60">
						<field-map attributeName="codice"  	   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="descrizione" length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>
		</output-mapping>
	</rule>
</rules>
    