<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VA-TRADIZIONALE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE635</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO">
			<!-- Totali 181 car (150 + 35) -->
			<!-- 35 car --> 
            <field-map attributeName="fromRiepilogo" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flVersamentoAgg" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"			length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="dataDecorrenza"       	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data" nomeAttributoERR="dataDecorrenza" nomeKeyERR="erroriDatiTecnici" />
            <field-map attributeName="flTipoVersamentoAgg" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            <field-map attributeName="flTipoWorkflow" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flSoloLettura"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flagSenzaCalcoli"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO">
		    <!-- Totali 14274 car (150 + 12300 + 1824) --> 
		    
		    <field-map attributeName="fromRiepilogo" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flVersamentoAgg" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="idPrenotazione"			length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="dataDecorrenza"       	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data" nomeAttributoERR="dataDecorrenza" nomeKeyERR="erroriDatiTecnici" />
            <field-map attributeName="flTipoVersamentoAgg" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            <field-map attributeName="flTipoWorkflow" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flSoloLettura"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flagSenzaCalcoli"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            	
		    <!-- Campi di output 12029 + 34 + 43 + 10 + 57 + 125 + 1 + 5 + 16car -->
		    <field-map attributeName="flMultigaranzia" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flMultiquestionario" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    
		    <field-map attributeName="flAderente"  				length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flAzienda"  				length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flTfr"  					length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="opzioneGestionale"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="codiceConvenzione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceDeroga"        		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="dataRichiestaCliente" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    
		    <field-map attributeName="preRataCompl"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="contributoIscritto"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
              	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="contributoIscritto" nomeKeyERR="erroriDatiTecnici"/>
            <field-map attributeName="contributoAzienda"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
              	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="contributoAzienda" nomeKeyERR="erroriDatiTecnici"/>
            <field-map attributeName="tfr"          		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
              	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="tfr" nomeKeyERR="erroriDatiTecnici"/>
		    <field-map attributeName="volontaTrasferimento" length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		     	
		    <field-map attributeName="statoEvento"  		length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		     	
			<field-map attributeName="numUT" 					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteUT" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaUTRiepilogo" length="12000">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="100" blockLength="120">
            		<field-map attributeName="erroreUT" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="codiceUT"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descCodiceUT"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="decorrenzaUT"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="data"/>
					<field-map attributeName="durataAnniUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerAAMM"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataMesiUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="premioNetto" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioRata" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioIngr" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazione" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="flCapRen"	 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flConsolidata"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flAttSs"	 		length="1" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statusUT"	 		length="1" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="codPagatorePrimaRata"  	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		
			<!-- 125 -->
			<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoProdotto" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flGestioneTranching" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
		    <field-map attributeName="flGestioneModelloCosti" 	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
		    <field-map attributeName="dataPervenimentoCartaceo" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    <field-map attributeName="dataIncasso" 				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    <field-map attributeName="letteraSollecito" 	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="letteraSollecitoProtetto" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="pianoPens" 	        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  
		    
		    <field-map attributeName="flVAPur"  			length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  
		    <field-map attributeName="flSelVAPur"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>        
		    <field-map attributeName="importoVA"   			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		</output-mapping>
	</rule>
</rules>
    