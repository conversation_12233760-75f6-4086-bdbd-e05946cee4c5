<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-PROPOSTA-DIREZIONE-SENZA-CONTRIBUTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE529</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRequestDTO" nomeKeyERR="erroriRiepilogo">
			<!-- Totali 206 car (150 + 56) -->
			<!-- 56 car -->
			<field-map attributeName="flQuietanza"         	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaQuietanza"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaPolizza"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaAltriDoc"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="bloccoAttivazione" 	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descBloccoAttiv" 	    length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataEmissione" 	    length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataEmissione" nomeKeyERR="erroriRiepilogo"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaResponseDTO">
		    <!-- Totali 2067 car (150 + 93 + 1824) --> 
		    <!-- Campi di output 93 car -->
		    <!-- input 56 car -->
		    <field-map attributeName="flQuietanza"         	  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaQuietanza"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaPolizza"        length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStampaAltriDoc"		  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="bloccoAttivazione" 	  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descBloccoAttiv" 	      length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataEmissione" 	      length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="data"/>
		    <!-- output 37 -->
		    <field-map attributeName="categoria"      		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"   		  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenzia"  	  		  length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta" 		  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tabInErrore"      	  length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="bloccoAttivazioneCdErr" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="descBloccoAttivCdErr"   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataEmissioneCdErr" 	  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    