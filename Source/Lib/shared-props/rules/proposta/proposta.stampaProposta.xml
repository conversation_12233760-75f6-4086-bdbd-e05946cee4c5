<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>STAMPA-PROPOSTA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0029</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>INQUIRY PROPOSTA</logAppServDesc>
		<areaFunzionale>PROPOSTA</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportiBaseRequestDTO" nomeKeyERR="erroriRapportiVita">
			<!-- Totali 174 car (150 + 24) -->
			<!-- 24 car -->
		    <field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
            	valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroColl" nomeKeyERR="erroriRapportiVita"/>
			<field-map attributeName="numeroProposta"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroProposta" nomeKeyERR="erroriRapportiVita"/>
		    <field-map attributeName="flDaVersamentoAggiuntivo" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.rapporti.dto.RapportiResponseDTO">
		    <!-- Totali 2624 car (150 + 650 + 1824) --> 
		    <!--  output 650 car -->
		    <field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"            length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta"         length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta"          length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="flDaVersamentoAggiuntivo" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="tipoCategoria"           length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statoColl"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statoProposta"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="bloccoAttivazione"       length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceProdotto"          length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numAnagrafichePresenti"  length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="codiceCustomer"  		   length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>            
			<field-map attributeName="agenziaPolizza"  		   length="6" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>            
	        <field-map attributeName="numeroPolizza"  		   length="7" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>            
           	
           	<field-map attributeName="codLineaInvestimento"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	<field-map attributeName="senzaContributi"    	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	<field-map attributeName="convenzione"   		   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	
            <!--
            <field-map attributeName="datiAnagrafiche" length="8918">
            	<nested-mapping className="it.sistinf.common.albedo.rapporti.dto.AnagraficaRapportiBaseDTO" iterations="14" blockLength="637">
            		<field-map attributeName="coincidente"          length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="tipoFigura"           length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="descFigura"           length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
            		<field-map attributeName="cognome"   			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="nome"      			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            		<field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiResidenza"     length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
					<field-map attributeName="capResidenza"         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
            		<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="stato"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            		<field-map attributeName="sesso"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codiceProfessione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="titolo"               length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            		<field-map attributeName="tipologia"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            		<field-map attributeName="descBeneficiario"     length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="estensione"           length="350" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="luogoDiNascita"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provinciaDiNascita"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            	</nested-mapping>
            </field-map>
            -->
            
            <field-map attributeName="agenziaGest"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descAgenziaGest"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="subagente"            length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descSubagente"        length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="promotore"            length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descPromotore"        length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="dataProposta" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="data"/>
            <field-map attributeName="dataEmissione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="data"/>
            <field-map attributeName="dataDecorrenza" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="data"/>
            <field-map attributeName="dataScadenza" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="data"/>
            <field-map attributeName="prodotto"      		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="descProdotto"  		length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codiceTariffa"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="descTariffa"  		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="decorrenza"    		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="durataAnni"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="durataMesi"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="posizione"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="decorrenzaUT"    		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="codiceFrazionamento"  length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descFrazionamento"    length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="rateoInteresse"       length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="costoPolizza"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="costoPolizzaAgenzia"  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="costoQuietanza"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="speseMediche"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="pagamento"            length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>          
            <field-map attributeName="tipoPremio"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="importo"              length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="proxPremioNetto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioScadenza"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioNetto"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioRata"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capitale"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioTariffa"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioInventario"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioPuro"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioRataSuccessivo" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="interessiFrazionati"  length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="premioRischio"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="etaMassima"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="etaMassimaTariffa" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           
            <!--  
            <field-map attributeName="questionario" length="206">
				<nested-mapping className="it.sistinf.albedoweb.questionario.dto.QuestionarioDTO" iterations="0" blockLength="206">  
					<field-map attributeName="tipoQuestionario"   length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					<field-map attributeName="flEsistonoDati"     length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>					
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding=""/>
					<field-map attributeName="vociQuestionario" length="200">
						<nested-mapping className="it.sistinf.albedoweb.questionario.dto.VoceQuestionarioDTO" iterations="50" blockLength="4">
							<field-map attributeName="codiceDomanda"  length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="0" offset="" padding=""/>
							<field-map attributeName="codiceRisposta" length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="0" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>	
			-->
			
		
	</output-mapping>
     </rule>
</rules>
    