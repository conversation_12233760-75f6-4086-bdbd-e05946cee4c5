<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLA-MOD-PAG-PRIMA-RATA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE591</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO">
			<!-- Totali 347 car (150 + 197) -->
			<!-- input 197 car -->
			<field-map attributeName="modPagPrimaRata"  				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codPagatorePrimaRata"  			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intestatarioPrimaRata"			length="85" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceIbanPrimaRata"    			length="45" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codTipoAssegnoPrimaRata"  		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroAssegnoPrimaRata"  			length="13" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codBancaPrimaRata"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFilialePrimaRata"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="flNavigazione"          			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoModPagamentoPrimaRata"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flReinvioDiretto"          		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO">
		    <!-- Totali 2204 car (150 + 230 + 1824) -->
			<!-- Output 197 + 1 + 32 car-->
			<!-- input 197 car -->
			<field-map attributeName="modPagPrimaRata"  				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codPagatorePrimaRata"  			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intestatarioPrimaRata"			length="85" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceIbanPrimaRata"    			length="45" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codTipoAssegnoPrimaRata"  		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroAssegnoPrimaRata"  			length="13" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codBancaPrimaRata"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFilialePrimaRata"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="flNavigazione"          			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoModPagamentoPrimaRata"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flReinvioDiretto"          		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flTabRosso" 	  	  	      		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- ERRORI 32car-->
			<field-map attributeName="modPagamentoPrimaRataCdErr"  		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codPagatorePrimaRataCdErr"  		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="intestatarioPrimaRataCdErr"  		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codiceIbanPrimaRataCdErr"  		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codTipoAssegnoPrimaRataCdErr"  	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroAssegnoPrimaRataCdErr"  	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codBancaPrimaRataCdErr"  			length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codFilialePrimaRataCdErr"  		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
	</output-mapping>
	</rule>
</rules>