<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLO-PREMI-RIEPILOGO-PROPOSTA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0171</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO">
			<!-- Totali 11755 car (150 + 11605) -->
			<!-- 11605 car -->
            <field-map attributeName="numUT" 					length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteUT" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaUTRiepilogo" length="11600">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="100" blockLength="116">
            		<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="codiceUT"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descCodiceUT"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="decorrenzaUT"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Data" nomeAttributoERR="dataEmissione" nomeKeyERR="erroriRiepilogo"/>
					<field-map attributeName="durataAnniUT"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerAAMM"		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataMesiUT"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="premioNetto" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioNetto" nomeKeyERR="erroriRiepilogo"/>
					<field-map attributeName="premioRata" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioRata" nomeKeyERR="erroriRiepilogo"/>
					<field-map attributeName="prestazione" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="prestazione" nomeKeyERR="erroriRiepilogo"/>
					<field-map attributeName="frazionamento"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>						
					<field-map attributeName="flAttSs"	 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO">
		    <!-- Totali 14321 car (150 + 12347 + 1824) --> 
		    <!-- Campi di output 12347 car -->
		    <!-- input 11605 car -->
		    <field-map attributeName="numUT" 					length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteUT" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaUTRiepilogo" length="11600">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="100" blockLength="116">
            		<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="codiceUT"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descCodiceUT"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="decorrenzaUT"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Data"/>
					<field-map attributeName="durataAnniUT"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerAAMM"		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataMesiUT"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="premioNetto" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioRata" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazione" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="frazionamento"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flAttSs"	 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
            <!-- output 168 car -->
		    <field-map attributeName="premioVita" 				length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="sconti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="complementariInfort" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="invaliditaPerm" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="altreComplementari" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="totalePremioAnnuo" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="interessiFrazionamento" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioFrazionato" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="diritti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="speseMediche" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="imposteFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioAllaFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<!-- da aggiungere  car 574 -->
			<field-map attributeName="numFondi" 				length="3" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteFondi" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaFondi" length="570">
            	<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="57">
            		<field-map attributeName="codice"        	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descrizione"		length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="percentuale"		length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
            	</nested-mapping>
            </field-map>
		</output-mapping>
	</rule>
</rules>
    