<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>LISTA-UT-PROPOSTA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0173</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO">
			<!-- Totali 151 car (150 + 1) -->
			<!-- 1 car -->
			<field-map attributeName="numCategoria"    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO">
		    <!-- Totali 18393 car (150 + 22426 + 1824) --> 
		    <!-- Campi di output 22426 car -->
			<field-map attributeName="numCategoria"     length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaProposta"  length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"       length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta"   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numUT" 			length="3" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteUT" 	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaUTRiepilogo" length="22400">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="200" blockLength="112">
       				<field-map attributeName="categoriaUT"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>     		
            		<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="codiceUT"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descCodiceUT"		length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="decorrenzaUT"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Data"/>
					<field-map attributeName="durataAnniUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataMesiUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="premioNetto" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazione" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioRata" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="statusUT"	        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
  		</output-mapping>
	</rule>
</rules>
    