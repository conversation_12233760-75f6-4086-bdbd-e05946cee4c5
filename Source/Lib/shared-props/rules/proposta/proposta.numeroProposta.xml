<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>NUMERO-PROPOSTA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE457</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.prodotto.dto.NumeroPropostaRequestDTO">
			<!-- Totali 151 car (150 + 1) -->
			<!-- 1 car -->
            <field-map attributeName="flNumeroNuovo"	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.prodotto.dto.NumeroPropostaResponseDTO">
		    <!-- Totali 1982 car (150 + 8 + 1824) -->
		    <!-- Campi di output 8 car -->
		   <field-map attributeName="flNumeroNuovo"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   <field-map attributeName="numeroProposta"	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>