<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ATTIVAZIONE-PROPOSTA-DIREZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0168</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaAttivazioneRequestDTO">
			<!-- Totali 13596 car (150 + 13446) -->
			<!-- 13446 car -->
			<field-map attributeName="categoria"      	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="numCategoria"       length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="agenziaPolizza"     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>            
            <field-map attributeName="numeroColl" 	      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numPreventivo" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroProposta" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataEmissione"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data" nomeAttributoERR="dataEmissione" nomeKeyERR="erroriRiepilogo"/>
            <field-map attributeName="flQuietanza"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="stato"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="indiceUt"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="numUTPresenti"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="listaUTAttivazione" length="13200">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="1100" blockLength="12">
            		<field-map attributeName="numeroProposta"   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="posizione"		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="flAttSs"	        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="premioVita" 				length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioVita" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="sconti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sconti" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="complementariInfort" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariInfort" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="invaliditaPerm" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="invaliditaPerm" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="altreComplementari" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="altreComplementari" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="totalePremioAnnuo" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="totalePremioAnnuo" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="interessiFrazionamento" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="interessiFrazionamento" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="premioFrazionato" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioFrazionato" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="diritti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="diritti" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="speseMediche" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="speseMediche" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="imposteFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="imposteFirma" nomeKeyERR="erroriRiepilogo"/>
			<field-map attributeName="premioAllaFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioAllaFirma" nomeKeyERR="erroriRiepilogo"/>
		    <field-map attributeName="flStampaPolizza" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaAltriDoc" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ottico" 			        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaQuietanza" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="incasso" 			        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flVersamentoAgg" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flTipoVersamentoAgg" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="livelloZero" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDirezione" 				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flChiamataServizio" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaAttivazioneResponseDTO">
		    <!-- Totali 15429 car (150 + 13455 + 1824) --> 
		    <!-- 13455 car -->
		    <!-- Input 13446 car -->
			<field-map attributeName="categoria"      	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="numCategoria"    	  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="agenziaPolizza"     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>            
            <field-map attributeName="numeroColl" 	      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numPreventivo" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroProposta" 	  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataEmissione"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="data"/>
            <field-map attributeName="flQuietanza"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="stato"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="indiceUt"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="numUTPresenti"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="listaUTAttivazione" length="13200">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="1100" blockLength="12">
            		<field-map attributeName="numeroProposta"   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="posizione"		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="flAttSs"	        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="premioVita" 				length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="sconti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="complementariInfort" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="invaliditaPerm" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="altreComplementari" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="totalePremioAnnuo" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="interessiFrazionamento" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioFrazionato" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="diritti" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="speseMediche" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="imposteFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioAllaFirma" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="flStampaPolizza" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaAltriDoc" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ottico" 			        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaQuietanza" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="incasso" 			        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flVersamentoAgg" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flTipoVersamentoAgg" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="livelloZero" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDirezione" 				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flChiamataServizio" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- 9 car -->
			<field-map attributeName="numeroPolizzaOut" 	  	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" 	padding="0"/>
			
			<field-map attributeName="flAvvisoDirezione" 	  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" 	padding=""/>
			<field-map attributeName="flTipoMessaggio" 	  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" 	padding=""/>
		</output-mapping>
     </rule>
</rules>
    