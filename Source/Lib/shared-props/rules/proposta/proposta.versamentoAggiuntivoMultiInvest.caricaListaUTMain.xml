<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VERSAMENTO-AGGIUNTIVO-MULTIINVEST-LISTA-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE559</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO">
			<!-- Totali 175 car (150 + 25) -->
			<!-- 25 car --> 
            <field-map attributeName="fromRiepilogo" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
            <field-map attributeName="flVersamentoAgg" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            <field-map attributeName="flTipoVersamentoAgg" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
            <field-map attributeName="flTipoWorkflow" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"			length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flSoloLettura"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flagSenzaCalcoli"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO">
		    <!-- Totali 15573 car (150 + (13072 + 294 + 11 + 210 + 5 + 2 + 5= 13599) + 1824) --> 
		    
		    <!-- 12633 + 427 + 10 + 1 + 210-->
		    <field-map attributeName="fromRiepilogo" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
		    <field-map attributeName="flVersamentoAgg" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="flTipoVersamentoAgg" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="flTipoWorkflow" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"			length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flSoloLettura" 	    	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flagSenzaCalcoli"    		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			
		    <!-- Campi di output 12574 + 24 + 35 + 1  + 11 + 210 + 5 + 1 + 5 car -->
		    
		    <!-- 2 -->
		    <field-map attributeName="flMultigaranzia" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flMultiquestionario" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    
		     <!-- 63 + 35 + 2 -->
		    <field-map attributeName="flOpzioneFacoltativa"  	length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flVAProgrammatico"  		length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flClassePremio" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="opzioneGestionale"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="codLineaInvestimento"  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="codiceConvenzione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codiceDeroga"        		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="dataRichiestaCliente" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    <field-map attributeName="codClassePremio"        	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="premioRataCompl"   		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="dataPervenimentoCartaceo" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    <field-map attributeName="dataIncasso" 				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Data" />
		    <field-map attributeName="letteraSollecito" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="letteraSollecitoProtetto" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flMostraMessaggio"        length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="flProfiloLibero"          length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <!-- 48 -->
		  	<field-map attributeName="codFacoltativo"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="codImportoCedola"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codDurataCedola"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="flEsclusioneCedola"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="periodicitaVA"   			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="importoVA"   				length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="flSelVAProgrammatico"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    
		    <!-- 12 -->
		    <field-map attributeName="flMultiramo"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numUTMultiramo" 					length="4" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="totale"     						length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			
			<!-- 83 -->
			<field-map attributeName="flFondiI"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTI"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTI"   		 			length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoI"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoI"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeI"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoI"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiII"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTII"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTII"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoII"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoII"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeII"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoII"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiIII"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIII"  	      			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIII"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIII"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIII"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIII"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIII"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiIV"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIV"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIV"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIV"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIV"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIV"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIV"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiV"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTV"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTV"   		 			length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoV"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoV"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeV"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoV"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    
		    <!-- 12 -->
		    <field-map attributeName="flMultiramoProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numUTMultiramoProgr" 					length="4" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="totaleProgr"     						length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			
			<!-- 83 -->
			<field-map attributeName="flFondiIProgr"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIProgr"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIProgr"   		 			length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiIIProgr"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIIProgr"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIIProgr"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIIProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIIProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIIProgr"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIIProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiIIIProgr"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIIIProgr"  	      			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIIIProgr"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIIIProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIIIProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIIIProgr"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIIIProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiIVProgr"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTIVProgr"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTIVProgr"   		 		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoIVProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoIVProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeIVProgr"    				length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoIVProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- 83 -->
			<field-map attributeName="flFondiVProgr"  	      				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTVProgr"  	      				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCodiceUTVProgr"   		 			length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="minimoVProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="massimoVProgr"    						length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="percentualeVProgr"    					length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			<field-map attributeName="flProtettoVProgr"  					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="statoEvento"  						length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    
		    <!-- 12005 -->
			<field-map attributeName="numUT" 					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerLimiteUT" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaUTRiepilogo" length="12000">
            	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="100" blockLength="120">
            		<field-map attributeName="erroreUT" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            		<field-map attributeName="codiceUT"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="descCodiceUT"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="decorrenzaUT"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="data"/>
					<field-map attributeName="durataAnniUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerAAMM"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataMesiUT"		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="premioNetto" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioRata" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioIngr" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazione" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="flCapRen"	 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flConsolidata"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flAttSs"	 		length="1" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statusUT"	 		length="1" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="codPagatorePrimaRata"  	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		
			<!-- 101 -->
			<field-map attributeName="origineFondi"    					length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="origineFondiAltro"    			length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- 222 -->
            <field-map attributeName="modPagPrimaRata"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codPagatorePrimaRataVersamento"  	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intestatarioPrimaRata"			length="85"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceIbanPrimaRata"    			length="45"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codTipoAssegnoPrimaRata"  		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroAssegnoPrimaRata"  			length="13"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codBancaPrimaRata"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFilialePrimaRata"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cgProgrMod"						length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="cgModello"    					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cgRegola"    						length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="flOpzioneObbligatoria"  	length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codOpzioneObbligatoria"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="cgRegolaDefaultCod"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="cgRegolaDefaultDesc"  length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flSelVAMultiplo"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numVAMultiplo"   		length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		    	natura="Numerico"  segnato="false"  numInteri="3" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="flEsistenzaVap"  		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    