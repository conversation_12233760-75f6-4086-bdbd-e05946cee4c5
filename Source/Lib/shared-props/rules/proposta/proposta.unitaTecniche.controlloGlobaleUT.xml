<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLO-GLOBALE-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0160</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.UnitaTecnicheRequestDTO">
		<!-- Totali 151 car (150 + 1) -->
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="tabInErrore"	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.UnitaTecnicheResponseDTO">
		<!-- Totali 1975 car (150 + 1 + 1824) -->		
		<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="tabInErrore"	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>