<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DATI-IDENTIFICATIVI-COLLETTIVA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE577</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.datiidentificativi.dto.PropostaDatiIdentificativiRequestDTO">
			<!-- Totali 167 car (150 + 17) -->
			<!-- 11 + 6 car -->
			<field-map attributeName="flagVariazUT"  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceUTBase"  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flFrazionamento"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="origineFraz" 	    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flPrimaRata" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="originePrimaRata" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flDurataAnni" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flDurataMesi" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.datiidentificativi.dto.PropostaDatiIdentificativiResponseDTO">
		    <!-- Totali 2322 car (150 + 348 + 1824) -->
		    <!-- Campi output 17 + 331 car  = 348 -->
			<!-- input 11 + 6 = 17 car -->
			<field-map attributeName="flagVariazUT"  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceUTBase"  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<field-map attributeName="flFrazionamento"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="origineFraz" 	    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flPrimaRata" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="originePrimaRata" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flDurataAnni" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flDurataMesi" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- Output 111 + 130 + 79 + 10 + 1 = 331 car-->
			<!-- dati identificativi 111-->
			<field-map attributeName="tipoColl"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="titoloColl"  			length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoFondo"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataProposta"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Data" />
			<field-map attributeName="dataDecorrenza"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Data" />
			<field-map attributeName="dataEmissione"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Data" />
			<field-map attributeName="durataAnni"  			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="fillerAnni"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="durataMesi"  			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataScadenza"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Data" />
			<field-map attributeName="numMinTeste"  		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- dati generali 130-->
			<field-map attributeName="codPeriodicitaPUR"  	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="primaRata"   			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="convenzione" 	  	  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceDeroga" 	  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoCompPremio"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="quotaContr"         	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="quotaContrPerc"       length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			<field-map attributeName="quotaAss"         	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="quotaAssPerc"       length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			<field-map attributeName="lettereFiscali"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
			<field-map attributeName="tipoAdesione"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
			<field-map attributeName="etaMas"  				length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>	
			<field-map attributeName="etaFem"  				length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="calcoloEtaAss"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
			<field-map attributeName="flContrTeste"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
			<field-map attributeName="rinnovo"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
			<field-map attributeName="annoRinnovo"  		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>	
			<field-map attributeName="dataRinnovo"  		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="rivProRata"  			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
			<field-map attributeName="regPremio"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoEmissione"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoGestione"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codPool"  			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="coass"  				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="riass"  				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codCapogruppo"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoRiscatto"  		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			
			<!-- costi rendimenti 79-->
			<field-map attributeName="cifraFissa"         	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>	
			<field-map attributeName="costoPolizza"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="costoQuietanza"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="speseMediche"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>	
			<field-map attributeName="retrocessione"        length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>	
			<field-map attributeName="renMinTratt"          length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="2" numDecimali="3" separatoreMigliaia="false"/>	
			<field-map attributeName="renMinGarant"         length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="2" numDecimali="3" separatoreMigliaia="false"/>	
			<field-map attributeName="codiceSconto"  		length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="dataTfr"  			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
				natura="Data" />
			<field-map attributeName="flDesignazioneBenef"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>	
		</output-mapping>
	</rule>
</rules>