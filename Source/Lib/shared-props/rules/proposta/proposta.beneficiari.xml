<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-RUOLI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0016</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.ruolo.dto.RuoloRequestDTO">
			<!-- Totali 160 car (150 + 10) -->
			<!-- 10 car -->
			<field-map attributeName="codiceProdotto" length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.ruolo.dto.RuoloResponseDTO">
		    <!-- Totali 10268 car (150 + 8294 + 1824) --> 
		    <!--input + output 8294 car -->
			<field-map attributeName="codiceProdotto"     length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""     offset="" padding="0"/>
			<field-map attributeName="numElementiTrovati" length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
            <field-map attributeName="elencoRuoli"  length="610" >
            	<nested-mapping className="it.sistinf.albedoweb.ruolo.dto.RuoloDTO" iterations="10" blockLength="61">
            		<field-map attributeName="ruolo"            length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
            		<field-map attributeName="descrizioneRuolo" length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="flPresenzaBeneficiari"  length="1" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""     offset="" padding="0"/>
            <field-map attributeName="beneficiari" length="7670" >
            	<nested-mapping className="it.sistinf.albedoweb.beneficiari.dto.BeneficiarioDTO" iterations="10" blockLength="767">
					<field-map attributeName="ruolo"                   length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="descrizioneRuolo"        length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="descrizioneBeneficiario" length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="estensioneBeneficiario"  length="350" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>		
		            <field-map attributeName="datiAnagrafica" length="326" > 
		            	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="326">
		            		<field-map attributeName="codiceCliente"        length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
		            		<!-- field-map attributeName="nominativo"           length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/-->
		            		<field-map attributeName="cognome"   			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
							<field-map attributeName="nome"      			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            		<field-map attributeName="titolo"               length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
		            		<field-map attributeName="flConsenso1"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="flConsenso2"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="flConsenso3"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="flConsenso4"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="flConsenso5"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="flConsenso6"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
		            		<field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="luogoDiResidenza"     length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>					
							<field-map attributeName="capResidenza"         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
							<field-map attributeName="capNascita"           length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding="0"/>
							<field-map attributeName="provinciaDiResidenza" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="stato"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDiNascita" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="luogoDiNascita"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="provinciaDiNascita"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
							<field-map attributeName="sesso"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="codiceProfessione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="descProfessione"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="tipologia"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="codiceStatoCivile"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
							<field-map attributeName="descStatoCivile"      length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="numFigli"             length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0"/>
		            	</nested-mapping>
		            </field-map>
            	</nested-mapping>
            </field-map>
		</output-mapping>
	</rule>
</rules>
    