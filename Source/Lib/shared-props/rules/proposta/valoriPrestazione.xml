<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VALORI-PRESTAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSERD072</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DominioImportoRequestDTO">
			<!-- Totali 170 car (150 + 20) -->
			<!-- 20 car -->
			<field-map attributeName="categoria" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceTariffa" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DominioImportoResponseDTO">
		    <!-- Totali 13199 car (150 + 11225 + 1824) --> 
		    	
		    	<field-map attributeName="categoria" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				<field-map attributeName="codiceTariffa" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    	
	            <!-- 11205 car -->		    
				<field-map attributeName="numElementi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
				<field-map attributeName="flAltri"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
				<field-map attributeName="elementiDominio" length="11200">
					<nested-mapping className="it.sistinf.albedoweb.domini.dto.ElementoDominioDTO" iterations="400" blockLength="28">
						<field-map attributeName="codice"  	   length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="descrizione" length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					</nested-mapping>
				</field-map>
		</output-mapping>
	</rule>
</rules>
    