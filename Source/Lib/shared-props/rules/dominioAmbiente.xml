<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DOMINIO-AMBIENTE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>DWLSLAMB</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.domini.dto.ElencoDominiRequestDTO">
			<!-- Totali 153 car (150 + 50) -->
			<!-- 50 car -->
			<field-map attributeName="codice1" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.domini.dto.ElencoDominiResponseDTO">
		    <!-- Totali 25979 car (150 + 1245 + 1824) --> 
	            <!-- 1245 car -->		    
				<field-map attributeName="numElementi" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
				<field-map attributeName="flAltri"     length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
				<field-map attributeName="elementiDominio" length="1240">
					<nested-mapping className="it.sistinf.albedoweb.domini.dto.ElementoDominioDTO" iterations="20" blockLength="62">
						<field-map attributeName="codice"  	   length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="descrizione" length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>
		</output-mapping>
	</rule>
</rules>
    