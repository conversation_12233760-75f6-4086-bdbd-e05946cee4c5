<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>EXIT-SWITCH</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE616</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.common.albedo.login.dto.LoginRequestDTO">
		<!-- Totali 152 car (150 + 2) -->		
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="esito" length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.login.dto.LoginResponseDTO">
		<!-- Totali 1976 car (150 + 2 + 1824) -->		
		<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="esito" length="2" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</output-mapping>
	</rule>
</rules>