<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
	<rule>
	  	<id>SALVA-ANAGRAFICA-RAPPORTO</id> 
	  
	  	<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
		<program>VWLSE510</program>
		<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
	  	<logApp>true</logApp>
		<logAppServDesc>ANAGRAFICA</logAppServDesc>
		<areaFunzionale>VITA POLIZZA </areaFunzionale>
		
	    <multipleTransaction>true</multipleTransaction> 
	    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
	    <pageRequestField/> 
	    <limitPage>99</limitPage> 
	    <moreDataField/> 
	    <moreDataEndValue>1</moreDataEndValue> 
	    <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
	    <pastedFields> 
	    	<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
	    </pastedFields>
     
  <!-- Definizione commarea: VWLSE510 Lunghezza: 150 + 937 + 1824 = 2911 -->  
  
  		<input-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaRapportoRequestDTO" nomeKeyERR="erroriSalvaRiallocazione"> 
  		 <!-- input : 938 car -->
  		 
  		 	<field-map attributeName="anagraficaDettaglio" length="938">
  		 		<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="928">
  		 		  	<field-map attributeName="codiceCustomer"			length="009" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cognome"          	    length="055" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 	  		 	
			    	<field-map attributeName="nome"                 	length="030" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="codiceFiscale"        	length="016" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="dataDiNascita"        	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="provinciaDiNascita"   	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="luogoDiNascita"       	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="sesso"                	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProfessione"    	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 		
			    	<field-map attributeName="tipoPersona"				length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza1"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza2"    	    length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza3"	        length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codProvinciaDiResidenza"	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="luogoDiResidenza"     	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="indirizzoDiResidenza" 	length="030" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capResidenza"         	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codStato"             	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="presso"               	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaRecapito"    	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittaRecapitoDesc"    	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="indirizzoRecapito"    	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capRecapito"          	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="statoRecapito"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="telefono"             	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="emailRecapito"			length="060" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoDocumento"        	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="numeroDocumento"      	length="012" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="luogoRilascio"        	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="dataRilascio"         	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="enteRilascio"         	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cab" 	                	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="gruppo"               	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="sottogruppo"          	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="atecoAR"                  length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="politicamenteEsposta"		length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="greenCard"            	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tasseEstero"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tasseEsteroCodStato"		length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="usStatus"             	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="taxIdNumber"          	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="entePubblico"				length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="socQuotata"           	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="titolareEff"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoSocCod"           	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="attPrevalenteCod"     	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaSav"         	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittaSavDesc"         	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capSav"               	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="origineFondi"         	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="comportamentoCliente" 	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="statoRilascio"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaRilascio"    	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="dataScadenza"         	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
									valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="visuraCamerale"       	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="visuraCameraleAltro"  	length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flCommerciale"        	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso1"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso2"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso3"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso4"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso5"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso6"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="intestatarioConto"    	length="040" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="iban"                 	length="034" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>  		 		  		 	
			    	<field-map attributeName="tipoPagamento"            length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
  		 	</field-map>
		</input-mapping>
		
		
		<output-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaRapportoResponseDTO"> 
	    <!-- output : 947 (938 + 9) car --> 
  		 	<field-map attributeName="anagraficaDettaglio" length="947">
	  		 	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="937">
  		 			<field-map attributeName="codiceCustomer"       	length="009" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cognome"              	length="055" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 	  		 	
			    	<field-map attributeName="nome"                 	length="030" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="codiceFiscale"        	length="016" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="dataDiNascita"        	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="provinciaDiNascita"		length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="luogoDiNascita"       	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 	
			    	<field-map attributeName="sesso"                	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProfessione"    	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	  		 		
			    	<field-map attributeName="tipoPersona"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza1"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza2"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittadinanza3"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codProvinciaDiResidenza" 	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="luogoDiResidenza"     	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="indirizzoDiResidenza"		length="030" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capResidenza"         	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codStato"             	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="presso"               	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaRecapito"    	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittaRecapitoDesc"    	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="indirizzoRecapito"    	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capRecapito"          	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="statoRecapito"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="telefono"             	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="emailRecapito"		   	length="060" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoDocumento"        	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="numeroDocumento"      	length="012" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="luogoRilascio"        	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="dataRilascio"         	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
							valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="enteRilascio"         	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cab" 	                	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="gruppo"               	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="sottogruppo"          	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="atecoAR"                  length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="politicamenteEsposta"		length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="greenCard"            	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tasseEstero"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tasseEsteroCodStato"		length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="usStatus"             	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="taxIdNumber"          	length="020" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="entePubblico"         	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="socQuotata"           	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="titolareEff"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoSocCod"           	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="attPrevalenteCod"     	length="010" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaSav"         	length="002" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="cittaSavDesc"         	length="035" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="capSav"               	length="005" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="origineFondi"         	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="comportamentoCliente" 	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="statoRilascio"        	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="provinciaRilascio"    	length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="dataScadenza"         	length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
									valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSalvaRiallocazione"/>
			    	<field-map attributeName="visuraCamerale"       	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="visuraCameraleAltro"  	length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flCommerciale"        	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso1"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso2"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso3"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso4"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso5"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flConsenso6"          	length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="intestatarioConto"    	length="040" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="iban"                 	length="034" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoPagamento"            length="003" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceCliente"        	length="009" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
				</nested-mapping>
			</field-map>
		</output-mapping> 
	</rule> 
</rules> 