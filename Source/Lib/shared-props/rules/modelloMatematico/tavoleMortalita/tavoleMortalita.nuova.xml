<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-NUOVA-TAVMORTALITA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>MWLSE006</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA TAVOLE MORTALITA</logAppServDesc>
	<areaFunzionale>PRODUCT BUILDER-TAVOLE MORTALITA</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE028 Lunghezza: 150 + 2804 + 1824 = 4778  -->  
    <input-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaRequestDTO" nomeKeyERR="erroriSalvaTavoleMortalita"> 
   			<field-map attributeName="codTavoleMortalita"          length="5"  precision="0"     numericScale="0"    align="left" mandatory="0" separator="" occurs="1"    default=""   offset="" padding=" "/>
   			<field-map attributeName="descrizioneTavoleMortalita"  length="40"   precision="0"    numericScale="0"    align="left" mandatory="1" separator="" occurs="1" 	default="" 	 offset="" padding="" /> 
      		<field-map attributeName="dettaglioTavoleMortalita" length="1703" > 
	    		<nested-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaDTO" iterations="131" blockLength="13">      
		          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="valore"       length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" "
		          	valida="true" natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="dettaglioTavoleMortalitaErr[].valoreErr" nomeKeyERR="erroriSalvaTavoleMortalita" />
		   		</nested-mapping>
     		</field-map>  
  	</input-mapping> 
    <output-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.DettaglioTavoleMortalitaDTO"> 
      <!-- output :1748 + 1056  = 2804  car -->      
      	 <field-map attributeName="codTavoleMortalita" 			length="5"    precision="0"    numericScale="0"   align="left" mandatory="0" separator="" occurs="1"    default=""     offset="" padding=" "/>
   		 <field-map attributeName="descrizioneTavoleMortalita"  length="40"   precision="0"    numericScale="0"    align="left" mandatory="1" separator="" occurs="1" 	default="" 		offset="" padding="" /> 
      	 <field-map attributeName="dettaglioTavoleMortalita" length="1703" > 
	    	<nested-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaValoriDTO" iterations="131" blockLength="13"> 	      
	          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
	          <field-map attributeName="valore"       length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" "/>
	   		</nested-mapping>
     	  </field-map> 
	      <field-map attributeName="codTavoleMortalitaErr"          length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding=""/>
	      <field-map attributeName="descrizioneTavoleMortalitaErr"  length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding=""/> 
	      <field-map attributeName="dettaglioTavoleMortalitaErr" length="1048" > 
			    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaValoriDTO" iterations="131" blockLength="8"> 		   
			          <field-map attributeName="annoErr"    length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="" />
			          <field-map attributeName="valoreErr"  length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="" />
			   </nested-mapping>
	     </field-map> 
     
   </output-mapping> 
  </rule>
</rules>
