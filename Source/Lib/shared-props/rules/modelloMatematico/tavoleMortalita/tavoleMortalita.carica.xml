<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CARICA-DETTAGLIO-TAVMORTALITA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSE005</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL026 Lunghezza: 150 + 1748 + 1824 = 3722  -->  
  <input-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaRequestDTO" nomeKeyERR="erroriSalvaTavoleMortalita">
   <!-- input : 5 car --> 
   <field-map attributeName="codTavoleMortalita" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.DettaglioTavoleMortalitaDTO"> 
      <!-- output :5 + 1743  car -->      
      <field-map attributeName="codTavoleMortalita"          length="5"    precision="0" 	numericScale="0" 	align="left" mandatory="0" separator="" occurs="1"  default=""      offset=""  padding=" "/>
      <field-map attributeName="descrizioneTavoleMortalita"  length="40"   precision="0"    numericScale="0"    align="left" mandatory="1" separator="" occurs="0" 	default="" 		offset="" padding="" /> 
      <field-map attributeName="dettaglioTavoleMortalita" length="1703" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.tavolemortalita.dto.TavoleMortalitaValoriDTO" iterations="131" blockLength="13">  
		          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="valore"       length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " 
		          natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3" separatoreMigliaia="false" />
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 