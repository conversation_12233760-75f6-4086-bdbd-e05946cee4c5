<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CARICA-METALINGUAGGIO</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSE013</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL026 Lunghezza: 150 + 4510 + 1824 = 6484  -->  
  <input-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.MetalinguaggioRequestDTO" nomeKeyERR="erroriMetalinguaggio">
   <!-- input : 10 car --> 
   <field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.MetalinguaggioResponseDTO"> 
      <!-- output :5 + 2575  car -->      
      <field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="impostazioniGlobali" length="151" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.ImpostazioniGlobaliDTO" iterations="0" blockLength="151"> 
		          <field-map attributeName="idMacro"            length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="idAutoAcquizMacro"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="nomeVarRisultato"   length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="ctrlRisultato"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="carCtrlVarEst"   	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="idLabel"   			length="1" 	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="carICommento"   	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="carFCommento"   	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="debug"   			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmScelta"   		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmAlternativa"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmFineScelta"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmSalto"   		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmExecRoutine"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmCicloCond"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmFineCicloCond"   length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmInizioCiclo"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmFineCiclo"   	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="cdmStop"   			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="operatoreAnd"  		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="operatoreOr"   		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="esposAutoTariffe"   length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="codTariffaGeneric"  length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map> 
     <field-map attributeName="simboliCommutazione" length="120" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.SimboliCommutazioneDTO" iterations="0" blockLength="120"> 
		          <field-map attributeName="outFnLx"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnNx"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnDx"   	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMx"   	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnCx"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnCsgn"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMsgn"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnRx"   	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnRsGn"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnSx"   	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnAvp"   length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnAvp2"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map> 
     <field-map attributeName="funzioniSpeciali" length="2880" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.FunzioniSpecialiDTO" iterations="40" blockLength="72"> 
		          <field-map attributeName="indice"  length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="csp"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="dsp"  length="60"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map> 
     <field-map attributeName="macroGeneriche" length="240" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.MacroGenericheDTO" iterations="0" blockLength="240"> 
		          <field-map attributeName="outFnArrotondamento"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnTroncamento"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnAlfanumerico"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnIntero"   			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnDecimale"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnNumerico"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreMin"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreMed"   		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreMax"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreAtt"   		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreAttAnt"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnValoreAttPost" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMontante"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMontanteAnt"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMontantePost"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnCtrlDivide"  		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnCall"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnRc"  				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnLog"  				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnSum"  				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnProd"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnVett"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnInvers"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnString"  			length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map>
     <field-map attributeName="altreMacro" length="80" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.metalinguaggio.dto.AltreMacroDTO" iterations="0" blockLength="80"> 
		          <field-map attributeName="outFnLetturaFondi"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnLetturaIstat"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnLetturaPtf"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnLxsel"   		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnMacroPers"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnCodiceDivisa"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnLetturaProp"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="outFnDisponibile"   length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map>       
   </output-mapping> 
 </rule> 
</rules> 