<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-VETTORIESTERNI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>MWLSE004</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA VETTORI ESTERNI</logAppServDesc>
	<areaFunzionale>PRODUCT BUILDER-VETTORI ESTERNI</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE029 Lunghezza: 150 + 10 + 1824 = 1984  -->  
      <input-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniRequestDTO" nomeKeyERR="erroriSalvaVettoriEsterni">
        	<!-- input :  10  car -->
      	<field-map attributeName="codVettoriEsterni"          length="10"  precision="0"     numericScale="0"    align="left" mandatory="1" separator="" occurs="1"    default=""   offset="" padding=" "/>
      
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniResponseDTO">
      
    	  <field-map attributeName="codVettoriEsterni"          length="10"  precision="0"     numericScale="0"    align="left" mandatory="0" separator="" occurs="1"    default=""   offset="" padding=" "/>
      
     </output-mapping>
  </rule>
</rules>
