<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>CARICA-DETTAGLIO-VETTORIESTERNI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSE001</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL026 Lunghezza: 150 + 4510 + 1824 = 6484  -->  
  <input-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniRequestDTO" nomeKeyERR="erroriSalvaVettoriEsterni">
   <!-- input : 10 car --> 
   <field-map attributeName="codVettoriEsterni" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.DettaglioVettoriEsterniDTO"> 
      <!-- output :5 + 2575  car -->      
      <field-map attributeName="codVettoriEsterni"          length="10"    precision="0" 	numericScale="0" 	align="left" mandatory="0" separator="" occurs="1"  default=""      offset=""  padding=" "/>
      <field-map attributeName="descrizioneVettoriEsterni"  length="40"   precision="0"    numericScale="0"    align="left" mandatory="1" separator="" occurs="0" 	default="" 		offset="" padding="" /> 
      <field-map attributeName="dettaglioVettoriEsterni" length="2530" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniValoriDTO" iterations="115" blockLength="22"> 
		          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="valore"       length="19" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 