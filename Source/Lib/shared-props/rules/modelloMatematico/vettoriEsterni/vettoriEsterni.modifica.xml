<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-MODIFICA-VETTORIESTERNI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>MWLSE003</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA VETTORI ESTERNI</logAppServDesc>
	<areaFunzionale>PRODUCT BUILDER-VETTORI ESTERNI</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE028 Lunghezza: 150 + 2468 + 1824 = 02061  -->  
    <input-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniRequestDTO" nomeKeyERR="erroriSalvaVettoriEsterni">
   			
   			<field-map attributeName="codVettoriEsterni"          length="10"  precision="0"     numericScale="0"    align="left"  mandatory="1" separator="" occurs="1"    default=""   offset="" padding=""/>
   			<field-map attributeName="descrizioneVettoriEsterni"  length="40"   precision="0"    numericScale="0"    align="left" mandatory="0" separator="" occurs="1" 	default="" 	 offset="" padding="" /> 
      		<field-map attributeName="dettaglioVettoriEsterni" length="2530" > 
		    	<nested-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniDTO" iterations="115" blockLength="22">
		          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="valore"       length="19" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		          	valida="true" natura="Numerico" segnato="false" numInteri="11" numDecimali="07" separatoreMigliaia="false" nomeAttributoERR="dettaglioVettoriEsterniErr[].valoreErr" nomeKeyERR="erroriSalvaVettoriEsterni"/>
		   		</nested-mapping>
     		</field-map>  
  	</input-mapping> 
    <output-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.DettaglioVettoriEsterniDTO"> 
      <!-- output :2580 + 928  car -->      
      	 <field-map attributeName="codVettoriEsterni" 			length="10"    	precision="0"    numericScale="0"   align="left" mandatory="0" separator="" occurs="1"    default=""     offset="" padding=""/>
   		 <field-map attributeName="descrizioneVettoriEsterni"  	length="40"   	precision="0"    numericScale="0"    align="left" mandatory="0" separator="" occurs="1" 	default="" 		offset="" padding="" /> 
      	 <field-map attributeName="dettaglioVettoriEsterni" length="2530" > 
	    	<nested-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniValoriDTO" iterations="115" blockLength="22"> 
	          <field-map attributeName="anno"         length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
	          <field-map attributeName="valore"       length="19" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " 
	          	natura="Numerico" segnato="false" numInteri="11" numDecimali="07" separatoreMigliaia="false"/>
	   		</nested-mapping>
     	  </field-map> 
	      <field-map attributeName="codVettoriEsterniErr"          length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding=""/>
	      <field-map attributeName="descrizioneVettoriEsterniErr"  length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding=""/> 
	      <field-map attributeName="dettaglioVettoriEsterniErr" length="920" > 
			    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.vettoriesterni.dto.VettoriEsterniValoriDTO" iterations="115" blockLength="8"> 
			          <field-map attributeName="annoErr"    length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="" />
			          <field-map attributeName="valoreErr"  length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="" />
			   </nested-mapping>
	     </field-map> 
     
   </output-mapping> 
  </rule>
</rules>
