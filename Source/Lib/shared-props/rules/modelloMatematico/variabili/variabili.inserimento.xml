<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>INSERIMENTO-VARIABILI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSE010</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  <logApp>true</logApp>
  <logAppServDesc>SALVA VARIABILI</logAppServDesc>
  <areaFunzionale>PRODUCT BUILDER-VARIABILI</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: MWCSE010 Lunghezza: 150 + 100 + 1824 = 2074  -->  
	  <input-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliRequestDTO" nomeKeyERR="erroriVariabili"> 
	   <!-- input : 80 car --> 
	   	  <field-map attributeName="codVariabili"    	 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabili"  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabiliRead" 	length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="descrizioneVariabili"  			length="50" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	   	  <field-map attributeName="descrizioneVariabiliRead"		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		  <field-map attributeName="tipoVariabili"			  		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="tipoVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabili"  				length="4"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabiliRead"			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	  </input-mapping> 
	  <output-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliResponseDTO"> 
	      <!-- output :80 + 20 = 100 car -->      
	      <field-map attributeName="codVariabili"    	 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabili"  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabiliRead" 	length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="descrizioneVariabili"  			length="50" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	   	  <field-map attributeName="descrizioneVariabiliRead"		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		  <field-map attributeName="tipoVariabili"			  		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="tipoVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabili"  				length="4"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabiliRead"			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />	
	      <field-map attributeName="codVariabiliCdErr"              length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	      <field-map attributeName="codOrdinamentoVariabiliCdErr"   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	      <field-map attributeName="descrizioneVariabiliCdErr"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	      <field-map attributeName="tipoVariabiliCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	      <field-map attributeName="numeroVariabiliCdErr"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	     
	   </output-mapping> 
 </rule> 
</rules> 