<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-VARIABILI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSLVAR</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: MWCSLVAR Lunghezza: 150 + 15015 + 1824 = 16989  -->  
  <input-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliRequestDTO" nomeKeyERR="erroriElencoVariabili"> 
   <!-- input : 10 car --> 
   <field-map attributeName="codVariabiliRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliResponseDTO"> 
      <!-- output :10 + 15005 = 15015 car -->      
      <field-map attributeName="codVariabiliRicerca"    	 length="10"    precision="0" 	numericScale="0" 	align="left" mandatory="0" separator="" occurs="1"  default=""      offset=""  padding=""/> 
      <field-map attributeName="numElementiTrovati" 		 length="4" 	precision="0" 	numericScale="0" 	align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         			 length="1" 	precision="0" 	numericScale="0" 	align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaVariabili" length="15000" > 
		    <nested-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliValoriDTO" iterations="300" blockLength="50"> 
		          <field-map attributeName="codVariabili"          length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="descrizioneVariabili"  length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 