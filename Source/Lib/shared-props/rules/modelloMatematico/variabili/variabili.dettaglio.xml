<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>DETTAGLIO-VARIABILI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>MWLSE009</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: MWCSE009 Lunghezza: 150 + 80 + 1824 = 2054  -->  
	  <input-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliRequestDTO" nomeKeyERR="erroriVariabili">
	   <!-- input : 10 car --> 
	   	  <field-map attributeName="codVariabili" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	  </input-mapping> 
	  <output-mapping className="it.sistinf.albedoweb.modellomatematico.variabili.dto.VariabiliResponseDTO"> 
	      <!-- output :10 + 70 = 80 car -->      
	      <field-map attributeName="codVariabili"    	 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabili"  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codOrdinamentoVariabiliRead" 	length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="descrizioneVariabili"  			length="50" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	   	  <field-map attributeName="descrizioneVariabiliRead"		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		  <field-map attributeName="tipoVariabili"			  		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="tipoVariabiliRead" 				length="1" 	precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabili"  				length="4"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   	  <field-map attributeName="numeroVariabiliRead"			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />	
	     
	   </output-mapping> 
 </rule> 
</rules> 