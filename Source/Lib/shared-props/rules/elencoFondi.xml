<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-FONDI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0044</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.fondi.dto.FondiRequestDTO">
			<!-- Totali 192 car (150 + 42) -->
			<!-- 42 car -->
		    <field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenza" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data"/>
			<field-map attributeName="dataCreazione" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fondi.dto.FondiResponseDTO">
		    <!-- Tot 11618 + 42 = 11660 car -->
		    <!-- Campi di input 42 car -->
		    <field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenza" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataCreazione" length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<!--Dati output	 4 + 11600 + 14 = 11618-->
			<field-map attributeName="numFondiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="flAltri"         length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
            <field-map attributeName="elencoFondi"     length="11600" >
            	<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondiDettaglioDTO" iterations="100" blockLength="116">
            		<field-map attributeName="codice"              length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
            		<field-map attributeName="dataValorizzazione"   length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
            			natura="Data"/>
            		<field-map attributeName="valoreQuota"          length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
            			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            		<field-map attributeName="numeroQuote"          length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=" "/>
            		<field-map attributeName="controvalore"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "
            			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            		<field-map attributeName="percentuale"          length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            			natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
            		<field-map attributeName="percentualeOriginaria"          length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            			natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
            		<field-map attributeName="descrizione"          length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="importoTotale"   length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>	    
	</output-mapping>
     </rule>
</rules>
    