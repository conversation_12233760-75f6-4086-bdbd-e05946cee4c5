<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-DETTAGLIO-DELEGA-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE022</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>MODIFICA DELEGA RID</logAppServDesc>
   	    <areaFunzionale>CONTABILITA-GESTIONE DELEGHE RID</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<!-- input: 237 car -->
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidDettaglioRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<field-map attributeName="dettaglioDelegaRidDTO" length="237">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="234">
					<field-map attributeName="codiceSocieta"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="tipoRapporto"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"					length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="numRapporto"				length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>			
					<field-map attributeName="nominativo"				length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>															
					<field-map attributeName="numMandato"				length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="numProgressivo"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoDelega"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFrazionamento"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="importo"					length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="numInsolutiCons"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numInsolutiCons" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="flStampaQuetanza"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="giorniAddebito"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="note"						length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataProssimoInvio"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="codiceIban"				length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>													
					<field-map attributeName="codAzioneDelega"			length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			
		</input-mapping>

		<!-- output : Tot: 309 = (stessi dati input) 237 + 72 -->	
		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidDettaglioResponseDTO">

			<!-- stessi campi di Input: 237  -->
			<field-map attributeName="dettaglioDelegaRidDTO" length="237">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="234">
					<field-map attributeName="codiceSocieta"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="tipoRapporto"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"					length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="numRapporto"				length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>			
					<field-map attributeName="nominativo"				length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>															
					<field-map attributeName="numMandato"				length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="numProgressivo"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoDelega"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFrazionamento"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="importo"					length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="numInsolutiCons"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false"/>					
					<field-map attributeName="flStampaQuetanza"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="giorniAddebito"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="note"						length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataProssimoInvio"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="codiceIban"				length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>													
					<field-map attributeName="codAzioneDelega"			length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>			
			
			<!-- (campi in errore): 72 -->
			<field-map attributeName="codiceSocietaErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRapportoErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaErr"						length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="agenziaErr"						length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCollettivaErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numRapportoErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="nominativoErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numMandatoErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numProgressivoErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statoDelegaErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFrazionamentoErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoErr"						length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numInsolutiConsErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaQuetanzaErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="giorniAddebitoErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="noteErr"							length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataProssimoInvioErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceIbanErr"					length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    