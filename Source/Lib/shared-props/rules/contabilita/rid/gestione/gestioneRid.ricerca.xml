<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>RICERCA-GESTIONE-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSL005</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidRicercaRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<!-- input: 147 car -->
			<field-map attributeName="gestioneRidRicercaDTO" length="147">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidRicercaReqDTO" iterations="0" blockLength="147">
					<field-map attributeName="codLivello_0"   		length="03" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_1"   		length="06" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_2"   		length="06" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_3"   		length="07" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceSocieta"	   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"   		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"   			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"   		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"   		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numMandato"   		length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="codFiscaleInt"   		length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="cognomeInt"   		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="nomeInt"   			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="entitaRichiedente"   	length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
				</nested-mapping>
			</field-map>	
		</input-mapping>

		<!-- output : Tot: 21852 = 147 + 21705 -->
		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidRicercaResponseDTO">
			
			<!-- 147 (stessi dati input) -->
			<field-map attributeName="gestioneRidRicercaDTO" length="147">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidRicercaReqDTO" iterations="0" blockLength="147">
					<field-map attributeName="codLivello_0"   		length="03" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_1"   		length="06" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_2"   		length="06" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codLivello_3"   		length="07" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceSocieta"   		length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"   		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"   			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"   		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"  	 	length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numMandato"	   		length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="codFiscaleInt"   		length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="cognomeInt"   		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="nomeInt"  	 		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="entitaRichiedente" 	length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
				</nested-mapping>
			</field-map>	
			
			<!-- (dati specifici di output) 21705 = 5 + 21700 -->
			<field-map attributeName="numElementiTrovati"   	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="flAltri"   				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="listaRid" length="21700">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidRicercaResDTO" iterations="100" blockLength="217">						
					<field-map attributeName="codiceSocieta"		length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numMandato"			length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numProgressivo"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"				length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="numRapporto"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>			
					<field-map attributeName="tipoRapporto"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeInt"			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeInt"				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statoDelega"			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descStatoDelega"		length="50" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataInizioVal"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataFineVal"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="flScudato"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>											
					<field-map attributeName="codiceIban"			length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    