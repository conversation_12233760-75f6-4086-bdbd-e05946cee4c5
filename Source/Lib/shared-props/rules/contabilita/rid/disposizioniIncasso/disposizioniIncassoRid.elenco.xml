<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-DISPOSIZIONI-INCASSO-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSL006</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue=""/>  
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<!-- input: 23 car -->
			<field-map attributeName="dispIncassoRidDTO" length="23">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDTO" iterations="0" blockLength="128">
					<field-map attributeName="codiceSocieta"   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numMandato"   	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="numProgressivo"	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>	
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidResponseDTO">
			
			<!-- output : Tot: 5328 (stessi dati input) 23 + 5305 -->
			<field-map attributeName="dispIncassoRidDTO" length="23">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDTO" iterations="0" blockLength="128">
					<field-map attributeName="codiceSocieta"   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numMandato"   	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="numProgressivo"	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>	
			
			<!-- (dati specifici di output) 5305 = 5 + 5300 -->
			<field-map attributeName="numElementiTrovati"   	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="flAltri"   				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="listaDispIncasso" length="5300">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoDTO" iterations="100" blockLength="53">						
					<field-map attributeName="codiceSocieta"		length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numDistinta"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataOperazione"		length="10" precision="0" numericScale="0" align="left"	 mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="numDisposizione"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataEffetto"			length="10" precision="0" numericScale="0" align="left"	 mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataPagamento"		length="10" precision="0" numericScale="0" align="left"	 mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="statoDisposizione"	length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>											
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    