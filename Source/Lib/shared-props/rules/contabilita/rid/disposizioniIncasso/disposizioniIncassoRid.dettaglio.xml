<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-DISPOSIZIONI-INCASSO-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE023</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue=""/>  
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDettaglioRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<!-- input: 51 car -->
			<field-map attributeName="dettaglioDispIncassoDTO" length="51">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDettaglioReqDTO" iterations="0" blockLength="128">
					<field-map attributeName="codiceSocieta"   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numMandato"   	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="numProgressivo"	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDistinta"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numDistinta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="dataOperazione"	length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data"  nomeAttributoERR="dataOperazione" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numDisposizione"	length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numDistinta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
				</nested-mapping>
			</field-map>	
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDettaglioResponseDTO">
			
			<!-- output : Tot: 298 (stessi dati input) 51 + 247 -->
			<field-map attributeName="dettaglioDispIncassoReqDTO" length="51">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDettaglioReqDTO" iterations="0" blockLength="128">
					<field-map attributeName="codiceSocieta"   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numMandato"   	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="numProgressivo"	length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDistinta"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numDistinta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="dataOperazione"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" nomeAttributoERR="dataOperazione" nomeKeyERR="erroreDisposizioniIncassoRid"/>
					<field-map attributeName="numDisposizione"	length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numDistinta" nomeKeyERR="erroreDisposizioniIncassoRid"/>
				</nested-mapping>
			</field-map>

			<!-- (dati specifici di output) 247 -->
			<field-map attributeName="dettaglioDispIncassoResDTO" length="247">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DisposizioniIncassoRidDettaglioResDTO" iterations="0" blockLength="247">
					<field-map attributeName="statoDisposizione"		length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausale"   			length="05" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrCausale"   			length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codModPagamento"   		length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codDivisa"   				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importo"   				length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="importoSpeseRid"   		length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="importoSpeseInsoluto"   	length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="note"   					length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEmissione"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="dataEffetto"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="dataPagamento"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="dataIncasso"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="dataInvioBanca"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="dataRicezBanca"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
					<field-map attributeName="codOttico"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data" />
				</nested-mapping>
			</field-map>	
		</output-mapping>
	</rule>
</rules>
    