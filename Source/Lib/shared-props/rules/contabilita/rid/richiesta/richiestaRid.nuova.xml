<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-NUOVA-RICHIESTA-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE019</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>SALVA DELEGA RID</logAppServDesc>
   	    <areaFunzionale>CONTABILITA-GESTIONE DELEGHE RID</areaFunzionale> 
   	    
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<!-- input: 441 car -->
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidDettaglioRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<field-map attributeName="dettaglioDelegaRidDTO" length="441">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="441">
					<field-map attributeName="codiceSocieta"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"					length="06" precision="0" numericScale="0" align="left"	 mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"				length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>			

					<field-map attributeName="nominativo"				length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numMandato"				length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numProgressivo"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoDelega"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausale"				length="05" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCausale"				length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codFrazionamento"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="importo"					length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeAttributoERR="importo" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="codDivisa"				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numInsolutiCons"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flStampaQuetanza"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flFacoltaStorno"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flAttivazione"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="giorniAddebito"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="note"						length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataInizioAut"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataInizioAut" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataFineAut"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataFineAut" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRicezioneRich"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataRicezioneRich" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataInvioRichBanca"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataInvioRichBanca" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRicezioneEsitoBanca"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataRicezioneEsitoBanca" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRipresentazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataRipresentazione" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataProssimoInvio"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataProssimoInvio" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="codiceIban"				length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeInt"				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeInt"					length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFiscaleInt"			length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flScudato"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
				</nested-mapping>
			</field-map>			
		</input-mapping>

		<!-- output : Tot: 569 = (stessi dati input) 441 + 128 -->	
		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidDettaglioResponseDTO">
			<field-map attributeName="dettaglioDelegaRidDTO" length="441">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="441">
					<field-map attributeName="codiceSocieta"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"					length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"				length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>			

					<field-map attributeName="nominativo"				length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numMandato"				length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numProgressivo"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoDelega"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausale"				length="05" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCausale"				length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codFrazionamento"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="importo"					length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeAttributoERR="importo" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="codDivisa"				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numInsolutiCons"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flStampaQuetanza"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flFacoltaStorno"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flAttivazione"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="giorniAddebito"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="note"						length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataInizioAut"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataInizioAut" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataFineAut"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataFineAut" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRicezioneRich"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataRicezioneRich" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataInvioRichBanca"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataInvioRichBanca" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRicezioneEsitoBanca"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataRicezioneEsitoBanca" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataRipresentazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataRipresentazione" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="dataProssimoInvio"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" nomeAttributoERR="dataProssimoInvio" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="codiceIban"				length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeInt"				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeInt"					length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFiscaleInt"			length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flScudato"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
				</nested-mapping>
			</field-map>

			<field-map attributeName="codiceSocietaErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRapportoErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCollettivaErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numRapportoErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="nominativoErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numMandatoErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numProgressivoErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statoDelegaErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codCausaleErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
			<field-map attributeName="codFrazionamentoErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codDivisaErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numInsolutiConsErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStampaQuetanzaErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacoltaStornoErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flAttivazioneErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="giorniAddebitoErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="noteErr"						length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInizioAutErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataFineAutErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRicezioneRichErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInvioRichBancaErr"		length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRicezioneEsitoBancaErr"	length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRipresentazioneErr"		length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataProssimoInvioErr"			length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceIbanErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cognomeIntErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="nomeIntErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codFiscaleIntErr"				length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flScudatoErr"					length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
			
		</output-mapping>
	</rule>
</rules>
    