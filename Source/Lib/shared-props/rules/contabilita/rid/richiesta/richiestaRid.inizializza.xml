<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INIT-NUOVA-RICHIESTA-RID</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE018</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.RichiestaRidDettaglioRequestDTO" nomeKeyERR="erroreGestioneDelegaRid">
			<!-- input: 34 car -->
			<field-map attributeName="dettaglioDelegaRidDTO" length="34">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="34">
					<field-map attributeName="codiceSocieta"   	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"   	length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"   		length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   		length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"   	length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"   	length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="posizione"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="posizione" nomeKeyERR="erroreGestioneDelegaRid"/>							
				</nested-mapping>
			</field-map>	
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.GestioneRidDettaglioResponseDTO">
			
			<!-- output : Tot: 73 = 34 + 15 + 24 -->
			<field-map attributeName="dettaglioDelegaRidDTO" length="49">
				<nested-mapping className="it.sistinf.albedoweb.contabilita.rid.dto.DettaglioDelegaRidDTO" iterations="0" blockLength="49">						
					<!-- stessi dati input: 34 -->
					<field-map attributeName="codiceSocieta"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="codSocieta" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="tipoRapporto"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="categoria"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"					length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numCollettiva"			length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettiva" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="numRapporto"				length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numRapporto" nomeKeyERR="erroreGestioneDelegaRid"/>
					<field-map attributeName="posizione"				length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="posizione" nomeKeyERR="erroreGestioneDelegaRid"/>			

					<!-- (dati specifici di output) 15 -->
<!-- 					<field-map attributeName="nominativo"				length="55" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numMandato"				length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numProgressivo"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoDelega"				length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausale"				length="05" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCausale"				length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codFrazionamento"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> -->
					<field-map attributeName="importo"					length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<!-- <field-map attributeName="codDivisa"				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numInsolutiCons"			length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flStampaQuetanza"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flFacoltaStorno"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flAttivazione"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="giorniAddebito"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="note"						length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataInizioAut"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataFineAut"				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataRicezioneRich"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataInvioRichBanca"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataRicezioneEsitoBanca"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataRipresentazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="dataProssimoInvio"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="codiceIban"				length="34" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeInt"				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeInt"					length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFiscaleInt"			length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flScudato"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codAddebitoRid"			length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flProteggiCampoFraz"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					 -->
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="codiceSocietaErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRapportoErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaErr"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaErr"			length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCollettivaErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numRapportoErr"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
		</output-mapping>
	</rule>
</rules>
    