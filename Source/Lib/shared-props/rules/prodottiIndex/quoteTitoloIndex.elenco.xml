<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-QUOTE-TITOLI-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLQTI</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLQTI Lunghezza: 150 + 7500 + 1824 = 9501 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexRequestDTO" nomeKeyERR="erroriElencoQuoteTitoloIndex"> 
   <!-- input: 22 car --> 
   <field-map attributeName="codTitoloRicerca" 	length="12"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
   <field-map attributeName="dataRicerca" 		length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataRicerca" nomeKeyERR="erroriElencoQuoteTitoloIndex" />   
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.ListaQuoteTitoloIndexResponseDTO"> 
      <!-- output: 6027  car -->      
      <field-map attributeName="codTitoloRicerca" 	length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="dataRicerca" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
      <field-map attributeName="numElementiTrovati" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaQuote" length="7500"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexResponseDTO" iterations="300" blockLength="25"> 
		    	<field-map attributeName="dataQuota" 	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/> 
   	  			<field-map attributeName="valoreQuota" 	  		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
   	  			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true" />
		    </nested-mapping>
   </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 