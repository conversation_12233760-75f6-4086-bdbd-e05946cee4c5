<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-NUOVO-PRODOTTO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE140</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
 	<logApp>true</logApp>
	<logAppServDesc>SALVA PRODOTTI INDEX</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSE069 Lunghezza: 150 + 84 + 1824 = 2058 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriSalvaProdottiIndex"> 
   	<!-- input: 64 car --> 
   	<field-map attributeName="codProdotto" 	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	<field-map attributeName="descProdotto" 	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	<field-map attributeName="barriera1"  	 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
     		valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="barriera1" nomeKeyERR="erroriSalvaProdottiIndex"/>
	<field-map attributeName="barriera2"  	 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
     		valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="barriera2" nomeKeyERR="erroriSalvaProdottiIndex"/>
	<field-map attributeName="dataIni"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
   			valida="true" natura="Data" nomeAttributoERR="dataIni" nomeKeyERR="erroriSalvaProdottiIndex"/> 
  </input-mapping>
    
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexResponseDTO"> 
    <!-- output: 84  car -->      
   	<field-map attributeName="codProdotto" 	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	<field-map attributeName="descProdotto" 	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	<field-map attributeName="barriera1"  	  			length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	 	natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
	<field-map attributeName="barriera2"  	  			length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	 	natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
	<field-map attributeName="dataIni"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		natura="Data"/>

	<field-map attributeName="codProdottoErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="descProdottoErr"      length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="barriera1Err"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="barriera2Err"    		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="dataIniErr"      		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
   </output-mapping> 
 </rule> 
</rules> 