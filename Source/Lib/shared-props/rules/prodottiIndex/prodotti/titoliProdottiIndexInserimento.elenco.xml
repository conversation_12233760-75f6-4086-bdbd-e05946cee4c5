<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-TITOLI-INSERIMENTO-PRODOTTO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLPTT</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLPRT Lunghezza: 150 + 15617 + 1824 = 17517 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriSalvaProdottiIndex"> 
   <!-- input: 52 car --> 
   <field-map attributeName="codTitoloRicerca" 	length="12"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ListaTitoliProdottoIndexResponseDTO"> 
      <!-- output: 26457  car -->      
      <field-map attributeName="codTitoloRicerca" 	length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="numElementiTrovati" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaInserisciTitoliProdotto" length="15600"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.TitoloProdottoIndexResponseDTO" iterations="300" blockLength="52"> 
		    	<field-map attributeName="codTitolo" 	  	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	  			<field-map attributeName="descTitolo" 	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
		    </nested-mapping>
      </field-map> 
    
   </output-mapping> 
 </rule> 
</rules> 