<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-DATE-PRODOTTO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE146</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>ELIMINA DATE PRODOTTI INDEX</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLPRT Lunghezza: 150 + 1010 + 1824 = 2984 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriDateOssProdottoIndex"> 
   <!-- input: 52 car --> 
   <field-map attributeName="codProdotto" 	length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
   <field-map attributeName="listaEliminaDateProdotto" length="1000"> 
	    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexRequestDTO" iterations="100" blockLength="10"> 
	    	<field-map attributeName="dataOss"  length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data"/> 
	    </nested-mapping>
   </field-map> 
  </input-mapping> 
   <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexResponseDTO">
    <!-- output :  10  car -->      
  	<field-map attributeName="codProdotto" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />      
    <field-map attributeName="listaDateOss" length="1000"> 
	    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexResponseDTO" iterations="100" blockLength="10"> 
	    	<field-map attributeName="dataOss"  length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	    </nested-mapping>
    </field-map> 
   </output-mapping>
 </rule> 
</rules> 