<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>INSERISCI-TITOLI-PRODOTTO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE143</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>SALVA TITOLI PRODOTTI INDEX</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLPRT Lunghezza: 150 + 5710 + 1824 = 7684 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriEliminaTitoliProdottoIndex"> 
   <!-- input: 52 car --> 
   <field-map attributeName="codProdotto" 	length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
   <field-map attributeName="listaInserisciTitoliProdotto" length="5200"> 
	    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.TitoloProdottoIndexRequestDTO" iterations="100" blockLength="10"> 
	    	<field-map attributeName="codTitolo"  length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="descTitolo"  length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	    </nested-mapping>
   </field-map> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ListaTitoliProdottoIndexResponseDTO">
    <!-- output :  10  car -->      
  	<field-map attributeName="codProdotto" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />      
    <field-map attributeName="listaInserisciTitoliProdotto" length="5700"> 
	    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.TitoloProdottoIndexResponseDTO" iterations="100" blockLength="10"> 
	    	<field-map attributeName="codTitolo"  length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="descTitolo"  length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="flagErr"  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
     		<field-map attributeName="codErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
	    </nested-mapping>
    </field-map> 
  </output-mapping>
 </rule> 
</rules> 