<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>INSERISCI-DATE-PRODOTTO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE145</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>SALVA DATE PRODOTTI INDEX</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLPDT Lunghezza: 150 + 1510 + 1824 = 3484 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriInserisciDateOssProdottoIndex"> 
   <!-- input: 52 car --> 
	  <field-map attributeName="codProdotto" 	length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
      <field-map attributeName="listaDateOssInserimento" length="1000"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexRequestDTO" iterations="100" blockLength="10"> 
				  <field-map attributeName="dataOss" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		   				valida="false" natura="Data"/> 
		    </nested-mapping>
      </field-map> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexResponseDTO"> 
      <!-- output: 3014  car -->      
      <field-map attributeName="codProdotto" 	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="listaDateOssInserimento" length="1000"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexResponseDTO" iterations="100" blockLength="10"> 
				  <field-map attributeName="dataOss" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		   				valida="true" natura="Data"/> 
		    </nested-mapping>
      </field-map> 
      <field-map attributeName="listaDateOssInserimentoErr" length="500"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexResponseDTO" iterations="100" blockLength="5"> 
		    	<field-map attributeName="flagErr"  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	     		<field-map attributeName="codErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
		    </nested-mapping>
      </field-map> 
   </output-mapping> 
 </rule> 
</rules> 