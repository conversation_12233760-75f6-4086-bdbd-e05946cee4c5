<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-DATE-PRODOTTI-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLPDT</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLPDT Lunghezza: 150 + 3015 + 1824 = 4989 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexRequestDTO" nomeKeyERR="erroriElencoDateOssProdottoIndex"> 
   <!-- input: 52 car --> 
   <field-map attributeName="codProdotto" 	length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.ProdottiIndexResponseDTO"> 
      <!-- output: 3014  car -->      
      <field-map attributeName="codProdotto" 	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="numElementiTrovati" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaDateOss" length="3000"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.prodottiIndex.dto.DateOssProdottoIndexResponseDTO" iterations="300" blockLength="10"> 
		        <field-map attributeName="dataOss" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		        natura="Data"/>
		    </nested-mapping>
      </field-map> 
   </output-mapping> 
 </rule> 
</rules> 