<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-NUOVO-PERDITA-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE150</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA PERDITE PRODOTTI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE070 Lunghezza: 150 + 48 + 1826 = 2024  -->  
      <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.perditeIndex.dto.PerditeIndexRequestDTO" nomeKeyERR="erroriSalvaPerditeIndex">
      <!-- input :  48  car -->
      	<field-map attributeName="codPerdita" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
		<field-map attributeName="valore"  	  	length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 	valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="valore" nomeKeyERR="erroriSalvaPerditeIndex"/>
		<field-map attributeName="dataRif"  	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
   				valida="true" natura="Data" nomeAttributoERR="dataRif" nomeKeyERR="erroriSalvaPerditeIndex"/> 
		<field-map attributeName="percBarriera"  	 length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 		valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="percBarriera" nomeKeyERR="erroriSalvaPerditeIndex"/>
      </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.perditeIndex.dto.PerditeIndexResponseDTO">
      <!-- output :  48  car -->      
      	<field-map attributeName="codPerdita" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
		<field-map attributeName="valore"  	  length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="dataRif"  	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
				natura="Data"/>
		<field-map attributeName="percBarriera"  	  length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 		valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="codPerditaErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
		<field-map attributeName="valoreErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
		<field-map attributeName="dataRifErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
		<field-map attributeName="percBarrieraErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
     </output-mapping>
  </rule>
</rules>
