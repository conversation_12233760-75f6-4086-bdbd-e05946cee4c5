<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-NUOVA-CEDOLA-PERDITA-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE152</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA CEDOLE PERDITE PRODOTTI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE070 Lunghezza: 150 + 34 + 1826 = 2010  -->  
      <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.perditeIndex.dto.PerditeIndexRequestDTO" nomeKeyERR="erroriSalvaCedolePerditeIndex">
      <!-- input :  26  car -->
      	<field-map attributeName="codPerdita" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
		<field-map attributeName="valore"  	  	length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 	valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="valore" nomeKeyERR="erroriSalvaCedolePerditeIndex"/>
		<field-map attributeName="dataRif"  	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
   				valida="true" natura="Data" nomeAttributoERR="dataRif" nomeKeyERR="erroriSalvaCedolePerditeIndex"/> 
      </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.perditeIndex.dto.PerditeIndexResponseDTO">
      <!-- output :  34  car -->      
      	<field-map attributeName="codPerdita" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
		<field-map attributeName="valore"  	  length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		 		valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="dataRif"  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
				natura="Data"/>
		<field-map attributeName="valoreErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
		<field-map attributeName="dataRifErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
     </output-mapping>
  </rule>
</rules>
