<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-TITOLI-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLTIT</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLTIT Lunghezza: 150 + 26457 + 1824 = 28431 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.TitoliIndexRequestDTO" nomeKeyERR="erroriElencoTitoliIndex"> 
   <!-- input: 52 car --> 
   <field-map attributeName="codTitoloRicerca" 	length="12"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
   <field-map attributeName="descTitoloRicerca" 	length="40"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.ListaTitoliIndexResponseDTO"> 
      <!-- output: 26457  car -->      
      <field-map attributeName="codTitoloRicerca" 		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="descTitoloRicerca" 		length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>  
      <field-map attributeName="numElementiTrovati" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaTitoliIndex" length="26400"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.TitoliIndexResponseDTO" iterations="300" blockLength="88"> 
		    	<field-map attributeName="codTitolo" 	  	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	  			<field-map attributeName="descTitolo" 	  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
		        <field-map attributeName="codReuters"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        <field-map attributeName="dataUltOss"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		        natura="Data"/>
		        <field-map attributeName="valore"  	 	 	length="15"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		        natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true" />
		        <field-map attributeName="tipo"  			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		    </nested-mapping>
      </field-map> 
    
   </output-mapping> 
 </rule> 
</rules> 