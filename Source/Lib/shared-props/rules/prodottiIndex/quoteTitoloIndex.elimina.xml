<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-QUOTA-TITOLO-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE139</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA QUOTE TITOLO</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE088 Lunghezza: 150 + 22 + 1824 = 1996  -->  
      <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexRequestDTO" nomeKeyERR="erroriSalvaQuoteTitoloIndex">
      <!-- input :  20  car -->
      	<field-map attributeName="codTitoloRicerca" 	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
      	<field-map attributeName="dataQuota" 		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
   		 natura="Data"/>
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexResponseDTO">
      <!-- output :  20  car -->      
    	<field-map attributeName="codTitoloRicerca" 	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>      
      	<field-map attributeName="dataQuota" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
      	 natura="Data"/>      
     </output-mapping>
  </rule>
</rules>
