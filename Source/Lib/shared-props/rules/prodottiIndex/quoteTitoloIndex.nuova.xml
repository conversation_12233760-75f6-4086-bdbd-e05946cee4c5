<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-NUOVA-QUOTA-TITOLO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE137</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>SALVA QUOTE TITOLO</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE137 Lunghezza: 150 + 44 + 1824 = 2018 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexRequestDTO" nomeKeyERR="erroriSalvaQuoteTitoloIndex"> 
   	<!-- input: 32 car --> 
   	<field-map attributeName="codTitoloRicerca" 	  		length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    	 
	<field-map attributeName="dataQuota"  	  				length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
   			valida="true" natura="Data" nomeAttributoERR="dataQuota" nomeKeyERR="erroriSalvaQuoteTitoloIndex"/> 
	<field-map attributeName="valoreQuota"  				length="15" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
			valida="true" natura="Numerico"  segnato="false"  numInteri="11" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="valoreQuota" nomeKeyERR="erroriSalvaQuoteTitoloIndex"/>
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.QuoteTitoloIndexResponseDTO"> 
    <!-- output: 44  car -->      
   	<field-map attributeName="codTitoloRicerca" 	  	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    	 
	<field-map attributeName="dataQuota"  	  			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
	natura="Data"/>
	<field-map attributeName="valoreQuota"  			length="15" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
	natura="Numerico"  segnato="false"  numInteri="11" numDecimali="3" separatoreMigliaia="false" />
	<field-map attributeName="codTitoloRicercaErr" 	  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>    	 
	<field-map attributeName="dataQuotaErr"  	  		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="" />
	<field-map attributeName="valoreQuotaErr"  			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="" />
   </output-mapping> 
 </rule> 
</rules> 