<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>MODIFICA-FONDO-ZIL</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE148</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>MODIFICA FONDI ZIL</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE FONDI</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE148 Lunghezza: 150 + 196 + 1824 = 2170 -->  
  <input-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilRequestDTO" nomeKeyERR="erroriGestioneFondiZil"> 
   <!-- input: 164 car --> 
   	   <field-map attributeName="codSocieta" 	  	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	   <field-map attributeName="codFondo" 	  		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
       <field-map attributeName="descrizione"  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="socCertific" 	  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="proventi"  	 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="utileNetto"		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="oneri"		  		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="utileGest" 	  	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="periodoDa"  	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="periodoDa" nomeKeyERR="erroriGestioneFondiZil"/>
       <field-map attributeName="periodoA" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="periodoA" nomeKeyERR="erroriGestioneFondiZil"/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilResponseDTO"> 
      <!-- input + output: 164 + 32 = 196  car -->      
       <field-map attributeName="codSocieta" 	  	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	   <field-map attributeName="codFondo" 	  		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
       <field-map attributeName="descrizione"  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="socCertific" 	  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="proventi"  	 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="utileNetto"		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="oneri"		  		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="utileGest" 	  	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
       <field-map attributeName="periodoDa"  	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		natura="Data"/>
       <field-map attributeName="periodoA" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		natura="Data"/>
       
	   <field-map attributeName="descrizioneCdErr"  	length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="socCertificCdErr"  	length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="proventiCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="utileNettoCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="oneriCdErr"  			length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="utileGestCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="periodoDaCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	   <field-map attributeName="periodoACdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
    
   </output-mapping> 
 </rule> 
</rules> 