<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-FONDI-ZIL</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE149</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  <logApp>true</logApp>
	<logAppServDesc>ELIMINA FONDI ZIL</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE FONDI</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE149 Lunghezza: 150 + 14 + 1824 = 1988 -->  
  <input-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilRequestDTO" nomeKeyERR="erroriGestioneFondiZil"> 
  <!-- input: 14 car --> 
	   <field-map attributeName="codSocieta" 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
	   <field-map attributeName="codFondo" 		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	   <field-map attributeName="periodoDa"  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		valida="true" natura="Data" nomeAttributoERR="periodoDa" nomeKeyERR="erroriGestioneFondiZil" />
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilResponseDTO"> 
      <!-- input : 14 car -->      
     	<field-map attributeName="codSocieta" 		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
	    <field-map attributeName="codFondo" 		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	    <field-map attributeName="periodoDa"  	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
       		natura="Data" />
   </output-mapping> 
 </rule> 
</rules> 