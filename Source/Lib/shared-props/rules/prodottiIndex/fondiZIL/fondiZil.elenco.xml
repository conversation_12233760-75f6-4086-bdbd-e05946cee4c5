<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-FONDI-ZIL</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLFON</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLFON Lunghezza: 150 + 24609 + 1824 = 26583 -->  
  <input-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilRequestDTO" nomeKeyERR="erroriGestioneFondiZil"> 
  <!-- input: 4 car --> 
   <field-map attributeName="codSocietaRicerca" 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>    
   <field-map attributeName="codFondoRicerca" 		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilResponseDTO"> 
      <!-- input + output: 4 + 24605 = 24609  car -->      
      <field-map attributeName="codSocietaRicerca" 		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="codFondoRicerca" 		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>  
      <field-map attributeName="numElementiTrovati" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flLimite"         		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaFondiZil" length="24600"> 
		    <nested-mapping className="it.sistinf.albedoweb.prodottiIndex.fondiZil.dto.FondiZilValoriDTO" iterations="150" blockLength="164"> 
		    	<field-map attributeName="codSocieta" 	  	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	  			<field-map attributeName="codFondo" 	  	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
		        <field-map attributeName="descrizione"  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="socCertific" 	  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="proventi"  	 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="utileNetto"		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="oneri"		  	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="utileGest" 	  	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		        <field-map attributeName="periodoDa"  	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
		        	natura="Data"/>
		        <field-map attributeName="periodoA" 		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
		        	natura="Data" />
		    </nested-mapping>
      </field-map> 
   </output-mapping> 
 </rule> 
</rules> 