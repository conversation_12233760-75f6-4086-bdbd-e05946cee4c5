<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>SALVA-NUOVO-TITOLO-INDEX</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE134</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId>
  	<logApp>true</logApp>
	<logAppServDesc>SALVA TITOLI INDEX</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
   
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSE069 Lunghezza: 150 + 112 + 1824 = 2086 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.TitoliIndexRequestDTO" nomeKeyERR="erroriSalvaTitoliIndex"> 
   	<!-- input: 88 car --> 
   	<field-map attributeName="codTitolo" 	  	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	<field-map attributeName="descTitolo" 	  	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	<field-map attributeName="codReuters"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" nomeAttributoERR="codReuters" nomeKeyERR="erroriSalvaTitoliIndex"/>
	<field-map attributeName="dataUltOss"  	  	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
   			valida="true" natura="Data" nomeAttributoERR="dataUltOss" nomeKeyERR="erroriSalvaTitoliIndex"/> 
	<field-map attributeName="valore"  	 	 	length="15"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
     			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeAttributoERR="valore" nomeKeyERR="erroriSalvaTitoliIndex" valida="true"/>
	<field-map attributeName="tipo"  	  		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.titoliIndex.dto.TitoliIndexResponseDTO"> 
    <!-- output: 112  car -->      
   	<field-map attributeName="codTitolo" 	  		length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
   	<field-map attributeName="descTitolo" 	  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
	<field-map attributeName="codReuters"  	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	<field-map attributeName="dataUltOss"  	  		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	natura="Data"/>
	<field-map attributeName="valore"  	  			length="15"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
	  natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="false"/>
	<field-map attributeName="tipo"  	 	 		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	<field-map attributeName="codTitoloErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="descTitoloErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="codReutersErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="dataUltOssErr"      	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="valoreErr"      		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
	<field-map attributeName="tipoErr"      		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
   </output-mapping> 
 </rule> 
</rules> 