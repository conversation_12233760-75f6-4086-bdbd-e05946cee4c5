<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-VALORI-CEDOLA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLVCD</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLVCD Lunghezza: 150 + 15515 + 1824 = 17489  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaRequestDTO"> 
	   <!-- input : 10 car --> 
	   <field-map attributeName="codiceCedola"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaResponseDTO"> 
      <!-- output :50 + 21305 = 15515 car -->      
      <field-map attributeName="codiceCedola"  	 length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
      
      <field-map attributeName="numElementiTrovati" 		 length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flLimite"         			 length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaValoriCedola" length="15500" > 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.DatiValoriCedolaDTO" iterations="500" blockLength="31"> 
		          <field-map attributeName="codiceCedola"       		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				  <field-map attributeName="dataValidita"         		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
				  <field-map attributeName="valoreCedola"         		length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
				  	natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3" separatoreMigliaia="true"/>
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 