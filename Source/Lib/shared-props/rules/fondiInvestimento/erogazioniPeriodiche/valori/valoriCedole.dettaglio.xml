<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>DETTAGLIO-VALORI-CEDOLA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE130</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE130 Lunghezza: 150 + 101 + 1824 = 2075  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaRequestDTO"> 
	   <!-- input : 20 car --> 
	   <field-map attributeName="codiceCedola"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	   <field-map attributeName="dataValiditaInput"      length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	   		valida="false" natura="Data"/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaResponseDTO"> 
      <!-- output :20 + 81 = 101 car -->      
      <field-map attributeName="codiceCedola"  	 	  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="dataValiditaInput"    length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
      		natura="Data"/> 
      
	  <field-map attributeName="descrizioneCedola"     		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	  <field-map attributeName="dataValidita"         		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	  		natura="Data"/>
	  <field-map attributeName="valoreCedola"         		length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	  		natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3" separatoreMigliaia="true"/>
   </output-mapping> 
 </rule> 
</rules> 