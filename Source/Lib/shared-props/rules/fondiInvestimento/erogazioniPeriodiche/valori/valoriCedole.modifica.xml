<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>MODIFICA-VALORI-CEDOLA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE132</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE132 Lunghezza: 150 + 83 + 1824 = 2057  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaRequestDTO"> 
	   <!-- input : 71 car --> 
	    <field-map attributeName="codiceCedola"  		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    <field-map attributeName="dataValidita"    		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	    	valida="true" natura="Data"  nomeAttributoERR="dataValidita" nomeKeyERR="erroriValoreCedola"/> 
	    <field-map attributeName="descrizioneCedola"    length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	    <field-map attributeName="valoreCedola"         length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	    	valida="true" natura="Numerico" segnato="false" numInteri="6" numDecimali="3" separatoreMigliaia="true"  nomeAttributoERR="valoreCedola" nomeKeyERR="erroriValoreCedola"/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.valori.dto.ValoriCedolaResponseDTO"> 
      <!-- output :71 + 12 = 83 car -->      
      	<field-map attributeName="codiceCedola"  		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    <field-map attributeName="dataValidita"    		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	    	natura="Data"/> 
	    <field-map attributeName="descrizioneCedola"    length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	    <field-map attributeName="valoreCedola"         length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
	    	natura="Numerico" segnato="false" numInteri="6" numDecimali="3" separatoreMigliaia="true"/>
	    
	    <field-map attributeName="dataValiditaCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
	    <field-map attributeName="descrizioneCedolaCdErr"  	length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	    <field-map attributeName="valoreCedolaCdErr"  		length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
   </output-mapping> 
 </rule> 
</rules> 