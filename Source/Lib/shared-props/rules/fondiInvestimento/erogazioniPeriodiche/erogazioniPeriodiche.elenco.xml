<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-EROGAZIONI_PERIODICHE</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLGCD</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSLGCD Lunghezza: 150 + 21356 + 1824 = 23330  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.dto.ErogazioniPeriodicheRequestDTO" nomeKeyERR="erroriErogazioniPeriodiche"> 
   <!-- input : 51 car --> 
   <field-map attributeName="codCedolaRicerca"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   <field-map attributeName="descCedolaRicerca" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   <field-map attributeName="tipoCedolaRicerca" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.dto.ErogazioniPeriodicheResponseDTO"> 
      <!-- output :51 + 21305 = 21356 car -->      
      <field-map attributeName="codCedolaRicerca"  	 length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   	  <field-map attributeName="descCedolaRicerca" 	 length="40"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
      <field-map attributeName="tipoCedolaRicerca" 	 length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
      <field-map attributeName="numElementiTrovati" 		 length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flLimite"         			 length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaCedole" length="21300" > 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.dto.ErogazioniPeriodicheValoriDTO" iterations="300" blockLength="71"> 
		          <field-map attributeName="codiceCedola"       		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="descrizioneCedola"  		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
				  <field-map attributeName="dataInizioValidita"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
				  <field-map attributeName="dataFineValidita"         	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
				  <field-map attributeName="tipoCedola"         		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 