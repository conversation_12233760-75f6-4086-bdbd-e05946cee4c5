<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-EROGAZIONI_PERIODICHE</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE121</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>ELIMINA EROGAZIONI_PERIODICHE</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE FONDI</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE121 Lunghezza: 150 + 31 + 1824 = 2005  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.dto.ErogazioniPeriodicheRequestDTO" nomeKeyERR="erroriErogazioniPeriodiche"> 
   <!-- input : 31 car --> 
	   <field-map attributeName="codiceCedola"      	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	   <field-map attributeName="dataInizioValidita"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
	   <field-map attributeName="dataFineValidita"		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset=""  padding="" valida="false" natura="Data"/>
	   <field-map attributeName="tipoCedola"         	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />     
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.erogazioniPeriodiche.dto.ErogazioniPeriodicheResponseDTO"> 
      <!-- output :31 car -->      
       <field-map attributeName="codiceCedola"      	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	   <field-map attributeName="dataInizioValidita"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
	   <field-map attributeName="dataFineValidita"		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset=""  padding="" natura="Data"/>
	   <field-map attributeName="tipoCedola"         	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />     
   </output-mapping> 
 </rule> 
</rules> 