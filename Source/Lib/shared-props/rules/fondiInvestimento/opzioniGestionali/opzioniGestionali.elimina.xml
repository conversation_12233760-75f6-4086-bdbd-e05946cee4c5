<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-OPZIONI-GESTIONALI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE176</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>ELIMINA OPZIONI GESIONALI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE MULTIGARANZIA</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE176 Lunghezza: 150 + 10 + 1824 = 1984 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioniGestionaliRequestDTO" nomeKeyERR="erroriSalvaOpzioniGestionali"> 
   	<!-- input: 10 car -->
   	<field-map attributeName="opzioneGestionale" length="10" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		   		</nested-mapping>
	</field-map> 
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioniGestionaliResponseDTO"> 
    <!-- output: 10  car -->      
   		<field-map attributeName="opzioneGestionale" length="10" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		   		</nested-mapping>
	</field-map>
   </output-mapping> 
 </rule> 
</rules> 