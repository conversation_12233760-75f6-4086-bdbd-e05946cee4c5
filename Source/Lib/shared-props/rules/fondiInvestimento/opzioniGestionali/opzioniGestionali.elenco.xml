<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-OPZIONI-GESTIONALI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLOPZ</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>ELENCO OPZIONI GESIONALI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE MULTIGARANZIA</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSLOPZ Lunghezza: 150 + 21055 + 1824 = 23029 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioniGestionaliRequestDTO" > 
   	<!-- input: 50 car -->
   	<field-map attributeName="opzioneGestionale" length="50" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		   				<field-map attributeName="descrizione"		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		   		</nested-mapping>
	</field-map> 
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.ListaOpzioniGestionaliResponseDTO"> 
    <!-- output: 21055  car -->      
 	<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	<field-map attributeName="descrizione"		length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	<field-map attributeName="numeroElementi"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
	<field-map attributeName="flagLimite"		length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
   		
   	<field-map attributeName="opzioniGestionali" length="21000" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale" iterations="300" blockLength="70"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="dataInizio"      	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="dataFine"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
				</nested-mapping>
	</field-map>
   </output-mapping> 
 </rule> 
</rules> 
