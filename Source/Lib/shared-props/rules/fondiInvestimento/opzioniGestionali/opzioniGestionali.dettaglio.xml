<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>DETTAGLIO-OPZIONI-GESTIONALI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE173</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>DETTAGLIO OPZIONI GESIONALI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE MULTIGARANZIA</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWLSE176 Lunghezza: 150 + 393 + 1824 = 2367 -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioniGestionaliRequestDTO"> 
   	<!-- input: 10 car -->
   	<field-map attributeName="opzioneGestionale" length="10" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		   		</nested-mapping>
	</field-map> 
  </input-mapping>  
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioniGestionaliResponseDTO"> 
    <!-- output: 393  car -->      
   		<field-map attributeName="opzioneGestionale" length="393" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.opzioniGestionali.dto.OpzioneGestionale"> 
		   				<field-map attributeName="codice"			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="dataInizio"      	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							natura="Data"/>
			   			<field-map attributeName="dataFine"         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
			    			natura="Data"/>
		   				<field-map attributeName="allocazione" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="riallocazione" 	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="codicePercorso"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="codiceUtTFR" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInvTFR"      	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						<field-map attributeName="codiceUT1" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv1"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						<field-map attributeName="codiceUT2" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv2"      	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						<field-map attributeName="codiceUT3" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv3"      	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						<field-map attributeName="codiceUT4" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv4"      	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						<field-map attributeName="codiceUT5" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv5"      	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="takeProfit" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="stopLoss" 	    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percMin1"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax1"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin2"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax2"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin3"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax3"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin4"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax4"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin5"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax5"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<!--  NEW: 7 + 20 + 31 + 31 + 31 + 17 = 137 car -->	
		   				<field-map attributeName="eta" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="antidurata" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="infoEtaAntidurata" length="5"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
		   				<field-map attributeName="codicePianoSpostamento"		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="codiceProfiloInvestimento"	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="codiceUT1_VA" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv1_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin1_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax1_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>	
		   				<field-map attributeName="codiceUT2_VA" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv2_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin2_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax2_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>	
		   				<field-map attributeName="codiceUT3_VA" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInv3_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMin3_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
		   				<field-map attributeName="percMax3_VA"     	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>	
		   				<field-map attributeName="codiceUtTFR_VA" 	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   				<field-map attributeName="percInvTFR_VA"    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		   					valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>		   					
		   		</nested-mapping>
		</field-map>
   </output-mapping> 
 </rule> 
</rules> 