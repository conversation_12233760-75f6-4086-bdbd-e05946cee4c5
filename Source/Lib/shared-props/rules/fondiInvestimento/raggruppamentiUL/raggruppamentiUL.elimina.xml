<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELIMINA-RAGGRUPPAMENTO</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSE078</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
  	<logApp>true</logApp>
	<logAppServDesc>ELIMINA RAGGRUPPAMENTI</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
  
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSE078 Lunghezza: 150 + 10 + 1824 = 1984  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiRequestDTO" nomeKeyERR="erroriRaggruppamenti"> 
   <!-- input : 10 car --> 
   <field-map attributeName="codiceRaggruppamento"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiResponseDTO"> 
      <!-- output :10  car -->      
        <field-map attributeName="codiceRaggruppamento"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   </output-mapping> 
 </rule> 
</rules> 
