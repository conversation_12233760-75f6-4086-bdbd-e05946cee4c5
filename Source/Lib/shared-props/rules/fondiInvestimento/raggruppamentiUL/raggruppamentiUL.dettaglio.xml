<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
	<rule> 
		<id>DETTAGLIO-RAGGRUPPAMENTO</id> 
		<initialProgram>WNDISPC0</initialProgram> 
		<initialTransaction>SB00</initialTransaction> 
		<program>DWLSE075</program>     
		<transaction>SB00</transaction> 
		<connectorId>A05TARE</connectorId> 
		<multipleTransaction>true</multipleTransaction> 
		<!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
		<pageRequestField/> 
		<limitPage>99</limitPage> 
		<moreDataField/> 
		<moreDataEndValue>1</moreDataEndValue> 
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
		<pastedFields> 
			<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
		</pastedFields>      
  
		<!-- Definizione commarea: DWCSE075 Lunghezza: 150 + 857 + 1824 = 2831  -->  
	
		<input-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiRequestDTO" nomeKeyERR="erroriRaggruppamenti"> 
   			<!-- input : 10 car --> 
			<field-map attributeName="codiceRaggruppamento"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
		</input-mapping> 
	
		<output-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiResponseDTO"> 
			<!-- output :10 + 848 = 858 car -->      
			<field-map attributeName="codiceRaggruppamento"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   	    
			<field-map attributeName="descrizioneRaggruppamento"  	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataInizioRaggruppamento"   	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/>
			<field-map attributeName="dataFineRaggruppamento"     	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/> 
			<field-map attributeName="calcoloDecorrenza" 		 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="codiceCalendario"  	 	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="descrizioneCalendario"  	 	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioDis"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="periodicitaCostoGestione"  	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="periodicitaManageFee"  	 	 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="giorniAggInc"  	 	 		 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
   	    
			<field-map attributeName="ggInvestimentoQuote"  	 	 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggEstrazioneQuote"   	 		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggDisinvestimentoQuote"  		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggDisinvEstrazQuote"   		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flSin" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSin"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSin"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flSca" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSca"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSca"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flRiall" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggRiall"  	 	 			 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioRiall"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaFondiRaggruppamento" length="570" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.FondiRaggruppamentoValoriDTO" iterations="10" blockLength="57"> 
					<field-map attributeName="codiceFondoRaggr"		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descFondoRaggr"       length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="percFondoRaggr"       length="7" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
						valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
			   </nested-mapping>
			</field-map>
			<field-map attributeName="listaModPagamento" length="40">
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.ModPagamentoFondiInvDTO" iterations="10" blockLength="04">
					<field-map attributeName="codModPagamento"       		length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="ggAggiuntiviPerModPagamento"	length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceCalAperMercati"   	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="descrCalAperMercati"   	 	 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="calcoloDecorrenzaDisinv" 		 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="sceltaCalendarioDecRadio" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="ggDecorrenza"  				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flDataDecorrenzaBloccata" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riferimentoDecorrenza" 	 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
		
	</rule> 
</rules>