<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
	<rule> 
		<id>MODIFICA-RAGGRUPPAMENTO</id> 
		<initialProgram>WNDISPC0</initialProgram> 
		<initialTransaction>SB00</initialTransaction> 
		<program>DWLSE077</program>     
		<transaction>SB00</transaction> 
		<connectorId>A05TARE</connectorId> 
		<logApp>true</logApp>
		<logAppServDesc>MODIFICA RAGGRUPPAMENTI</logAppServDesc>
		<areaFunzionale>FONDI INVESTIMENTO-GESTIONE LINKED</areaFunzionale>
		
		<multipleTransaction>true</multipleTransaction> 
		
		<!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
		<pageRequestField/> 
		<limitPage>99</limitPage> 
		<moreDataField/> 
		<moreDataEndValue>1</moreDataEndValue> 
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
		<pastedFields> 
		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
		</pastedFields>      
		
		<!-- Definizione commarea: DWCSE077 Lunghezza: 150 + 1096 + 1824 = 3070 -->  
		<input-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiRequestDTO" nomeKeyERR="erroriRaggruppamenti" >
			<!-- input : 818 car --> 
			<field-map attributeName="codiceRaggruppamento"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="descrizioneRaggruppamento"  	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataInizioRaggruppamento"   	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				valida="true" natura="Data"  nomeAttributoERR="dataInizioRaggruppamento" nomeKeyERR="erroriRaggruppamenti"/>
			<field-map attributeName="dataFineRaggruppamento"     	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				valida="true" natura="Data"  nomeAttributoERR="dataFineRaggruppamento" nomeKeyERR="erroriRaggruppamenti"/> 
			<field-map attributeName="calcoloDecorrenza" 		 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="codiceCalendario"  	 	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceCalendarioDis"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="periodicitaCostoGestione"  	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="periodicitaManageFee"  	 	 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="giorniAggInc"  	 	 		 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggInvestimentoQuote"  	 	 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggEstrazioneQuote"   	 		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggDisinvestimentoQuote"  		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggDisinvEstrazQuote"   		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flSin" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSin"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSin"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flSca" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSca"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSca"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flRiall" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggRiall"  	 	 			 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioRiall"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaFondiRaggruppamento" length="570" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.FondiRaggruppamentoValoriDTO" iterations="10" blockLength="57"> 
					<field-map attributeName="codiceFondoRaggr"		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descFondoRaggr"       length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="percFondoRaggr"       length="7" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
						valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"    nomeAttributoERR="percFondoRaggr" nomeKeyERR="erroriRaggruppamenti" />
				</nested-mapping>
			</field-map> 
			<field-map attributeName="listaModPagamento" length="40" > 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.ModPagamentoFondiInvDTO" iterations="10" blockLength="57"> 
					<field-map attributeName="codModPagamento"       		length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="ggAggiuntiviPerModPagamento"	length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceCalAperMercati"   	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="descrCalAperMercati"   	 	 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="calcoloDecorrenzaDisinv" 		 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sceltaCalendarioDecRadio" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="ggDecorrenza"  				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flDataDecorrenzaBloccata" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riferimentoDecorrenza" 	 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>
		
		
		<output-mapping className="it.sistinf.albedoweb.fondiInvestimento.raggruppamenti.dto.RaggruppamentiResponseDTO">
			<!-- output :818 + 288 = 1106 car -->
			<field-map attributeName="codiceRaggruppamento"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descrizioneRaggruppamento"  	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInizioRaggruppamento"   	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/>
			<field-map attributeName="dataFineRaggruppamento"     	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/> 
			<field-map attributeName="calcoloDecorrenza" 		 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceCalendario"  	 	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceCalendarioDis"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="periodicitaCostoGestione"  	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="periodicitaManageFee"  	 	 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="giorniAggInc"  	 	 		 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggInvestimentoQuote"  	 	 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggEstrazioneQuote"   	 		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggDisinvestimentoQuote"  		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggDisinvEstrazQuote"   		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flSin" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSin"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSin"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flSca" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggSca"  	 	 				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioSca"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flRiall" 		 	 			 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ggRiall"  	 	 			 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codiceCalendarioRiall"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="listaFondiRaggruppamento" length="570">
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.FondiRaggruppamentoValoriDTO" iterations="10" blockLength="57"> 
					<field-map attributeName="codiceFondoRaggr"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="descFondoRaggr"       	length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="percFondoRaggr"       	length="7" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
						valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="listaModPagamento" length="40"> 
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.ModPagamentoFondiInvDTO" iterations="10" blockLength="04"> 
					<field-map attributeName="codModPagamento"       		length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="ggAggiuntiviPerModPagamento"	length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceCalAperMercati"   	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="descrCalAperMercati"   	 	 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
			<field-map attributeName="calcoloDecorrenzaDisinv" 		 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sceltaCalendarioDecRadio" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="ggDecorrenza"  				 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flDataDecorrenzaBloccata" 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riferimentoDecorrenza" 	 	 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- campi di errore -->
			<field-map attributeName="codiceRaggruppamentoCdErr"  	 	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
			<field-map attributeName="descrizioneRaggruppamentoCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataInizioRaggruppamentoCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataFineRaggruppamentoCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
			<field-map attributeName="calcoloDecorrenzaCdErr"  	 		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
			<field-map attributeName="codiceCalendarioCdErr"  	 		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceCalendarioDisCdErr"  	 	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="periodicitaCostoGestioneCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
			<field-map attributeName="periodicitaManageFeeCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="giorniAggIncCdErr"  	 			 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggInvestimentoQuoteCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggEstrazioneQuoteCdErr"  		 	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggDisinvestimentoQuoteCdErr"  	 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggDisinvEstrazQuoteCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggSinCdErr"  		 				 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceCalendarioSinCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggScaCdErr"  		 				 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceCalendarioScaCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggRiallCdErr"  		 			 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceCalendarioRiallCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr1CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr1CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr1CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr2CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr2CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr2CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr3CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr3CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr3CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr4CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr4CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr4CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr5CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr5CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr5CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr6CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr6CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr6CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr7CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr7CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr7CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr8CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr8CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr8CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr9CdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr9CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr9CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceFondoRaggr10CdErr"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descFondoRaggr10CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percFondoRaggr10CdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="listaModPagamentoErr" length="80">
				<nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.dto.ModPagamentoFondiInvDTO" iterations="10" blockLength="08">
					<field-map attributeName="codModPagamentoErr"       		length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="ggAggiuntiviPerModPagamentoErr"	length="04" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map> 
			<field-map attributeName="calcoloDecorrenzaDisinvCdErr"  	         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ggDecorrenzaCdErr"  		 length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping> 
	</rule> 
</rules>  
