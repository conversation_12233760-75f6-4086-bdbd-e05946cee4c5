<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-PROFILO-INVESTIMENTO</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLPFI</program>     
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSLPFI Lunghezza: 150 + 21055 + 1824 = 23029  -->  
  <input-mapping className="it.sistinf.albedoweb.fondiInvestimento.profiloInvestimento.dto.ProfiloInvestimentoRequestDTO" nomeKeyERR="erroriRaggruppamenti" > 
   <!-- input : 50 car --> 
   <field-map attributeName="codProfiloInvestimentoRicerca"  length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   <field-map attributeName="descProfiloInvestimentoRicerca" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.fondiInvestimento.profiloInvestimento.dto.ProfiloInvestimentoResponseDTO"> 
      <!-- output :50 + 21005 = 21055 car -->      
      <field-map attributeName="codProfiloInvestimentoRicerca"  	 length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   	  <field-map attributeName="descProfiloInvestimentoRicerca" 	 length="40"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
      
      <field-map attributeName="numElementiTrovati" 		 length="4"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flLimite"         			 length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaProfiliInvestimento" length="21000"> 
		    <nested-mapping className="it.sistinf.albedoweb.fondiInvestimento.profiloInvestimento.dto.ProfiloInvestimentoValoriDTO" iterations="300" blockLength="70"> 
		          <field-map attributeName="codiceProfiloInvestimento"       length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="descrizioneProfiloInvestimento"  length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="dataInizioProfiloInvestimento"   length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		          	natura="Data"/>
		          <field-map attributeName="dataFineProfiloInvestimento"     length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" 
		          	natura="Data"/>
		   </nested-mapping>
     </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 
