<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
	<rule> 
		<id>DETTAGLIO-PROFILO-INVESTIMENTO</id> 
		<initialProgram>WNDISPC0</initialProgram> 
		<initialTransaction>SB00</initialTransaction> 
		<program>DWLSE181</program>     
		<transaction>SB00</transaction> 
		<connectorId>A05TARE</connectorId> 
		<multipleTransaction>true</multipleTransaction> 
		<!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
		<pageRequestField/> 
		<limitPage>99</limitPage> 
		<moreDataField/> 
		<moreDataEndValue>1</moreDataEndValue> 
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
		<pastedFields> 
			<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
		</pastedFields>      
  
		<!-- Definizione commarea: DWCSE075 Lunghezza: 150 + 104 + 1824 = 2078  -->  
	
		<input-mapping className="it.sistinf.albedoweb.fondiInvestimento.profiloInvestimento.dto.ProfiloInvestimentoRequestDTO" nomeKeyERR="erroriProfiloInvestimento"> 
   			<!-- input : 10 car --> 
			<field-map attributeName="codiceProfiloInvestimento"  	length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
		</input-mapping> 
	
		<output-mapping className="it.sistinf.albedoweb.fondiInvestimento.profiloInvestimento.dto.ProfiloInvestimentoResponseDTO"> 
			<!-- output :10 + 94 + = 104 car -->      
			<field-map attributeName="codiceProfiloInvestimento"  	 	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   	    
			<field-map attributeName="descrizioneProfiloInvestimento"  	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataInizioProfiloInvestimento"   	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/>
			<field-map attributeName="dataFineProfiloInvestimento"     	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/> 
			<field-map attributeName="periodicitaCostoGestione"  	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			<field-map attributeName="periodicitaManageFee"  	 	 length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
   	    
			<field-map attributeName="ggEstrazioneQuote"   	 		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ggDisinvEstrazQuote"   		 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="regolaDateInvest"   		     length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flProfiloPredefinito"   		 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="rischioFinanziario"   		 length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numMinFondiInterni"   		 length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numMaxFondiInterni"   		 length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numMinFondiEsterni"   		 length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numMaxFondiEsterni"   		 length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			
		</output-mapping>
		
	</rule> 
</rules>
