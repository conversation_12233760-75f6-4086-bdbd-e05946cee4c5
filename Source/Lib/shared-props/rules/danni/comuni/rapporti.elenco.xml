<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-POLIZZE-DANNI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>CWLSL001</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaRequestDTO">
			<!-- input : 13 car -->
			<field-map attributeName="codiceCliente"  							length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.rapporti.dto.RapportoRicercaResponseDTO">
		    <!-- output : 22017 car -->
		    <field-map attributeName="codiceCliente"						length="13"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="listaRapporti"						length="22004">
				<nested-mapping className="it.sistinf.common.albedo.utility.BaseAlbedoListaElementiDTO" iterations="0" blockLength="22004">
					<field-map attributeName="numElementi" 					length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0" />
			      	<field-map attributeName="flAltri" 						length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      	<field-map attributeName="listaDaCommarea" 				length="22000">
			      		<nested-mapping className="it.sistinf.albedoweb.danni.polizza.dto.PolizzaDanniDTO" iterations="200" blockLength="110">	
			      			<field-map attributeName="codSocieta"  			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			      			<field-map attributeName="codProdotto" 			length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			      			<field-map attributeName="descProdotto"			length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      			<field-map attributeName="targa" 				length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      			<field-map attributeName="numPolizza" 			length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			      			<field-map attributeName="dataDecorrenza" 		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      			<field-map attributeName="stato" 				length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			      		</nested-mapping>
			      	</field-map>
				</nested-mapping>		
			</field-map>
 		</output-mapping>
	</rule>
</rules>