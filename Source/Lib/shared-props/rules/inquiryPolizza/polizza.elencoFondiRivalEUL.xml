<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-FONDI-RIVAL-E-UL</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE543</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>APRI-POLIZZA</logAppServDesc>
		<areaFunzionale>POLIZZA INDIVIDUALE</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.fondi.dto.FondiRivalutazioneEULRequestDTO">
			<!-- Totali 176 car (150 + 26) -->
			<!-- 26 car -->
   			<field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.fondi.dto.FondiRivalutazioneEULResponseDTO">
		    <!-- Totali 14429 car (150 + 12455 + 1824) --> 
		    <!-- 12429 + 26 = 12455 car -->
		    <!-- Campi di input 26 car -->
		    <field-map attributeName="numCategoria"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- FONDI RIVALUTAZIONE 84 car -->
			<field-map attributeName="listaFondiRivalutazione" length="84">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.ListaFondiRivalutazioneDTO" iterations="0" blockLength="84">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
					<field-map attributeName="fondiRivalutazione" length="81">
						<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondiRivalutazioneDTO" iterations="1" blockLength="81">  
							<field-map attributeName="codiceFondoRiv"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneFondoRiv"    	 length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="dataRivalutazione" 	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="valorePrestazRival"    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="percAllocRival"    	 length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<!-- FONDI UNIT-LINKED  -->
			<field-map attributeName="listaFondiUnitLinked" length="12345">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.ListaFondiUnitLinkedDTO" iterations="0" blockLength="12345">
					<field-map attributeName="totalePrestazUL"    	 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="totaleNumeroQuoteUL"    	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="totalePercInvestUL"    	length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="totalePercControvalUL"    length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>        
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
					<field-map attributeName="fondiUnitLinked" length="12300">
						<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondiUnitLinkedDTO" iterations="100" blockLength="123">  
							<field-map attributeName="codiceFondoUL"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneFondoUL"    	 length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="dataValorizzazione" 	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="valoreQuotaUL"    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="numeroQuoteUL"    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="valorePrestazUL"    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="percInvestUL"    	 length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<field-map attributeName="percControvalUL"    	 length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<field-map attributeName="percAllocControvalUL"    	 length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
	</output-mapping>
     </rule>
</rules>