<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-STORICO-POSIZIONE-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE273</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    <!-- Definizione commarea: VWLSE273 Lunghezza: 150 + 2973 + 1824 = 4947  -->
		<input-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UnitaTecnicaDettaglioRequestDTO">
			<!-- input: 31 car -->
		    <field-map attributeName="categoria"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="progressivoStoricoUT"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UnitaTecnicaDettaglioResponseDTO">
		    <!-- Output: 31 + 4 + 495 + 191 + 316 + 232 + 720 + 92 + 92 + 800 + 1098 + 464 + 147 = 4682 car -->
		    <field-map attributeName="categoria"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"    			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="progressivoStoricoUT"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
<!-- 		    <field-map attributeName="dettaglioPosizione" length="2942" > -->
<!--             	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="0" blockLength="2942"> -->
			<field-map attributeName="dettaglioPosizione" length="4651" >
             	<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="0" blockLength="4651">
            		<field-map attributeName="progressivoAttualeUT"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<!-- Dati UT 495 car -->
					<field-map attributeName="totaleUT"              length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="unitaTecnica"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statusUT"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="causaleUT"        	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descCausaleUT"         length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataCreazioneUT"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>
					<field-map attributeName="agenziaGest"        	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descAgenziaGest"       length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="subagente"             length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="descSubagente"         length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="collocUT"          	 length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="descCollocUT"          length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="premioLim"          	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="convenzioneUT"     	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="descConvenzioneUT"     length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="visita"    		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="decorrenzaUT"    	     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>			
					<field-map attributeName="descrizioneDurataUT"   length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
		            <field-map attributeName="durataUT"              length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="dataScadenzaUT"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>			
					<field-map attributeName="eta"    		         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
					<field-map attributeName="defisc"    		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>	
					<field-map attributeName="descDurataPagam"       length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
		            <field-map attributeName="durataPagam"           length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
		            <field-map attributeName="dataPerfezUT"    	     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>			
		            <field-map attributeName="rateoIniz"    	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
		            <field-map attributeName="carenza"    	         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
		            <field-map attributeName="dataEmissioneUT"  	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		            <field-map attributeName="dataCompetenzaUT" 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		            <field-map attributeName="costoPolizza"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="costoQuietanza"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="costoPolizzaAgenzia"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="speseMediche"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="dataRivalutazioneUT"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		            <field-map attributeName="descFondoUT"           length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="fondoUT"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="dataInvQuote"      	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		             <field-map attributeName="dataAggQuote"      	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		            <field-map attributeName="dataVariazioneUT"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
		            	natura="Data"/>
		            <field-map attributeName="dataIncassoQuietanza"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		            	natura="Data"/>
		            <field-map attributeName="ultimoProgrStorico"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="quietanzaPremio"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		            	natura="Data"/>
		            <field-map attributeName="quietanzaRendita"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
		            	natura="Data"/>
		            <field-map attributeName="userCreaz"             length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		            <field-map attributeName="codVarUT"       		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="doublePremium"       	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="mmPrimarata"       	 length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="utRendita"       	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="dataTime"       	 	 length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<!-- Dati amministrativi 191 car -->
					<field-map attributeName="coass"       	         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descCoass"       	     length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="riass"       	         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descRiass"       	     length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="annoFrazionamento"     length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nuovoFrazionamento"    length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descFrazVariato"       length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="modalitaPagamento"     length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descModalitaPagamento" length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="ibanNazione"           length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="ibanCCN"           	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="ibanCin"           	 length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="abi"       	         length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cabCed"       	     length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="contoCorr"       	     length="12" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statisticaAmm1"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statisticaAmm2"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statisticaAmm3"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statisticaAmm4"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<!-- Dati premi  316 car -->
					<field-map attributeName="tipoPremio"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premioNetto"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="premioRata"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioRataSuccessivo" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>            
		            <field-map attributeName="frazRendita"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="premioPuro"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="ultimoPremioPuro"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="premioInventario"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="ultimoPremioInv"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="premioTariffa"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="ultimoPremioTariffa"  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="capitaleIniz"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="capitale"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="capitaleRivalutato"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="premioAgg"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="premioAggRivalutato"  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="capOpzioneIni"        length="8"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="capOpzione"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="percentualeImp"       length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
		            <field-map attributeName="fondoRivalutato"      length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="frazPremio"           length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="interessiFrazionati"  length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
		            <field-map attributeName="cessioneLegale"       length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="tipoPremio2"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="appendice"            length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="decrescenza"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="abbuonoTotale"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="abbuonoAnnuo"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="anniAbbuono"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="scadenzaAntic"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="tipoGaranzia"         length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="combinata"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="quotaCaricoAss"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		            <field-map attributeName="bloq"                 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="percAbbProv"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<!-- Dati provvigioni 232 car -->
					<field-map attributeName="tipoProvV" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="modalV"   		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>            
					<field-map attributeName="provvig10V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig11V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig20V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig21V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="incassi10V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="incassi11V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioComputo"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="premioProvv" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoProvN" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="modalN"   		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>            
					<field-map attributeName="provvig10N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig11N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig20N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvig21N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="incassi10N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="incassi11N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoProvOldV" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provvIncrV" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoProvOldN" 	    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provvIncrN" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="ricalcolo" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="metodoProv" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <!-- Complementari 720 car -->
		            <field-map attributeName="garanzieComplementari" length="720">
						<nested-mapping className="it.sistinf.albedoweb.polizza.dto.GaranzieComplementariDTO" iterations="6" blockLength="120">  
							<field-map attributeName="titoloGaranzia"   	 length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoGaranzia"     	 length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="tassoPremio" 		   	 length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
							<field-map attributeName="valorePremio"     	 length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>	
							<field-map attributeName="tipoPremio"		   	 length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="opzionePrestazione"    length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="valorePrestazione" 	 length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="tipoPrestazione"     	 length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	
						</nested-mapping>
					</field-map>
					<!-- Sovrapremi 92 car -->
					 <field-map attributeName="tipoSanitario"  		     	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="tassoSanitario"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 <field-map attributeName="premioSanitario"  		     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="tipoProfessione"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="tassoProfessione"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 <field-map attributeName="premioProfessione"  		     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="tipoSport"  		     		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="tassoSport"  		    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 <field-map attributeName="premioSport"  		    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="tipoAltro"  		     		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="tassoAltro"  		    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 <field-map attributeName="premioAltro"  		    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="sopramortalita" 		    	 length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="4" numDecimali="2" separatoreMigliaia="false"/>
					 <!-- Vincoli 92 car -->
					 <field-map attributeName="tipoVincolo"  		     	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="prestazioneVincolo"  		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="dataScadenzaVincolo"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Data"/>	
					 <field-map attributeName="importoRata"  		     	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					 <field-map attributeName="tassoInteressi"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 <field-map attributeName="dataDecorrenzaAmmortamento"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Data"/>	
					 <field-map attributeName="dataScadenzaAmmortamento"  	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 	natura="Data"/>	
					 <field-map attributeName="aFavoreDi"  		    	 	 length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <field-map attributeName="flagVincolo"  		    	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					 <!-- Clausole 800 car -->
					 <field-map attributeName="elencoClausole" length="350">
						<nested-mapping className="it.sistinf.albedoweb.polizza.dto.ClausoleValoriDTO" iterations="10" blockLength="35">  
							<field-map attributeName="codiceClausola"   	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
							<field-map attributeName="descClausola"     	 length="25" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
						</nested-mapping>
					</field-map>
					<field-map attributeName="elencoTestoLibero" length="450">
						<nested-mapping className="it.sistinf.albedoweb.polizza.dto.TestoLiberoDTO" iterations="6" blockLength="75">  
							<field-map attributeName="descrizTestoLibero"     	 length="75" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>					
						</nested-mapping>
					</field-map>
					<!-- Riassicurazione 1098 car -->					
					<field-map attributeName="riassicurazioneStorico"     	 		length="1098" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<!-- Dettaglio riassicurazione 464 car -->
					<field-map attributeName="dettaglioRiassicurazioneStorico"     length="464" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<!-- Sovrapremi 147 car -->
					<field-map attributeName="sovrapremi" length="147">						
						<nested-mapping className="it.sistinf.albedoweb.polizza.dto.SovrapremiDTO" iterations="0" blockLength="147">
							<field-map attributeName="dataSanitario"  	  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniSanitario"	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tipoSanitario2"	  length="1"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tassoSanitario2"    length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 		<field-map attributeName="premioSanitario2"	  length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="dataSanitario2"  	  length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniSanitario2"	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tipoSanitario3"	  length="1"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tassoSanitario3"    length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 		<field-map attributeName="premioSanitario3"	  length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="dataSanitario3"  	  length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniSanitario3"	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="dataProfessione"    length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniProfessione"	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tipoProfessioneI"	  length="1"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="tassoProfessioneI"  length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					 		<field-map attributeName="premioProfessioneI" length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="dataProfessioneI"   length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniProfessioneI"	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="dataSport"	  	  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniSport"	  	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					 		<field-map attributeName="dataAltro"  	  	  length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					 			natura="Data"/>
					 		<field-map attributeName="anniAltro"	  	  length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
						</nested-mapping>					
					</field-map>		
				</nested-mapping>				
			</field-map>		  
		</output-mapping>
     </rule>
</rules>
