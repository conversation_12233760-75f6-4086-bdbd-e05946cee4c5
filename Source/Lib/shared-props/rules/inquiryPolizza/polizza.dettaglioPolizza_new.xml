<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>NEW-DETTAGLIO-POLIZZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0033</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>APRI-POLIZZA</logAppServDesc>
		<areaFunzionale>POLIZZA INDIVIDUALE</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   <!-- Definizione commarea: WSER0033 Lunghezza: 150 + 7687 + 1824 = 9661  -->
		<input-mapping className="it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioRequestDTO" nomeKeyERR="erroriRapportiVita">
			<!-- input: 27 car -->
		    <field-map attributeName="categoria"      		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"   		length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza"  		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
            	valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroColl" nomeKeyERR="erroriRapportiVita"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numeroPolizza" nomeKeyERR="erroriRapportiVita"/>
			<field-map attributeName="progressivoAttuale"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioResponseDTO">
		    <!-- Output: 27 + 18173  = 18200 car -->
		    <!-- Campi di input 27 car -->
		    <field-map attributeName="categoria"      		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"   		length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza"  	    length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="progressivoAttuale"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- Campi di output 104 + 911 + 105 + 106 + 472 + 191 + 330 + 232 + 11 + 108 + 10 + 720 + 92 + 92 + 350 + 450 + 1042 + 464 + 12 + 91 + 93 + 11510 + 439 + 24 + 10 + 34 + 66 + 4 = 18173 -->
			<!-- Dati principali 104 car -->
			<field-map attributeName="funzione"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categoriaPcp"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaPcp"            length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroCollPcp"         length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="testa"                 length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="codiceProdotto"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codiceProdottoN"  	 length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
	    	<field-map attributeName="descProdotto"  		 length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="progrStorico"  		 length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="agenziaProposta"    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroProposta" 		 length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="categoriaTesto" 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <!-- Dati identificativi 911 car -->			
			<field-map attributeName="status"        		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="causale"        		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="codiceVariazione"   	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codUltimaVariazione"   length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descUltimaVariazione"  length="43" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCausale"        	 length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="flagTrasformata"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="trasformataInAge"  	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="trasformataInPolizza"  length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataEmissione"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="dataOperazione"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="dataCreazione"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="userCreazione"    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="progetto"         	 length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="convenzione"     		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descConvenzione"       length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="dataCompetenza"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>	
			<field-map attributeName="sconto"                length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descSconto"            length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="orgagen"               length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="divisa"  		         length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="colloc"          		 length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descColloc"            length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="canaleAcq"     		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descCanaleAcq"         length="41" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="canaleGest"     		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descCanaleGest"        length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="canaleSec"     		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descCanaleSec"         length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="fondoUL"     		     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descFondoUL"           length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="percorsoFondo"     	 length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descPercorsoFondo"     length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="dataDecorrenza"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>							
			<field-map attributeName="descrizioneDurata"     length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="durataAnni"            length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="durataMesi"            length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataScadenza" 		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>	
			<field-map attributeName="labValCompl"   		 length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="valCompl"   		     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="labValAttuale"   		 length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="valAttuale"   	 	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="labRataMensile"     	 length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="rataMensile"   		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="defiscalizzata"  		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="premiRicorrenti"	     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="periodicita"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoRivalutazione"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descTipoRivalutazione" length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="labAvvisoInsRID"       length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="avvisoInsRID"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="labFondo"              length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="fondo"                 length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="labDurataPur"          length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="durataPur"             length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="obCopTcm" 			 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="obCopIp" 				 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="dataVariazione" 		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>	
            <field-map attributeName="progrStoricoId" 		 length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="labAziendaMatr" 		 length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
            <field-map attributeName="compagnia"             length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="matricola"             length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="statistico1"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="statistico2"  		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	    	
            <field-map attributeName="statistico3"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="statistico4"    		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="codVar"       		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<!-- Dati domicilio 105 car -->
	    	<field-map attributeName="nominativoDom"       	 length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="indirizzoDom"       	 length="35" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="localitaDom"       	 length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="capDom"           	 length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="provinciaDom"       	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="statoDom"         	 length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<!-- Dati Antiriciclaggio 106 car -->
	    	<field-map attributeName="telefonoAR"         	 length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="numDocumentoAR"        length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="tipoDocumentoAR"       length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="dataRilascioAR"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	    		natura="Data"/>	
	    	<field-map attributeName="locRilascioAR"         length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="enteRilascioAR"        length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="cabAR"         	     length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="gruppoAR"         	 length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="sottogruppoAR"         length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="atecoAR"    	     	 length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati UT 472 car -->
			<field-map attributeName="totaleUT"              length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="unitaTecnica"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statusUT"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="causaleUT"        	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descCausaleUT"         length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="dataCreazioneUT"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>	
			<field-map attributeName="agenziaGest"        	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descAgenziaGest"       length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="subagente"             length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descSubagente"         length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="collocUT"          	 length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="descCollocUT"          length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="premioLim"          	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="convenzioneUT"     	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="descConvenzioneUT"     length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="visita"    		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
			<field-map attributeName="decorrenzaUT"    	     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>				
			<field-map attributeName="descrizioneDurataUT"   length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="durataUT"              length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataScadenzaUT"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>				
			<field-map attributeName="eta"    		         length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
			<field-map attributeName="sessoAss"    		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="defisc"    		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>	
			<field-map attributeName="descDurataPagam"       length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="durataPagam"           length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		
            <field-map attributeName="dataPerfezUT"    	     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>				
            <field-map attributeName="rateoIniz"    	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="carenza"    	         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>			
            <field-map attributeName="dataEmissioneUT"  	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>	
            <field-map attributeName="dataCompetenzaUT" 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>	
            <field-map attributeName="costoPolizza"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="costoQuietanza"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="costoPolizzaAgenzia"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="speseMediche"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="dataRivalutazioneUT"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>	
            <field-map attributeName="descFondoUT"           length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="fondoUT"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="dataVariazioneUT"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
            	natura="Data"/>	
            <field-map attributeName="dataIncassoQuietanza"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
            	natura="Data"/>	
            <field-map attributeName="ultimoProgrStorico"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="quietanzaPremio"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "
            	natura="Data"/>
            <field-map attributeName="quietanzaRendita"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="userCreaz"             length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="codVarUT"       		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="doublePremium"       	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="mmPrimarata"       	 length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="utRendita"       	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataTime"       	 	 length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati amministrativi 191 car -->
			<field-map attributeName="coass"       	         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descCoass"       	     length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riass"       	         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descRiass"       	     length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="annoFrazionamento"     length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="nuovoFrazionamento"    length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descFrazVariato"       length="25" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="modalitaPagamento"     length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descModalitaPagamento" length="20" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanNazione"           length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCCN"           	 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCin"           	 length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="abi"       	         length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cab"       	         length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="contoCorr"       	     length="12" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statisticaAmm1"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statisticaAmm2"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statisticaAmm3"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statisticaAmm4"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati premi  330 car --> 
			<field-map attributeName="tipoPremio"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="premioNetto"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioRata"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
             <field-map attributeName="pianoPur"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioRataSuccessivo" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>            
            <field-map attributeName="frazRendita"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="premioPuro"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="ultimoPremioPuro"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioInventario"     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="ultimoPremioInv"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioTariffa"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="ultimoPremioTariffa"  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capitaleIniz"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capitale"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capitaleRivalutato"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioAgg"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="premioAggRivalutato"  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="capOpzioneIni"        length="8"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="capOpzione"           length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="percentualeImp"       length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
            <field-map attributeName="fondoRivalutato"      length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="frazPremio"           length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="interessiFrazionati"  length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
            <field-map attributeName="cessioneLegale"       length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="tipoPremio2"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="appendice"            length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="decrescenza"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="abbuonoTotale"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="abbuonoAnnuo"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="anniAbbuono"          length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="scadenzaAntic"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="tipoGaranzia"         length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="combinata"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="quietanzaCaricoAss"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="bloq"                 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="percAbbProv"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati provvigioni 232 car -->
			<field-map attributeName="tipoProvV" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="modalV"   		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>            
			<field-map attributeName="provvig10V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig11V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig20V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig21V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="incassi10V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="incassi11V" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioComputo"        length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premioProvv" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="tipoProvN" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="modalN"   		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>            
			<field-map attributeName="provvig10N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig11N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig20N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="provvig21N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="incassi10N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="incassi11N" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="tipoProvOldV" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="provvIncrV" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="tipoProvOldN" 	    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="provvIncrN" 		    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="ricalcolo" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="metodoProv" 		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<!-- Dati anagrafici 11 car -->
			<field-map attributeName="numAnagrafichePresenti"  length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="codiceCustomer"  		   length="9" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>            
            
            <!-- Dati riscatto 108 car -->
	  	    <field-map attributeName="dataUltimoPremioVersato"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Data"/>	
	  	    <field-map attributeName="cumuloPremiVersati" 		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="valoreRiscatto"   		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="valoreRiscattoLordo" 		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="importoLiquidato" 		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="penale"           		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="ritenutaAcconto"  		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	  	    <field-map attributeName="ritenutaVisentini" 		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
	  	    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
  	        <!-- Area flag 10 car -->
  	        <field-map attributeName="flUnitLinked"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flDatiRiscatto"    		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoProdotto"    		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoPrestazione"    	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoTariffa"    		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoPremio"	    		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoPIP"    	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTipoGestSep"	   		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flTranching"	   		 	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    <field-map attributeName="flModCosti"	   		 	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	  	    
	  	     <!-- Complementari 720 car --> 
            <field-map attributeName="garanzieComplementari" length="720">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.GaranzieComplementariDTO" iterations="6" blockLength="120">  
					<field-map attributeName="titoloGaranzia"   	 length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoGaranzia"     	 length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="tassoPremio" 		   	 length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					<field-map attributeName="valorePremio"     	 length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>	
					<field-map attributeName="tipoPremio"		   	 length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="opzionePrestazione"    length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="valorePrestazione" 	 length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoPrestazione"     	 length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
			 <!-- Sovrapremi 92 car -->	
			 <field-map attributeName="tipoSanitario"  		     	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="tassoSanitario"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			 <field-map attributeName="premioSanitario"  		     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="tipoProfessione"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="tassoProfessione"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			 <field-map attributeName="premioProfessione"  		     length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="tipoSport"  		     		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="tassoSport"  		    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			 <field-map attributeName="premioSport"  		    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="tipoAltro"  		     		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="tassoAltro"  		    	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			 <field-map attributeName="premioAltro"  		    	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="sopramortalita" 		    	 length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="4" numDecimali="2" separatoreMigliaia="false"/>
			 <!-- Vincoli 92 car -->
			 <field-map attributeName="tipoVincolo"  		     	 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="prestazioneVincolo"  		 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="dataScadenzaVincolo"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Data"/>	
			 <field-map attributeName="importoRata"  		     	 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			 <field-map attributeName="tassoInteressi"  		     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			 <field-map attributeName="dataDecorrenzaAmmortamento"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Data"/>	
			 <field-map attributeName="dataScadenzaAmmortamento"  	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
			 	natura="Data"/>	
			 <field-map attributeName="aFavoreDi"  		    	 	 length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <field-map attributeName="flagVincolo"  		    	 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			 <!-- Clausole 350 car -->
			 <field-map attributeName="elencoClausole" length="350">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.ClausoleValoriDTO" iterations="10" blockLength="35">  
					<field-map attributeName="codiceClausola"   	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="00" offset="" padding=""/>
					<field-map attributeName="descClausola"     	 length="25" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>
			<!-- Testo libero 450 car -->
			<field-map attributeName="elencoTestoLibero" length="450">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.TestoLiberoDTO" iterations="6" blockLength="75">  
					<field-map attributeName="descrizTestoLibero"     	 length="75" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>					
				</nested-mapping>
			</field-map>
			<!-- Riassicurazione 1042 car -->
			<field-map attributeName="numeroRiassicurazione"  length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="elencoRiassicurazioni" length="1040">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.RiassicurazioniValoriDTO" iterations="10" blockLength="104">  
					<field-map attributeName="progrRecord"		   	 length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="codiceTrattato"     	 length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="codiceCompagnia"     	 length="3"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrCompagnia"     	 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descTrattatoRiass"   	 length="19" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="capitaleRiass"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazRiass"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
			<!-- Dettaglio riassicurazione 464 car --> 
			<field-map attributeName="dettaglioRiassicurazione" length="464">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.DettaglioRiassicurazioneDTO" iterations="0" blockLength="464">
					<field-map attributeName="posizioneRiass"				 length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
					<field-map attributeName="codiceTrattato"     	 		 length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descTrattato"     	 		 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceCompagnia"     	 		 length="3"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrCompagnia"     			 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descTrattatoRiass"   	 		 length="19" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="capitaleRiass"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazRiass"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioSanitario"	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioProfessione"	 	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioSport"	 		 	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioAltro"	 			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invaliditaPermanente" 			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniIncasso"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
				</nested-mapping>
			</field-map>
			<!-- TOTALE PREMI 112 car -->
			<field-map attributeName="primoPremioPagato"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="accumuloPremi"     	 		 	length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="totalePremiPagati"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="totalePrestazioniErogate"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="premiNettoCaricamenti"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>				
			<field-map attributeName="saldoPremiNettoCaricamenti"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>				
			<field-map attributeName="premiRischio"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>				
			<field-map attributeName="saldoPremiRischio"     	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>												
			<!-- Nuovi dati generali 91 car -->
			<field-map attributeName="flagPrestito"     	 length="1" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagRid"     			 length="1" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDecPolOrig"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="eta2"     			 length="5" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sesso2"     			 length="1" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ultimoPremioPagato"    length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="quietanzamentoPremio"  length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>									
			<field-map attributeName="deroga"     			 length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descDeroga"     		 length="25" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataPrimaIscrPrCom" 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<!-- dati fendac 93 car --> 
			<field-map attributeName="codMatricola"     	 length="12" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="statoMatricola"     	 length="3"  precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descMatricola"     	 length="20" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataNomina"    	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="dataPrimoVers"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="dataUltimoVers"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="capitaleGarantito"     length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="risMat3112"     		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<!-- dati vipensiono 11460 car -->
			<!-- dati vipensiono 11510 car -->
			<field-map attributeName="opzGestionaleVp"     	 length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descGestionaleVp"    	 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codPosizioneMaturataVp"   length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descPosizioneMaturataVp"   length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataUltRiallocVp"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>			
            <field-map attributeName="elencoFondiVipensiono" length="11400">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.VipensionoDTO" iterations="100" blockLength="114">  
					<field-map attributeName="fondoVp"   	 		 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descFondoVp"     	 	 length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="percAllocVp" 		   	 length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
					<field-map attributeName="dataValVp"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>
					<field-map attributeName="quotaVp"     		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="numQuoteVp"     		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valPosVp"     		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="percFondoVp" 		   	 length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<!-- Multiramo 113 + 324 + 2 = 439 car -->
			<field-map attributeName="flgPolizzaProrogata" 	 		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset=""  padding=""/>
			<field-map attributeName="profiloInvestimento"    		length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="opzioniFacoltative"    		length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoPercCedola"    		length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false"/>   
			<field-map attributeName="durataCedola"	    			length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="10" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="periodCedola"	    			length="2" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="obiettivoTakeProfit"			length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="obiettivoStopLoss"	    	length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAdesione"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="dataRevoca"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Data"/>
			<field-map attributeName="listaAttivitaFinanziarie" length="324">
				<nested-mapping className="it.sistinf.albedoweb.polizza.dto.ListaAttivitaFinanziarieDTO" iterations="0" blockLength="324">            
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
					<field-map attributeName="attivitaFinanziarie" length="320">
						<nested-mapping className="it.sistinf.albedoweb.polizza.dto.AttivitaFinanziarieDTO" iterations="5" blockLength="64">  
							<field-map attributeName="codiceTariffa"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneTariffa"    length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="percAllocIniziale" 	 length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<field-map attributeName="percAllocAttuale"    	 length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="frazionamentoPrimaRata"    	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<!--  NUOVI CAMPI SEZIONE PREMIO 24 car -->
			<field-map attributeName="ultimaQiuetEmessa"     		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataUltimaQiuetEmessa"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>
			<!-- Relazione benef 10 car -->
			<field-map attributeName="relazioneConBeneficiario"    	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			<!--  NUOVI CAMPI MULTIRAMO AGENZIA 34car -->
			<field-map attributeName="classePremio"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagRibilanciamento"   length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataCostiG"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataManagFee"   	 	 length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flagScudata"   	 	 length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="visibilityClassePremio"   	 	 length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="visibilityRibilanciamento"   	 	 length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="quotaAnnua" length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataAssunzione" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="contributoAzienda" length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="versVolontario" length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="conferimentoTfr" length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
	</output-mapping>
     </rule>
</rules>
    