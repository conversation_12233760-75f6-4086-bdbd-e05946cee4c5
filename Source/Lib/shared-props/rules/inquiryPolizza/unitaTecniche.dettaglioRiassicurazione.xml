<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-POSIZIONE-RIASS</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE274</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    <!-- Definizione commarea: VWLSE274 Lunghezza: 150 + 468 + 1824 = 6419  -->
		<input-mapping className="it.sistinf.albedoweb.riassicurazione.dto.RiassicurazioniDettaglioRequestDTO">
			<!-- input: 8 car -->
			<field-map attributeName="posizioneUT"      length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizioneRias"    length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.riassicurazione.dto.RiassicurazioniDettaglioResponseDTO">
		    <!-- Output: 8 + 464 = 472 car -->
		    <field-map attributeName="posizioneUT"      length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizioneRias"    length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="dettaglioPosizioneRiassicurazione" length="464" >
            	<nested-mapping className="it.sistinf.albedoweb.polizza.dto.DettaglioRiassicurazioneDTO" iterations="0" blockLength="464">
					<field-map attributeName="posizioneRiass"				 length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
					<field-map attributeName="codiceTrattato"     	 		 length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descTrattato"     	 		 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceCompagnia"     	 		 length="3"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrCompagnia"     			 length="40" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descTipoRiass"   	 		 length="19" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="capitaleRiass"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="prestazRiass"     	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioSanitario"	 		 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioProfessione"	 	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioSport"	 		 	 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapremioAltro"	 			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invaliditaPermanente" 			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="provvigioniIncasso"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSanIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovProfIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovSportIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroAcquisto1"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroAcquisto2"			 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="sovAltroIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvAcquisto1"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvAcquisto2"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="invProvIncasso"				 length="14" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		         </nested-mapping>
		   </field-map>			  
	</output-mapping>
     </rule>
</rules>
