<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-TRASFERIMENTO-FONDI-ENTRATA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE407</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.trasferimentoFondiEntrata.dto.ElencoTrasferimentoFondiEntrataRequestDTO">
			<!-- Totali 171 car (150 + 21) -->
			<!-- 21 car -->
            <field-map attributeName="categoria" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"  	length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"   	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.trasferimentoFondiEntrata.dto.ElencoTrasferimentoFondiEntrataResponseDTO">
		    <!-- Totali 2905 car (150 + 2905 + 1824 = 4879) --> 
		    <!-- Campi di output (21+2884) car -->
		    
		    <field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"  	length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroColl"      	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"   	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			 
			 <field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000" offset="" padding=""/>
			<field-map attributeName="flAltri"   			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			           
            <field-map attributeName="oggTrasferimentoFondiEntrataDTOArrayList"	length="2880" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.trasferimentoFondiEntrata.dto.TrasferimentoFondiEntrataDTO" iterations="16" blockLength="180">
					<field-map attributeName="posizione"      					length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="dataTrasferimentoCessione"        length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="denominazioneProvenienza"        	length="150" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="importoTrasferito"        		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico"/>
					<field-map attributeName="flagIncompleti"		        	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="flagDecadimento"        			length="1" 	 precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    