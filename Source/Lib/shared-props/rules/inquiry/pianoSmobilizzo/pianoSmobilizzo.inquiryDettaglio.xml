<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INQUIRY-DETTAGLIO-PIANO-SMOBILIZZO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE588</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.inquiryPianoSmobilizzo.dto.InquiryPianoSmobilizzoRequestDTO">
			<!-- Totali 176 car (150 + 26) -->
			<!-- 26 car -->
            <field-map attributeName="categNum"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dupKey"  		  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiryPianoSmobilizzo.dto.InquiryPianoSmobilizzoResponseDTO">
		    <!-- Totali 20464 car (20470 + 26 = 20496) --> 
		    <!-- Campi di input 26 car -->
            <field-map attributeName="categNum"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dupKey"  		  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- Dati Output 77 + 18 + 274 + 97 + 20004 = 20438 car -->
			<field-map attributeName="tipoPianoSmobilizzo"    	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataInizio"          		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="periodicitaSmobilizzo"    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataFine"         		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="prossimaLiquidazione"     length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="durata"    		length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="percImporto"    	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="2" />
			<field-map attributeName="importoUltimoRiscatto"    length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="dataUltimaElab" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataChiusuraPiano" 		length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>				
			<field-map attributeName="datiGenerali" length="18">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiGenerali" iterations="0" blockLength="18">
	           		<field-map attributeName="codAnag"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	 				<field-map attributeName="codiceCustomer"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPercipiente" length="274">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="0" blockLength="274">
					<field-map attributeName="cognome"   	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nome"  		 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFiscale"   		  length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoSoggetto"   		  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provNascita"            length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoDiNascita"         length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDiNascita"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="sesso"                  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="professione"            length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="indirizzoDiResidenza"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCivicoDiResidenza" length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provRes"   			  length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="comuRes"   			  length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDocumento"           length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRilascio"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="luogoRilascio"          length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="enteRilascio"           length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cab"          		  length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="gruppo"          		  length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottoGruppo"            length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamento" length="97">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="97">
					<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateContoCorrente"     	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="paese"     	      		    length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="swift"     	        		length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- Erogazioni 20004 car -->
			<field-map attributeName="nmEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    		<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    		<field-map attributeName="listaErogazioniPeriodiche"       length="20000" > 	
				<nested-mapping className="it.sistinf.albedoweb.inquiryPianoSmobilizzo.dto.ErogazioniPeriodicheDTO" iterations="400" blockLength="50">
					<field-map attributeName="dataEffetto"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoErogato"    	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
					<field-map attributeName="importoPenale"    	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
                    <field-map attributeName="dataApplic"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>                                       
                     <field-map attributeName="codEsito"           length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>         
				</nested-mapping>
			</field-map>        
		</output-mapping>
	</rule>
</rules>
    
