<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>INQUIRY-SCADENZA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE338</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE338 Lunghezza: 150 + 26 + [3 + 1 + 100 * 53 ]= 150+26+[4+5300]= 150+5330 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.InquiryScadenzaRequestDTO">
	<!-- input : 26 car --> 
		<field-map attributeName="categoriaI" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenziaI" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="numCollI" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizzaI" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	 	<field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	</input-mapping>
    <output-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.InquiryScadenzaResponseDTO">
        <field-map attributeName="categoriaI" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenziaI" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numCollI" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/>
	 	<field-map attributeName="numPolizzaI" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	 	<field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 		
	 	<!-- output : 26 (stessi dati input) + 5304  car =  5330 -->
    	<field-map attributeName="nmEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaInquiryScadenza"       length="5300" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.ListaInquiryScadenza" iterations="100" blockLength="53">            
    	        <field-map attributeName="progOper" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="tipoMovimento" length="12"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
    	        <field-map attributeName="dataLiquid" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
    	        <field-map attributeName="impScadenza" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
    	        <field-map attributeName="impLiquidato" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>    	             	        
            </nested-mapping>
        </field-map>
     </output-mapping>
  </rule>
</rules>
