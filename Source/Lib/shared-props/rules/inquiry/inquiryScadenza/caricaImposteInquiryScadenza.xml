<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-IMPOSTE-INQUIRY-SCADENZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE432</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.DettaglioImposteInquiryScadenzaRequestDTO">			
			<!-- Input 26 car -->
		  	<field-map attributeName="categoria"	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="agenzia"		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="numColl"		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="numPolizza"	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="progOper"		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.DettaglioImposteInquiryScadenzaResponseDTO">
		    <!-- Totali 1494 car (150 + 1494 + 1824) -->
		    <!-- input 26 --> 
		    <field-map attributeName="categoria"	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="agenzia"		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="numColl"		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="numPolizza"	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		  	<field-map attributeName="progOper"		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
          <!-- Elenchi -->
            <!-- 264 car -->		    
			<field-map attributeName="numElementiImposteRedditi" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriImposteRedditi"   			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="imposteRedditi" length="260" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="4" blockLength="65">
					<field-map attributeName="tipoImposta"      		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneImposta"   	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="imponibile"   	    	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="imposta"   	    		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="aliquota"   	    		length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<!-- 1204 car -->
			<field-map attributeName="numElementiAltreImposte" 			length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriAltreImposte"     			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="altreImposte" length="1200" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="6" blockLength="200">
					<field-map attributeName="tipoImposta"      		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneImposta"   	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneLungaImposta"  length="135" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="imponibile"   	    	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="imposta"   	    		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="aliquota"   	    		length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>			
		</output-mapping>
	</rule>
</rules>
    