<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-INQUIRY-SCADENZA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE339</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE339 Lunghezza: 150 + 244 + 1824 = 2218 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.DettaglioInquiryScadenzaRequestDTO">
	<!-- input : 30 car --> 
		<field-map attributeName="categoria"	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" 		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="numColl" 		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" 	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	    <field-map attributeName="progOper" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <field-map attributeName="posizione" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.inquiryScadenza.dto.DettaglioInquiryScadenzaResponseDTO">
        <field-map attributeName="categoria" 	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" 		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numColl" 		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" 	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="progOper" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <field-map attributeName="posizione" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <!-- output : 30 (stessi dati input) + 214  car =  244 -->
    	<field-map attributeName="impScadenza" 			length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="riservaMatematica" 	length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>        			
		<field-map attributeName="signUtili" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="utili" 				length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>       
    	<field-map attributeName="signPrestiti" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="prestiti" 			length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>          			
		<field-map attributeName="signInteressiPrestiti" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="interessiPrestiti" 	length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>          			
		<field-map attributeName="tipoMov" 				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="signRitenute" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="ritenute" 			length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>           			
		<field-map attributeName="dataRivalutazione" 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""
			natura="Data"/>        
		<field-map attributeName="liquidato" 			length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>         			
		<field-map attributeName="dataLiquid" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""
			natura="Data"/>        
		<field-map attributeName="tipoRendita" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="frazionamentoRendita" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>         			
		<field-map attributeName="renditaAnnua" 		length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="renditaZero" 			length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		<field-map attributeName="liquidatoCap" 		length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="liquidatoRen" 		length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="anniRendCerta" 		length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
		<field-map attributeName="reversibilita" 		length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
		<field-map attributeName="importoPrimaRata" 	length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding=""
			natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		 <field-map attributeName="dataPagamento" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""
		 	natura="Data"/>       			     		       			       			
     </output-mapping>
  </rule>
</rules>
