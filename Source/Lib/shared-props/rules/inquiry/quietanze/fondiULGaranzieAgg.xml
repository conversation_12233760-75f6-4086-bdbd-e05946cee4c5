<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>FONDI-UL-GARANZIE-AGG</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE433</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLSE433 Lunghezza: 150 + 1654 + 1824 = 3628  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryRequestDTO">
			<!-- input : 40 car -->
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="false" natura="Data"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryResponseDTO">
		    <!-- output :40 + 1614 = 1654 car -->  
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                  
			<field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoFondiGaranzie"     length="1610" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.FondiGaranzieValoriDTO" iterations="14" blockLength="115">
					<field-map attributeName="codice"        				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrizione"        			length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premioInvestito"   			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="commissioni"   	    		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="numeroQuote"  				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valoreQuote"				   	length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="premioRischio"       			length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    