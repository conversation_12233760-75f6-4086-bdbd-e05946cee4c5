<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-QUIETANZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE246</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA QUIETANZA</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-INQUIRY</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue=""  />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLSE246 Lunghezza: 150 + 678 + 1824 = 2652  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryRequestDTO" nomeKeyERR="erroreInquiryPolizza">
			<!-- input : 526 car -->
            <field-map attributeName="funzione"		       			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataScadenzaQuietanza"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="quietanzaVR"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStorno"			   			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoQuietanza"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoPremio"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataScadenza"     	   		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataScadenza"/>
		    <field-map attributeName="divisa"		     	   		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="premioNetto"  				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioNetto"/>
		    <field-map attributeName="complementariInfort" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariInfort" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="frazionamentoPremio" 			length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="altreComplementari" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="altreComplementari" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="invaliditaPerm" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="invaliditaPerm" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="sovrapremi" 					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrapremi" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="provPrimoAnno"	 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provPrimoAnno" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="interessiFrazionamento" 		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="interessiFrazionamento" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="provSecondoAnno"				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provSecondoAnno" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="interessiPrestiti" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="interessiPrestiti" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="provIncasso"		 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provIncasso" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="interessiMora" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="interessiMora" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="diritti" 						length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="diritti" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="speseMediche" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="speseMediche" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="cambio"			 			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataCambio"		 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataCambio" nomeKeyERR="erroreInquiryPolizza" />
		    <field-map attributeName="imposte" 						length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="imposte" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="dataValuta"	 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataPagamento" nomeKeyERR="erroreInquiryPolizza" />
		    <field-map attributeName="premioRata" 					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="premioRata" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="valuta" 						length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dateTime" 					length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="numeroOttico" 				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dateTimeQ" 					length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="dataEmissione" 				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataEmissione" nomeKeyERR="erroreInquiryPolizza" />
		    <field-map attributeName="dataStorno" 					length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataStorno" nomeKeyERR="erroreInquiryPolizza" />
		    <field-map attributeName="dataIncasso" 					length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data"  nomeAttributoERR="dataIncasso" nomeKeyERR="erroreInquiryPolizza" />
		    <field-map attributeName="modalitaPagamento" 			length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		     <field-map attributeName="ggRitardatoPag" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="codAgenziaRif" 				length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="provIncassiSucc" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provIncassiSucc" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="provRicorrenti" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provRicorrenti" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="provAnticipo" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="provAnticipo" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="managementFee" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="managementFee" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="contributoService" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="contributoService" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="versamentoVolontario" 		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="versamentoVolontario" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="contributoAzienda" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="contributoAzienda" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="conferimentoTFR" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="conferimentoTFR" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="dataContributoDal"		 	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data" nomeAttributoERR="dataContributoDal" nomeKeyERR="erroreInquiryPolizza"/>
		    <field-map attributeName="dataContributoAl"		 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data" nomeAttributoERR="dataContributoAl" nomeKeyERR="erroreInquiryPolizza"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryResponseDTO">
		    <!-- output :526 + 152 = 678 car -->  
            <field-map attributeName="funzione"		       			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="quietanzaVR"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flStorno"			   			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoQuietanza"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoPremio"   		   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataScadenza"     	   		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data" />
		    <field-map attributeName="divisa"		     	   		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="premioNetto"  				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="complementariInfort" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="frazionamentoPremio" 			length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="altreComplementari" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="invaliditaPerm" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="sovrapremi" 					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="provPrimoAnno"	 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="interessiFrazionamento" 		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="provSecondoAnno"				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="interessiPrestiti" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="provIncasso"		 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="interessiMora" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="diritti" 						length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="speseMediche" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="cambio"			 			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataCambio"		 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="imposte" 						length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="dataValuta"	 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="premioRata" 					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico" segnato="true" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="valuta" 						length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dateTime" 					length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="numeroOttico" 				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dateTimeQ" 					length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="dataEmissione" 				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="dataStorno" 					length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="dataIncasso" 					length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="modalitaPagamento" 			length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		     <field-map attributeName="ggRitardatoPag" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="codAgenziaRif" 				length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="provIncassiSucc" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="provRicorrenti" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="provAnticipo" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="managementFee" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="contributoService" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="versamentoVolontario" 		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="contributoAzienda" 			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="conferimentoTFR" 				length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		    <field-map attributeName="dataContributoDal"		 	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data" />
		    <field-map attributeName="dataContributoAl"		 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data" />
		    <!-- Dati output 152 car -->
		    <field-map attributeName="flStornoCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="tipoQuietanzaCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="tipoPremioCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataScadenzaCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="divisaCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="premioNettoCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="complementariInfortCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="frazionamentoPremioCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="altreComplementariCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="invaliditaPermCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="sovrapremiCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provPrimoAnnoCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="interessiFrazionamentoCdErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provSecondoAnnoCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="interessiPrestitiCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provIncassoCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="interessiMoraCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dirittiCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="speseMedicheCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="cambioCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataCambioCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="imposteCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataValutaCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="premioRataCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="valutaCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dateTimeCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dateTimeQCdErr"  				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataEmissioneCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataIncassoCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="modalitaPagamentoCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="codAgenziaRifCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provIncassiSuccCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provRicorrentiCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="provAnticipoCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="managementFeeCdErr"  			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
		    <field-map attributeName="contributoServiceCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		     <field-map attributeName="dataContributoDalCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
		    <field-map attributeName="dataContributoAlCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> 
		</output-mapping>
	</rule>
</rules>
    