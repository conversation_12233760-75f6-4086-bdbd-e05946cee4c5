<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>FLUSSO-RCT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE434</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLSE434 Lunghezza: 150 + 117 + 1824 = 2091  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryRequestDTO">
			<!-- input : 40 car -->
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="false" natura="Data"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioInquiryResponseDTO">
		    <!-- output :40 + 77 = 117 car -->  
            <field-map attributeName="numCategoria"   				length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"   			length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    		length="7"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    			length="7"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizioneUT"        			length="4"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataScadenzaQuietanza"        length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data"/>
            <field-map attributeName="dupKey"       				length="4"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flussoRCT"     length="77" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.FlussoRCTValoriDTO" iterations="0" blockLength="77">      
					<field-map attributeName="flagEmesso"  					length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataEmesso"		        	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>
					<field-map attributeName="flagFoglioCassa"  			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataFoglioCassa"	        	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>	
		            <field-map attributeName="flagRidSeparati"  			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataRidSeparati"        		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>	 
		            <field-map attributeName="flagRitornoIncassi"  			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataRitornoIncassi"        	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/> 
		            <field-map attributeName="flagStornoRegis" 				length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataStornoRegis"        		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>  
		            <field-map attributeName="flagStornoFoglioCassa"		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataStornoFoglioCassa"        length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>
		            <field-map attributeName="flagStornoEmesso"  			length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>
					<field-map attributeName="dataStornoEmesso"        		length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		            	natura="Data"/>
		        </nested-mapping>
		    </field-map>
		</output-mapping>
	</rule>
</rules>
    