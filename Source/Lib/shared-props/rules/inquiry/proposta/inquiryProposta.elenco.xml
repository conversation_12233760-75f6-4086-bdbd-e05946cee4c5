<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INQUIRY-PROPOSTA-ELENCO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLBLPRE</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.proposta.inquiry.dto.InquiryPropostaRicercaRequestDTO">
			<!-- input: 126 car -->
            <field-map attributeName="codSocietaRicerca"    	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoPropostaRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoProdottoRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="agenziaRicerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPropostaRicerca"		length="09" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false" numInteri="09" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numPropostaRicerca" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="numCollettivaRicerca"		length="09" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false" numInteri="09" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettivaRicerca" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="nominativoRicerca"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />			
			<field-map attributeName="tipoRuoloRicerca"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="livello1Ricerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="livello2Ricerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataRegistrazioneMin"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataRegistrazioneMin" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataRegistrazioneMax"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataRegistrazioneMax" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataDecorrenzaMin"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDecorrenzaMin" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataDecorrenzaMax"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDecorrenzaMax" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="codConvenzioneRicerca"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codProdottoRicerca"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />						
			<field-map attributeName="statoPropostaRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.proposta.inquiry.dto.ListaInquiryPropostaDTO">
            <!--  stessi campi di input : 133 car -->
            <field-map attributeName="codSocietaRicerca"    	length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoPropostaRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoProdottoRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="agenziaRicerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numPropostaRicerca"		length="09" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false" numInteri="09" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numPropostaRicerca" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="numCollettivaRicerca"		length="09" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false" numInteri="09" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numCollettivaRicerca" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="nominativoRicerca"		length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />			
			<field-map attributeName="tipoRuoloRicerca"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="livello1Ricerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="livello2Ricerca"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataRegistrazioneMin"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataRegistrazioneMin" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataRegistrazioneMax"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataRegistrazioneMax" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataDecorrenzaMin"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDecorrenzaMin" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="dataDecorrenzaMax"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDecorrenzaMax" nomeKeyERR="erroriRicercaInquiryProposta"/>
			<field-map attributeName="codConvenzioneRicerca"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="codProdottoRicerca"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />						
			<field-map attributeName="statoPropostaRicerca"		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />

            <!-- mappatura area output: 133 (stessi dati input) + 17036  car = 17169 Totali  -->	    
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaProposta" length="17000" > 
		    	<nested-mapping className="it.sistinf.albedoweb.proposta.inquiry.dto.InquiryPropostaResponseDTO" iterations="200" blockLength="85">
		        	<field-map attributeName="codSocieta" 	 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="0"/>
		        	<field-map attributeName="categoria" 		length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />		        	
		        	<field-map attributeName="agenzia" 	 		length="06" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		        	<field-map attributeName="numCollettiva" 	length="09" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numProposta" 	 	length="09" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="nominativo" 		length="30" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="codProdotto" 		length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />										
					<field-map attributeName="dataDecorrenza" 	length="10" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data" valida="false"/>
					<field-map attributeName="numPosizioni" 	length="04" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="statoProposta" 	length="01" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
					<field-map attributeName="numCategoria" 	length="02" precision="0" numericScale="0" align="left"	 mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		   		</nested-mapping>
			</field-map> 

			<field-map attributeName="tipoRuoloRicercaErr"		length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPropostaRicercaErr"	length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numCollettivaRicercaErr"	length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />			
			<field-map attributeName="agenziaErr"				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataDecorrenzaMinErr"		length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataDecorrenzaMaxErr"		length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataRegistrazioneMinErr"	length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataRegistrazioneMaxErr"	length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />			
		</output-mapping>
	</rule>
</rules>
    