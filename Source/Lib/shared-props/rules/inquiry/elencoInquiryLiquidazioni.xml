<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-INQUIRY-LIQUIDAZIONI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLBLLIQ</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	     
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.InquiryListaLiquidazioniRequestDTO">
			<!-- input : 31 car -->
            <field-map attributeName="contesto"				length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codSoc"   			length="3"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numCategoria"   		length="2"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"    	length="6"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"   		length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"    		length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.InquiryListaLiquidazioniResponseDTO">
		    <!-- output :31 + 15004 = 15035 car -->  
            <field-map attributeName="contesto"				length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codSoc"   			length="3"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numCategoria"   		length="2"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"    	length="6"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"   		length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"    		length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                  
			<field-map attributeName="numElementiTrovati"  		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" 	offset="" padding=""/>		    
			<field-map attributeName="elencoLiquidazioni"     	length="15000" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.InquiryLiquidazioniDTO" iterations="250" blockLength="60">
					<field-map attributeName="dataPagamento"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="tipoLiq"        	length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codDiv"   		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impLordo"   		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="impLiquidato"   	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="liquidato"  		length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="stornata"			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dupKey"       	length="9"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizioneLista"   length="4"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    