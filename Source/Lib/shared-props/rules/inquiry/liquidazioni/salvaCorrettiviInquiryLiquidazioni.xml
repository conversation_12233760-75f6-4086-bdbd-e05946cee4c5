<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-CORRETTIVI-INQUIRY-LIQUIDAZIONI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE481</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioLiquidazioniRequestDTO" nomeKeyERR="erroreCorrettiviInquiryLiquidazioni">
			<!-- input : 952 car -->
            <field-map attributeName="funzione"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"    		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataPagI"        		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="false" natura="Data" nomeAttributoERR="dataPagI" nomeKeyERR="erroreCorrettiviInquiryLiquidazioni"/>
            <field-map attributeName="tipoPagI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dupkey"       		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="datiCorrettivi" length="904">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="904">
					<field-map attributeName="numElementiTrovati" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  		length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="900" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="60">
							<field-map attributeName="flgCorrettivo"     			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoCorrettivo"    			length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoCorrettivo" nomeKeyERR="erroreCorrettiviInquiryLiquidazioni"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioLiquidazioniResponseDTO">
			<!-- totale: 982 + 240 = 1222 car -->
			<field-map attributeName="funzione"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"    		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataPagI"        		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="false" natura="Data"/>
            <field-map attributeName="tipoPagI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dupkey"       		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="datiCorrettivi" length="919">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="919">
					<field-map attributeName="numElementiTrovati" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  		length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="900" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="60">
							<field-map attributeName="flgCorrettivo"     			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoCorrettivo"    			length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
					<field-map attributeName="importoTotaleCorrettivi"  length="15"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="importoTotaleLiquidato"  	length="15"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>			
			<!-- campi errore: 240 -->
			<field-map attributeName="erroriDatiCorrettivi"     length="240">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ErroriDatiCorrettiviDTO" iterations="15" blockLength="16">
					<field-map attributeName="flgCorrettivoCdErr"  		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="descrCorrettivoCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="segnoCorrettivoCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="importoCorrettivoCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
		
	</rule>
</rules>
    