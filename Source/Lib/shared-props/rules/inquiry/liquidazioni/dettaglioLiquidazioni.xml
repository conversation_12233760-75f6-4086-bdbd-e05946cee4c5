<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-LIQUIDAZIONI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE241</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	     
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioLiquidazioniRequestDTO">
			<!-- input : 51 car -->
            <field-map attributeName="livelloGestione"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"    		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataPagI"        		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="false" natura="Data"/>
            <field-map attributeName="tipoPagI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codDivI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="storno"       		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dupkey"       		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.DettaglioLiquidazioniResponseDTO">
		<!-- output : 1114 car -->
            <field-map attributeName="livelloGestione"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numCategoria"    		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroPolizza"    	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numeroPolizzaColl"    length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="dataPagI"        		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Data"/>
            <field-map attributeName="tipoPagI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codDivI"       		length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="storno"       		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dupkey"       		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
             
             <!-- output : 51 + 170 + 312 + 536 + 11 + 9 + 10 + 1 + 11 + 3 = 1114 car -->  
            <field-map attributeName="flModInGiornata"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flRipagamento"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flCambioStato"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flFendac"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataPrenotazione"  	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
			<field-map attributeName="codage"  				length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="descAgenzia"     	   	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="codben"			   	length="8"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="dataPagO"   		   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		   		natura="Data"/>
		    <field-map attributeName="tipoPagO"     	   	length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataAssegno"  		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="dataEstrazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataCedola"  			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flStornata" 			length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="coddivO" 				length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="cambio" 				length="8" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Numerico"  segnato="false"  numInteri="5" numDecimali="2" separatoreMigliaia="true" />
		    <field-map attributeName="dataCambio" 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="ramo" 				length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="mpdpag" 				length="3" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		 	<field-map attributeName="invioDatiCollettore"	length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		 	<field-map attributeName="dataEsborso" 			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
		    <field-map attributeName="livelloAutorizzativo" length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flAutorizzata" 		length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="flPostel" 			length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		   
		    <field-map attributeName="elencoImportiMaschera"	length="312" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.ImportiMascheraDTO" iterations="12" blockLength="26">
					<field-map attributeName="tipoImporto"  	length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importo"        	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="chiaveImporto"  	length="9" 	precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="0" separatoreMigliaia="false" />
				</nested-mapping>
			</field-map>
			
			 <field-map attributeName="datiBeneficiario"	length="550" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.DatiBeneficiarioDTO" iterations="0" blockLength="550">
					<field-map attributeName="codiceCustomer"	length="13"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="nominativo"  		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nome"   			length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="localita"     	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="indirizzo"    	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cap"        		length="5" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provincia"    	length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="stato"       		length="3" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codFisc"      	length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataNascita"  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="provinciaNascita" length="2" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoNascita"     length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sesso"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoPersona"     	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="presso"       	length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="iban"	 				length="34"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="intestatario"	 		length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				    <field-map attributeName="tipoDocumentoAR"  	length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDocumentoAR"  		length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRilascioAR"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="enteRilascioAR"  		length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoRilascioAR"  	length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataScadenzaAR"  		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="cabAR"  				length="6" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="gruppoAR"  			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottogruppoAR"  		length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="atecoAR"  				length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flDocumentoEsteroAR" 	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nazioneRilascioAR"  	length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provinciaRilascioAR"  length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flAllegatiAR" 		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descAllegatiAR" 		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="swift"				length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="paese"     		    length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			 </field-map>	
			 <field-map attributeName="percRiscatto" 	length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        numInteri="3" numDecimali="7" separatoreMigliaia="false" />
			 <field-map attributeName="codiceCustomerSoggettoTerzo" length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			 <field-map attributeName="tipoSoggettoTerzo" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			 <field-map attributeName="flInibizioneBloccoMaxErogabile" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    