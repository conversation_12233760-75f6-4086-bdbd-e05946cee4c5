<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-DETTAGLIO-DISINVESTIMENTI-UNIT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE260</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA DISINVESTIMENTI UNIT</logAppServDesc>
	<areaFunzionale>APRI POLIZZA-INQUIRY</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input VWLSE260 Lunghezza: 150 + 487 + 200 + 1824 = 2661  -->  
    <input-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiUnitRequestDTO">
		<!-- input : 80 + 450 = 530 car -->
    	<field-map attributeName="disinvestimentiUnitRequest" length="80" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiRequest" iterations="0" blockLength="80">            
				<field-map attributeName="funzione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" /> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico"  segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" /> 
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" />
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>       
            </nested-mapping>
        </field-map>
             
        <field-map attributeName="disinvestimentiUnitRequestPerSave" length="450" >
        	<!-- 119 + 204 + 45 + 82 = 450 car -->
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitResponse" iterations="0" blockLength="450">  
			    <!-- 119 car -->
			    <field-map attributeName="liquidato" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />          
		    	<field-map attributeName="dataAggQ" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiUnit[].eDataAggQ" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
		    	<field-map attributeName="codSoc" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" />
		    	<field-map attributeName="importo" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" valida="true" nomeAttributoERR="disinvestimentiUnit[].eImporto" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
		        <field-map attributeName="tipoImporto" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="dataEstr" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiUnit[].eDataEstr" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>        
		        <field-map attributeName="impRecesso" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" valida="true" nomeAttributoERR="disinvestimentiUnit[].eImpRecesso" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>        
		        <field-map attributeName="dataRich" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiUnit[].eDataRich" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
		        <field-map attributeName="riferimentoU" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="tipoSwitch" length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="costoSwitch" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" valida="true" nomeAttributoERR="disinvestimentiUnit[].eCostoSwitch" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
		        <field-map attributeName="fondoDa" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="valoreDa" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" valida="true" nomeAttributoERR="disinvestimentiUnit[].eValoreDa" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
		        <field-map attributeName="righeFondo" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false" />
		        <!-- 204 car -->
		        <field-map attributeName="listaFondiUnit" length="204" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitElement" iterations="12" blockLength="17">            
    	        		<field-map attributeName="fondoA" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
    	        		<field-map attributeName="percA" length="7" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" valida="true" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="true" nomeAttributoERR="listaErroriFondiUnit[].percA" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>      							    	        
           			</nested-mapping>
        		</field-map>
        		<!-- 45 car -->
        		<field-map attributeName="codiceIBAN" length="45" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
				        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="disUnitIban[].eCodiceABI" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
				        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="disUnitIban[].eCodiceCAB" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
				        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" />
				        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
				        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="disUnitIban[].eIbanCCN" nomeKeyERR="salvaErroriDettaglioDisinvestimentiUnit"/>
				        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		            </nested-mapping>
        		</field-map>
        		<!-- 82 car -->
		        <field-map attributeName="detrazione" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="tariffaSt" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="cumpremAcc" length="15" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" />
		        <field-map attributeName="richRisc" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="cumpremVis" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" />      
		        <field-map attributeName="cA" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="altriCosti" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" />
		        <field-map attributeName="cumuloPR" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" />        		
		    </nested-mapping>
		</field-map>		    
    </input-mapping>
     <!-- Definizione commarea output VWLSE260 Lunghezza: 150 + 80 + 450 + 248 + 1824 = 150 + 748 + 1824 = 2752 -->    
    <output-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiUnitResponseDTO">
		<!-- input : 80 car -->
    	<field-map attributeName="disinvestimentiUnitRequest" length="80" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiRequest" iterations="0" blockLength="80">            
				<field-map attributeName="funzione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false"/>	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>        
            </nested-mapping>
        </field-map>
		<!-- input : 450 car -->   
    	<field-map attributeName="disinvestimentiUnitResponse" length="450" > 	
    	    <!-- 119 + 204 + 45 + 82 = 450 car -->
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitResponse" iterations="0" blockLength="450">  
			    <!-- 119 car -->   
			    <field-map attributeName="liquidato" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>       
		    	<field-map attributeName="dataAggQ" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
			    <field-map attributeName="codSoc" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" />
		    	<field-map attributeName="importo" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="tipoImporto" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="impRecesso" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        
		        <field-map attributeName="dataRich" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		        <field-map attributeName="riferimentoU" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="tipoSwitch" length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="costoSwitch" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="fondoDa" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="valoreDa" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="righeFondo" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" valida="true" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
		        <!-- 204 car -->
		        <field-map attributeName="listaFondiUnit" length="204" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitElement" iterations="12" blockLength="17">            
    	        		<field-map attributeName="fondoA" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        		<field-map attributeName="percA" length="7" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        							numInteri="3" numDecimali="3" separatoreMigliaia="false"/>    	        
           			</nested-mapping>
        		</field-map>
        		<!-- 45 car -->
        		<field-map attributeName="codiceIBAN" length="45" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
				        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false"/>
				        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false"/>
				        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" />
				        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
				        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
				        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		            </nested-mapping>
        		</field-map>
        		<!-- 82 car -->
		        <field-map attributeName="detrazione" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="tariffaSt" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="cumpremAcc" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true"/>		        	
		        <field-map attributeName="richRisc" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cumpremVis" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true"/>      
		        <field-map attributeName="cA" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="altriCosti" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="cumuloPR" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        		
		    </nested-mapping>
        </field-map>    
		<!-- 248 car -->        
    	<field-map attributeName="disinvestimentiSaveError" length="248" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiSaveError" iterations="0" blockLength="248">            
		        <field-map attributeName="eFunzione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eSotFunz" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eLivelloGestione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCategoria" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eAgenzia" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumColl" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumPolizza" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="ePosizione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDataDisinv" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTipoDisinv" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDupKey" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  
		        <field-map attributeName="eDataAggQ" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eLiquidato" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCodSoc" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eImporto" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTipoImporto" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDataEstr" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eImpRecesso" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDataRich" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eRiferimentoU" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTipoSwitch" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCostoSwitch" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/> 
		        <field-map attributeName="eFondoDa" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eValoreDa" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        
		        <field-map attributeName="listaErroriFondiUnit" length="96" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitElementSaveError" iterations="12" blockLength="8">            
    	        		<field-map attributeName="eFondoA" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        		<field-map attributeName="ePercA" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  	        
           			</nested-mapping>
        		</field-map>			        
		        
		        <field-map attributeName="eCodiceABI" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCodiceCAB" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCodiceCC" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eIbanNazione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eIbanCCN" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eIbanCIN" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDetrazione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTariffaST" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCumpremACC" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>	        
		        <field-map attributeName="eRichRisch" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCumpremVis" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCA" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eAltriCosti" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCumuloPR" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  		        		        		          		        		        
            </nested-mapping>
        </field-map>
    </output-mapping>
  </rule>
</rules>
  