<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-DETTAGLIO-DISINVESTIMENTI-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE262</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
     <logApp>true</logApp>
	<logAppServDesc>SALVA DISINVESTIMENTI INDEX</logAppServDesc>
	<areaFunzionale>APRI POLIZZA-INQUIRY</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input: VWLSE261 Lunghezza: 150 + 51 + 58 + 45 + 44 + 52 + 1824 = 2224 -->  
    <input-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.SalvaDettaglioDisinvestimentiIndexRequestDTO">
	<!-- input : 51 car -->
    	<field-map attributeName="disinvestimentiIndexRequest" length="51" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexRequest" iterations="0" blockLength="51">            
				<field-map attributeName="funzione" length="1"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  /> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico"  numInteri="7" numDecimali="0" separatoreMigliaia="false" />	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico"  segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false" />
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			     
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" valida="true" nomeAttributoERR="disIndexUno[].eDupKey" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false" nomeKeyERR="salvaErroriDettaglioDisinvestimentiIndex"/>         
            </nested-mapping>
        </field-map>
        
        <field-map attributeName="disinvestimentiIndexResponse" length="58" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexResponse" iterations="0" blockLength="58">            
		    	<field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiIndexResponse[].eDataRich" nomeKeyERR="salvaErroriDettaglioDisinvestimentiIndex"/>    	
		    	<field-map attributeName="metodoTab" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cA" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiIndexResponse[].eDataEstr" nomeKeyERR="salvaErroriDettaglioDisinvestimentiIndex"/>        
		        <field-map attributeName="dataAggQ" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="disinvestimentiIndexResponse[].eDataAggQ" nomeKeyERR="salvaErroriDettaglioDisinvestimentiIndex"/>        
		        <field-map attributeName="riferimentoI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="importo" length="15" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" valida="true" nomeAttributoERR="disinvestimentiIndexResponse[].eImporto" segnato="false" numInteri="9" numDecimali="3" separatoreMigliaia="true" nomeKeyERR="salvaErroriDettaglioDisinvestimentiIndex"/>       
            </nested-mapping>
        </field-map>
        <field-map attributeName="codiceIBAN" length="45" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
		        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false" />
		        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false" />
		        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="12" numDecimali="0" separatoreMigliaia="false" />
		        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false" />
		        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
    </input-mapping>
    <!-- Definizione commarea output: VWLSE261 Lunghezza:  58 + 45 + 44 + 52 = 199 --> 
    <output-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.SalvaDettaglioDisinvestimentiIndexResponseDTO">
        <field-map attributeName="disinvestimentiIndexRequest" length="51" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexRequest" iterations="0" blockLength="51">            
				<field-map attributeName="funzione" length="1"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset=""  />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" /> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false"/>	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="4" numDecimali="0" separatoreMigliaia="false"/>
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			     
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="9" numDecimali="0" separatoreMigliaia="false"/>        
            </nested-mapping>
        </field-map>  
        <field-map attributeName="disinvestimentiIndexResponse" length="58" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexResponse" iterations="0" blockLength="58">            
		    	<field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>    	
		    	<field-map attributeName="metodoTab" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cA" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="dataAggQ" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="riferimentoI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="importo" length="15"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" 
		        		   numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        
            </nested-mapping>
        </field-map>
        <field-map attributeName="codiceIBAN" length="45" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
		        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false"/>
		        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="5" numDecimali="0" separatoreMigliaia="false"/>
		        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Numerico" segnato="false" numInteri="12" numDecimali="0" separatoreMigliaia="false"/>
		        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" natura="Numerico" segnato="false" numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
		        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
        <!-- output: errori 44 car -->
        <field-map attributeName="disinvestimentiVisualizzaError" length="44" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiVisualizzaError" iterations="0" blockLength="44"> 
        		<field-map attributeName="eFunzione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eSotFunz" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eLivelloGestione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eCategoria" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eAgenzia" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eNumColl" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
     		    <field-map attributeName="eNumPolizza" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="ePosizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
       			<field-map attributeName="eDataDisinv" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
       			<field-map attributeName="eTipoDisinv" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eDupKey" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        	</nested-mapping>
        </field-map>        
        <field-map attributeName="eDataRich" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eMetodoTab" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eCa" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eDataEstr" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eDataAggQ" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eRiferimento" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eImporto" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eCodiceABI" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eCodiceCAB" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eCodiceCC" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eIbanNazione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eIbanCCN" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eIbanCIN" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>   
        <field-map attributeName="eLiquidato" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>     
    </output-mapping>
  </rule>
</rules>
