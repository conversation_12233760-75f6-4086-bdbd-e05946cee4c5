<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DISINVESTIMENTI-UL-RICERCA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLBLDUL</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLBLDUL Lunghezza: 150 + 44 + 25016 [3 + 1 + 250 * 100 + 4 + 4 +4 ] + 1824 = 27034  -->  
    <input-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DisinvestimentiULRequestDTO">
	<!-- input : 44 car --> 
		<field-map attributeName="tipoDisinvestimentoRicerca" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		<field-map attributeName="eseguitoRicerca" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		<field-map attributeName="dataDisinvestimentoRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataDisinvestimentoRicerca" nomeKeyERR="disinvestimentiErroriRicerca"/>
	     <field-map attributeName="tipoProd" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="contesto" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="codSoc" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="Posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ListaDisinvestimentiULResponseDTO">
    	 <field-map attributeName="tipoDisinvestimentoRicerca" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		 <field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		 <field-map attributeName="eseguitoRicerca" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		 <field-map attributeName="dataDisinvestimentoRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
         <field-map attributeName="tipoProd" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="contesto" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="codSoc" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	     <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	     <field-map attributeName="Posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	
    	<!-- output : 44 (stessi dati input) + 25016  car =  25060 -->
    	<field-map attributeName="nmEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaDisinvestimentiULResponse"       length="25250" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DisinvestimentiULResponseDTO" iterations="250" blockLength="101">            
    	        <field-map attributeName="categoriaLst" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="agenziaLst" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="numCollLst" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="numPolizzaLst" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="posizioneLst" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>    	        
    	        <field-map attributeName="dataDisinvest" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
    	        <field-map attributeName="descrizioneTipoInvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>    	        
    	        <field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
    	        <field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>   	      
    	        <field-map attributeName="liquidato" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="fondo" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="flagProtetto" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
        <field-map attributeName="tipoDisinvestimentoRicercaCdErr"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="eseguitoRicercaCdErr"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="dataDisinvestimentoRicercaCdErr"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
    </output-mapping>
  </rule>
</rules>
       