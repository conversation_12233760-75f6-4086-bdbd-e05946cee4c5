<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-DISINVESTIMENTI-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE261</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input: VWLSE261 Lunghezza: 150 + 92 + 48 + 45 + 44 + 1824 = 2203 -->  
    <input-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiIndexRequestDTO">
	<!-- input : 92 car -->
    	<field-map attributeName="disinvestimentiIndexRequest" length="92" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexRequest" iterations="0" blockLength="92">            
				<field-map attributeName="funzione" length="1"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="funzione" nomeKeyERR="erroriDettaglioDisinvestimentiIndex" />
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="sotFunz" nomeKeyERR="erroriDettaglioDisinvestimentiIndex" />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="funzione" nomeKeyERR="erroriDettaglioDisinvestimentiIndex" />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="livelloGestione" nomeKeyERR="erroriDettaglioDisinvestimentiIndex" />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="categoria" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numColl" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numPolizza" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="posizione" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="liquidato" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDisinv" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>			     
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="tipoDisinv" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="dupKey" nomeKeyERR="erroriDettaglioDisinvestimentiIndex"/>
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />        
            </nested-mapping>
        </field-map>
    </input-mapping>
    <!-- Definizione commarea output: VWLSE261 Lunghezza:  48 + 45 + 44 = 137 --> 
    <output-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiIndexResponseDTO">
        <field-map attributeName="disinvestimentiIndexRequest" length="92" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexRequest" iterations="0" blockLength="92">            
				<field-map attributeName="funzione" length="1"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset=""  />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset=""  />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" /> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			     
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />        
            </nested-mapping>
        </field-map>  
        <field-map attributeName="disinvestimentiIndexResponse" length="48" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiIndexResponse" iterations="0" blockLength="48">            
		    	<field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>    	
		    	<field-map attributeName="metodoTab" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cA" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="riferimentoI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="importo" length="15"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        		   numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        
            </nested-mapping>
        </field-map>
        <field-map attributeName="codiceIBAN" length="45" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
		        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
        <!-- output: errori 44 car -->
        <field-map attributeName="disinvestimentiVisualizzaError" length="44" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiVisualizzaError" iterations="0" blockLength="44"> 
        		<field-map attributeName="eFunzione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eSotFunz" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eLivelloGestione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eCategoria" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eAgenzia" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eNumColl" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
     		    <field-map attributeName="eNumPolizza" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="ePosizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
       			<field-map attributeName="eDataDisinv" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
       			<field-map attributeName="eTipoDisinv" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eDupKey" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        	</nested-mapping>
        </field-map>
    </output-mapping>
  </rule>
</rules>
