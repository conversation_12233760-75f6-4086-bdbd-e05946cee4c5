<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-DISINVESTIMENTI-UNIT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE267</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input VWLSE267 Lunghezza: 150 + 50 = 200 -->  
    <input-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiUnitRequestDTO">
		<!-- input : 50 car -->
    	<field-map attributeName="disinvestimentiUnitRequest" length="102" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiRequest" iterations="0" blockLength="102" >            
				<field-map attributeName="funzione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="funzione" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="categoria" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="livelloGestione" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="categoria" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="agenzia" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numColl" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numPolizza" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="posizione" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDisinv" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/> 
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="tipoDisinv" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/>
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="dupKey" nomeKeyERR="erroriDettaglioDisinvestimentiUnit"/> 
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="fondo" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />       
            </nested-mapping>
        </field-map>
    </input-mapping>
    <!-- Definizione commarea output VWLSE267 Lunghezza: 150 + 50 + 450 + 44 + 1824 = 150 + 544 + 1824  -->    
    <output-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.DettaglioDisinvestimentiUnitResponseDTO">
		<!-- input : 50 car -->
    	<field-map attributeName="disinvestimentiUnitRequest" length="102" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiRequest" iterations="0" blockLength="102">            
				<field-map attributeName="funzione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>	    
			    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
			    <field-map attributeName="dataDisinv" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
		        <field-map attributeName="tipoDisinv" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dupKey" length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		    	<field-map attributeName="descrizioneTipoDisinvestimento" length="30"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="fondo" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />        
            </nested-mapping>
        </field-map>
		<!-- input : 450 car -->   
    	<field-map attributeName="disinvestimentiUnitResponse" length="439" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitResponse" iterations="0" blockLength="439">  
			    <!-- 119 car -->          
		    	<field-map attributeName="codSoc" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		    	<field-map attributeName="importo" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="tipoImporto" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="impRecesso" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        
		        <field-map attributeName="dataRich" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		        <field-map attributeName="riferimentoU" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="tipoSwitch" length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="costoSwitch" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="fondoDa" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="valoreDa" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="righeFondo" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <!-- 204 car -->
		        <field-map attributeName="listaFondiUnit" length="204" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiUnitElement" iterations="12" blockLength="17">            
    	        		<field-map attributeName="fondoA" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        		<field-map attributeName="percA" length="7" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        							numInteri="3" numDecimali="3" separatoreMigliaia="false"/>    	        
           			</nested-mapping>
        		</field-map>
        		<!-- 45 car -->
        		<field-map attributeName="codiceIBAN" length="45" > 	
					<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.CodiceIBAN" iterations="0" blockLength="45">            
				        <field-map attributeName="codiceABI" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
				        <field-map attributeName="codiceCAB" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
				        <field-map attributeName="codiceCC" length="30"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
				        <field-map attributeName="ibanNazione" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
				        <field-map attributeName="ibanCCN" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
				        <field-map attributeName="ibanCIN" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		            </nested-mapping>
        		</field-map>
        		<!-- 82 car -->
		        <field-map attributeName="detrazione" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="tariffaSt" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="cumpremAcc" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="richRisc" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cumpremVis" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>       
		        <field-map attributeName="cA" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="altriCosti" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="cumuloPR" length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        		
		    </nested-mapping>
        </field-map>    
		<!-- input : 44 car -->        
    	<field-map attributeName="disinvestimentiSaveError" length="44" > 	
			<nested-mapping className="it.sistinf.albedoweb.disinvestimentiUL.dto.ElemDisinvestimentiSaveError" iterations="0" blockLength="44">            
		        <field-map attributeName="eFunzione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eSotFunz" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eLivelloGestione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCategoria" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eAgenzia" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumColl" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumPolizza" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="ePosizione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDataDisinv" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTipoDisinv" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDupKey" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  
            </nested-mapping>
        </field-map>
    </output-mapping>
  </rule>
</rules>
    
  