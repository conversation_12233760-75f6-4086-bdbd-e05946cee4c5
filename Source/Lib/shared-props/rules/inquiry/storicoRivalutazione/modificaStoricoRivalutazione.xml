<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-DETTAGLIO-STORICO-RIVALUTAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE254</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>MODIFICA STORICO RIVALUTAZIONE</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-INQUIRY</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    
	    <!-- Definizione commarea: VWLSE254 Lunghezza: 150 + 20030 + 1824 = 22004  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneRequestDTO">
			<!-- input : 20030 car -->
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            
            <field-map attributeName="numElementiTrovati"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"				length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaDettaglioModifica"	length="20000" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneRequestDTO" iterations="200" blockLength="100">
					<field-map attributeName="flagModifica"				length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRivalutazione"		length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="premioRivalutazione"  	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="listaDettaglioModificaError[].premioRivalutazione" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="prestazioneRivalutazione" length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="listaDettaglioModificaError[].prestazioneRivalutazione" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="tassoRendimento"      	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" nomeAttributoERR="listaDettaglioModificaError[].tassoRendimento" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="retrocessione"        	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" nomeAttributoERR="listaDettaglioModificaError[].retrocessione" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="riservaMat"        		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="listaDettaglioModificaError[].riservaMat" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="prestazioneAggiuntiva"	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="listaDettaglioModificaError[].prestazioneAggiuntiva" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
					<field-map attributeName="prestazioneBase"        	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="listaDettaglioModificaError[].prestazioneBase" nomeKeyERR="erroriDettaglioInquiryStoricoRivalutazione"/>
				</nested-mapping>
			</field-map>
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.ElencoStoricoRivalutazioneResponseDTO">
		    <!-- stessi campi di input : 20030 car -->
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            
            <field-map attributeName="numElementiTrovati"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"				length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaDettaglioModifica"	length="20000" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneRequestDTO" iterations="200" blockLength="100">
					<field-map attributeName="flagModifica"				length="01" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRivalutazione"		length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="premioRivalutazione"  	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="prestazioneRivalutazione" length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="tassoRendimento"      	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" />
					<field-map attributeName="retrocessione"        	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" />
					<field-map attributeName="riservaMat"        		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="prestazioneAggiuntiva"	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="prestazioneBase"        	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
				</nested-mapping>
			</field-map>

			<field-map attributeName="listaDettaglioModificaError" length="5600" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneResponseDTO" iterations="200" blockLength="28">
					<field-map attributeName="premioRivalutazioneErr"  		length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="prestazioneRivalutazioneErr" 	length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tassoRendimentoErr"      		length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="retrocessioneErr"        		length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="riservaMatErr"       			length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="prestazioneAggiuntivaErr"		length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="prestazioneBaseErr"      		length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    