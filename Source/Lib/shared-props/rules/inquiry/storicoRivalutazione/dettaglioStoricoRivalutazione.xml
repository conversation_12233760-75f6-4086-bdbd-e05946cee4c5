<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-STORICO-RIVALUTAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE253</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    
	    <!-- Definizione commarea: VWCBLSRV Lunghezza: 150 + 26 + 1824 = 2000  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneRequestDTO">
			<!-- input : 26 car -->
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.ElencoStoricoRivalutazioneResponseDTO">
		    <!-- output : 26 + 22804 = 22830 car -->              
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                  
			<field-map attributeName="numElementiTrovati"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"				length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaDettaglio"	length="22800" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneResponseDTO" iterations="200" blockLength="114">
					<field-map attributeName="dataRivalutazione"		length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" valida="false" natura="Data"/>
					<field-map attributeName="premioRivalutazione"  	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="prestazioneRivalutazione" length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="tassoRendimento"      	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" />
					<field-map attributeName="retrocessione"        	length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="03" separatoreMigliaia="false" />
					<field-map attributeName="riservaMat"        		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="prestazioneAggiuntiva"	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="prestazioneBase"        	length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="sovrapRiv"        		length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>

		</output-mapping>
	</rule>
</rules>
    