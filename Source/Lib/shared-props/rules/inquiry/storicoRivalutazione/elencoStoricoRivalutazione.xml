<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-STORICO-RIVALUTAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLBLSRV</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    
	    <!-- Definizione commarea: VWCBLSRV Lunghezza: 150 + 35 + 1824 = 2010  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneRequestDTO">
			<!-- input : 35 car -->
            <field-map attributeName="categoria"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0"  separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codSoc"				length="03"  precision="0" numericScale="0" align="right" mandatory="0"  separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0"  separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left"  mandatory="0"  separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0"  separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0"  separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0"  separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="progrTranche"			length="04"  precision="0" numericScale="0" align="left"  mandatory="0"  separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.ElencoStoricoRivalutazioneResponseDTO">
		    <!-- output : 35 + 22504 = 22539 car -->              
            <field-map attributeName="categoria"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codSoc"				length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="numCategoria"			length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="progrTranche"			length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                  
			<field-map attributeName="numElementiTrovati"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"				length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaStoricoRival"	length="22500" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.StoricoRivalutazioneResponseDTO" iterations="500" blockLength="45">
					<field-map attributeName="posizione"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataDecorrenza"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
					<field-map attributeName="premioNetto"      length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="prestazione"      length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="stato"        	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="causale"        	length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

		</output-mapping>
	</rule>
</rules>
    