<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-INQUIRY-RISCATTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE337</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE337 Lunghezza: 30 + 339= 369 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiryRiscatto.dto.DettaglioInquiryRiscattoRequestDTO">
	<!-- input : 30 car --> 
		<field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	    <field-map attributeName="progOper" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.inquiryRiscatto.dto.DettaglioInquiryRiscattoResponseDTO">
        <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="progOper" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	    <!-- output : 30 (stessi dati input) + 339  car =  369 -->
    	<field-map attributeName="impRiscatto" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
    	<field-map attributeName="signRitVisentini" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="descrRVis" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>           
    	<field-map attributeName="impRitVisentini" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>       
    	<field-map attributeName="descrCumVis" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>       
    	<field-map attributeName="cumpremVis" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>       
    	<field-map attributeName="signRitAcconto" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="descrRitAcc" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="ritAcconto" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="descrCumAcconto" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="cumPremAcconto" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="signRitTFR" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="ritTFR" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="cumPremTFR" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="signPrestiti" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="prestiti" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="signIntPrestiti" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="intPrestiti" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="cumpremIS" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/> 
		<field-map attributeName="signRettif" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="desRettif" length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="variazioni" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="cumpremRisc" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="signImpSost" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="impostaSost" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="imponibileIS" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>         			
		<field-map attributeName="importoTot" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="imponibileES" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>   
		<field-map attributeName="dataEffetto" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		<field-map attributeName="dataRiscatto" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		<field-map attributeName="tipoFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="tipoRisc" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="metodoRisc" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="percRisc" length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="7" separatoreMigliaia="false"/>   		
     </output-mapping>
  </rule>
</rules>
