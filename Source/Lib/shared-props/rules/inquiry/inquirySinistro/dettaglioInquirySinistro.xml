<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-INQUIRY-SINISTRO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE319</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE319 Lunghezza: 150 + 26 + 224= 150+250=400 -->  
    <input-mapping className="it.sistinf.albedoweb.inquirySinistro.dto.DettaglioInquirySinistroRequestDTO">
	<!-- input : 26 car --> 
		<field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	    <field-map attributeName="progOper" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.inquirySinistro.dto.DettaglioInquirySinistroResponseDTO">
        <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="progOper" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	    <!-- output : 26 (stessi dati input) + 249  car =  275 -->
    	<field-map attributeName="impSinistro" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
    	<field-map attributeName="tipoMov" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="flagStorno" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>           
    	<field-map attributeName="indennita" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>       
    	<field-map attributeName="tipoSinistro" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>       
    	<field-map attributeName="utili" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>       
    	<field-map attributeName="sospInfortuni" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>          	       
    	<field-map attributeName="oneriProp" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="dataMorte" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		<field-map attributeName="prestiti" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>          			
		<field-map attributeName="dataDenuncia" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		<field-map attributeName="interPrestito" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="dataRicezioneDenuncia" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        		
		<field-map attributeName="rateiPremio" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="dataSinistro" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        		
		<field-map attributeName="causaSinistro" length="19"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>           	
		<field-map attributeName="descrOrdDecesso" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>           	
		<field-map attributeName="ordineDecesso" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>           	
		<field-map attributeName="impFinale" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="quota1" length="6" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="rendInval" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="quota2" length="6" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="quota3" length="6" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="2" separatoreMigliaia="true"/>  
		<field-map attributeName="totaleImposte" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  
	     <field-map attributeName="dateTimeImposte"  length="11" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/> 	        			        			        					
     </output-mapping>
  </rule>
</rules>
