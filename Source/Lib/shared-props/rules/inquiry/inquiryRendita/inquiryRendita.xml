<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>INQUIRY-RENDITA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE338</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    
    
    <!-- Definizione commarea: VWLSE338 Lunghezza: 150 + 26 + [3 + 1 + 100 * 53 ]= 150+26+[4+5300]= 150+5330 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiryrendita.dto.InquiryRenditaRequestDTO">
	<!-- input : 26 car --> 
		<field-map attributeName="categoriaI"    length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenziaI"      length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="numCollI"      length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numPolizzaI"   length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	 	<field-map attributeName="posizione"     length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	</input-mapping>
	
    <output-mapping className="it.sistinf.albedoweb.inquiryrendita.dto.InquiryRenditaResponseDTO">
        <field-map attributeName="categoriaI"    length="02"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="agenziaI"      length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numCollI"      length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/>
	 	<field-map attributeName="numPolizzaI"   length="07"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 
	 	<field-map attributeName="posizione"     length="04"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 		
	 	
	 	<!-- output : 26 (stessi dati input) + 5304  car =  5330 -->
    	<field-map attributeName="nmEleTrovati"    length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite"      length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaInquiryRendita" length="5300" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiryrendita.dto.ListaInquiryRendita" iterations="100" blockLength="53">            
    	        <field-map attributeName="progOper"        length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="tipoMovimento"   length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
    	        <field-map attributeName="dataRivalut"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
    	        <field-map attributeName="renditaIniz"     length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
    	        <field-map attributeName="renditaRivalut"    length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>    
            </nested-mapping>
        </field-map>
     </output-mapping>
  </rule>
</rules>
