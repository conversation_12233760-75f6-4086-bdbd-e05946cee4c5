<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-MOVIMENTAZIONI-INDEX</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE428</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input: VWLSE261 Lunghezza: 150 + 86 + 47 + 45 + 44 + 1824 = 2203 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.DettaglioMovimentazioniIndexRequestDTO">
	<!-- input : 86 car -->
    	<field-map attributeName="movimentazioniIndexRequest" length="86" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniRequest" iterations="0" blockLength="86">            
				<field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="sotFunz" nomeKeyERR="erroriDettaglioMovimentazioniIndex" />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="funzione" nomeKeyERR="erroriDettaglioMovimentazioniIndex" />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="livelloGestione" nomeKeyERR="erroriDettaglioMovimentazioniIndex" />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="categoria" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numColl" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/>
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numPolizza" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/>	    
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="liquidato" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/>
			    <field-map attributeName="dataMovimentazione" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataDisinv" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/>			     
		        <field-map attributeName="tipoMovimentazione" length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="tipoDisinv" nomeKeyERR="erroriDettaglioMovimentazioniIndex"/>
		    	<field-map attributeName="descrizioneTipoMovimentazione" length="30"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />        
            </nested-mapping>
        </field-map>
    </input-mapping>
    <!-- Definizione commarea output: VWLSE261 Lunghezza:  48 + 28 = 76 --> 
    <output-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.DettaglioMovimentazioniIndexResponseDTO">
        <field-map attributeName="movimentazioniIndexRequest" length="79" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniRequest" iterations="0" blockLength="79">            
				<field-map attributeName="sotFunz" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  />
			    <field-map attributeName="livelloGestione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset=""  />
			    <field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset=""  />
			    <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" /> 
			    <field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
			    <field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />	    
			    <field-map attributeName="liquidato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
			    <field-map attributeName="dataMovimentazione" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			     
		        <field-map attributeName="tipoMovimentazione" length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		    	<field-map attributeName="descrizioneTipoMovimentazione" length="30"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="dataAggiorno" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="flagComucAnul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />        
            </nested-mapping>
        </field-map>  
        <field-map attributeName="movimentazioniIndexResponse" length="47" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniIndexResponse" iterations="0" blockLength="47">            
		    	<field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>    	
		    	<field-map attributeName="metodoTab" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cA" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="riferimentoI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="importo" length="14"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        		   numInteri="9" numDecimali="2" separatoreMigliaia="true"/>        
            </nested-mapping>
        </field-map>
        
        <!-- output: errori 28 car -->
        <field-map attributeName="movimentazioniCaricaDettaglioError" length="28" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniCaricaDettaglioErrori" iterations="0" blockLength="28"> 
        		<field-map attributeName="eSotFunz" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eCategoria" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eAgenzia" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eNumColl" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
     		    <field-map attributeName="eNumPolizza" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        		<field-map attributeName="eDataMovimentazione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
       			<field-map attributeName="eTipoMovimentazione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        	</nested-mapping>
        </field-map>
    </output-mapping>
  </rule>
</rules>
