<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>RICERCA-MOVIMENTAZIONI-UL</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLBL003</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLBLDUL Lunghezza: 150 + 75 + 21766 [3 + 1 + 250 * 87 + 4 + 4 +4 ] + 1824 = 23815  -->  
    <input-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.MovimentazioniULRequestDTO">
	<!-- input : 75 car --> 
		<field-map attributeName="tipoMovimentazioneRicerca" 		length="3"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		<field-map attributeName="descrizioneTipoMovimentazione" 	length="30" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		<field-map attributeName="liquidatoRicerca" 				length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		<field-map attributeName="dataMovimentazioneRicerca" 		length="10"	precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataDisinvestimentoRicerca" nomeKeyERR="movimentazioniErroriRicerca"/>
	    <field-map attributeName="tipoProd" 						length="1"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/> 
	    <field-map attributeName="codSoc" 							length="3"  precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	    <field-map attributeName="categoria" 						length="2"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	    <field-map attributeName="agenzia" 							length="6"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/> 
	    <field-map attributeName="numColl" 							length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	    <field-map attributeName="numPolizza" 						length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 	
    	<field-map attributeName="progrTranche"						length="04" precision="0" numericScale="0" align="left"  	mandatory="0" separator="" occurs="1" default="" 	offset="" padding=""/>
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ListaMovimentazioniULResponseDTO">
    	 <field-map attributeName="tipoMovimentazioneRicerca" 		length="3"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		 <field-map attributeName="descrizioneTipoMovimentazione" 	length="30" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		 <field-map attributeName="liquidatoRicerca" 				length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
		 <field-map attributeName="dataMovimentazioneRicerca" 		length="10" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding="" natura="Data"/>
         <field-map attributeName="tipoProd" 						length="1"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/> 
	     <field-map attributeName="codSoc" 							length="3"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	     <field-map attributeName="categoria" 						length="2"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	     <field-map attributeName="agenzia" 						length="6"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/> 
	     <field-map attributeName="numColl"						 	length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	     <field-map attributeName="numPolizza" 						length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" 	offset="" padding="0"/> 
	     <field-map attributeName="progrTranche"					length="04" precision="0" numericScale="0" align="left"  	mandatory="0" separator="" occurs="1" default="" 	offset="" padding=""/>
	     
	      <!-- output : 75 (stessi dati input) + 21770  car =  21841 -->
    	<field-map attributeName="numElementiTrovati" 				length="3" 	precision="0" numericScale="0" align="right" 	mandatory="1" separator="" occurs="0" default="0" 	offset="" padding="0"/>
    	<field-map attributeName="flagLimite" 						length="1"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " 	offset="" padding=""/>
    	<field-map attributeName="listaMovimentazioniULResponseDTO" length="21750" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.MovimentazioniULResponseDTO" iterations="250" blockLength="87">            
    	        <field-map attributeName="categoriaLst" 					length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="agenziaLst" 						length="6"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="numCollLst" 						length="9"  precision="0" numericScale="0" align="right"	mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="numPolizzaLst" 					length="7"  precision="0" numericScale="0" align="right" 	mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
    	        <field-map attributeName="dataMovimentazione" 				length="10" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
    	        <field-map attributeName="descrizioneTipoMovimentazione"	length="30" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>    	        
    	        <field-map attributeName="dataAggiorno" 					length="10" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
    	        <field-map attributeName="liquidato" 						length="2"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="tipoMovimentazione" 				length="10" precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="flagComucAnul" 					length="1"  precision="0" numericScale="0" align="left" 	mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
        <field-map attributeName="tipoMovimentazioneRicercaCdErr"   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="liquidatoRicercaCdErr"         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="dataMovimentazioneRicercaCdErr"   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
    </output-mapping>
  </rule>
</rules>
       