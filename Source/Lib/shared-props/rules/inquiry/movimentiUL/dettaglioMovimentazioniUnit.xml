<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>DETTAGLIO-MOVIMENTAZIONI-UNIT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE427</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea input VWLSE267 Lunghezza: 150 + 93 = 243 -->  
    <input-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.DettaglioMovimentazioniUnitRequestDTO">
		<!-- input : 93 car -->
    	<field-map attributeName="movimentazioniUnitRequest" length="93" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniUnitRequest" iterations="0" blockLength="93" >            
				<field-map attributeName="sotFunz" 							length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  valida="true" nomeAttributoERR="categoria" 		nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="livelloGestione" 					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  valida="true" nomeAttributoERR="livelloGestione" 	nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="categoria" 						length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="categoria" 		nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="agenzia" 							length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  valida="true" nomeAttributoERR="agenzia" 			nomeKeyERR="erroriDettaglioMovimentazioniUnit"/> 
			    <field-map attributeName="numColl" 							length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numColl" 			nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="numPolizza" 						length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" valida="true" nomeAttributoERR="numPolizza" 		nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>	    
			    <field-map attributeName="dataMovimentazione" 				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  valida="true" nomeAttributoERR="dataDisinv"    	nomeKeyERR="erroriDettaglioMovimentazioniUnit" natura="Data"/> 
		        <field-map attributeName="tipoMovimentazione" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  valida="true" nomeAttributoERR="tipoDisinv" 		nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
		    	<field-map attributeName="descrizioneTipoMovimentazione"	length="30" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding=""  />
		    	<field-map attributeName="liquidato" 						length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
		    	<field-map attributeName="dataAggiorno" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""  natura="Data"/>
		    	<field-map attributeName="fondo" 							length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=""  offset="" padding=""  />
		    	<field-map attributeName="flagComucAnul" 					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=""  offset="" padding=""  />       
            	<field-map attributeName="progrTranche"						length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""  />
            </nested-mapping>
        </field-map>
    </input-mapping>
    <!-- Definizione commarea output VWLSE267 Lunghezza: 150 + 93 + 8288 + 28 + 1824 = 150 + 8409 + 1824  -->    
    <output-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.DettaglioMovimentazioniUnitResponseDTO">
		<!-- input : 93 car -->
    	<field-map attributeName="movimentazioniUnitRequest" length="93" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniUnitRequest" iterations="0" blockLength="93">            
				<field-map attributeName="sotFunz" 							length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="categoria" 			nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="livelloGestione" 					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" nomeAttributoERR="livelloGestione" 	nomeKeyERR="erroriDettaglioMovimentazioniUnit"/>
			    <field-map attributeName="categoria" 						length="2"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
			    <field-map attributeName="agenzia" 							length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
			    <field-map attributeName="numColl"						 	length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
			    <field-map attributeName="numPolizza" 						length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>	    
			    <field-map attributeName="dataMovimentazione" 				length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
		        <field-map attributeName="tipoMovimentazione" 				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="descrizioneTipoMovimentazione"	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="" />
		    	<field-map attributeName="liquidato" 						length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0" />
		    	<field-map attributeName="dataAggiorno" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		    	<field-map attributeName="fondo" 							length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=""  offset="" padding="" />
		    	<field-map attributeName="flagComucAnul" 					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=""  offset="" padding="" />    
		    	<field-map attributeName="progrTranche"						length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""  />    
            </nested-mapping>
        </field-map>
		<!-- input : 8288 car -->   
    	<field-map attributeName="movimentazioniUnitResponse" length="8288" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniUnitResponse" iterations="0" blockLength="8288">  
			    <!-- 106 car -->          
		    	<field-map attributeName="codSoc" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		    	<field-map attributeName="importo" 				length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		        <field-map attributeName="tipoImporto" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="dataEstr" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>        
		        <field-map attributeName="impRecesso" 			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="2" separatoreMigliaia="true"/>        
		        <field-map attributeName="dataRich" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		        <field-map attributeName="riferimentoU" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="tipoSwitch" 			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" />
		        <field-map attributeName="costoSwitch" 			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		        <field-map attributeName="quoteDisinvestite"	length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="dataRicezione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
		        <field-map attributeName="righeFondo" 			length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="flagLimite" 			length="1"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <!-- 8100 car -->
		        <field-map attributeName="listaFondiUnit" length="8100" > 	
					<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemListaFondiMovimentazioniUnitResponse" iterations="100" blockLength="81">
						<field-map attributeName="fondoDa" 			 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>            
    	        		<field-map attributeName="fondoA" 			 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        		<field-map attributeName="importoA" 		 length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        					numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
        				<field-map attributeName="numeroQuoteA"		 length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        					numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
        				<field-map attributeName="valoreQuotaA" 	 length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        					numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
        				<field-map attributeName="dataAggiornoQuote" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset=""  padding="" natura="Data"/>
    	        		<field-map attributeName="percA" 			 length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
        					numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
        				<field-map attributeName="liquidatoA" 		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>    	        
           			</nested-mapping>
        		</field-map>
        		        		
        		<!-- 82 car -->
		        <field-map attributeName="detrazione" 	length="1" 	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="tariffaSt" 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="cumpremAcc" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="richRisc" 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		    	<field-map attributeName="cumpremVis" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="3" separatoreMigliaia="true"/>       
		        <field-map attributeName="cA" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
		        <field-map attributeName="altriCosti" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
		        <field-map attributeName="cumuloPR" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        	numInteri="9" numDecimali="3" separatoreMigliaia="true"/>        		
		    </nested-mapping>
        </field-map>    
		<!-- input : 28 car -->        
    	<field-map attributeName="movimentazioniCaricaDettaglioError" length="28" > 	
			<nested-mapping className="it.sistinf.albedoweb.inquiry.movimentazioniUL.dto.ElemMovimentazioniCaricaDettaglioErrori" iterations="0" blockLength="44">            
		        <field-map attributeName="eSotFunz" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eCategoria" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eAgenzia" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumColl" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eNumPolizza" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eDataMovimentazione" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
		        <field-map attributeName="eTipoMovimentazione" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  
            </nested-mapping>
        </field-map>
    </output-mapping>
  </rule>
</rules>
    
  