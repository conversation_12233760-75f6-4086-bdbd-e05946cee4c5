<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-CONTRIBUTI-NONDEDOTTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE411</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    
	    <!-- Definizione commarea: VWLSE411 Lunghezza: 150 +  + 1824 =  -->        
		<input-mapping className="it.sistinf.albedoweb.inquiry.dto.ContributiNonDedottiRequestDTO">
			<!-- input : 25 car -->
            <field-map attributeName="categoria"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.inquiry.dto.ElencoContributiNonDedottiResponseDTO">
		    <!-- output : 25 + 4 + 5250  = 5279 car -->              
            <field-map attributeName="categoria"			length="01"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"		length="06"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroPolizzaColl"	length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"		length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="posizione"			length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                  
			<field-map attributeName="numElementiTrovati"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"				length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="listaContributiNonDedotti"	length="5250" >
				<nested-mapping className="it.sistinf.albedoweb.inquiry.dto.ContributiNonDedottiResponseDTO" iterations="50" blockLength="105">
					<field-map attributeName="dataComunicazioneO" 			length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
					<field-map attributeName="nonDedottiPrima"      		length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="dataComunicazionePrima" 		length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Data" />
					<field-map attributeName="flg"							length="1" 	precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="anno"   						length="4" precision="0" numericScale="0"  align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="versati"      				length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="versatiNX"      			    length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="nonDedotti"      			    length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="nonDedottiNXDopo"      		length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Numerico" segnato="true" numInteri="08" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="dataComunicazioneDopo" 		length="10" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
					valida="false" natura="Data" />
				</nested-mapping>
			</field-map>

		</output-mapping>
	</rule>
</rules>
    