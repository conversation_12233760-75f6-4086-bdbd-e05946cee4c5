<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLO-COMPAGNIA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE420</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>Controllo compagnia selezionata</logAppServDesc>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.common.albedo.compagnia.dto.CompagniaRequestDTO">
		<!-- Totali 153 car (150 + 3) -->
			<field-map attributeName="compagnia" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.compagnia.dto.CompagniaResponseDTO">
		<!-- Totali  car (150 + 3 + 1824) = 1977 -->		
			<field-map attributeName="compagnia" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>			
		</output-mapping>
	</rule>
</rules>