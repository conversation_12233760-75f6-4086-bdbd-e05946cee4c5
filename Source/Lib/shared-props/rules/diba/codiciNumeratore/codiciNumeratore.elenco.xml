<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>ELENCO-CODICI-NUMERATORE</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSLNUM</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWLSLNUM Lunghezza: 150 + 7713 + 1824 = 9687  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreRequestDTO" nomeKeyERR="erroriCodiceNumeratore"> 
  		 <!-- input : 8 car --> 
	    <field-map attributeName="codiceNumeratoreRicerca" 	 length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
 	 </input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreResponseDTO"> 
	      <!-- output : 25820 car -->      
	      <field-map attributeName="codiceNumeratoreRicerca"     	length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="numElementiTrovati" 			length="4" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/> 
	   	  <field-map attributeName="flLimite"           			length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
	      <field-map attributeName="listaCodiciNumeratore" length="7700" > 
			    <nested-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.ElencoCodiciNumeratoreDTO" iterations="100" blockLength="77">            
			          <field-map attributeName="codiceNumeratore"         		length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			          <field-map attributeName="descrizioneCodiceNumeratore"    length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			          <field-map attributeName="numeratore"		      			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
			    </nested-mapping>
	   </field-map> 
       
   </output-mapping> 
 </rule> 
</rules> 