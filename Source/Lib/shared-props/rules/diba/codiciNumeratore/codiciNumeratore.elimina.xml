<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>ELIMINA-CODICE-NUMERATORE</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSE039</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<logApp>true</logApp>
	<logAppServDesc>ELIMINA CODICI NUMERATORE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE GESTIONALI</areaFunzionale>
	
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWLSE039 Lunghezza: 150 + 8 + 1824 = 1982  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreRequestDTO" nomeKeyERR="erroriCodiceNumeratore"> 
  		 <!-- input : 8 car --> 
	    <field-map attributeName="codiceNumeratore"         		length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" /> 
	</input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreResponseDTO"> 
	      <!-- output : 8 car -->      
	      <field-map attributeName="codiceNumeratore" 	 			length="8"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
   </output-mapping> 
 </rule> 
</rules> 