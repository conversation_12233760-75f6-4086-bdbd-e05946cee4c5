<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>MODIFICA-CODICE-NUMERATORE</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSE038</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<logApp>true</logApp>
	<logAppServDesc>MODIFICA CODICI NUMERATORE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE GESTIONALI</areaFunzionale>
	
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWCSE038 Lunghezza: 150 + 89 + 1824 = 2063  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreRequestDTO" nomeKeyERR="erroriCodiceNumeratore"> 
  		 <!-- input : 77 car --> 
	    <field-map attributeName="codiceNumeratore"         		length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="descrizioneCodiceNumeratore"      length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="numeratore"		      			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="numeratore" nomeKeyERR="erroriCodiceNumeratore"/> 
 	 </input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.codiciNumeratore.dto.CodiciNumeratoreResponseDTO"> 
	      <!-- output : 89 car -->      
		<field-map attributeName="codiceNumeratore"         		length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="descrizioneCodiceNumeratore"      length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		<field-map attributeName="numeratore"		      			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="0" separatoreMigliaia="false" />
		<field-map attributeName="codiceNumeratoreCdErr"         	length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
		<field-map attributeName="descrizioneCodiceNumeratoreCdErr" length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
		<field-map attributeName="numeratoreCdErr"		      		length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
       
   </output-mapping> 
 </rule> 
</rules> 