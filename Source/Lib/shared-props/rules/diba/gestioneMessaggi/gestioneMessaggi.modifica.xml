<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>MODIFICA-MESSAGGIO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE008</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA MESSAGGIO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE DI SISTEMA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
     <!-- Definizione commarea: DWLSE007 Lunghezza: 150 + 141 + 1824 = 02115  -->  
    <input-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiRequestDTO" nomeKeyERR="erroriGestioneMessaggi">
      	<!-- input : 109 car -->
         	<field-map attributeName="codiceAmbiente" 			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="codiceLingua"   			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="codiceErrore"   			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
	    	<field-map attributeName="flagBloccante" 			length="1" 	 precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>       
            <field-map attributeName="flagTecnicoGest"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding="" />
            <field-map attributeName="causaForzatura"      	 	length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            <field-map attributeName="descrizioneMsg"        	length="75"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            <field-map attributeName="descrizioneMsgBreve"  	length="15"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
	  </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiResponseDTO">
      <!-- output 109 + 32= 141 -->
     		<field-map attributeName="codiceAmbiente" 				length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    	<field-map attributeName="codiceLingua"   				length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="codiceErrore"   				length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
	    	<field-map attributeName="flagBloccante" 				length="1" 	 precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"  offset="" padding=""/>       
            <field-map attributeName="flagTecnicoGest"   			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding="" />
            <field-map attributeName="causaForzatura"      	 		length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            <field-map attributeName="descrizioneMsg"        		length="75"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            <field-map attributeName="descrizioneMsgBreve"  		length="15"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
            
            <field-map attributeName="codiceAmbienteCdErr" 			length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/> 
	    	<field-map attributeName="codiceLinguaCdErr" 			length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
	    	<field-map attributeName="codiceErroreCdErr" 			length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1"  default="0000" offset=""  padding="0"/>
	    	<field-map attributeName="flagBloccanteCdErr" 			length="4"   precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset=""  padding="0"/>      
            <field-map attributeName="flagTecnicoGestCdErr" 		length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
            <field-map attributeName="causaForzaturaCdErr" 			length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
            <field-map attributeName="descrizioneMsgCdErr" 			length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
            <field-map attributeName="descrizioneMsgBreveCdErr" 	length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
     
	  </output-mapping>
  </rule>
</rules>
