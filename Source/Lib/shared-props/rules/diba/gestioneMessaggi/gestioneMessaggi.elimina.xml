<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>ELIMINA-MESSAGGIO</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSE009</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<logApp>true</logApp>
	<logAppServDesc>ELIMINA MESSAGGIO</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE DI SISTEMA</areaFunzionale>
	
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWLSL005 Lunghezza: 150 + 15 + 1824 = 1989  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiRequestDTO" nomeKeyERR="erroriGestioneMessaggi"> 
  		 <!-- input : 15 car --> 
	    <field-map attributeName="codiceAmbiente" 		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    <field-map attributeName="codiceLingua"   		 length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    <field-map attributeName="codiceErrore"			 length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
 	 </input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiResponseDTO"> 
	      <!-- output : 15 car -->      
	      <field-map attributeName="codiceAmbiente"     	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codiceLingua"       	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	      <field-map attributeName="codiceErrore"    	    length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
     </output-mapping> 
 </rule> 
</rules> 