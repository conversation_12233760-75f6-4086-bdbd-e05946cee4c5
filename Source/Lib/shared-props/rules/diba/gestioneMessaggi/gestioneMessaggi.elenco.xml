<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>ELENCO-MESSAGGI</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSL005</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWLSL005 Lunghezza: 150 + 25820 + 1824 = 27794  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiRequestDTO" nomeKeyERR="erroriGestioneMessaggi"> 
  		 <!-- input : 15 car --> 
	    <field-map attributeName="codiceAmbiente" 		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    <field-map attributeName="codiceLingua"   		 length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    <field-map attributeName="codiceErroreRicerca"   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
 	 </input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.GestioneMessaggiResponseDTO"> 
	      <!-- output : 25820 car -->      
	      <field-map attributeName="codiceAmbiente"     	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="codiceLingua"       	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	      <field-map attributeName="codiceErroreRicerca"    length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
	     
	      <field-map attributeName="numElementiTrovati" length="4" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/> 
	   	  <field-map attributeName="flLimite"           length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
	      <field-map attributeName="listaMessaggi" length="25800" > 
			    <nested-mapping className="it.sistinf.albedoweb.diba.gestioneMessaggi.dto.ElencoMessaggiDTO" iterations="300" blockLength="86">            
			          <field-map attributeName="codiceErrore"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
			          <field-map attributeName="descrizioneErrore"    length="75" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			          <field-map attributeName="erroreBloccante"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			          <field-map attributeName="tipoErrore"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			           
			    </nested-mapping>
	   </field-map> 
       
   </output-mapping> 
 </rule> 
</rules> 