<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-CONDIZIONE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE020</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA CONDIZIONI</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE020 Lunghezza: 150 + 10 + 1824 = 1984  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneRequestDTO" nomeKeyERR="erroriSalvaCondizione">
        	<!-- input :  10  car -->
      	<field-map attributeName="codCondizione" 	length="10"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
      
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneResponseDTO">
      
      	<field-map attributeName="codCondizione" 	length="10"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
      
     </output-mapping>
  </rule>
</rules>
