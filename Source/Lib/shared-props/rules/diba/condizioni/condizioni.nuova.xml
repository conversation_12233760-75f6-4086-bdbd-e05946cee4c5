<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-NUOVA-CONDIZIONE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE018</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA CONDIZIONI</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE019 Lunghezza: 150 + 762 + 1824 = 2736  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneRequestDTO" nomeKeyERR="erroriSalvaCondizione">
        	<!-- input :  755  car -->
      	<field-map attributeName="codCondizione" 			length="10"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
       	<field-map attributeName="descrizioneCondizione" 	length="60"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
        <field-map attributeName="testoList" length="684" >         
	        <nested-mapping className="it.sistinf.albedoweb.base.dto.TestoResponseDTO" iterations="9" blockLength="76">            
			          <field-map attributeName="testo"  length="76" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" /> 
			</nested-mapping>        
        </field-map>  
        <field-map attributeName="flClausolaVessatoria"  length="1"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneResponseDTO">
     		 <field-map attributeName="codCondizione" 			length="10"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	       	<field-map attributeName="descrizioneCondizione" 	length="60"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
	        <field-map attributeName="testoList" length="684" >         
		        <nested-mapping className="it.sistinf.albedoweb.base.dto.TestoResponseDTO" iterations="9" blockLength="76">            
				          <field-map attributeName="testo"  length="76" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" /> 
				</nested-mapping>        
	        </field-map> 
	        <field-map attributeName="flClausolaVessatoria"  length="1"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
      	<!-- output :  8 car -->
      		<field-map attributeName="codCondizioneErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
      		<field-map attributeName="descrizioneCondizioneErr" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
     </output-mapping>
  </rule>
</rules>
