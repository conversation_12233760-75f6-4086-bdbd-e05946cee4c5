<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-CONDIZIONI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSL016</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL016 Lunghezza: 150 + 22839 + 1824 = 22989  -->  
  <input-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneRequestDTO" nomeKeyERR="erroriElencoCondizione">
   <!-- input : 10 car --> 
   <field-map attributeName="codCondizioneRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.ListaCondizioniResponseDTO"> 
      <!-- output :10 + 21005  car -->      
      <field-map attributeName="codCondizioneRicerca" length="10"  precision="0" 	numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
      <field-map attributeName="numElementiTrovati" length="4" 		precision="0" 	numericScale="0" 	  align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/> 
   	  <field-map attributeName="flAltri"         length="1" 		precision="0" 	numericScale="0" 		  align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
      <field-map attributeName="listaCondizioni" length="21000" > 
		    <nested-mapping className="it.sistinf.albedoweb.diba.condizioni.dto.CondizioneResponseDTO" iterations="300" blockLength="70">            
		          <!-- <field-map attributeName="idCondizione" 		   length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />-->
		          <field-map attributeName="codCondizione"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="descrizioneCondizione" length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          
		           
		    </nested-mapping>
   </field-map> 
   <!-- <field-map attributeName="codCondizioneRicercaCdErr" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/> -->    
   </output-mapping> 
 </rule> 
</rules> 