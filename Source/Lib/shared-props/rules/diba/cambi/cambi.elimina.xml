<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-CAMBI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE045</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA CAMBI DIVISE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE045 Lunghezza: 150 + 14 + 1824 = 01986  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiRequestDTO">
        	<!-- input :  10  car -->
      	    <field-map attributeName="codDivisa" 	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
         	<field-map attributeName="data" 	 	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data" valida="false"/>
		  
      
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiResponseDTO">      
    	    <field-map attributeName="codDivisa" 	length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
         	<field-map attributeName="data" 	 	length="12" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
	  </output-mapping>
  </rule>
</rules>
