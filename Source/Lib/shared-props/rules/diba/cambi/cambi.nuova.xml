<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-NUOVA-CAMBI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE043</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA CAMBI DIVISE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
     <!-- Definizione commarea: DWCSE043 Lunghezza: 150 + 36 + 1824 = 02056  -->  
    	<input-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiRequestDTO">
      	<!-- input : 24 car -->
         	<field-map attributeName="codDivisa" 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
         	<field-map attributeName="data" 	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataC" nomeKeyERR="erroriSalvaCambi"/>
		    <field-map attributeName="valore"  		length="12" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="6" numDecimali="5" separatoreMigliaia="false" valida="true" nomeAttributoERR="valore" nomeKeyERR="erroriSalvaCambi"/>
		   
	  </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiResponseDTO">
     		<field-map attributeName="codDivisa" 	length="2"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
         	<field-map attributeName="data" 	 	length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
		    <field-map attributeName="valore"  		length="12" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="6" numDecimali="5" separatoreMigliaia="false"/>
      	<!-- output :  12  car -->  
	       <field-map attributeName="codDivisaErr"      length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
		   <field-map attributeName="dataErr" 	 		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
		   <field-map attributeName="valoreErr"         length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
	  </output-mapping>
  </rule>
</rules>
