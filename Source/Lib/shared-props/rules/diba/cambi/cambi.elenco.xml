<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-CAMBI</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSLCMB</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSLCMB Lunghezza: 150 + 6617 + 1824 = 08592  -->  
  <input-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiRequestDTO"> 
   <!-- input : 2 car --> 
   <field-map attributeName="codDivisa" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
   <field-map attributeName="data" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " natura="Data" valida="true" nomeAttributoERR="dataC" nomeKeyERR="erroriElencoCambi"/>
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.diba.cambi.dto.ListaCambiResponseDTO"> 
      <!-- output :6617  car -->      
      <field-map attributeName="codDivisa" 				 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
   	  <field-map attributeName="data" 					 length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " natura="Data" /> 
      <field-map attributeName="numElementiTrovati" 	 length="4" 	precision="0" 	numericScale="0" 	align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         		 length="1" 	precision="0" 	numericScale="0" 	align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaCambi" length="6600" > 
		    <nested-mapping className="it.sistinf.albedoweb.diba.cambi.dto.CambiResponseDTO" iterations="300" blockLength="22"> 		        
		          <field-map attributeName="data" 	 length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" natura="Data"/>
		          <field-map attributeName="valore"  length="12" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " natura="Numerico" segnato="false" 
		        			numInteri="6" numDecimali="5" separatoreMigliaia="false"/>
		    </nested-mapping>
   </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 