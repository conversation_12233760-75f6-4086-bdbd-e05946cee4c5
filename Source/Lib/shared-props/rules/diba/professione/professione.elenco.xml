<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-PROFESSIONE</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSL026</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL026 Lunghezza: 150 + 22515 + 1824 = 24489  -->  
  <input-mapping className="it.sistinf.albedoweb.diba.professione.dto.ProfessioneRequestDTO" nomeKeyERR="erroriElencoProfessione"> 
   <!-- input : 10 car --> 
   <field-map attributeName="codProfessioneRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.diba.professione.dto.ListaProfessioneResponseDTO"> 
      <!-- output :10 + 22505  car -->      
      <field-map attributeName="codProfessioneRicerca"   length="10"    precision="0" 	numericScale="0" 	align="left" mandatory="0" separator="" occurs="1"  default=""      offset=""  padding=" "/> 
      <field-map attributeName="numElementiTrovati" 	 length="4" 	precision="0" 	numericScale="0" 	align="right" mandatory="1" separator="" occurs="1" default="0000"  offset=""  padding="0"/> 
   	  <field-map attributeName="flAltri"         		 length="1" 	precision="0" 	numericScale="0" 	align="left"  mandatory="1" separator="" occurs="1" default="N"     offset=""  padding=""/>      
      <field-map attributeName="listaProfessione" length="22500" > 
		    <nested-mapping className="it.sistinf.albedoweb.diba.professione.dto.ProfessioneResponseDTO" iterations="300" blockLength="75"> 
		          <field-map attributeName="codProfessione"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding=" " />
		          <field-map attributeName="descrizioneProfessione" length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="sovrappremio"         length="5" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
		          	valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
		    </nested-mapping>
   </field-map> 
     
   </output-mapping> 
 </rule> 
</rules> 