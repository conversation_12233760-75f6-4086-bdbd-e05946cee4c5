<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-MODIFICA-PROFESSIONE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE028</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA PROFESSIONE</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE028 Lunghezza: 150 + 87 + 1824 = 02061  -->  
    <input-mapping className="it.sistinf.albedoweb.diba.professione.dto.ProfessioneRequestDTO" nomeKeyERR="erroriSalvaProfessione">
      	<!-- input : 75 car -->
         	<field-map attributeName="codProfessione"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	        <field-map attributeName="descrizioneProfessione" length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	        <field-map attributeName="sovrappremio"         length="5" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
	        	valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrappremio" nomeKeyERR="erroriSalvaProfessione"/>
	  </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.professione.dto.ProfessioneResponseDTO">
     		<field-map attributeName="codProfessione"         length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	        <field-map attributeName="descrizioneProfessione" length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
	        <field-map attributeName="sovrappremio"         length="5" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="" offset="" padding=""
	        	valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="true"/>
      	<!-- output :  12  car -->  
	        <field-map attributeName="codProfessioneErr" 			length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
     		<field-map attributeName="descrizioneProfessioneErr" 	length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       		<field-map attributeName="sovrappremioErr" 				length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
     
	  </output-mapping>
  </rule>
</rules>
