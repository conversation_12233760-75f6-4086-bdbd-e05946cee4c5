<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-PARAMETRI-DIBA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE011</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>SALVA PARAMETRI DIBA</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE VITA</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE011 Lunghezza: 150 + 76 + 1824 = 2050  -->  
    <input-mapping className="it.sistinf.albedoweb.diba.parametri.dto.ParametriDibaRequestDTO" nomeKeyERR="erroriSalvaParametriDiba">
        	<!-- input :  48  car -->
		<field-map attributeName="giorniDiMora" 	length="3"    precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="giorniDiMora" nomeKeyERR="erroriSalvaParametriDiba"/>
      	<field-map attributeName="percentuale1" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" valida="true" nomeAttributoERR="percentuale1" nomeKeyERR="erroriSalvaParametriDiba"/>
      	<field-map attributeName="dataFine1" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataFine1" nomeKeyERR="erroriSalvaParametriDiba"/>      
      	<field-map attributeName="percentuale2" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" valida="true" nomeAttributoERR="percentuale2" nomeKeyERR="erroriSalvaParametriDiba"/>
      	<field-map attributeName="dataFine2" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataFine2" nomeKeyERR="erroriSalvaParametriDiba"/>      
      	<field-map attributeName="percentuale3" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" valida="true" nomeAttributoERR="percentuale3" nomeKeyERR="erroriSalvaParametriDiba"/>
      	<field-map attributeName="dataFine3" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataFine3" nomeKeyERR="erroriSalvaParametriDiba"/>      
      	
	</input-mapping>

    <output-mapping className="it.sistinf.albedoweb.diba.parametri.dto.ParametriDibaResponseDTO">
    	<field-map attributeName="giorniDiMora" 	length="3"    precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" />
      	<field-map attributeName="percentuale1" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine1" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
      	<field-map attributeName="percentuale2" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine2" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
      	<field-map attributeName="percentuale3" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="2" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine3" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      

      	<!-- output :  28 car -->
   		<field-map attributeName="giorniDiMoraErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
   		<field-map attributeName="percentuale1Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
		<field-map attributeName="dataFine1Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />      		
        <field-map attributeName="percentuale2Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
		<field-map attributeName="dataFine2Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />      		
   		<field-map attributeName="percentuale3Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
		<field-map attributeName="dataFine3Err"  length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />      		
     
	</output-mapping>
     
  </rule>
</rules>
