<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELENCO-PARAMETRI-DIBA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE010</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE010 Lunghezza: 150 + 48 + 1824 = 2022  -->
    <input-mapping className="it.sistinf.albedoweb.diba.parametri.dto.ParametriDibaRequestDTO" nomeKeyERR="erroriSalvaParametriDiba">
        <field-map attributeName="giorniDiMora" length="3"    precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="giorniDiMora" nomeKeyERR="erroriSalvaParametriDiba"/>
	</input-mapping>
	
    <output-mapping className="it.sistinf.albedoweb.diba.parametri.dto.ParametriDibaResponseDTO">
      	<!-- output :  48 car -->
      	<field-map attributeName="giorniDiMora" 	length="3"    precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" />
      	<field-map attributeName="percentuale1" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine1" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
      	<field-map attributeName="percentuale2" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine2" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>      
      	<field-map attributeName="percentuale3" 	length="5"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="3" numDecimali="0" separatoreMigliaia="false" />
      	<field-map attributeName="dataFine3" 		length="10"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
      	     
    </output-mapping>
     
  </rule>
</rules>
