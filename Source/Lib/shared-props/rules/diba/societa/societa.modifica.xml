<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-MODIFICA-SOCIETA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE024</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA SOCIETA</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
   <!-- Definizione commarea: DWCSE024 Lunghezza: 150 + 122 +36 + 1824 = 2132  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaRequestDTO" nomeKeyERR="erroriSalvaSocieta">
        	<!-- input : 122  car -->
      	<field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/>
      	<field-map attributeName="ragSoc" 		length="40"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
       	<field-map attributeName="indirizzo" 	length="30"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="comune" 		length="20"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="prov" 		length="3"     precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="cap" 			length="5"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="tipoSoc"  	length="1"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/>
       	<field-map attributeName="inizGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		valida="false" natura="Data"  nomeAttributoERR="inizGest" nomeKeyERR="erroriSalvaSocieta"/>
       	<field-map attributeName="fineGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		valida="false" natura="Data"  nomeAttributoERR="fineGest" nomeKeyERR="erroriSalvaSocieta"/>
     </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaResponseDTO">
      	<field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/>
      	<field-map attributeName="ragSoc" 		length="40"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
       	<field-map attributeName="indirizzo" 	length="30"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="comune" 		length="20"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="prov" 		length="3"     precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="cap" 			length="5"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="tipoSoc"  	length="1"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/>
       	<field-map attributeName="inizGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		natura="Data"/>
       	<field-map attributeName="fineGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		natura="Data"/>
      
      <!-- output :  36  car -->
      	<field-map attributeName="codSocietaErr" 	length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
     	<field-map attributeName="ragSocErr" 		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="indirizzoErr" 	length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="comuneErr" 		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0" />
       	<field-map attributeName="provErr" 			length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="capErr" 			length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="tipoSocErr"  		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="inizGestErr" 		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
       	<field-map attributeName="fineGestErr" 		length="4"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
      </output-mapping>
  </rule>
</rules>
