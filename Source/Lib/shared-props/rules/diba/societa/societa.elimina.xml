<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-SOCIETA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE025</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA SOCIETA</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE ANAGRAFICHE</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
    
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWCSE025 Lunghezza: 150 + 3 + 1824 = 1977  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaRequestDTO" nomeKeyERR="erroriSalvaSocieta">
        	<!-- input :  3  car -->
      	<field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/>
      
     </input-mapping>
     	<!-- output :  3  car -->
      <output-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaResponseDTO">
       
      	<field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/>
      
     </output-mapping>
  </rule>
</rules>
