<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>ELENCO-SOCIETA</id> 
  <initialProgram>WNDISPC0</initialProgram> 
  <initialTransaction>SB00</initialTransaction> 
     <program>DWLSL021</program> 
     <transaction>SB00</transaction> 
  <connectorId>A05TARE</connectorId> 
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: DWCSL021 Lunghezza: 150 + 22839 + 1824 = 27782  --> 
  <!-- Definizione commarea: DWCSL021 Lunghezza: 150 + 25812 + 1824 = 27786  -->   
  <input-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaRequestDTO" nomeKeyERR="erroriElencoSocieta"> 
   <!-- input : 3 car --> 
   <field-map attributeName="codSocietaRicerca" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/> 
  </input-mapping> 
  <output-mapping className="it.sistinf.albedoweb.diba.societa.dto.ListaSocietaResponseDTO"> 
      <!-- output : 25812 car -->      
      	<field-map attributeName="codSocietaRicerca"  length="3"  		    precision="0" 	numericScale="0" 	align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/> 
      	<field-map attributeName="numElementiTrovati" length="4" 		    precision="0" 	numericScale="0" 	  align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/> 
   	  	<field-map attributeName="flAltri"            length="1" 		    precision="0" 	numericScale="0" 	  align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>      
      	<field-map attributeName="listaSocieta"       length="25800" > 
		    <nested-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaResponseDTO" iterations="600" blockLength="43">            
		          <!-- <field-map attributeName="idSocieta" 		   length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />-->
		          <field-map attributeName="codSocieta"                length="3" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		          <field-map attributeName="descrizioneSocieta"        length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="" offset="" padding="" />
		           
		    </nested-mapping>
		 </field-map>    
		<field-map attributeName="codSocietaRicercaCdErr" 	  	length="4"  		precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
   
       
   </output-mapping> 
 </rule> 
</rules> 