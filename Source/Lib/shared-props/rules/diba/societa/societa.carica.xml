<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>CARICA-SOCIETA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE022</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE022 Lunghezza: 150 + 122 + 1824 = 2096  -->  
      <input-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaRequestDTO" nomeKeyERR="erroriSalvaSocieta">
      	<!-- input : 3 car --> 
  		 <field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/> 
   </input-mapping>
      <output-mapping className="it.sistinf.albedoweb.diba.societa.dto.SocietaResponseDTO">
        <field-map attributeName="codSocieta" length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="000" offset="" padding=""/>
      	<!-- output :  119  car -->
      	<field-map attributeName="ragSoc" 		length="40"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
       	<field-map attributeName="indirizzo" 	length="30"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="comune" 		length="20"    precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="prov" 		length="3"     precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="cap" 			length="5"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding="" />
       	<field-map attributeName="tipoSoc"  	length="1"  	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/>
       	<field-map attributeName="inizGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		natura="Data"/>
       	<field-map attributeName="fineGest" 	length="10"    	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
       		natura="Data"/>
     
      </output-mapping>
  </rule>
</rules>
