<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>SALVA-INDICE-ISTAT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE014</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA INDICI ISTAT</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE FONDI</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE014 Lunghezza: 150 + 29 + 1824 = 2003  -->  
    
    <input-mapping className="it.sistinf.albedoweb.diba.indiciIstat.dto.IndiciIstatRequestDTO" nomeKeyERR="erroriSalvaIndiciIstat">
    	<!-- input : 17 car --> 
   		<field-map attributeName="annoCompetenza" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="4" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="annoCompetenza" nomeKeyERR="erroriSalvaIndiciIstat"/> 
   		<field-map attributeName="meseCompetenza" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="meseCompetenza" nomeKeyERR="erroriSalvaIndiciIstat"/>
   		<field-map attributeName="indiceIstat" length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=" " natura="Numerico" segnato="false" 
		        			numInteri="7" numDecimali="3" separatoreMigliaia="false" valida="true" nomeAttributoERR="indiceIstat" nomeKeyERR="erroriSalvaIndiciIstat"/>
  	</input-mapping> 
  	  	
  	<output-mapping className="it.sistinf.albedoweb.diba.indiciIstat.dto.IndiciIstatResponseDTO"> 
      	<!-- output : 17 (stessi dati input) + 12 car -->      
   		<field-map attributeName="annoCompetenza"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="4" numDecimali="0" separatoreMigliaia="false"/> 
   		<field-map attributeName="meseCompetenza"	length="2"	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
   		<field-map attributeName="indiceIstat" 		length="11"	precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="7" numDecimali="3" separatoreMigliaia="false"/>
 
      	<field-map attributeName="annoCompetenzaErr"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"	offset="" padding="0"/> 
   	  	<field-map attributeName="meseCompetenzaErr"	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="N" 	offset="" padding="0"/>      
      	<field-map attributeName="indiceIstatErr" 		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="N" 	offset="" padding="0"/>      
   </output-mapping> 
  </rule>
</rules>
