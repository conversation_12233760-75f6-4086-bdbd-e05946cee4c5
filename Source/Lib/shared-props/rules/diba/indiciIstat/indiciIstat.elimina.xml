<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>ELIMINA-INDICE-ISTAT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>DWLSE015</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>ELIMINA INDICI ISTAT</logAppServDesc>
	<areaFunzionale>FONDI INVESTIMENTO-GESTIONE FONDI</areaFunzionale>
	
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: DWLSE015 Lunghezza: 150 + 6 + 1824 = 1980  -->  
    
    <input-mapping className="it.sistinf.albedoweb.diba.indiciIstat.dto.IndiciIstatRequestDTO" nomeKeyERR="erroriSalvaIndiciIstat">
    	<!-- input : 17 car --> 
   		<field-map attributeName="annoCompetenza" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
   		<field-map attributeName="meseCompetenza" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
  	</input-mapping> 

	<!--  Questo output e' fittizio ma e' obbligatorio per l'infrastruttura. 
	      In realtà la commarea non prevede nessun dato di rispota. -->
	<output-mapping className="it.sistinf.albedoweb.diba.indiciIstat.dto.IndiciIstatResponseDTO">
    	<!-- input : 17 car --> 
   		<field-map attributeName="annoCompetenza" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
   		<field-map attributeName="meseCompetenza" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
  	</output-mapping> 
  </rule>
</rules>
