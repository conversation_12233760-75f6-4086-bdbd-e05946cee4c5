<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
 	<id>SALVA-NUOVO-SOTTOSISTEMA</id> 
  	<initialProgram>WNDISPC0</initialProgram> 
  	<initialTransaction>SB00</initialTransaction> 
	<program>DWLSE030</program> 
	<transaction>SB00</transaction> 
	<connectorId>A05TARE</connectorId> 
	<logApp>true</logApp>
	<logAppServDesc>SALVA SOTTOSISTEMI</logAppServDesc>
	<areaFunzionale>GESTIONE TABELLE-TABELLE DI SISTEMA</areaFunzionale>
	
	<multipleTransaction>true</multipleTransaction> 
    <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
  	<pageRequestField/> 
  	<limitPage>99</limitPage> 
  	<moreDataField/> 
  	<moreDataEndValue>1</moreDataEndValue> 
  	<flagFirstTime absolutePosition="0" length="0" secondValue="" />    
  	<pastedFields> 
  	    <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
  	</pastedFields>      
  	<!-- Definizione commarea: DWCSL006 Lunghezza: 150 + 70 + 1824 = 2044  -->  
  	<input-mapping className="it.sistinf.albedoweb.diba.sottosistemi.dto.SottosistemiRequestDTO" nomeKeyERR="erroriSottosistemi"> 
  		 <!-- input : 62 car --> 
	    <field-map attributeName="codiceSottosistema" 	            length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	    <field-map attributeName="descrizioneSottosistema"   		length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	 </input-mapping> 
 	 <output-mapping className="it.sistinf.albedoweb.diba.sottosistemi.dto.SottosistemiResponseDTO"> 
	      <!-- output : 70 car -->      
	      <field-map attributeName="codiceSottosistema" 	 		length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
	      <field-map attributeName="descrizioneSottosistema"   		length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	      <field-map attributeName="codiceSottosistemaCdErr"		length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>    
	      <field-map attributeName="descrizioneSottosistemaCdErr" 	length="4"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>       
   </output-mapping> 
 </rule> 
</rules> 