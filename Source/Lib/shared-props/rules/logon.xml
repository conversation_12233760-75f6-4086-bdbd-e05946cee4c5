<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>LOGON</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0001</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>false</logApp>
		<logAppServDesc>INGRESSO APPLICAZIONE</logAppServDesc>
		<areaFunzionale>LOGIN</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.common.albedo.login.dto.LoginRequestDTO">
		<!-- Totali 165 car (150 + 24) -->		
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="username" 		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="password" 		length="8"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flagDirezione"	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>		
		</input-mapping>

		<output-mapping className="it.sistinf.common.albedo.login.dto.LoginResponseDTO">
		<!-- Totali  car (150 + 262 + 1824) -->		
			<field-map attributeName="username" 		length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="password" 		length="8"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flagDirezione"	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<!-- output -->
			<field-map attributeName="descSocieta" 	 length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="datiEsterni" length="178">
				<nested-mapping className="it.sistinf.common.albedo.dto.DatiEsterniDTO" iterations="0" blockLength="178">
					<field-map attributeName="codAgenzia" 	 length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="descAgenzia" 	 length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codSubAgenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="desSubAgenzia" length="40" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codPromotore"  length="7"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="desPromotore"  length="60" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="userAlbedo" 	 length="6"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codGruppo" 	 length="3"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="profiloUtente" length="10" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>