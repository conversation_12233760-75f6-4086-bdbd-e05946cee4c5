<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>RIMBORSO-PRESTITI-CONTROLLA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0132</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiRequestDTO">
			<!-- Totali 197 car (150 + 47) -->
			<!-- 47 car -->
			<field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="27">
		            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggCaricaRimborsoPrestiti" length="67" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.CaricaRimborsoPrestiti" iterations="0" blockLength="67">
					<field-map attributeName="dataRimborso"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data" nomeAttributoERR="dataRimborsoCdErr" nomeKeyERR="errorePrestiti"/>
					<field-map attributeName="importoRimborsoNetto"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoRimborsoNettoCdErr" nomeKeyERR="errorePrestiti" />
					<field-map attributeName="importoPrestitoDaRimborsare"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totalePrestitoDaRimborsareCdErr" nomeKeyERR="errorePrestiti" />
					<field-map attributeName="importoConguaglioInteressi"       length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="12" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totaleInteressiCdErr" nomeKeyERR="errorePrestiti" />
					<field-map attributeName="importoPrestitoResiduo"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoResiduoCdErr" nomeKeyERR="errorePrestiti" />
				</nested-mapping>
			</field-map>
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiResponseDTO">
		    <!-- Totali 2051 car (150 + 51 + 1824) --> 
		    <!-- Campi di output 51 car -->
		    <field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="27">
		            <field-map attributeName="categoria"      			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza" 			   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"    			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza" 			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"     			   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"        			   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggCaricaRimborsoPrestiti" length="67" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.CaricaRimborsoPrestiti" iterations="0" blockLength="67">
					<field-map attributeName="dataRimborso"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="importoRimborsoNetto"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoPrestitoDaRimborsare"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoConguaglioInteressi"       length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="12" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoPrestitoResiduo"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="dataRimborsoCdErr"       length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
			<field-map attributeName="importoRimborsoNettoCdErr"       length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		</output-mapping>
	</rule>
</rules>
    