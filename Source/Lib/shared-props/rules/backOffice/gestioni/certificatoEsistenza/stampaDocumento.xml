<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>STAMPA-DOCUMENTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE376</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE376 Lunghezza: 150 + 28 + 288= 150+316= -->  
    <input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.StampaDocumentoRequestDTO">
	<!-- input : 28 car --> 
		<field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="categNum" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 	 	
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numColl" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="codLettera" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="codCompagnia" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="titolo" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="intestatario" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="presso" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="indirizzo" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="cap" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="comune" length="25"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="provincia" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataOdierna" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="polizza" length="13"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataScadenza" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="iban" length="27"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	</input-mapping>
    <output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.StampaDocumentoResponseDTO">
        <field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="categNum" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 	 	
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numColl" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="codLettera" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="codCompagnia" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="titolo" length="15"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="intestatario" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="presso" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="indirizzo" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	 	<field-map attributeName="cap" length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="comune" length="25"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="provincia" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataOdierna" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="polizza" length="13"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataScadenza" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="iban" length="27"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="filler" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="fillerFinale" length="40"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	</output-mapping>
  </rule>
</rules>
