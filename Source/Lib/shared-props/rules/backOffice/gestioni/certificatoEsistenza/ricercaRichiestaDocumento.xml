<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>RICERCA-RICHIESTA-DOCUMENTO</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE367</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE367 Lunghezza: 150 + 35 + 7304= 150+7339= -->  
    <input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.RicercaRichiestaDocumentoRequestDTO">
	<!-- input : 35 car --> 
	    <field-map attributeName="categoriaI" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="agenziaI" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numCollI" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizzaI" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="tipoDocumI" length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataRichiestaI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.RicercaRichiestaDocumentoResponseDTO">
        <field-map attributeName="categoriaI" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="agenziaI" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numCollI" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizzaI" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="tipoDocumI" length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="dataRichiestaI" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    <!-- output : 35 (stessi dati input) + 7304  car =  7339 -->  
    	<field-map attributeName="numEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaRicercaRichiestaDocumento"       length="7300" > 	
			<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.ListaRicercaRichiestaDocumento" iterations="100" blockLength="73">            
    	        <field-map attributeName="tipoDocum" length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    		    <field-map attributeName="dataRicev" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
   				<field-map attributeName="note" length="50"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	    </nested-mapping>
   	    </field-map>    
	 </output-mapping>
  </rule>
</rules>
