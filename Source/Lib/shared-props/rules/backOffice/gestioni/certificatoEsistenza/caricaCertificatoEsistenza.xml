<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>CARICA-CERTIFICATO-ESISTENZA</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE356</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE356 Lunghezza: 150 + 28 + 288= 150+316= -->  
    <input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.CaricaCertificatoEsistenzaRequestDTO">
	<!-- input : 26 car --> 
		<field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="categNum" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 	 	
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numColl" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	</input-mapping>
    <output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.certificatoEsistenza.dto.CaricaCertificatoEsistenzaResponseDTO">
        <field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="categNum" length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=" " offset="" padding="0"/> 	 	
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
	 	<field-map attributeName="numColl" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<!-- output : 28 (stessi dati input) + 288  car =  316 -->  
        <field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>	
        <field-map attributeName="filler" length="278"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 			
    </output-mapping>
  </rule>
</rules>
