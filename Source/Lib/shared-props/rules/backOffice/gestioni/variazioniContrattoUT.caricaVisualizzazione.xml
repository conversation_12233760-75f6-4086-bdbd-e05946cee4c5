<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VISUALIZZAZIONE-VARIAZIONI-CONTRATTO-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE385</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.VariazioniContrattoUTRequestDTO">
			<!-- Totali 199 car (150 + 49) -->
			<!-- 49 car -->
			<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="categNum"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataContabile" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataContabile" nomeKeyERR="erroriVariazioniContrattoUT"/>
			<field-map attributeName="dataEffettoVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataEffettoVariazione" nomeKeyERR="erroriVariazioniContrattoUT"/>
						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.VariazioniContrattoUTResponseDTO">
		    <!-- Totali 2577 car (150 + 49 + 554 + 1824) --> 
		    <!-- Dati input + Output = 603 -->
            <!-- Dati input 49 car -->
             <field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="categNum"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataContabile" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<field-map attributeName="dataEffettoVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<!-- Dati output car 554 -->
			
			<field-map attributeName="oggSovrapremi" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Sovrapremi" iterations="0" blockLength="60">
					<field-map attributeName="sovrapremiSanitario"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiAltro"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiSport"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiProfessione"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="flagImportoTassoSanitario" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoAltro" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoSport" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoProfessione" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggComplementari" length="176" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Complementari" iterations="0" blockLength="176">	
					<field-map attributeName="complementariTassoInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariCapitaleInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari1Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari1Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari2Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari2Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDiariaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDiariaCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDreedTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDreedCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariInvaliditaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariInvaliditaRendita"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="flagTipoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagTipoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementari1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementari2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementariDiaria" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementariDreed" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggRata" length="41" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Rata" iterations="0" blockLength="41">	
					<field-map attributeName="importoRata"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="tassoInteresse"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="true"  numInteri="5" numDecimali="2" separatoreMigliaia="false" />
					<field-map attributeName="dataDecorrenzaAmmortamento"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Data" />
					<field-map attributeName="dataScadenzaMutuo"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Data" />
				</nested-mapping>
			</field-map>
				
			<field-map attributeName="oggDbv" length="18" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Dbv" iterations="0" blockLength="18">	
					<field-map attributeName="diariaTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="diariaDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
					<field-map attributeName="dreedTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="dreedDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
						valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="flagInserimentoComplementari" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoSovrapremi" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoRata" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataScadenzaRata"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
				valida="false" natura="Data" />
			<field-map attributeName="carenza"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
				valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
				
			<field-map attributeName="oggVincoli" length="86" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Vincoli" iterations="0" blockLength="86">
            		<field-map attributeName="flagVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="prestazioneVincolo" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="beneficiarioVincolo" 					length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="dataScadenzaVincolo" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
				</nested-mapping>
			</field-map>
			<field-map attributeName="oggCostanteRivalutato" length="12" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.CostanteRivalutato" iterations="0" blockLength="12">	
					<field-map attributeName="tipoRendita1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="sopram" 					length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="costoOperazioneAgenzia" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			
			<field-map attributeName="flagSubagente" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="subagente" 					length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="scadenzaAnticipata" 					length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="modalitaPagamento" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="defiscalizzazione" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="abi" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cab" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="contoCorrente" 					length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="codiceStat1" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat2" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat3" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat4" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="bloq" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="percentualeAbbProv" 					length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="carico"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
				valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="unitat" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanNazionale" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCcn" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCin" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
						
			<field-map attributeName="dataContabileCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataEffettoVariazioneCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
								
		</output-mapping>
	</rule>
</rules>
    