<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-SCU-LIMITAZIONE-OPERATIVITA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE600</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>DETTAGLIO LIMITAZIONE OPERATIVITA SCU</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	
		<!-- Definizione commarea: VWCSE600 Lunghezza: 150 + 286 (305 + 1) + 1824 = 2260  -->  		
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaRequestDTO">
			<!-- input : 1 car -->   
            <field-map attributeName="livello"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaResponseDTO">   
			<!-- stessi campi di input : 1 car -->   
            <field-map attributeName="livello"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- dati Output 305 car -->
			 <field-map attributeName="dataInizio" 	    length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false"/>
			 <field-map attributeName="ibanNazione"		length="002"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		     <field-map attributeName="ibanCcn"			length="002"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		   	 <field-map attributeName="ibanCin"			length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		     <field-map attributeName="abi"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		     <field-map attributeName="cab"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="contoCorr"		length="030"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/> 
		    <field-map attributeName="intermediario"	length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="dataFine"			length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""  natura="Data" valida="false"/>		   	
		    <field-map attributeName="motivoRevoca"		length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>		   	
		    <field-map attributeName="note"		 		length="120"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" />		    		    		   	
		</output-mapping>		
	</rule>
</rules>
    