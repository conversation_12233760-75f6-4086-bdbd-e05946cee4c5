<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLO-VARIAZIONI-CONTRATTO-DATIANAG</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE375</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	  
	   	<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagRequestDTO">			
			<!-- Totali  4124 = car 150 + 2150 (24 + 1707 + 416) + 1824 -->
			
			<!-- 27 car -->
			<field-map attributeName="categoria"    	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="categNum"			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"	length="006" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numeroColl"		length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numeroPolizza"	length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="livello"			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flPip"			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flFacta"			length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flDac2"			length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			
			<!-- 1707 car -->
			<field-map attributeName="figureAnagrafiche" length="1707">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="1707">
					<field-map attributeName="flCoincidente"     		  		length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					           
	            	<!-- 1706 car -->
					<field-map attributeName="figuraAnagrafica" length="1706">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="1706">
							<!-- 3 car -->
							<field-map attributeName="tipoRuolo" 	  				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    				length="002" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale = 178 car -->
							<field-map attributeName="codiceCliente" 				length="009" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   					length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  					length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    			length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"	    length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"   	 		length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    					length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    		length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza = 75 car -->
							<field-map attributeName="codProvinciaDiResidenza"  	length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    		length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    				length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio  = 205 car -->
							<field-map attributeName="presso"  						length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    		length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"	    	length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"    		length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    				length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    			length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    			length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio = 93 car -->
							<field-map attributeName="codTipoDocumento"    			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    			length="012" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    				length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    						length="005" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte = 336 car -->
							<field-map attributeName="politicamenteEsposta" 	    length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"  	   	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"     				length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     				length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"     			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="titolareEff"  	   			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"	     			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     		length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"     			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"     					length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"     			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     		length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente" 	   	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    		length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"    			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"  	  	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  			length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Numerico" segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<field-map attributeName="titoloStudio"  				length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
								
							<!-- area consensi = 393 car -->
							<field-map attributeName="flConsenso1"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="quotaAssegnata" 	  			length="007" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								/>
							<field-map attributeName="beneficiarioGenerico" 		length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioDescEstesa" 		length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area pagamento = 77 car -->
							<field-map attributeName="tipoPagamento"    			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    		length="040" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"    						length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="esecutore"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"   	 	length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    					length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<!-- 27 car -->
							<field-map attributeName="codiceAzienda"  				length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="012" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="fondoPensione"     		length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"     			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagResponseDTO">
		    <!-- Totali 2150 car = (27 + 1707 + 416) --> 
			
			<!-- 27 car -->
			<field-map attributeName="categoria"    	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="categNum"			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="agenziaPolizza"	length="006" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numeroColl"		length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="numeroPolizza"	length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="livello"			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="flPip"     	    length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flFacta"		    length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flDac2"		    length="001" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			
		    <!-- 1707 car -->
			<field-map attributeName="figureAnagrafiche" length="1707">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="1707">
					<field-map attributeName="flCoincidente"     		  		length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>           	            	
	            						
	            	<!-- 1706 car -->
					<field-map attributeName="figuraAnagrafica" length="1706">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="1706">
							<!-- 3 car -->
							<field-map attributeName="tipoRuolo" 	  				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    				length="002" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale = 178 car -->
							<field-map attributeName="codiceCliente" 				length="009" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   					length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  					length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    			length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
							  natura="Data"/>
							<field-map attributeName="codProvinciaDiNascita"    	length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    					length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    		length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza = 75 car -->
							<field-map attributeName="codProvinciaDiResidenza"  	length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza" 	   	length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    				length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio  = 185 car -->
							<field-map attributeName="presso"  						length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"	    	length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    		length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"    		length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    				length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    			length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    			length="060" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio = 93 car -->
							<field-map attributeName="codTipoDocumento"    			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    			length="012" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    				length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    						length="005" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    					length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte = 336 car -->
							<field-map attributeName="politicamenteEsposta" 	    length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"  	   	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"     				length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     				length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"     			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="titolareEff"     				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"     				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     		length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"     			length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"     					length="005" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"     			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     		length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente" 	   	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    			length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    		length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"    			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"  	  	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 		  		length="007" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Numerico" segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>						
							<field-map attributeName="titoloStudio"  				length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
								
							<!-- area consensi = 393 car -->
							<field-map attributeName="flConsenso1"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="quotaAssegnata" 		  		length="007" precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								/>
							<field-map attributeName="beneficiarioGenerico" 		length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioDescEstesa"	 	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area pagamento = 77 car -->
							<field-map attributeName="tipoPagamento"    			length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    		length="040" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"	    					length="035" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="esecutore"    				length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    			length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"	    	length="030" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    					length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="016" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="020" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- 27 car -->
							<field-map attributeName="codiceAzienda"  				length="003" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="012" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="002" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="010" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="fondoPensione"    		length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"    			length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="001" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
            <!-- Errori Anagrafiche --> 
            <!-- 416 car -->
			<field-map attributeName="figureAnagraficheErr" length="416">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="416">
	            	
	            	<field-map attributeName="flCoincidente"     		  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	            	
	            	<!--Figura Anagrafica 412 car -->
					<field-map attributeName="figuraAnagrafica" length="412">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="412">
							<field-map attributeName="tipoRuolo" 	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"	    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"		    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capRecapito"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"	    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta" 	    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="greenCard"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"  	   	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="usStatus"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="entePubblico"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="socQuotata"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>							
							<field-map attributeName="titolareEff"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod" 	    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaSav"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capSav"     					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="origineFondi"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente" 	   	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="visuraCamerale"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"  	  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 		  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="titoloStudio"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
								
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"	 				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso2"  	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso3"  	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="quotaAssegnata" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico" 		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"	    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="iban"    						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="esecutore"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoRelazione"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="classeBeneficiario"   	 	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flDelega"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gin"    						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						
							<field-map attributeName="codiceAzienda"  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceMatricola"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="qualificaProfessionale"  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataAssunzione"  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="entitaNonFinanziaria"  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataConsComunicazioniElettroniche"  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagDisabile"				length="4"   precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="0000" padding="0"/>
							<field-map attributeName="dataDisabile"    			length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="0000" padding="0"/>
							<field-map attributeName="fondoPensione"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="fiduciaria"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="flPaesiBlackListAR" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagComunicazione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="email"    		  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>