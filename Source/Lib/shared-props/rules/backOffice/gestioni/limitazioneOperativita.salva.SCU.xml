<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-SCU-LIMITAZIONE-OPERATIVITA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE601</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA LIMITAZIONE OPERATIVITA SCU</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	
		<!-- Definizione commarea: VWCSE601 Lunghezza: 150 + 318 (306 + 8) + 1824 = 2292  -->  		
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaRequestDTO">
			<!-- input : 306 car -->  
			<field-map attributeName="livello"      	length="001"    precision="0"   numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataInizio" 	    length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""  natura="Data" valida="true" nomeAttributoERR="dataInizio" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>
			<field-map attributeName="ibanNazione"		length="002"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="ibanCcn"			length="002"	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		   	<field-map attributeName="ibanCin"			length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="abi"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="cab"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="contoCorr"		length="030"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/> 
		    <field-map attributeName="intermediario"	length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="dataFine"			length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataFine" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>		   	
		    <field-map attributeName="motivoRevoca"		length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" />		   	
		    <field-map attributeName="note"		 		length="120"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" />	 
          </input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaResponseDTO">   
			<!-- stessi campi di input : 306 car -->   
			<field-map attributeName="livello"      	length="001"    precision="0"   numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="dataInizio" 	    length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""  natura="Data" />
			<field-map attributeName="ibanNazione"		length="002"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="ibanCcn"			length="002"	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		   	<field-map attributeName="ibanCin"			length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="abi"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="cab"				length="005"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="contoCorr"		length="030"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/> 
		    <field-map attributeName="intermediario"	length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="dataFine"			length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" />		   	
		    <field-map attributeName="motivoRevoca"		length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" />		   	
		    <field-map attributeName="note"		 		length="120"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" />	 

			<!-- dati specifici di Output 12 car -->
			<field-map attributeName="dataInizioErr"	length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />		   	
		    <field-map attributeName="dataFineErr" 		length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />
		    <field-map attributeName="motRevErr" 		length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />
		</output-mapping>		
	</rule>
</rules>
    