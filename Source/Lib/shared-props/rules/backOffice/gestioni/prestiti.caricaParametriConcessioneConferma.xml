<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONCESSIONE-PRESTITI-CONFERMA</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE266</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		
		<logApp>true</logApp>
		<logAppServDesc>SALVA CONCESSIONE PRESTITI</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.dto.PrestitiRequestDTO" nomeKeyERR="errorePrestiti">
			
			<!--Input 163 car -->
            <field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="27">
		            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		 length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="oggConcessionePrestiti" length="86" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ConcessionePrestiti" iterations="0" blockLength="86">
            		<field-map attributeName="importoTotRichiesto"         length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"    nomeAttributoERR="importoTotaleRichiesto" nomeKeyERR="errorePrestiti"/>
            		<field-map attributeName="dirittiQuietanza"            length="11" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="true" natura="Numerico" segnato="false" numInteri="7" numDecimali="02" separatoreMigliaia="true"   nomeAttributoERR="dirittiQuietanza" nomeKeyERR="errorePrestiti"/> 
		            <field-map attributeName="interessiFrazionamento"      length="5"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="true" natura="Numerico" segnato="false" numInteri="2" numDecimali="02" separatoreMigliaia="true"   nomeAttributoERR="interessiFrazionamento" nomeKeyERR="errorePrestiti"/> 
		            <field-map attributeName="modPagamento"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="flMaxConcedibile"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataConcessione"             length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="importoMaxConcedibile"       length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotPrestiti"          length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotDisponibile"       length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="oggInfoConcessionePrestito" length="40" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.InfoConcessionePrestito" iterations="0" blockLength="40">
            		<field-map attributeName="segnoRettifica"             length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="descRettifica"       length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impRettifica"          length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"  nomeAttributoERR="impRettifica" nomeKeyERR="errorePrestiti" />
					<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
			
		</input-mapping>
		
		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiResponseDTO">
		  
		    <!-- Tot 234 car -->
           <field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="27">
		            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		       length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="oggConcessionePrestiti" length="86" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ConcessionePrestiti" iterations="0" blockLength="86">
            		<field-map attributeName="importoTotRichiesto"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="dirittiQuietanza"            length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
		            <field-map attributeName="interessiFrazionamento"      length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
		            <field-map attributeName="modPagamento"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="flMaxConcedibile"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataConcessione"             length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="importoMaxConcedibile"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotPrestiti"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotDisponibile"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="oggInfoConcessionePrestito" length="84" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.InfoConcessionePrestito" iterations="0" blockLength="84">
            		<field-map attributeName="segnoRettifica"      	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="descRettifica"       	length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impRettifica"        	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
            		<field-map attributeName="importoLiquidabile"  	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="interessiPrestiti"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/> 
		            <field-map attributeName="dataProxQuiet"      	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="false" natura="Data"/> 
		            <field-map attributeName="ggComplessivi"        length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="importoTotaleRichiestoCdErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dirittoQuietanzaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="interessiFrazionariCdErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="modalitaPagamentoCdErr"       length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="segnoRettificaCdErr"         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="descrizioneRettificaCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoRettificaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    