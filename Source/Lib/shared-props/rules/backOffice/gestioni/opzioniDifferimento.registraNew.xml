<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-OPZIONI-DIFFERIMENTO-NEW</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE327</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.opzioniDifferimento.dto.OpzioniDifferimentoNewRequestDTO">
			<!-- Totali 236 car (150 + 31) -->
			<!-- ?? car -->
			<field-map attributeName="livello"        				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffettoVariazione"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>              
           
		<!--  DATI RELATIVI ALLA OPZIONE DI DIFFERIMENTO  -->
			<field-map attributeName="anniDiff"           		    length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="percDiff"           		    length="06"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		     valida="true" natura="Numerico"  segnato="false"      numInteri="3" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="percDiff" nomeKeyERR="erroriOpzioniDifferimento" />
		    <field-map attributeName="riemissPur"          		    length="01"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.opzioniDifferimento.dto.OpzioniDifferimentoNewResponseDTO">
		    <!-- Totali 2025 car (150 + 43 + 1824) -->
		    <!--51 --> 
		   
            <field-map attributeName="livello"        				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffettoVariazione"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>              
          
		<!--  DATI RELATIVI ALLA OPZIONE DI DIFFERIMENTO  -->
			<field-map attributeName="anniDiff"           		    length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
			 natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
		    <field-map attributeName="percDiff"           		    length="06"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
		      natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false" />	
		    <field-map attributeName="riemissPur"          		    length="01"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		 
			<!-- output 28 car -->
         
			<field-map attributeName="anniDiffErr"           		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    <field-map attributeName="percDiffErr"           		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>	
		    <field-map attributeName="riemissPurErr"          		length="4"  precision="0" numericScale="0" align="right"   mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		    
		
		</output-mapping>
	</rule>
</rules>
    