<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-VARIAZIONE-RISCHIO-ASS</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE639</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioneRischioAssicurato.dto.VariazioneRischioAssicuratoRequestDTO" nomeKeyERR="erroriVariazioneRischioAssicurato">
			<!-- Totali 182 car (150 + 32) -->
			<!-- 32 car -->
			<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="dataComunicazioneAgenzia" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataComunicazioneCliente" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoVariazione" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioneRischioAssicurato.dto.VariazioneRischioAssicuratoResponseDTO">
		    <!-- Totali 2733 car (150 + 759 + 1824) --> 
		    <!-- Campi di input 32 car -->
			<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="dataComunicazioneAgenzia" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataComunicazioneCliente" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoVariazione" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

			<!-- Dati Output 727 car -->
            <field-map attributeName="flFumatore"       	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
            <field-map attributeName="codiceTariffa"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descrizioneTariffa"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="premioAttuale" 		length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="prestazioneAttuale" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>

			<field-map attributeName="numGaranzie" 					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="garanzie" length="640">
            	<nested-mapping className="it.sistinf.albedoweb.proposta.unitatecniche.dto.DatiTecniciUTResponseDTO" iterations="10" blockLength="64">
		            <field-map attributeName="codiceTariffa"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrizioneTariffa"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flProfessione" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flProfessioneInf" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flSport" 				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tassoImportoProfessione"    length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tassoImportoProfessioneInf" length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="tassoImportoSport"          length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
            </field-map>

			<field-map attributeName="premioComplessivo" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>

		</output-mapping>
	</rule>
</rules>
    