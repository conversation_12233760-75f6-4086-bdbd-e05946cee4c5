<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-FONDI-SWITCH</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE535</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchRequestDTO">
			<!-- Totali 8472 car (150 + 8322) -->
			<!-- 8322 car -->
			<field-map attributeName="codLineaInvestimentoIN" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numFondiMultiramo" 		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiUL" length="8300">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="83">
					<field-map attributeName="flSelezionato"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    	length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="minimo"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  	length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="percentualeFondiUL[].percentuale" nomeKeyERR="erroriFondiRaggruppamento"/>				
					<field-map attributeName="flProtetto"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondi"     		length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchResponseDTO">
		    <!-- Totali 10700 car (150 + 8726 + 1824) --> 
			
			<!-- Dati Output car 8322 + 404 -->
			<field-map attributeName="codLineaInvestimentoIN" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numFondiMultiramo" 		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiUL" length="8300">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="83">
					<field-map attributeName="flSelezionato"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    	length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="minimo"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  	length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>				
					<field-map attributeName="flProtetto"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondi"     		length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false" /> 
 			
 			<!-- ERRORI 400 + 4 car = 404 -->
            <field-map attributeName="elencoFondiULCdErr" 		length="400">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="4">
					<field-map attributeName="percentuale"		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondiCdErr"     	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>        
			
		</output-mapping>
	</rule>
</rules>
    