<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLA-RIMBORSO-PRESTITI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE546</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.rimborsoPrestiti.dto.RimborsoPrestitiRequestDTO" nomeKeyERR="errorePrestiti">
			<!-- Input: 1537 car -->
			<!-- 36 -->
            <field-map attributeName="categoria"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="idPrenotazione" 			length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<!-- 35 -->
			<field-map attributeName="dataPrenotazione"  		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	valida="true" natura="Data" nomeAttributoERR="dataPrenotazione" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="dataRimborso"       		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRimborso" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="tipoRimborso"				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRimborsoPrenotato" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoRimborsoPrenotato" nomeKeyERR="errorePrestiti"/>
			<!-- 1320 -->
			<field-map attributeName="elencoPrestitiPerRimborso"	length="1320" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.rimborsoPrestiti.dto.PrestitoPerRimborsoDTO" iterations="20" blockLength="66">
					<field-map attributeName="dataPrestito"   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data" nomeAttributoERR="dataPrestito" nomeKeyERR="errorePrestiti"/>
					<field-map attributeName="importoPrestito" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoPrestito" nomeKeyERR="errorePrestiti"/>
					<field-map attributeName="importoRimborso" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoRimborso" nomeKeyERR="errorePrestiti"/>
					<field-map attributeName="importoInteressi" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoInteressi" nomeKeyERR="errorePrestiti"/>
					<field-map attributeName="importoResiduo" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoResiduo" nomeKeyERR="errorePrestiti"/>
				</nested-mapping>
			</field-map>
			<!-- 70 -->
			<field-map attributeName="totalePrestiti" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totalePrestiti" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="totaleRimborsi" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totaleRimborsi" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="totaleInteressi" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totaleInteressi" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="totaleResidui" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="totaleResidui" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="importoRimborsoTotale"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoRimborsoTotale" nomeKeyERR="errorePrestiti"/>
			<!-- 76 -->
			<field-map attributeName="modPagamento" length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="iban"			length="27"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intestatario"	length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="bicCode"		length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.rimborsoPrestiti.dto.RimborsoPrestitiResponseDTO">
		    <!-- Totali 3447 (150 + 1549 + 1824 -->
		    <!-- Campi di input 1537 car --> 
		    <!-- 36 -->
            <field-map attributeName="categoria"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="idPrenotazione" 			length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<!-- 35 -->
			<field-map attributeName="dataPrenotazione"  		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
		    	natura="Data"/>
			<field-map attributeName="dataRimborso"       		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoRimborso"				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRimborsoPrenotato" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<!-- 1320 -->
			<field-map attributeName="elencoPrestitiPerRimborso" length="1320" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.rimborsoPrestiti.dto.PrestitoPerRimborsoDTO" iterations="20" blockLength="66">
					<field-map attributeName="dataPrestito"   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoPrestito" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoRimborso" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoInteressi" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="importoResiduo" 	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
			<!-- 70 -->
			<field-map attributeName="totalePrestiti" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totaleRimborsi" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totaleInteressi" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totaleResidui" 			length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="importoRimborsoTotale"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			<!-- 76 -->
			<field-map attributeName="modPagamento" length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="iban"			length="27"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intestatario"	length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="bicCode"		length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- Output: 12 car -->
			<field-map attributeName="dataPrenotazioneCdErr"      		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRimborsoCdErr" 				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoRimborsoPrenotatoCdErr" 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
