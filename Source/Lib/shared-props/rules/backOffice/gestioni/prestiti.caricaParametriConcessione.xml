<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-PARAMETRI-CONCESSIONE-PRESTITI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE265</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiRequestDTO" nomeKeyERR="errorePrestiti">
			<!-- Totali 206 car (150 + 56) -->
			<!-- 56 car -->
			<field-map attributeName="oggParametriPrestiti" length="47" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="47">
		            <field-map attributeName="categoria"      	     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"        length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"            length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"         length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"         length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataContabile"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="dataEffettoVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		 length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0" nomeKeyERR="errorePrestiti"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiResponseDTO">
		    <!-- Totali 2229 car (150 + 255 + 1824) --> 
		    <!-- Campi di output 255 car -->
		    <!-- input 56 -->
		    <field-map attributeName="oggParametriPrestiti" length="47" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="47">
		            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="dataEffettoVariazione"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		       length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				valida="false" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<!-- output 199 -->
			<field-map attributeName="oggConcessionePrestiti" length="116" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ConcessionePrestiti" iterations="0" blockLength="116">
            		<field-map attributeName="importoTotRichiesto"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="dirittiQuietanza"            length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/> 
		            <field-map attributeName="interessiFrazionamento"      length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="false" natura="Numerico" segnato="false" numInteri="2" numDecimali="02" separatoreMigliaia="true"/> 
		            <field-map attributeName="modPagamento"                length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="flMaxConcedibile"            length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataConcessione"             length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="importoMaxConcedibile"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotPrestiti"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
					<field-map attributeName="importoTotDisponibile"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="descrModPagamento"           length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="oggInfoConcessionePrestito" length="83" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.InfoConcessionePrestito" iterations="0" blockLength="83">
            		<field-map attributeName="segnoRettifica"      	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="descRettifica"       	length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impRettifica"        	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
            		<field-map attributeName="importoLiquidabile"  	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="interessiPrestiti"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/> 
		            <field-map attributeName="dataProxQuiet"      	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="false" natura="Data"/> 
		            <field-map attributeName="ggComplessivi"        length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    