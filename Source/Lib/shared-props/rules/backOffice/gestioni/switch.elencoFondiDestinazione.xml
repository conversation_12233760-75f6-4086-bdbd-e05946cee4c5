<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-FONDI-DESTINAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSERD039</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    <!-- Definizione commarea: WSERD039 Lunghezza: 150 + 504 + 1824 = 2478  -->  
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchFondiRequestDTO">
			  <!-- input :10 car -->      
			<field-map attributeName="raggrUl"        		   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchFondiResponseDTO">
		     <!-- output :504 = 504 car -->      
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoFondiDiDestinazione" length="500">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="50">
					<field-map attributeName="codice"  	      length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    