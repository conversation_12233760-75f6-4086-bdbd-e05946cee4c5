<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-MODALITA-PAGAMENTO-PRESTITI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE302</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ModalitaPagamentoPrestitiRequestDTO">
			
			<!-- input 30 car -->
			<field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="47">
		            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="modalitaPagamento"  length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ModalitaPagamentoPrestitiResponseDTO">
		    <!-- Totali 478 + 30 =508 -->
		    <!-- Campi di output 478 car -->
		    <field-map attributeName="oggParametriPrestiti" length="27" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="27">
		            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="modalitaPagamento"  		length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flagBeneficiario"         length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="chiamaBeneficiario"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="flagCoordinateBancarie"   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="oggDatiBeneficiario" length="196" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.DatiBeneficiario" iterations="0" blockLength="196">
					<field-map attributeName="nominativo"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="nome"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceFiscale"  length="16"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="indirizzo"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="localita"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="cap"  length="05"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="provincia"  length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="stato"  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="pressoBeneficiario"  length="50"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggCoordinateBancarie" length="133" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.CoordinateBancarie" iterations="0" blockLength="133">
            		<field-map attributeName="abiDescrizione"  	length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cabDescrizione"  	length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="iban"  			length="27"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="intestatoA"  		length="35"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="swift"  			length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggDatiAggiuntivi" length="62" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.DatiAggiuntivi" iterations="0" blockLength="62">
            		<field-map attributeName="telefono"  length="20"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="statoCivile"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataNascita"  length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Data"/>
					<field-map attributeName="sesso"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="localitaNascita"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            	</nested-mapping>
			</field-map>
			<field-map attributeName="oggAntiReciclaggio" length="80" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.AntiReciclaggio" iterations="0" blockLength="80">
            		
					<field-map attributeName="numeroDocumento"  length="12"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRilascio"  length="10"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="localitaRilascio"  length="20"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="enteRilascio"  length="30"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="gruppo"  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottogruppo"  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="modalitaPagamentoCdErr"  length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				
		</output-mapping>
	</rule>
</rules>
    