<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLO-SINGOLA-ANAGRAFICA-GEST-RUOLO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE389</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>VERIFICA CORRETTEZZA DEL SOGGETTO NELLA FUNZIONALITA CAMBIO SOGGETTO DI POLIZZA</logAppServDesc>
		<areaFunzionale>APRI POLIZZA- GESTIONE ANAGRAFICHE </areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheRequestDTO">
			<!-- Totali: 2136 car =  (1724 + 412)  --> 
			
			<!-- Campi di Input: 24 +  1700 = 1704 car -->
			<field-map attributeName="flPip"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flDac2"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flRelazioniRuoli"      	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"   		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzionePrenotaz" 	length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- 1700 -->
			<field-map attributeName="figureAnagrafiche" length="1700">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="1700"> 
					<field-map attributeName="flCoincidente"     		  		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>           
	            	
	            	<!-- 1699 car -->
					<field-map attributeName="figuraAnagrafica" length="1699">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="1699">
							<field-map attributeName="tipoRuolo"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"					length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente"				length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"						length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"							length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"				length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"			length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"					length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"						length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"			length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"			length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"					length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"				length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"				length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"				length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="enteRilascio"					length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"							length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"	 		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"					length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="titolareEff"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"						length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro" 			length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"			length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"			length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="quotaAssegnata"				length="07"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico"			length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioDescEstesa"		length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"				length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"			length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"							length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="titoloStudio"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data"/>
								
							<field-map attributeName="esecutore"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    		length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="codiceAzienda"  				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="fondoPensione"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario"	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- <field-map attributeName="numMandatoFiduciario"         length="15"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgMandatoFiduciarioPFPG"     length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="motivoCambioAssicurato"    	length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficioAccettato"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="altroRelazione"    			length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="motivoCambioBeneficiario"    	length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRappresentanza"     		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="altroTipoRappresentanza"  	length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	 -->
							
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheResponseDTO">
		   	
   		<!-- Totali: 2136 car =  (1724 + 412)  --> 
			
			<!-- Stessi Campi di Input: 24 +  1700 = 1724 car -->
			<field-map attributeName="flPip"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="flDac2"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flRelazioniRuoli"      	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"   		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoPrenotazione" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzionePrenotaz" 	length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- 1700 -->
			<field-map attributeName="figureAnagrafiche" length="1700">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="1700"> 
					<field-map attributeName="flCoincidente"     		  		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>           
	            	
	            	<!-- 1699 car -->
					<field-map attributeName="figuraAnagrafica" length="1699">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="1699">
							<field-map attributeName="tipoRuolo"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"					length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente"				length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"						length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"							length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"				length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"						length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"			length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"					length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"						length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"			length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"			length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"					length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"				length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"				length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"				length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="enteRilascio"					length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"							length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"	 		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"						length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"					length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="titolareEff"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"						length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"			length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"			length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"					length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"			length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="quotaAssegnata"				length="07"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico"			length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioDescEstesa"		length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"				length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"			length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"							length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="titoloStudio"					length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data"/>
								
							<field-map attributeName="esecutore"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    		length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    					length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="codiceAzienda"  				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="fondoPensione"			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario"	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
            <!-- Errori Anagrafiche --> 
            <!-- 412 car -->
			<field-map attributeName="figureAnagraficheErr" length="412">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="412">            
	            	<field-map attributeName="flCoincidente"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
	            	
	            	<!-- 408 car -->
					<field-map attributeName="figuraAnagrafica" length="408">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="0" blockLength="408">
							<field-map attributeName="tipoRuolo"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cognome"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"							length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"				length="04" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"				length="04" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"				length="04" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="enteRilascio"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"							length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gruppoAR"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="titolareEff"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"						length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCamerale"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>

							<!-- area consensi -->
							<field-map attributeName="flConsenso1"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="quotaAssegnata"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"							length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="titoloStudio"					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
						
							<field-map attributeName="esecutore"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gin"    						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						
							<field-map attributeName="codiceAzienda"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="codiceMatricola"  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="qualificaProfessionale"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="dataAssunzione"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="entitaNonFinanziaria"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataConsComunicazioniElettroniche"  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagDisabile"				length="4"   precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="0000" padding="0"/>
							<field-map attributeName="dataDisabile"    			length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="0000" padding="0"/>
							<field-map attributeName="fondoPensione"			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario"	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="flPaesiBlackListAR" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagComunicazione" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="email"    		  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>