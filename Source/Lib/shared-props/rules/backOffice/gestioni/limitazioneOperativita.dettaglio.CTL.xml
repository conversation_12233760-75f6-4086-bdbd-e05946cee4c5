<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>DETTAGLIO-CTL-LIMITAZIONE-OPERATIVITA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE335</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	
		<!-- Definizione commarea: WVCOMD52 Lunghezza: 150 + 32 (31 + 1) + 1824 = 2006  -->  		
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaRequestDTO">
			<!-- input : 1 car -->   
            <field-map attributeName="livello"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaResponseDTO">   
			<!-- stessi campi di input : 1 car -->   
            <field-map attributeName="livello"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

			<!-- dati Output 31 car -->
		    <field-map attributeName="sequestroSN"	length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/> 
		    <field-map attributeName="dataEffetto"	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false"/>
		    <field-map attributeName="dataInizio" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false"/>
		    <field-map attributeName="dataFine" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false"/>
		</output-mapping>		
	</rule>
</rules>
    