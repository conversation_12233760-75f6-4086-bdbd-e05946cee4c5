<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>MODIFICA-STORNI-INCASSI</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE380</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <logApp>true</logApp>
	<logAppServDesc>MODIFICA STORNI INCASSI</logAppServDesc>
	<areaFunzionale>APRI POLIZZA/APRI UNITA TECNICA-VARIAZIONI</areaFunzionale>
    
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLSE380 Lunghezza: 150 + 70 + 4 +300*63+16= 150+18990= -->  
    <input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.storniIncassi.dto.ModificaStorniIncassiRequestDTO">
	<!-- input : 70 car --> 
		<field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=" "/>
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="posizioneTest" length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="dataContabile" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataVariazione" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataScadenzaDa" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataScadenzaA" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />				   
	 	<field-map attributeName="tipoSelezione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 	 	
	    <field-map attributeName="numEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaStorniIncassi"       length="18900" > 	
			<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.storniIncassi.dto.StornoIncasso" iterations="300" blockLength="63">            
    	        <field-map attributeName="selezione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    			<field-map attributeName="dataScadQui" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    		    <field-map attributeName="premioRata" length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	      	<field-map attributeName="tipoQuietanza" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="quotaStornata" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="dataIncasso" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" valida="true" nomeAttributoERR="listaErrori[].dataIncasso" nomeKeyERR="erroriModificaStorniIncassi"/>			
    		    <field-map attributeName="importo" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true" valida="true" nomeAttributoERR="listaErrori[].importo" nomeKeyERR="erroriModificaStorniIncassi"/>  		
    		    <field-map attributeName="dupkey" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>    		  
    	    </nested-mapping>
   	    </field-map> 	 
	</input-mapping>	 	
    <output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.storniIncassi.dto.ModificaStorniIncassiResponseDTO">
        <field-map attributeName="livello" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="categoria" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding=""/> 
	 	<field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=" "/>
	 	<field-map attributeName="numColl" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 	 	
	 	<field-map attributeName="numPolizza" length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="posizioneTest" length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/> 
	 	<field-map attributeName="dataContabile" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataVariazione" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataScadenzaDa" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />			
	    <field-map attributeName="dataScadenzaA" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data" />				   
	 	<field-map attributeName="tipoSelezione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="numEleTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaStorniIncassi"       length="18900" > 	
			<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.storniIncassi.dto.StornoIncasso" iterations="300" blockLength="63">            
    	        <field-map attributeName="selezione" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    			<field-map attributeName="dataScadQui" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    		    <field-map attributeName="premioRata" length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	      	<field-map attributeName="tipoQuietanza" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="quotaStornata" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="dataIncasso" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>			
    		    <field-map attributeName="importo" length="14" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" natura="Numerico" segnato="false" 
		        			numInteri="9" numDecimali="2" separatoreMigliaia="true"/>  		
    		    <field-map attributeName="dupkey" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>    		  
    	    </nested-mapping>
   	    </field-map>	 	
	 	<!-- output :  (stessi dati input) + 16  car =   -->    	   
	 	<field-map attributeName="listaErrori"       length="21374" > 	
			<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.storniIncassi.dto.ListaErrori" iterations="300" blockLength="8">            
    	        <field-map attributeName="errDataIncasso" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>
				<field-map attributeName="errImporto" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="0" offset="" padding="0"/>     		  
    	    </nested-mapping>
   	    </field-map>	 	 	           	    
  	 </output-mapping>
  </rule>
</rules>
