<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRAZIONE-VARIAZIONI-CONTRATTO-UT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE386</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>SALVA VARIAZIONI DI CONTRATTO UT</logAppServDesc>
	    <areaFunzionale>APRI UNITA TECNICA-VARIAZIONI</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.VariazioniContrattoUTRequestDTO">
			<!-- Totali 11883 car (150 + 12265) -->
			<!-- 42 car prestVincolata -->
			<field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="categNum"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataContabile" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataContabile" nomeKeyERR="erroriVariazioniContrattoUT"/>
			<field-map attributeName="dataEffettoVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataEffettoVariazione" nomeKeyERR="erroriVariazioniContrattoUT"/>
			
			<field-map attributeName="oggSovrapremi" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Sovrapremi" iterations="0" blockLength="60">
					<field-map attributeName="sovrapremiSanitario"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrapremiSanitario" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="sovrapremiAltro"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrapremiAltro" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="sovrapremiSport"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrapremiSport" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="sovrapremiProfessione"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="sovrapremiProfessione" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="flagImportoTassoSanitario" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoAltro" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoSport" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoProfessione" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggComplementari" length="176" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Complementari" iterations="0" blockLength="176">	
					<field-map attributeName="complementariTassoInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariTassoInfortuni" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariCapitaleInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariCapitaleInfortuni" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementari1Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementari1Tasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementari1Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementari1Capitale" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementari2Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementari2Tasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementari2Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementari2Capitale" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariDiariaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariDiariaTasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariDiariaCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariDiariaCapitale" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariDreedTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariDreedTasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariDreedCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariDreedCapitale" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariInvaliditaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariInvaliditaTasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="complementariInvaliditaRendita"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="complementariInvaliditaRendita" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="flagTipoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagTipoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementari1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementari2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementariDiaria" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementariDreed" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggRata" length="41" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Rata" iterations="0" blockLength="41">
					<field-map attributeName="importoRata"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoRata" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="tassoInteresse"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="5" numDecimali="2" separatoreMigliaia="false" nomeAttributoERR="tassoInteresse" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="dataDecorrenzaAmmortamento"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Data" nomeAttributoERR="dataDecorrenzaAmmortamento" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="dataScadenzaMutuo"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Data" nomeAttributoERR="dataScadenzaMutuo" nomeKeyERR="erroriVariazioniContrattoUT"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggDbv" length="18" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Dbv" iterations="0" blockLength="18">	
					<field-map attributeName="diariaTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="diariaTasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="diariaDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="diariaDurata" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="dreedTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="dreedTasso" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="dreedDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="dreedDurata" nomeKeyERR="erroriVariazioniContrattoUT"/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="flagInserimentoComplementari" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoSovrapremi" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoRata" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataScadenzaRata"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"
				valida="true" natura="Data" nomeAttributoERR="dataScadenzaRata" nomeKeyERR="erroriVariazioniContrattoUT"/>
			<field-map attributeName="carenza"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="carenza" nomeKeyERR="erroriVariazioniContrattoUT"/>
			
			<field-map attributeName="oggVincoli" length="86" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Vincoli" iterations="0" blockLength="86">
            		<field-map attributeName="flagVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="prestazioneVincolo" 					length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true"  nomeAttributoERR="prestazioneVincolo" nomeKeyERR="erroriVariazioniContrattoUT"/>
					<field-map attributeName="beneficiarioVincolo" 					length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="dataScadenzaVincolo" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data"  nomeAttributoERR="dataScadenzaVincolo" nomeKeyERR="erroriVariazioniContrattoUT"/>
				</nested-mapping>
			</field-map>	
            	
			<field-map attributeName="oggCostanteRivalutato" length="12" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.CostanteRivalutato" iterations="0" blockLength="12">	
					<field-map attributeName="tipoRendita1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="sopram" 					length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="costoOperazioneAgenzia" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			
			<field-map attributeName="flagSubagente" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="subagente" 					length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="scadenzaAnticipata" 					length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="modalitaPagamento" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="defiscalizzazione" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="abi" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cab" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="contoCorrente" 					length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="codiceStat1" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat2" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat3" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat4" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="bloq" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="percentualeAbbProv" 					length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="carico"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
				valida="true" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="carico" nomeKeyERR="erroriVariazioniContrattoUT"/>
			<field-map attributeName="unitat" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanNazionale" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCcn" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCin" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati identificativi (374) -->
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.VariazioniContrattoUTResponseDTO">
		    <!-- Totali 15943 car (150 + 14041 + 1824) --> 
		    <!-- Dati input + Output 13969 -->
            <!-- Dati input 12265 car -->
             <field-map attributeName="livello"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="categNum"      length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione" length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataContabile" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<field-map attributeName="dataEffettoVariazione" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Data" />
			<!-- Dati identificativi (374) -->
			
			<field-map attributeName="oggSovrapremi" length="60" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Sovrapremi" iterations="0" blockLength="60">
					<field-map attributeName="sovrapremiSanitario"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiAltro"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiSport"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="sovrapremiProfessione"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="flagImportoTassoSanitario" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoAltro" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoSport" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoProfessione" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggComplementari" length="176" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Complementari" iterations="0" blockLength="176">	
					<field-map attributeName="complementariTassoInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariCapitaleInfortuni"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari1Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari1Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari2Tasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementari2Capitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDiariaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDiariaCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDreedTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariDreedCapitale"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariInvaliditaTasso"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="complementariInvaliditaRendita"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="flagTipoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagTipoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInvalidita" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoInfortunio" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementari1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementari2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
					<field-map attributeName="flagImportoTassoComplementariDiaria" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagImportoTassoComplementariDreed" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggRata" length="41" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Rata" iterations="0" blockLength="41">
					<field-map attributeName="importoRata"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="tassoInteresse"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="true"  numInteri="5" numDecimali="2" separatoreMigliaia="false" />
					<field-map attributeName="dataDecorrenzaAmmortamento"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Data" />
					<field-map attributeName="dataScadenzaMutuo"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Data" />
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggDbv" length="18" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Dbv" iterations="0" blockLength="18">	
					<field-map attributeName="diariaTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="diariaDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
					<field-map attributeName="dreedTasso"         		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="dreedDurata"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="flagInserimentoComplementari" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoSovrapremi" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagInserimentoRata" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataScadenzaRata"         		length="10" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
				valida="false" natura="Data" />
			<field-map attributeName="carenza"         		length="2" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
				valida="false" natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false" />
			
			<field-map attributeName="oggVincoli" length="86" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.Vincoli" iterations="0" blockLength="86">
            		<field-map attributeName="flagVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="prestazioneVincolo" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					<field-map attributeName="beneficiarioVincolo" 					length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoVincolo" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="dataScadenzaVincolo" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="oggCostanteRivalutato" length="12" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContrattoUT.dto.CostanteRivalutato" iterations="0" blockLength="12">	
					<field-map attributeName="tipoRendita1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale1" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale2" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale3" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale4" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale5" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoCapitale6" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>	
			
			
			<field-map attributeName="sopram" 					length="4" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="costoOperazioneAgenzia" 					length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			
			<field-map attributeName="flagSubagente" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="subagente" 					length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="scadenzaAnticipata" 					length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="modalitaPagamento" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="defiscalizzazione" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="abi" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="cab" 					length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="contoCorrente" 					length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="codiceStat1" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat2" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat3" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceStat4" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="bloq" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="percentualeAbbProv" 					length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="carico"         		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
				valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="unitat" 					length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanNazionale" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCcn" 					length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="ibanCin" 					length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- Dati anagrafici (371 + 10920= 11291) -->
			
			<field-map attributeName="sovrapremiSanitarioCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="sovrapremiAltroCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="sovrapremiSportCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="sovrapremiProfessioneCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoSanitarioCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoAltroCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoSportCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoProfessioneCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariTassoInfortuniCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariCapitaleInfortuniCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementari1TassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementari1CapitaleCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementari2TassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementari2CapitaleCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariDiariaTassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariDiariaCapitaleCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariDreedTassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariDreedCapitaleCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariInvaliditaTassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="complementariInvaliditaRenditaCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagTipoInfortunioCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagTipoInvaliditaCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoInvaliditaCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoInfortunioCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoComplementari1CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoComplementari2CdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoComplementariDiariaCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagImportoTassoComplementariDreedCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoRataCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tassoInteresseCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDecorrenzaAmmortamentoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataScadenzaMutuoCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="diariaTassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="diariaDurataCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dreedTassoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dreedDurataCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataScadenzaRataCdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="carenzaCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagVincoloCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="prestazioneVincoloCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="beneficiarioVincoloCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoVincoloCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataScadenzaVincoloCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita1CdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale1CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita2CdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale2CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita3CdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale3CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita4CdErr"    			length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale4CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita5CdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale5CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoRendita6CdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoCapitale6CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="sopramCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="costoOperazioneAgenziaCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagSubagenteCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="subagenteCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="scadenzaAnticipataCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="modalitaPagamentoCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="defiscalizzazioneCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="abiCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="cabCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contoCorrenteCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceStat1CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceStat2CdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceStat3CdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="codiceStat4CdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="bloqCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeAbbProvCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="caricoCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="unitatCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ibanNazionaleCdErr"  	      		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ibanCcnCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ibanCinCdErr"    			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
								
		</output-mapping>
	</rule>
</rules>
    