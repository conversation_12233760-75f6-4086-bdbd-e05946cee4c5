<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-DETTAGLIO-TRASFERIMENTO-FONDI-ENTRATA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE406</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA TRASFERIMENTO FONDI ENTRATA</logAppServDesc>
		<areaFunzionale>UNITA TECNICA-TRASFERIMENTO FONDI ENTRATA</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.trasferimentoFondiEntrata.dto.TrasferimentoFondiEntrataRequestDTO">
			<!-- Totali 1281 car (150 + 1075 + 56) -->
		
		    <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataTrasferimentoCessione"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataTrasferimentoCessione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="denominazioneProvenienza"        	length="27" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipologia"        				length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataIscrizPrimoFondo"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataIscrizPrimoFondo" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiIscritto1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiIscritto1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiAzienda1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiAzienda1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiTfr1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiTfr1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiIscritto2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiIscritto2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiAzienda2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiAzienda2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiTfr2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiTfr2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiIscritto3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiIscritto3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiAzienda3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiAzienda3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contributiTfr3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contributiTfr3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiIscritto1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiIscritto1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiAzienda1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiAzienda1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiTfr1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTfr1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiIscritto2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiIscritto2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiAzienda2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiAzienda2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiTfr2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTfr2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiIscritto3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiIscritto3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiAzienda3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiAzienda3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiTfr3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTfr3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			
			<field-map attributeName="mesiApp1"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="mesiApp1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="anticipiErogati1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="anticipiErogati1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="impostePagate1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="impostePagate1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			
			<field-map attributeName="mesiApp2"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="mesiApp2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="anticipiErogati2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="anticipiErogati2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="impostePagate2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="impostePagate2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			
			<field-map attributeName="mesiApp3"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="mesiApp3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="anticipiErogati3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="anticipiErogati3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="impostePagate3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="impostePagate3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			
			<field-map attributeName="aliquotaTfr"        				length="7" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="03" separatoreMigliaia="false" nomeAttributoERR="aliquotaTfr" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="franchigiaTfr"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="franchigiaTfr" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="oneriPagati"        				length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="oneriPagati" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="detrazione4per100"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="detrazione4per100" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contrNonDedotti2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contrNonDedotti2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiTassati2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTassati2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="contrNonDedotti3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="contrNonDedotti3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="rendimentiTassati3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTassati3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="costiTrasferimento"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="costiTrasferimento" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="montanteRicezione1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="montanteRicezione1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="montanteRicezione2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="montanteRicezione2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="montanteRicezione3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="montanteRicezione3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			
			<field-map attributeName="codSocCedente"        			length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="codSocCedente" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="fondoProvenienza"        			length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="codFondoProvenienza" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="tipoFondo"        				length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="codTipologiaFondo" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="ramo"        						length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="codRamo" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="numPolizzaCedente"        		length="7" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="numPolizzaCedente" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataIscrizioneFondo"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataIscrizioneFondo" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataDirezione"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataArrivoDirezione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataBonifico"        				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataArrivoBonifico" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataDocCedente"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataRichiestaDocCedente" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataInfoCedente"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataRichiestaInfoCedente" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="infoRichieste"        			length="200" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="infoRichieste" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagDecadimento"        			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="true" nomeAttributoERR="flagDecadimentoRichiesta" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataDecadimento"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataDecadimentoRichiesta" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="integrazioneImportoTrasferito"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"  nomeAttributoERR="importoIntegrazione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="parzialeImportoTrasferito"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"  nomeAttributoERR="importoRestituzioneParziale" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="totaleImportoTrasferito"        	length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"  nomeAttributoERR="importoRestituzioneTotale" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagAutorizzazione"	        	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagAutorizzazione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataAutorizzazione"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataAutorizzazione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="letteraAutorizzazione"        	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="letteraAutorizzazione" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagLetteraAutorizzazioneInviata" length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagLetteraAutorizzazioneInviata" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagDatiIncompleti"        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagDatiIncompleti" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagSollecito1"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagSollecito1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataSollecito1"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataSollecito1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagSollecito2"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagSollecito2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataSollecito2"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataSollecito2" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagSollecito3"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagSollecito3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataSollecito3"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data"  nomeAttributoERR="dataSollecito3" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="flagAccettaMail"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="flagAccettaMail" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="mailSocieta"        				length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" nomeAttributoERR="mailSocieta" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="dataPervenimentoZainetto"       	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Data" nomeAttributoERR="dataPervenimentoZainetto" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="progressivoVersamentoAgg"    		length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="importoVersamentoAgg"    			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="dataVersamentoAgg"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="partitaIva"       				length="11" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="datoreLavoro"       				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="rendimentiTassati1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="rendimentiTassati1" nomeKeyERR="erroriTrasferimentoFondiEntrata"/>
			<field-map attributeName="tipoIscritto"        				length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.trasferimentoFondiEntrata.dto.TrasferimentoFondiEntrataResponseDTO">
		 	<!--Campi di output 1090 + 284 car -->
		    <!--Totali 3415 car (150 + 1441 + 1824) -->
            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="dataTrasferimentoCessione"        length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="denominazioneProvenienza"        	length="27" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipologia"        				length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataIscrizPrimoFondo"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="contributiIscritto1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiAzienda1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiTfr1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiIscritto2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiAzienda2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiTfr2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiIscritto3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiAzienda3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contributiTfr3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiIscritto1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiAzienda1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiTfr1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiIscritto2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiAzienda2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiTfr2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiIscritto3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiAzienda3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiTfr3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="mesiApp1"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="anticipiErogati1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="impostePagate1"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="mesiApp2"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="anticipiErogati2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="impostePagate2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="mesiApp3"        					length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="0" separatoreMigliaia="false" />
			<field-map attributeName="anticipiErogati3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="impostePagate3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="aliquotaTfr"        				length="7" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="03" separatoreMigliaia="false" />
			<field-map attributeName="franchigiaTfr"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="oneriPagati"        				length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="detrazione4per100"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contrNonDedotti2"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiTassati2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="contrNonDedotti3"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="rendimentiTassati3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="costiTrasferimento"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="montanteRicezione1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="montanteRicezione2"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="montanteRicezione3"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="codSocCedente"        			length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="fondoProvenienza"        			length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoFondo"        				length="3" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="ramo"        						length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="numPolizzaCedente"        		length="7" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataIscrizioneFondo"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="dataDirezione"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="dataBonifico"        				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="dataDocCedente"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="dataInfoCedente"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="infoRichieste"        			length="200" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flagDecadimento"        			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataDecadimento"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="integrazioneImportoTrasferito"    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="parzialeImportoTrasferito"        length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="totaleImportoTrasferito"        	length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="flagAutorizzazione"	        	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataAutorizzazione"        		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="letteraAutorizzazione"        	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="flagLetteraAutorizzazioneInviata"	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="flagDatiIncompleti"        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagSollecito1"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="flagSollecito2"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="flagSollecito3"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataSollecito3"        			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="flagAccettaMail"	        		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="mailSocieta"        				length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataPervenimentoZainetto"       	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
			<field-map attributeName="progressivoVersamentoAgg"    		length="9" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="importoVersamentoAgg"    			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="dataVersamentoAgg"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="partitaIva"       				length="11" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="datoreLavoro"       				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
			<field-map attributeName="rendimentiTassati1"        		length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			<field-map attributeName="tipoIscritto"        				length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="totaleCapitale"        			length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" />
			
			<field-map attributeName="dataTrasferimentoCessioneCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="denominazioneProvenienzaCdErr"  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipologiaCdErr"  	         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataIscrizPrimoFondoCdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoIscrittoCdErr"  	        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiIscritto1CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiAzienda1CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiTfr1CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiIscritto2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiAzienda2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiTfr2CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiIscritto3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiAzienda3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contributiTfr3CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiIscritto1CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiAzienda1CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTfr1CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiIscritto2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiAzienda2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTfr2CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiIscritto3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiAzienda3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTfr3CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="mesiApp1CdErr"  	         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anticipiErogati1CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="impostePagate1CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="mesiApp2CdErr"  	         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anticipiErogati2CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="impostePagate2CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="mesiApp3CdErr"  	         		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="anticipiErogati3CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="impostePagate3CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="aliquotaTfrCdErr"  	         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="franchigiaTfrCdErr"  	         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="oneriPagatiCdErr"  	         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="detrazione4per100CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contrNonDedotti2CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTassati2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="contrNonDedotti3CdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTassati3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="costiTrasferimentoCdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totaleCapitaleCdErr"  	        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="montanteRicezione1CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="montanteRicezione2CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="montanteRicezione3CdErr"  	    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="codSocCedenteErr"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="fondoProvenienzaErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="tipoFondoErr"        				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="ramoErr"        					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="numPolizzaCedenteErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataIscrizioneFondoErr"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDirezioneErr"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataBonificoErr"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDocCedenteErr"       			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataInfoCedenteErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="infoRichiesteErr"        			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagDecadimentoErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDecadimentoErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="integrazioneImportoTrasferitoErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="parzialeImportoTrasferitoErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totaleImportoTrasferitoErr"       length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataAutorizzazioneErr"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="letteraAutorizzazioneErr"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
	        <field-map attributeName="dataSollecito1Err"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataSollecito2Err"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataSollecito3Err"       			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="flagDatiIncompletiErr"        	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="mailSocietaErr"       			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataPervenimentoZainettoErr"      length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoVersamentoAggErr"       	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataVersamentoAggErr"       		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="partitaIvaErr"       				length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="datoreLavoroErr"       			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="rendimentiTassati1Err"       		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>