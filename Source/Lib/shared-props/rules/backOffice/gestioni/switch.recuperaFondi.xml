<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-FONDI-PER-SWITCH</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE190</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	    <!-- Definizione commarea: VWCSE190 Lunghezza: 150 + 1218 + 1824 = 3192  -->  
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchRequestDTO">
			  <!-- input :44 car -->      
			<field-map attributeName="livello"        			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"  nomeAttributoERR="dataContabile" nomeKeyERR="erroriSwitch"/>
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"  nomeAttributoERR="dataStorno" nomeKeyERR="erroriSwitch"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"  nomeAttributoERR="dataDisinvestimento" nomeKeyERR="erroriSwitch"/>
			<field-map attributeName="dataRichiesta"    		   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"  nomeAttributoERR="dataRichiesta" nomeKeyERR="erroriSwitch"/>
			<field-map attributeName="tipoSwitch"	    		   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchResponseDTO">
		     <!-- output :44 + 1174 = 1218 car -->      
			<field-map attributeName="livello"        			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="dataRichiesta"    		   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoSwitch"	    		   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="costoSwitch"	    		   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="raggrUl"	    		   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
			<field-map attributeName="elencoFondiProvenienza"     length="660" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.FondiSwitchProvenienzaDTO" iterations="10" blockLength="66">
					<field-map attributeName="fondoSelezionato"        	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFondo"  			   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneFondo"    		   length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroQuote"    			   length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreFondoSwitch"    	   length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoFondiDestinazione"     length="420" >
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="42">
					<field-map attributeName="codice"  			   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    	   length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="percentuale"    	   length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="valoreFondoSwitch1CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch2CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch3CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch4CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch5CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch6CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch7CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch8CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch9CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="valoreFondoSwitch10CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
			<field-map attributeName="percentualeDest1CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest2CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest3CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest4CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest5CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest6CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest7CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest8CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest9CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percentualeDest10CdErr"    			length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
		</output-mapping>
	</rule>
</rules>
    