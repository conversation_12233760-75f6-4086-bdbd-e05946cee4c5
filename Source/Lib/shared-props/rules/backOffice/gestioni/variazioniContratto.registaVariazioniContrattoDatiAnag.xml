<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-VARIAZIONI-CONTRATTO-DATIANAG</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE374</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA VARIAZIONI DI CONTRATTO DATI ANAGRAFICI</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   	
	   	<!-- Totali 24318 car = 19122 (Input) + 5196 (Area Errori) -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagRequestDTO">
			<!-- 19122 car = 19073 + 49 car -->
			<field-map attributeName="categoria"      			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" />
   			<field-map attributeName="categNum"       			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="" />
   			<field-map attributeName="agenziaPolizza" 			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" />
            <field-map attributeName="numeroColl"     			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="livello"        			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" />
			<field-map attributeName="dataContabile"  			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" 
				natura="Data" valida="false" nomeAttributoERR="dataContabile" nomeKeyERR="erroriVariazioniContrattoDatiAnag" />
           	<field-map attributeName="dataEffettoVariazione"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" 
           		natura="Data" valida="false" nomeAttributoERR="dataEffettoVariazione" nomeKeyERR="erroriVariazioniContrattoDatiAnag" />
		    <field-map attributeName="flModPag"     		  	length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding="" />	
			<field-map attributeName="flPip"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flFacta"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flDac2"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
							
			<!-- 19073 char = 18373 + 350 + 350 -->
			<field-map attributeName="figureAnagrafiche" length="18373">
				<!-- 1 + 2224 + 1768 + 924 + 2224 + 3854 + 3744 + 531 + 604 + 1009 + 583 + 531 + 376= 18373 -->
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="18373">					
	            	<!-- 1 char -->	            	
	            	<field-map attributeName="flCoincidente"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					
					<field-map attributeName="numElementiTrovatiContraenti" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000" offset="" padding="0"/>
					<field-map attributeName="flAltriContraenti"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>					
					<field-map attributeName="contraenti" length="2220">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="1110">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"     length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"     	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"     			length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="titolareEff"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"     			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"     		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"     				length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"    	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"    		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="fondoPensione"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"   	  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Assicurati 1768 = 4 + 1764 car -->		    
					<field-map attributeName="numElementiTrovatiAssicurati" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriAssicurati"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="assicurati" length="1764">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="882">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="titoloStudio"  				length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="codiceAzienda"  				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!--465-->
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
						</nested-mapping>
					</field-map>
					
					<!--RAPPRESENTANTI LEGALI (2) 924 = 4 + 920 car -->		    
					<field-map attributeName="numElementiTrovatiLegali" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriLegali"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="legali" length="920">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="460">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
						</nested-mapping>
					</field-map>
					
					
					<!--Titolari effettivi 2224 car -->
					<field-map attributeName="numElementiTrovatiTitolari" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriTitolari"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="titolari" length="2220">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="4" blockLength="555">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Numerico" segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Morte 3854 car = 4 + 3850 -->		    
					<field-map attributeName="numElementiTrovatiBenMorte" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenMorte"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="beneficiariMorte" length="3850">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="385">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" 
								valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="3" separatoreMigliaia="false"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="beneficiarioGenerico" 	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="55" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
								
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Vita 3744 car = 4 + 3740 -->		    
					<field-map attributeName="numElementiTrovatiBenVita" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenVita"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiariVita" length="3740">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="374">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" 
								valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="3" separatoreMigliaia="false"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="beneficiarioGenerico" 	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="55" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data"  />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari cedola 531 = 4 + 527 car -->		    
					<field-map attributeName="numElementiTrovatiBenCedola" 		length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenCedola"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiariCedola" length="527">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="527">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"	    				length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Esecutore 604 car -->	 	    
					<field-map attributeName="numElementiTrovatiEsecutore" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriEsecutore"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="esecutore" length="600">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="600">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""	natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio  -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""	natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""	natura="Data" />
						</nested-mapping>
					</field-map>
					
					<!--Azienda Associata 1009 car -->	 	    
					<field-map attributeName="numElementiTrovatiAziendaAssociata"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriAziendaAssociata"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="aziendaAssociata" length="1005">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="1005">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="titolareEff"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="visuraCamerale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Terzo Pagatore 583 car -->	 	    
					<field-map attributeName="numElementiTrovatiTerzoPagatore" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriTerzoPagatore"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="terzoPagatore" length="579">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="579">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio  -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiario rendita 531 car -->		    
					<field-map attributeName="numElementiTrovatiBeneficiarioRendita" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBeneficiarioRendita"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiarioRendita" length="527">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="527">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="contoCor"    				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Referente Terzo 376 car -->	 	    
					<field-map attributeName="numElementiTrovatiReferenteTerzo" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriReferenteTerzo"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="referenteTerzo" length="372">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="372">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
						
				</nested-mapping>
			</field-map>
			<field-map attributeName="beneficiarioMorteDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="beneficiarioVitaDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagResponseDTO">
		   	<!-- Totali 24318 car = 19122 (Input) + 5196 (Area Errori) -->
			
			<!-- 19122 car = 19073 + 49 car -->
			<field-map attributeName="categoria"      			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
   			<field-map attributeName="categNum"       			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" 			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default=""  offset="" padding="0"/>
			<field-map attributeName="livello"        			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""/>
			<field-map attributeName="dataContabile"  			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" 
				natura="Data" valida="false" nomeAttributoERR="dataContabile" nomeKeyERR="erroriVariazioniContrattoDatiAnag" />
            <field-map attributeName="dataEffettoVariazione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding="" 
            	natura="Data" valida="false" nomeAttributoERR="dataEffettoVariazione" nomeKeyERR="erroriVariazioniContrattoDatiAnag" />
		   	<field-map attributeName="flModPag"        			length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>	
			<field-map attributeName="flPip"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flFacta"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flDac2"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
							
			<!-- 19073 char = 18373 + 350 + 350 -->
			<field-map attributeName="figureAnagrafiche" length="18373">
				<!-- 1 + 2220 + 1768 + 924 + 2224 + 3854 + 3744 + 531 + 604 + 1009 + 583 + 531 = 18373 -->
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="18373">					
	            	<!-- 1 char -->	            	
	            	<field-map attributeName="flCoincidente"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					
					<field-map attributeName="numElementiTrovatiContraenti" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000" offset="" padding="0"/>
					<field-map attributeName="flAltriContraenti"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>					
					<field-map attributeName="contraenti" length="2220">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="1110">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"     length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"     	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"     			length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="titolareEff"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"     			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"     		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"     				length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"    	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="visuraCamerale"    		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="fondoPensione"     		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="fiduciaria"     			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Assicurati 1762 = 4 + 1764 car -->		    
					<field-map attributeName="numElementiTrovatiAssicurati" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriAssicurati"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="assicurati" length="1764">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="882">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<field-map attributeName="titoloStudio"  				length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" valida="false"/>
							<field-map attributeName="codiceAzienda"  				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceMatricola"  			length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="qualificaProfessionale"  		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataAssunzione"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!--465-->
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
						</nested-mapping>
					</field-map>
					
					<!--RAPPRESENTANTI LEGALI (2) 924 = 4 + 920 car -->		    
					<field-map attributeName="numElementiTrovatiLegali" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriLegali"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="legali" length="920">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="460">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
						</nested-mapping>
					</field-map>
					
					
					<!--Titolari effettivi 2224 car -->
					<field-map attributeName="numElementiTrovatiTitolari" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriTitolari"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="titolari" length="2220">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="4" blockLength="555">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>	
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Numerico" segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
							<!-- DAC 59-->
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Morte 3854 car = 4 + 3850 -->		    
					<field-map attributeName="numElementiTrovatiBenMorte" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenMorte"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="beneficiariMorte" length="3850">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="385">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" />
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" 
								valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="3" separatoreMigliaia="false"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="beneficiarioGenerico" 	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="55" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false"/>
							
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Vita 3744 car = 4 + 3740 -->		    
					<field-map attributeName="numElementiTrovatiBenVita" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenVita"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiariVita" length="3740">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="374">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<field-map attributeName="quotaAssegnata" 	  		length="07" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0" 
								valida="false" natura="Numerico" segnato="false" numInteri="03" numDecimali="3" separatoreMigliaia="false"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="beneficiarioGenerico" 	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="55" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" 
								natura="Data"  />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						
							<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari cedola 531 = 4 + 527 car -->		    
					<field-map attributeName="numElementiTrovatiBenCedola" 		length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBenCedola"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiariCedola" length="527">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="527">
							<field-map attributeName="tipoRuolo" 	  			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="09" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="05" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="05" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="iban"	    				length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Esecutore 604 car -->	 	    
					<field-map attributeName="numElementiTrovatiEsecutore" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriEsecutore"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="esecutore" length="600">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="600">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio  -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flDelega"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
						</nested-mapping>
					</field-map>
					
					<!--Azienda Associata 1009 car -->	 	    
					<field-map attributeName="numElementiTrovatiAziendaAssociata"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriAziendaAssociata"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="aziendaAssociata" length="1005">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="1005">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="greenCard"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entePubblico"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="socQuotata"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="titolareEff"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="visuraCamerale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Terzo Pagatore 583 car -->	 	    
					<field-map attributeName="numElementiTrovatiTerzoPagatore"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriTerzoPagatore"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="terzoPagatore" length="579">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="579">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio  -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiario rendita 531 car -->		    
					<field-map attributeName="numElementiTrovatiBeneficiarioRendita" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriBeneficiarioRendita"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="beneficiarioRendita" length="527">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="527">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="contoCor"    				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Referente Terzo 376 car -->	 	    
					<field-map attributeName="numElementiTrovatiReferenteTerzo" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriReferenteTerzo"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="referenteTerzo" length="372">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="372">
							<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
			<!-- 700 car -->
			<field-map attributeName="beneficiarioMorteDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="beneficiarioVitaDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			
			
            <!-- Errori Anagrafiche -->
            <!-- 5196 car = 5188 + 8  -->
            <field-map attributeName="dataContabileCdErr" 				length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0" />
            <field-map attributeName="dataEffettoVariazioneCdErr" 		length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0" />
            
            <!-- 5188 car = (4 + 664 + 528 + 256 + 720 + 1080 + 1000 + 140 + 164 + 272 + 156 + 140 + 64)) -->
			<field-map attributeName="figureAnagraficheErr" length="4964">
				<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="4964">            
	            	<field-map attributeName="flCoincidente"     		 length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
	            	
	            	<!--Contraenti 664 car -->
					<field-map attributeName="contraenti" length="664">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="332">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="provinciaRecapito"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaRecapito"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaRecapitoDesc"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoRecapito" 		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capRecapito"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="statoRecapito"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="telefonoRecapito"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="emailRecapito"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- dati altri seconda parte -->
							<field-map attributeName="politicamenteEsposta"     length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="greenCard"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEstero"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="usStatus"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>							
							<field-map attributeName="taxIdNumber"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="entePubblico"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="socQuotata"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>							
							<field-map attributeName="titolareEff"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoSocCod"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="provinciaSav"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capSav"     				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="origineFondi"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="origineFondiAltro"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="visuraCamerale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="visuraCameraleAltro"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flCommerciale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso2"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso3"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso4"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso5"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flConsenso6"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="esecutore"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="classeBeneficiario"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gin"    						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="entitaNonFinanziaria"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataConsComunicazioniElettroniche"  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="fondoPensione"     		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="fiduciaria"     			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="intermediarioFinanziario" length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="flPaesiBlackListAR" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					
					<!--Assicurati 528 car -->
					<field-map attributeName="assicurati" length="528">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="264">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"     length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							
							<field-map attributeName="titoloStudio"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="condizioneProfessionale"  	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataPrevidenzaComplementare"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							
							<field-map attributeName="codiceAzienda"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceMatricola"  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="qualificaProfessionale"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataAssunzione"  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!--136-->
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="provinciaRecapito"    	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaRecapito"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaRecapitoDesc"    	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capRecapito"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="statoRecapito"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="telefonoRecapito"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="emailRecapito"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="numeroDocumento"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoRilascio"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRilascio"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="enteRilascio"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cab"    					length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="sottogruppoAR"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="atecoAR"    				length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEstero"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEsteroCodStato"    	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="usStatus"    				length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="taxIdNumber"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="provinciaSav"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaSavDesc"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capSav"    				length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStatoRilascio"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaRilascio"    	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataScadenza"    			length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gin"    						length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flagConsComunicazioniElettroniche"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataConsComunicazioniElettroniche"  	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRevComunicazioniElettroniche"     length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					<!--rappresentanti legali 256 car -->		    		    
					<field-map attributeName="legali" length="256">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="128">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita" 	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					
					<!--Titolari effettivi 720 car -->	    
					<field-map attributeName="titolari" length="720">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="4" blockLength="180">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita" 	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>

							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="greenCard"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEstero"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="usStatus"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- DAC -->
							<field-map attributeName="residenzaFiscaleEstero"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2CodStato"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gin"    						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Morte 1080 car -->
					<field-map attributeName="beneficiariMorte" length="1080">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="108">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico" 	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
						
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="flagDisabile"				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDisabile"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="flagComunicazione"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="email"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari Vita 1000 car -->
					<field-map attributeName="beneficiariVita" length="1000">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="100">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="quotaAssegnata" 	  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="beneficiarioGenerico" 	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="tipoRelazione"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						
							<field-map attributeName="flagComunicazione"  		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="email"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiari cedola 140 car -->
					<field-map attributeName="beneficiariCedola" length="140">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="140">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="iban"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Esecutore 164 car -->		 
					<field-map attributeName="esecutore" length="164">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="164">
							<field-map attributeName="tipoRuolo" 	  			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="nome"  	  				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceFiscale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaDiNascita"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codSesso"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceProfessione"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoPersona"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza1"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza2"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza3"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="comDiResidenza"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoDiResidenza"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capResidenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStato"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="numeroDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="enteRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cab"    					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="sottogruppoAR"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="atecoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStatoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaRilascio"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataScadenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="politicamenteEsposta"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEstero"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEsteroCodStato"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="usStatus"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="taxIdNumber"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondi"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondiAltro"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoRelazione"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flDelega"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"     	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Azienda Associata 272 car -->		 
					<field-map attributeName="aziendaAssociata" length="272">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="272">
							<field-map attributeName="tipoRuolo" 	  			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="nome"  	  				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceFiscale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaDiNascita"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codSesso"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceProfessione"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoPersona"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza1"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza2"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza3"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="comDiResidenza"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoDiResidenza"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capResidenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStato"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area domicilio -->
							<field-map attributeName="presso"  					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="provinciaRecapito"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaRecapito"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaRecapitoDesc"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoRecapitoDesc"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capRecapito"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="statoRecapito"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="telefonoRecapito"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="emailRecapito"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="numeroDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="enteRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cab"    					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="sottogruppoAR"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="atecoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- dati sav -->
							<field-map attributeName="politicamenteEsposta"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="greenCard"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEstero"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEsteroCodStato"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="usStatus"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="taxIdNumber"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="entePubblico"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="socQuotata"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="titolareEff"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoSocCod"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="provinciaSav"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittaSavDesc"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capSav"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondi"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondiAltro"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="comportamentoCliente"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStatoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaRilascio"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataScadenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="visuraCamerale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="visuraCameraleAltro"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flCommerciale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area consensi -->
							<field-map attributeName="flConsenso1"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flConsenso2"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flConsenso3"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flConsenso4"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flConsenso5"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="flConsenso6"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="esecutore"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="classeBeneficiario"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>	
					
					<!--Terzo Pagatore 156 car -->		 
					<field-map attributeName="terzoPagatore" length="156">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="156">
							<field-map attributeName="tipoRuolo" 	  			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="nome"  	  				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceFiscale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaDiNascita"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codSesso"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceProfessione"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoPersona"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza1"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza2"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cittadinanza3"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="comDiResidenza"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoDiResidenza"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capResidenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStato"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="numeroDocumento"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="enteRilascio"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cab"    					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="gruppoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="sottogruppoAR"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="atecoAR"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStatoRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaRilascio"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataScadenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="politicamenteEsposta"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEstero"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tasseEsteroCodStato"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="usStatus"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="taxIdNumber"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondi"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="origineFondiAltro"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="attPrevalenteCod"    		length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
					
					<!--Beneficiario rendita 140 car -->
					<field-map attributeName="beneficiarioRendita" length="140">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="140">
							<field-map attributeName="tipoRuolo" 	  			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cognome"   				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="nome"  	  				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaDiNascita"    length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codSesso"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codiceProfessione"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="tipoPersona"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>								
							
							<!-- area residenza -->
							<field-map attributeName="codProvinciaDiResidenza"  length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="comDiResidenza"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="capResidenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStato"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area antiriciclaggio -->
							<field-map attributeName="codTipoDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="numeroDocumento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="cab"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="gruppoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="sottogruppoAR"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="atecoAR"    				length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codStatoRilascio"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="codProvinciaRilascio"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="dataScadenza"    			length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<!-- area pagamento -->
							<field-map attributeName="tipoPagamento"    		length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="intestatarioConto"    	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="iban"    					length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
							<field-map attributeName="attPrevalenteCod"     	length="04" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<!--Referente Terzo 64 car -->		 
					<field-map attributeName="referenteTerzo" length="64">
						<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="64">
							<field-map attributeName="tipoRuolo" 	  			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<!-- area principale -->
							<field-map attributeName="codiceCliente" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="cognome"   				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="nome"  	  				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codiceFiscale"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="dataDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaDiNascita"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="luogoDiNascita"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codSesso"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="tipoPersona"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codProvinciaDiResidenza"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="comDiResidenza"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="indirizzoDiResidenza"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="capResidenza"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="codStato"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							<field-map attributeName="email"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>