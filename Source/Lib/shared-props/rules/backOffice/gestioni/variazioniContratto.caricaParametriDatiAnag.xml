<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-PARAMETRI-VARIAZIONI-CONTRATTO-DATIANAG</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE373</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagRequestDTO">
			<!-- Totali 194 car (150 + 44) -->
			<!-- 44 car -->
            <field-map attributeName="categoria"      length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="categNum"       length="02" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="codiceProdotto" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           	<field-map attributeName="dataDecorrenza" length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioniContratto.dto.VariazioniContrattoGestioneAnagraficheDatiAnagResponseDTO">
		    <!-- Totali (150(Header) + 20026 + 1824(Area Errori))= 22000 --> 
		    	
		    	<!-- 24940 car = 44 + 25 + 884 + 23287 + 350 + 350  -->
		    	<!-- Stessi campi di input 44 car -->
             	<field-map attributeName="categoria"      		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   				<field-map attributeName="categNum"         	length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   				<field-map attributeName="agenziaPolizza" 		length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	<field-map attributeName="numeroColl"     		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="numeroPolizza"  		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="livello"        		length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            <field-map attributeName="codiceProdotto" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
    	       	<field-map attributeName="dataDecorrenza" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
               	
               	<!-- 25 car -->
               	<field-map attributeName="dataContabile" 			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
               		natura="Data" validate="false"/>
               	<field-map attributeName="dataEffettoVariazione" 	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=""
					natura="Data" validate="false"/>
               	<field-map attributeName="flModPag"					length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
				<field-map attributeName="flPip"					length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
				<field-map attributeName="flFacta"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N"  offset="" padding=""/>
				<field-map attributeName="flDac2"					length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N"  offset="" padding=""/>
				
				<!--Ruoli 884 car -->
	        	<field-map attributeName="numElementiTrovati" 		length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000" offset="" padding="0"/>
				<field-map attributeName="flAltri"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
				<field-map attributeName="ruoli" length="880">
					<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.RuoloDTO" iterations="20" blockLength="44">
						<field-map attributeName="tipoRuolo" 	  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="obbligatiorio" 	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="maxPrevisti"   	length="02" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					</nested-mapping>
				</field-map>
               
               	<!--  1 + 2256 + 2048 + 924 + 2224 + 5974 + 5864 + 753 + 744 + 1009 + 583 + 531 + 376 = 23287 -->
               	<field-map attributeName="figureAnagrafiche" length="23287">
				 	<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.FigureAnagraficheDTO" iterations="0" blockLength="23287">	  
	            		<field-map attributeName="flCoincidente"     		  		length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>	            	
	            		
	            		<!--Contraenti 2256 car -->		    
						<field-map attributeName="numElementiTrovatiContraenti" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000" offset="" padding="0"/>
						<field-map attributeName="flAltriContraenti"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="contraenti" length="2252">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="1126">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area domicilio -->
								<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaRecapito"    	length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapito"    	    length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoRecapito"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capRecapito"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="statoRecapito"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- dati altri seconda parte -->
								<field-map attributeName="politicamenteEsposta"     length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="greenCard"     			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"     			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"     	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"     			length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
								<field-map attributeName="taxIdNumber"     			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="entePubblico"     		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="socQuotata"     			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
								<field-map attributeName="titolareEff"     			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoSocCod"     			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaSav"     		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaSavDesc"     		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capSav"     				length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondi"     		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondiAltro"     	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comportamentoCliente"    	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="false"/>
								<field-map attributeName="visuraCamerale"    		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flCommerciale"    		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<!-- area consensi -->
								<field-map attributeName="flConsenso1"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso2"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso3"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso4"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso5"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso6"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- DAC 59-->
								<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="entitaNonFinanziaria"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="fondoPensione"     		length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="fiduciaria"     			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="intermediarioFinanziario" length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataUltimaAdeguataVerifica" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="flPaesiBlackListAR"           length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="numMandatoFiduciario"         length="15"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flgMandatoFiduciarioPFPG"     length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
					
						<!--Assicurati  2048 car = 4 + 2184 -->
						<field-map attributeName="numElementiTrovatiAssicurati" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriAssicurati"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="assicurati" length="2044">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="1022">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="titoloStudio"  				length="02" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="condizioneProfessionale"  	length="03" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataPrevidenzaComplementare"  length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Data" valida="false"/>
								<field-map attributeName="codiceAzienda"  				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceMatricola"  			length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="qualificaProfessionale"  		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataAssunzione"  				length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!--468-->
								<!-- area domicilio -->
								<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- dati sav -->
								<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<!-- DAC -->
								<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flagConsComunicazioniElettroniche"	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataConsComunicazioniElettroniche"    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="dataRevComunicazioniElettroniche"     length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="dataVarEmailComunicazioniElettroniche" length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
									
								<field-map attributeName="altroRelazione"    			length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoContrDiversoAss"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoRelazione"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
							
						<!--RAPPRESENTANTI LEGALI (2) 924 car = 4 + 920 -->
						<field-map attributeName="numElementiTrovatiLegali" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriLegali"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="legali" length="920">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="2" blockLength="460">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
							</nested-mapping>
						</field-map>	
						
						<!--Titolari effettivi 2224 car -->
						<field-map attributeName="numElementiTrovatiTitolari" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriTitolari"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="titolari" length="2220">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="4" blockLength="555">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
									
								<!-- dati sav -->
								<field-map attributeName="politicamenteEsposta"    	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="greenCard"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"    	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="quotaAssegnata" 	  		length="07"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
								<!-- DAC 59-->
								<field-map attributeName="residenzaFiscaleEstero"   	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1CodStato"	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2CodStato"    length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale1Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="residenzaFiscale2Nif"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="gin"    						length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
							</nested-mapping>
						</field-map>
						
						<!--Beneficiari Morte 5974 car -->
						<field-map attributeName="numElementiTrovatiBenMorte" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriBenMorte"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="beneficiariMorte" length="5970">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="597">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<field-map attributeName="quotaAssegnata" 	  		length="07"  precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Numerico"  segnato="true"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="beneficiarioGenerico" 	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="55"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
								<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flagDisabile"				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDisabile"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								
								<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="flgBeneficioAccettato"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="altroRelazione"    			length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoContrDiversoBen"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoCambioBeneficiario"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numMandatoFiduciario"         length="15"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flgMandatoFiduciarioPFPG"     length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Beneficiari Vita 5864 car -->
						<field-map attributeName="numElementiTrovatiBenVita" 	length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriBenVita"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
						<field-map attributeName="beneficiariVita" length="5860">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="10" blockLength="586">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="quotaAssegnata" 	  		length="07"  precision="0" numericScale="0" align="left"   mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="beneficiarioGenerico" 	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cognome"   				length="55"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
								<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							
								<field-map attributeName="flagComunicazione"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="flgBeneficioAccettato"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="altroRelazione"    			length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoContrDiversoBen"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoCambioBeneficiario"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numMandatoFiduciario"         length="15"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flgMandatoFiduciarioPFPG"     length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Beneficiari cedola 753 = 4 + 738 car -->		    
						<field-map attributeName="numElementiTrovatiBenCedola" 		length="03" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriBenCedola"     		  	length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
						<field-map attributeName="beneficiariCedola" length="749">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="749">
								<field-map attributeName="tipoRuolo" 	  			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="02"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="09"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>								
								
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="05"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="05"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="00000" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="03"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
								
								<!-- area pagamento -->
								<field-map attributeName="tipoPagamento"    		length="02"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="intestatarioConto"    	length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="iban"    					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								
								<field-map attributeName="flgBeneficioAccettato"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="altroRelazione"    			length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="motivoContrDiversoBen"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoRelazione"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
								<field-map attributeName="motivoCambioBeneficiario"    	length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numMandatoFiduciario"         length="15"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flgMandatoFiduciarioPFPG"     length="01"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Esecutore 744 car -->	 	    
						<field-map attributeName="numElementiTrovatiEsecutore" 		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriEsecutore"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="esecutore" length="740">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="740">
								<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio  -->
								<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<!-- dati sav -->
								<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoRelazione"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flDelega"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataUltimaAdeguataVerifica" length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
									
								<field-map attributeName="tipoRappresentanza"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="altroTipoRappresentanza"  length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="altroRelazione"    		length="65"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Azienda Associata 1009 car -->	 	    
						<field-map attributeName="numElementiTrovatiAziendaAssociata"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriAziendaAssociata"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="aziendaAssociata" length="1005">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="1005">
								<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area domicilio -->
								<field-map attributeName="presso"  					length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaRecapito"    	length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapito"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaRecapitoDesc"    	length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoRecapitoDesc"    length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capRecapito"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="statoRecapito"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="telefonoRecapito"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="emailRecapito"    		length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- dati sav -->
								<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="greenCard"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="entePubblico"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="socQuotata"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="titolareEff"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoSocCod"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="provinciaSav"    			length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittaSavDesc"    			length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capSav"    				length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comportamentoCliente"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="visuraCamerale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="visuraCameraleAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flCommerciale"    		length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area consensi -->
								<field-map attributeName="flConsenso1"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso2"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso3"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso4"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso5"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="flConsenso6"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="esecutore"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="classeBeneficiario"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Terzo Pagatore 583 car -->	 	    
						<field-map attributeName="numElementiTrovatiTerzoPagatore" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriTerzoPagatore"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="terzoPagatore" length="579">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="579">
								<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio  -->
								<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<!-- dati sav -->
								<field-map attributeName="politicamenteEsposta"    	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEstero"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tasseEsteroCodStato"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="usStatus"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="taxIdNumber"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondi"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="origineFondiAltro"    	length="100" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Beneficiario rendita 531 car -->		    
						<field-map attributeName="numElementiTrovatiBeneficiarioRendita" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriBeneficiarioRendita"     		  	length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="beneficiarioRendita" length="527">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="527">
								<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceProfessione"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza1"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza2"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cittadinanza3"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area antiriciclaggio -->
								<field-map attributeName="codTipoDocumento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="numeroDocumento"    		length="12"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoRilascio"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataRilascio"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="enteRilascio"    			length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="cab"    					length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="gruppoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="sottogruppoAR"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="atecoAR"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStatoRilascio"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codProvinciaRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataScadenza"    			length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<!-- area pagamento -->
								<field-map attributeName="tipoPagamento"    		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="intestatarioConto"    	length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="contoCor"    				length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="attPrevalenteCod"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
						
						<!--Referente Terzo 376 car -->	 	    
						<field-map attributeName="numElementiTrovatiReferenteTerzo" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
						<field-map attributeName="flAltriReferenteTerzo"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
						<field-map attributeName="referenteTerzo" length="372">
							<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO" iterations="1" blockLength="372">
								<field-map attributeName="tipoRuolo" 	  			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="progrRuolo"    			length="2"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<!-- area principale -->
								<field-map attributeName="codiceCliente" 			length="9"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
								<field-map attributeName="cognome"   				length="100"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="nome"  	  				length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codiceFiscale"    		length="16"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="dataDiNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
									natura="Data" />
								<field-map attributeName="codProvinciaDiNascita"    length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="luogoDiNascita"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codSesso"    				length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="tipoPersona"    			length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<!-- area residenza -->
								<field-map attributeName="codProvinciaDiResidenza"  length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="comDiResidenza"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="indirizzoDiResidenza"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="capResidenza"    			length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="codStato"    				length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="email"    				length="60"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							</nested-mapping>
						</field-map>
					
					</nested-mapping>
				</field-map>	
			<field-map attributeName="beneficiarioMorteDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>		    
			<field-map attributeName="beneficiarioVitaDescEstesa"     	length="350" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>