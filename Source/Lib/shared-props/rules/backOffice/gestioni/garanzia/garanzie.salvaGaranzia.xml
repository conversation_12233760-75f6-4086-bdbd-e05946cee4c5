<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-TARIFFE</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE647</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
	  	<!--  Totale 180 =  143 (stessi dati input) + 37 car -->	  	
	  	
	   	<!-- Lunghezza input = 81 + 62 = 143 -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.SalvaGaranziaRequestDTO">
			<!-- Lunghezza datiCommon = 81 car-->
		    <field-map attributeName="datiCommon" length="81">
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.SalvaGaranziaDatiCommon" iterations="0" blockLength="81">
					<!-- 51 car -->
					<field-map attributeName="idPrenotazione"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="tipoPrenotazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sotFunz"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataPervCartaceo"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="processo"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="dataRichiesta"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="dataEffetto"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					
					<!-- 30 car -->
					<field-map attributeName="categoria"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCategoria"		length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codAgenzia"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCollettiva"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPreventivo"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="numeroPolizza"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					
				</nested-mapping>
			</field-map>			
			
			<!-- Lunghezza datiInput = 62 -->
			<field-map attributeName="datiInput" length="62">
			    <nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.SalvaGaranziaDatiInput" iterations="0" blockLength="62">
					<!-- 57 car. -->
					<field-map attributeName="flAttQuietU"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampaProp"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampPol"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampaDoc"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausAttivaz"		length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dscCausAttivaz"		length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEmRiepilogo"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="callJava"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
										
					<!-- 5 car -->
					<field-map attributeName="gruppo"				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flDirezione"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flChiamata"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>   
			</field-map>			
		</input-mapping>
		
		<!-- Lunghezza output = 81 + 62 + 23 + 14 = 180 -->		
		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.SalvaGaranziaResponseDTO">	
			<!-- INPUT DATA section -->
			<!-- Lunghezza datiCommon = 81 car-->
		    <field-map attributeName="datiCommon" length="81">
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.SalvaGaranziaDatiCommon" iterations="0" blockLength="81">
					<!-- 51 car -->
					<field-map attributeName="idPrenotazione"		length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="tipoPrenotazione"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sotFunz"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataPervCartaceo"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="processo"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="dataRichiesta"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					<field-map attributeName="dataEffetto"			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
					
					<!-- 30 car -->
					<field-map attributeName="categoria"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCategoria"		length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codAgenzia"			length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numeroCollettiva"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPreventivo"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="numeroPolizza"		length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					
				</nested-mapping>
			</field-map>			
			
			<!-- Lunghezza datiInput = 62 -->
			<field-map attributeName="datiInput" length="62">
			    <nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.SalvaGaranziaDatiInput" iterations="0" blockLength="62">
					<!-- 57 car. -->
					<field-map attributeName="flAttQuietU"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampaProp"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampPol"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStampaDoc"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codCausAttivaz"		length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dscCausAttivaz"		length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEmRiepilogo"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="callJava"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
										
					<!-- 5 car -->
					<field-map attributeName="gruppo"				length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flDirezione"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flChiamata"			length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>   
			</field-map>
			
			
			<!-- OUTPUT DATA section -->
			<!-- 23 car -->
			<field-map attributeName="categoria"				length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroCategoria"			length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codAgenzia"				length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroCollettiva"			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroProposta"			length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<!-- 14 car -->
			<field-map attributeName="paginaConErrore"			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="errCodCausAttivaz"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="errDscCausAttivaz"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="errDataEmRiepilogo"		length="04" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
		</output-mapping>
	</rule>
</rules>
    