<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>LEGGI-TARIFFE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE644</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.CalcolaDataEffettoRequestDTO">
			<!-- Lunghezza input = 24 + 1264 + 1264 = 2552-->
		    <field-map attributeName="calcolaDataEffettoRequest" length="24">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.CalcolaDataEffettoRequest" iterations="0" blockLength="24">            
					<field-map attributeName="livelloGestione" 	   length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="sotFunz"             length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="idPrenotazione"      length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="processo"            length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="dtRichiesta"         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
					<field-map attributeName="flgNuovaTaboo"       length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="flgAvanti"	       length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>			
			<!-- Lunghezza tariffa = 4 + 1260 = 1264 -->
			<field-map attributeName="tariffe" length="1264">
			    <nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264">     
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>   
			</field-map>	
			<!-- Lunghezza nuoveTariffe = 4 + 1260 = 1264 -->
			<field-map attributeName="nuoveTariffe" length="1264">
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264"> 
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>					
			</field-map>	
		</input-mapping>
									
		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.CalcolaDataEffettoResponseDTO">	
			<!-- Lunghezza output = 24 + 1264 + 1264 + 26 = 2578-->	
			<!-- Lunghezza tariffaRequest = 24 -->
			<field-map attributeName="calcolaDataEffettoRequest" length="24">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.CalcolaDataEffettoRequest" iterations="0" blockLength="24">            
					<field-map attributeName="livelloGestione" 	   length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="sotFunz"             length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="idPrenotazione"      length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="processo"            length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="dtRichiesta"         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
					<field-map attributeName="flgNuovaTaboo"       length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgAvanti"	       length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>									
				</nested-mapping>
			</field-map>	
			<!-- Lunghezza tariffa = 4 + 1260 = 1264 -->
			<field-map attributeName="tariffe" length="1264">
			    <nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264">     
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>   
			</field-map>	
			<!-- Lunghezza nuoveTariffe = 4 + 1260 = 1264 -->
			<field-map attributeName="nuoveTariffe" length="1264">
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264"> 
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>					
			</field-map>	
			<!-- Lunghezza calcolaDataEffettoResponse = 26-->
			<field-map attributeName="calcolaDataEffettoResponse" length="26">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.CalcolaDataEffettoResponse" iterations="0" blockLength="26">            
					<field-map attributeName="proxRicorrenza"      length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
					<field-map attributeName="dtEffetto"           length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
					<field-map attributeName="errElimGar"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="errTipoGar"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="eDtRichiesta"        length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>		
		</output-mapping>
	</rule>
</rules>
    