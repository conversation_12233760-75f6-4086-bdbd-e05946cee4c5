<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INIZIALIZZA-TARIFFE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE643</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.InserimentoGaranziaRequestDTO">
			<!-- Lunghezza input = 12 -->
		    <field-map attributeName="tariffaRequest" length="12">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.TariffaRequest" iterations="0" blockLength="12">            
					<field-map attributeName="livelloGestione" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="sotFunz"         length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="idPrenotazione"  length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="processo"        length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>				
		</input-mapping>
									
		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.garanzia.dto.InserimentoGaranziaResponseDTO">		
			<!-- Lunghezza output = 12 + 41 + 1264 + 1264 = 2581 -->
			<!-- Lunghezza tariffaRequest = 12 -->
			<field-map attributeName="tariffaRequest" length="12">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.TariffaRequest" iterations="0" blockLength="12">            
					<field-map attributeName="livelloGestione" length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="sotFunz"         length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="idPrenotazione"  length="9" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<field-map attributeName="processo"        length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>
			<!-- Lunghezza headerTariffa = 41 -->
			<field-map attributeName="headerTariffa" length="41">			
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.HeaderTariffaResponse" iterations="0" blockLength="41">            
					<field-map attributeName="dtRichiesta"    length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
			        <field-map attributeName="dtDecorrenza"   length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
			        <field-map attributeName="proxRicorrenza" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
			        <field-map attributeName="dtEffetto"      length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           		   natura="Data"/>
					<field-map attributeName="flgNuovaTaboo"  length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
				</nested-mapping>
			</field-map>		
			<!-- Lunghezza tariffa = 4 + 1260 = 1264 -->
			<field-map attributeName="tariffe" length="1264">
			    <nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264">     
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>   
			</field-map>	
			<!-- Lunghezza nuoveTariffe = 4 + 1260 = 1264 -->
			<field-map attributeName="nuoveTariffe" length="1264">
				<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.ElencoTariffa" iterations="0" blockLength="1264"> 
					<field-map attributeName="numElemTariffe"  length="4" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
					<!-- Lunghezza tariffa = 1260 -->
					<field-map attributeName="listTariffe" length="1260">		
						<nested-mapping className="it.sistinf.albedoweb.services.backoffice.gestioni.garanzia.types.Tariffa" iterations="20" blockLength="63">            
							<field-map attributeName="codiceUt"       length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="descrUt"        length="50" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgSelezionato" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgProtetto"    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
							<field-map attributeName="flgErrore"      length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
						</nested-mapping>
					</field-map>
				</nested-mapping>					
			</field-map>			
		</output-mapping>
	</rule>
</rules>
    