<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-FONDI-SWITCH</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE192</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>REGISTRA FONDI</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	 
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchRequestDTO">
			<!-- input :75 + 14912 + 1502 + 1 car = 16490-->  
		  	<field-map attributeName="dataContabile"               	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataStorno"                  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichiesta"    		   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"         	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoSwitch"	    		   	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="costoSwitch"	    		   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="costoSwitch" nomeKeyERR="erroriSwitch" />
			<field-map attributeName="codLineaInvestimentoIN" 	   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"              	length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
				
			<!--14912-->
			<field-map attributeName="numFondiSwitchOut" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriSwitchOut"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiSwitchOut" 		length="14900">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="149">
					<field-map attributeName="flSelezionato"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numQuote"    			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valQuota"    			length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="percIniz"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percContr"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percDisinv"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="percDisinv[].percentuale" nomeKeyERR="erroriSwitch"/>
					<field-map attributeName="minimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>				
					<field-map attributeName="prestazione"          length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="flProtetto"			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondiSwitchOut"     	length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="totaleFondiSwitchOut" nomeKeyERR="erroriSwitch"/> 
			
			<!--1502-->
			<field-map attributeName="numFondiSwitchIn" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriSwitchIn"     			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiSwitchIn" 			length="1490">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="149">
					<field-map attributeName="flSelezionato"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numQuote"    			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valQuota"    			length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="percIniz"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percContr"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percDisinv"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="minimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="percFutura[].percentuale" nomeKeyERR="erroriSwitch"/>				
					<field-map attributeName="prestazione"          length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="flProtetto"			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondiSwitchIn"     		length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false" nomeAttributoERR="totaleFondiSwitchIn" nomeKeyERR="erroriSwitch" />
			<field-map attributeName="ribilanciamento"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.stornoSwitch.dto.SwitchResponseDTO">   
		    <!-- output :75 + 14912 + 1502 + 1 + 452 car = 16942--> 
		  	<field-map attributeName="dataContabile"               	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataStorno"                  	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichiesta"    		  	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"         	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoSwitch"	    		   	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="costoSwitch"	    		   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="costoSwitch" nomeKeyERR="erroriSwitch" />
			<field-map attributeName="codLineaInvestimentoIN" 		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"              	length="9"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
				
			<!--14912-->
			<field-map attributeName="numFondiSwitchOut" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriSwitchOut"     		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiSwitchOut" 		length="14900">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="149">
					<field-map attributeName="flSelezionato"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numQuote"    			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valQuota"    			length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="percIniz"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percContr"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percDisinv"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="minimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>				
					<field-map attributeName="prestazione"          length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="flProtetto"			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondiSwitchOut"     	length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/> 
			
			<!--1502-->
			<field-map attributeName="numFondiSwitchIn" 			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltriSwitchIn"     			length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    		
			<field-map attributeName="elencoFondiSwitchIn" 			length="1490">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="149">
					<field-map attributeName="flSelezionato"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="codice"  	      		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizione"    		length="50" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numQuote"    			length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="valQuota"    			length="15"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="percIniz"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percContr"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percDisinv"    		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="minimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="massimo"    			length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
					<field-map attributeName="percentuale"  		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false"/>				
					<field-map attributeName="prestazione"          length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3" separatoreMigliaia="true"/>
					<field-map attributeName="flProtetto"			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleFondiSwitchIn"     		length="7" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="ribilanciamento"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>		
			
			<!-- ERRORI 4 + 404 + 44 car = 452 -->
			<field-map attributeName="codLineaInvestimentoINCdErr"	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
			
            <field-map attributeName="elencoFondiSwitchOutCdErr" 	length="400">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="100" blockLength="4">
					<field-map attributeName="percDisinv"			length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleSwitchOutCdErr"     	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>        
			
			<field-map attributeName="elencoFondiSwitchInCdErr" 	length="40">
				<nested-mapping className="it.sistinf.albedoweb.fondi.dto.FondoDTO" iterations="10" blockLength="4">
					<field-map attributeName="percentuale"			length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
				</nested-mapping>
			</field-map>
			<field-map attributeName="totaleSwitchInCdErr"     		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>        

		</output-mapping>
	</rule>
</rules>
    