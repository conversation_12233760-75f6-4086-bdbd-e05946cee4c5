<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-CQS-LIMITAZIONE-OPERATIVITA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE332</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA LIMITAZIONE OPERATIVITA CQS</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	
		<!-- Definizione commarea: WVCOMD52 Lunghezza: 150 + 118 (106 + 12) + 1824 = 2088  -->  		
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaRequestDTO">
			<!-- input : 106 car -->   
            <field-map attributeName="livello"      length="001"  	precision="0" 	numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		    <field-map attributeName="sequestroSN" 	length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="dataInizio" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataInizio" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>		   	
		    <field-map attributeName="dataFine" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="true" nomeAttributoERR="dataFine" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>
		    <field-map attributeName="dataEffetto"	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false" nomeAttributoERR="dataEffetto" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>
		    <field-map attributeName="descEvento"	length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="importo"		length="014"	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" natura="Numerico" valida="true" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importo" nomeKeyERR="erroriLimitazioneOperativitaSalva" />
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.limitazioneOperativita.dto.LimitazioneOperativitaResponseDTO">   
			<!-- stessi campi di input : 106 car -->   
            <field-map attributeName="livello"      length="001"  	precision="0" 	numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   		    <field-map attributeName="sequestroSN" 	length="001"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="dataInizio" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false" nomeAttributoERR="dataInizio" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>		   	
		    <field-map attributeName="dataFine" 	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false" nomeAttributoERR="dataFine" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>
		    <field-map attributeName="dataEffetto"	length="010"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="" natura="Data" valida="false" nomeAttributoERR="dataEffetto" nomeKeyERR="erroriLimitazioneOperativitaSalva"/>
		    <field-map attributeName="descEvento"	length="060"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding=""/>
		    <field-map attributeName="importo"		length="014"	precision="0" 	numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" natura="Numerico" valida="false" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importo" nomeKeyERR="erroriLimitazioneOperativitaSalva" />

			<!-- dati specifici di Output 12 car -->
		    <field-map attributeName="dataInizioErr"	length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />		   	
		    <field-map attributeName="dataFineErr" 		length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />
		    <field-map attributeName="importoErr" 		length="04"	precision="0" 	numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=""	offset="" padding="0" />
		</output-mapping>		
	</rule>
</rules>
    