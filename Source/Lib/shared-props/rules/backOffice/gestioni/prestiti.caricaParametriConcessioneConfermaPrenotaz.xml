<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONCESSIONE-PRESTITI-CONFERMA-PRENOTAZ</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE477</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		
		<logApp>true</logApp>
		<logAppServDesc>SALVA PRENOTAZIONE CONCESSIONE PRESTITI</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-VARIAZIONI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.dto.PrestitiRequestDTO" nomeKeyERR="errorePrestiti">
			<!-- Input: 32 + 48 + 44 + 20 = 144 car -->
            <field-map attributeName="oggParametriPrestiti" length="22" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="22">
		            <field-map attributeName="categoria"      	     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"        length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"            length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"         length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		 length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="oggConcessionePrestiti" length="48" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ConcessionePrestiti" iterations="0" blockLength="48">
            		<field-map attributeName="modPagamento"				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flMaxConcedibile"         length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="importoTotRichiesto"      length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="importoTotaleRichiesto" nomeKeyERR="errorePrestiti"/>
            		<field-map attributeName="dirittiQuietanza"         length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="true" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="dirittiQuietanza" nomeKeyERR="errorePrestiti"/>
            		<field-map attributeName="dataConcessione"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data" nomeAttributoERR="dataConcessione" nomeKeyERR="errorePrestiti"/>	 
		            <field-map attributeName="interessiFrazionamento"   length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="true" natura="Numerico" segnato="false" numInteri="3" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="interessiFrazionamento" nomeKeyERR="errorePrestiti"/> 
	            </nested-mapping>
			</field-map>
			<field-map attributeName="oggInfoConcessionePrestito" length="44" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.InfoConcessionePrestito" iterations="0" blockLength="44">		
					<field-map attributeName="importoLiquidabile"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="interessiPrestiti"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>		          
		            <field-map attributeName="dataProxQuiet"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
		            <field-map attributeName="ggComplessivi"      	length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="flInserisci"    	  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataConsDocCdErr" nomeKeyERR="errorePrestiti"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataRicezioneDocCdErr" nomeKeyERR="errorePrestiti"/>
		</input-mapping>
		
		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.PrestitiResponseDTO">
		    <!-- Totali 144 + 8 = 152 --> 
		    <!-- 0utput  8  -->
		   
           <field-map attributeName="oggParametriPrestiti" length="22" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ParametriPrestiti" iterations="0" blockLength="22">
		            <field-map attributeName="categoria"      	     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="agenziaPolizza"        length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numeroColl"            length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"         length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="livello"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>					
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" 		 length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="oggConcessionePrestiti" length="48" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.ConcessionePrestiti" iterations="0" blockLength="48">
            		<field-map attributeName="modPagamento"				length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="flMaxConcedibile"         length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            		<field-map attributeName="importoTotRichiesto"      length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="dirittiQuietanza"         length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            			valida="false" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
            		<field-map attributeName="dataConcessione"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>	 
		            <field-map attributeName="interessiFrazionamento"   length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	valida="false" natura="Numerico" segnato="false" numInteri="3" numDecimali="02" separatoreMigliaia="true"/> 
	            </nested-mapping>
			</field-map>
			<field-map attributeName="oggInfoConcessionePrestito" length="44" >
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.gestioni.prestiti.dto.InfoConcessionePrestito" iterations="0" blockLength="44">		
					<field-map attributeName="importoLiquidabile"	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="interessiPrestiti"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Numerico" segnato="false" numInteri="11" numDecimali="02" separatoreMigliaia="true"/>		          
		            <field-map attributeName="dataProxQuiet"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
		            <field-map attributeName="ggComplessivi"      	length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="flInserisci"    	  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	            </nested-mapping>
			</field-map>
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="false"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
		
			<field-map attributeName="dataConsDocCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRicezioneDocCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>			
			
		</output-mapping>
	</rule>
</rules>
    