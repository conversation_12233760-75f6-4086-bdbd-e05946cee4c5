<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ESEGUI-FORMULA-PROVVIGIONI</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>MWLSE017</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioneVAProgrammati.dto.CalcoloProvvigioniRequestDTO">			
			<!-- input - 1682  car (105 + 1577)-->
			
            <field-map attributeName="codSoc"   		length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categNum"  	  	length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="agenziaPolizza"   length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"       length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"    length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoEntita"	 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="codiceFormula"    length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="facoltativa"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRichiamo" 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffetto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

			<field-map attributeName="prodotto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="decorrenza" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codDeroga" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="pianoPUR"  	 	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="periodicitaTesta" length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="percGsTranche"    length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>

			<!-- posizioni 2 + 1575 = 1577-->
			<field-map attributeName="totalePosizioni" 	length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizioniList" length="1575">
				<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="25" blockLength="63">		    					
					<field-map attributeName="posizione" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceUT" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="decorrenzaUT" 	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoPremio2" 		length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premioNetto"  	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="prestazione"  	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="convenzioneUT" 	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

<!-- 			variabili esterne per posizione 2 + 15650 =  15652 -->
<!-- 			<field-map attributeName="totaleVariabiliPerPosizioni" 	length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> -->
<!-- 			<field-map attributeName="variabiliPerPosizioniList" length="16724"> -->
<!-- 				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleDTO" iterations="25" blockLength="626">		    					 -->
<!-- 					<field-map attributeName="numeroPosizione" 		length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>  -->
<!-- 					<field-map attributeName="numVariabiliSimple" 	length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> -->
<!-- 					<field-map attributeName="elencoVariabiliSimple" length="620"> -->
<!-- 						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="20" blockLength="31"> -->
<!-- 							<field-map attributeName="nomeVariabile"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 							<field-map attributeName="tipoVariabile"   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 							<field-map attributeName="valoreVariabile" length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 						</nested-mapping> -->
<!-- 					</field-map>		     -->
<!-- 				</nested-mapping> -->
<!-- 			</field-map> -->
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.gestioni.variazioneVAProgrammati.dto.CalcoloProvvigioniResponseDTO">
		    <!-- Totali 9784 car (1682 + 8102) --> 

            <field-map attributeName="codSoc"   		length="03"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categNum"  	  	length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>		    		
			<field-map attributeName="agenziaPolizza"   length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"       length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"    length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoEntita"	 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="codiceFormula"    length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="facoltativa"   	length="01"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRichiamo" 	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffetto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>

			<field-map attributeName="prodotto" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="decorrenza" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codDeroga" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="pianoPUR"  	 	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="periodicitaTesta" length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="percGsTranche"    length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>

			<!-- posizioni 2 + 1575 = 1577-->
			<field-map attributeName="totalePosizioni" 	length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizioniList" length="1575">
				<nested-mapping className="it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO" iterations="25" blockLength="63">		    					
					<field-map attributeName="posizione" 		length="04"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceUT" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="decorrenzaUT" 	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoPremio2" 		length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premioNetto"  	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="prestazione"  	length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
					<field-map attributeName="convenzioneUT" 	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>

			<!-- variabili esterne per posizione 2 + 15650 =  15652-->
<!-- 			<field-map attributeName="totaleVariabiliPerPosizioni" 	length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> -->
<!-- 			<field-map attributeName="variabiliPerPosizioniList" length="16724"> -->
<!-- 				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleDTO" iterations="25" blockLength="626">		    					 -->
<!-- 					<field-map attributeName="numeroPosizione" 		length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>  -->
<!-- 					<field-map attributeName="numVariabiliSimple" 	length="02"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> -->
<!-- 					<field-map attributeName="elencoVariabiliSimple" length="620"> -->
<!-- 						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.VariabileFormulaDTO" iterations="20" blockLength="31"> -->
<!-- 							<field-map attributeName="nomeVariabile"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 							<field-map attributeName="tipoVariabile"   length="01" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 							<field-map attributeName="valoreVariabile" length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/> -->
<!-- 						</nested-mapping> -->
<!-- 					</field-map>		     -->
<!-- 				</nested-mapping> -->
<!-- 			</field-map> -->
		   
  			<!-- DATI OUTPUT -->
  			<!-- 2 + 8100 = 8102 car -->
  			<field-map attributeName="totalePosizioniOut" 		length="02"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>  			
  			<field-map attributeName="eseguiFormulaResultList" 	length="8100">
  				<nested-mapping className="it.sistinf.albedoweb.productBuilder.formulaPosizioni.dto.PosizioniSimpleResultDTO" iterations="25" blockLength="324">
  					<field-map attributeName="numeroPosizione" 			length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
  					<field-map attributeName="elencoRisultatiFormula" 	length="320" >
						<nested-mapping className="it.sistinf.albedoweb.productBuilder.formula.dto.RisultatiFormulaDTO" iterations="16" blockLength="20">
							<!-- risultato formula -->
							<field-map attributeName="descrizione" 		length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
  				</nested-mapping>
  			</field-map>
  			
 		</output-mapping>
	</rule>
</rules>