<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLA-PARAMETRI-LIQUIDAZ-TRASFER-FONDI-OUT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE502</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	 <!-- Definizione commarea: VWCSE502 Lunghezza: 150 + 1262 + 1824 = 3236  -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaRequestDTO" nomeKeyERR="erroriTrasferimentoFondiUscita">
			<!-- 939 car -->
            <field-map attributeName="categNum"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataPagamento"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataContabile" nomeKeyERR="erroriTrasferimentoFondiUscita"/> 
			<field-map attributeName="dataEffettoStorno"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataEffettoStorno" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="dataDisinvestimento"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataDisinvestimento" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="flUnit"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="datiFondoDestinazione" length="1">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondoDTO" iterations="0" blockLength="1">
					<field-map attributeName="flUnificazioneFondi"   	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="capitaleDaTrasferire"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true"  nomeAttributoERR="capitaleDaTrasferire" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="datiCorrettivi" length="860">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="860">
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="855" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="57">
							<field-map attributeName="flgCorrettivo"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoCorrettivo"    			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flCalcoloLiquidato"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaResponseDTO">
		    <!-- Output 939 + 323 = 1262 car -->
            <!-- Campi di input 939 car -->
            <field-map attributeName="categNum"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataPagamento"        length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"/> 
			<field-map attributeName="dataEffettoStorno"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"/>
			<field-map attributeName="dataDisinvestimento"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="flUnit"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="datiFondoDestinazione" length="1">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondoDTO" iterations="0" blockLength="1">
					<field-map attributeName="flUnificazioneFondi"   	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="capitaleDaTrasferire"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="datiCorrettivi" length="860">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="860">
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="855" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="57">
							<field-map attributeName="flgCorrettivo"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoCorrettivo"    			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flCalcoloLiquidato"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Output 71 + 252 = 323 car -->
			<field-map attributeName="impOneriOperazioni"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="segnoOneriOperazioni"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoNettoOperazione"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="importoImpNuovaFiscalita"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="importoImpVecchiaFiscalita"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="importoLiquidato"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<!-- area errori 12 + 240 = 252 car -->
			<field-map attributeName="dataPagamentoCdErr"          length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="capitaleDaTrasferireCdErr"   length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoNettoOperazioneCdErr" length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="erroriDatiCorrettivi" length="240" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ErroriDatiCorrettiviDTO" iterations="15" blockLength="16">
					<field-map attributeName="flgCorrettivoCdErr"      length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="descrCorrettivoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="segnoCorrettivoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="importoCorrettivoCdErr"  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			
		</output-mapping>
	</rule>
</rules>

