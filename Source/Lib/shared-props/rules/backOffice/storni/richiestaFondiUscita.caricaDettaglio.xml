<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-RICHIESTA-FONDI-OUT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE437</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	 <!-- Definizione commarea: VWLSE437 Lunghezza: 150 + 42 + 1824 = 2016  -->
			<input-mapping className="it.sistinf.albedoweb.backoffice.storni.richiestaFondiUscita.dto.RichiestaFondiUscitaRequestDTO">
	     	  	<!-- area cliente  42 car-->
            	<field-map attributeName="livello"	length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
 				<field-map attributeName="sottoFunzione"  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.richiestaFondiUscita.dto.RichiestaFondiUscitaResponseDTO">
		  	<!-- DATI 42  car -->   
			<field-map attributeName="livello"		length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
 			<field-map attributeName="sottoFunzione"  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	        <field-map attributeName="dataContabile"    	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataEffettoStorno"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataTrasferimento"    length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"  length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
		</output-mapping>
	</rule>
</rules>
