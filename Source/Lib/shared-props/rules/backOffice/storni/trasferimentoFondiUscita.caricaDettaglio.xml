<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-TRASFER-FONDI-OUT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE413</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	 <!-- Definizione commarea: VWCSE403 Lunghezza: 150 + 1227 + 1824 = 3201  -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaRequestDTO" nomeKeyERR="erroriTrasferimentoFondiUscita">
			<!-- 53 car -->
            <field-map attributeName="categNum"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataEffettoStorno"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataEffettoStorno" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="dataDisinvestimento"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataDisinvestimento" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flUnit"                      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaResponseDTO">
		    <!-- Output 53 + 1174 = 1227 car -->
            <!-- Campi di input 53 car -->
            <field-map attributeName="categNum"   			length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  		length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataEffettoStorno"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flUnit"                      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- output 14 + 27 + 19 + 142 + 70 + 902 = 1174 car -->
			<field-map attributeName="capitaleDaTrasferire"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<!-- DATI Fondo 27 car -->
			<field-map attributeName="datiFondoDestinazione" length="27">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondoDTO" iterations="0" blockLength="27">
					<field-map attributeName="flUnificazioneFondi"   	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codFondoDest"   			length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />
					<field-map attributeName="tipoFondo"   				length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="compartoFondo"			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="dataIscrizioneAlbo"   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Data"/>
					<field-map attributeName="codSocDest"   		length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- DATI SECONDARI  -->
			<field-map attributeName="datiSecondariPolizza" length="19">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSecondariPolizzaDTO" iterations="0" blockLength="19">
					<field-map attributeName="divisa"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="matricola"			length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="statoPolizza"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="causale"    			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
			</field-map>
			<field-map attributeName="datiSocietaDestinazione" length="142">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSocietaDTO" iterations="0" blockLength="142">
					<field-map attributeName="stato"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provincia"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="cap"    			length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="comune"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrUrbana"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrStrada"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numCivico"    	length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamento" length="70">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="70">
							<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           		<field-map attributeName="intestatario"     	        length="40"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateContoCorrente"     	length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- Importi e correttivi 42 + 860 = 902 -->
			 <field-map attributeName="riservaMaturata"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="impOneriOperazioni"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="importoNettoOperazione"   	length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="datiCorrettivi" length="860">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="860">
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="855" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="57">
							<field-map attributeName="flgCorrettivo"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoCorrettivo"    			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
