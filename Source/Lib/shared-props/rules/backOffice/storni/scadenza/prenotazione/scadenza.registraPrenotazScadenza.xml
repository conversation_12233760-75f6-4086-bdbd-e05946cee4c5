<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-PRENOTAZ-SCADENZA</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE448</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

		<multipleTransaction>true</multipleTransaction>
		<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
		<pageRequestField/>
		<limitPage>99</limitPage>
		<moreDataField/>
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
		<pastedFields>
			<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
		</pastedFields>	   	

		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaRequestDTO" nomeKeyERR="erroriPercipientiNew">
			<!-- 55 car -->
            <field-map attributeName="categoria"					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>						
			<field-map attributeName="idPrenotazione"				length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="9" numDecimali="0" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="tipoVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataConsDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataRicezioneDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="sottoFunzionePrenotaz"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaResponseDTO">
		    <!-- 55 + 8 = 63 car -->
            <field-map attributeName="categoria"					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>			
			<field-map attributeName="idPrenotazione"				length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="tipoVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="false"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="sottoFunzionePrenotaz"    	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Campi errore 4 car -->
			<field-map attributeName="dataConsDocCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRicezioneDocCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    