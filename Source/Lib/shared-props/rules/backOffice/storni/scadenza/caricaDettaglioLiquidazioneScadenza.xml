<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-STORNI-LIQUIDAZIONE-SCADENZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE219</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaRequestDTO">
			
			<!--  31 + 16 + 22 + 77 + 1= 147 car -->
            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFunz"       length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- DATI VINCOLO 16 car -->
			<field-map attributeName="tipoVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRichiamo"       length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="importoInps" nomeKeyERR="erroriStorniScadenze"/>
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" nomeAttributoERR="percInps" nomeKeyERR="erroriStorniScadenze"/>
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" nomeAttributoERR="percIrpef" nomeKeyERR="erroriStorniScadenze"/>
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="imponibileIrpef" nomeKeyERR="erroriStorniScadenze"/>
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="importoIrpef" nomeKeyERR="erroriStorniScadenze"/>
			<field-map attributeName="flgVarPrestazione"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaResponseDTO">
			<!-- Totale area applicativa 147 + 152 + 26 + 710 + 1 + 1  = 1037 car -->
		     <!-- 69 + 77 + 1 = 147 car -->
            <field-map attributeName="categoria"      length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           	<field-map attributeName="numeroColl"     length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFunz"       length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione" length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- DATI VINCOLO 16 car -->
			<field-map attributeName="tipoVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="dataConsDoc"       			length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRicezioneDoc"       		length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="tipoRichiamo"       length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
		    <!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="flgVarPrestazione"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <!-- 152 -->
           	<field-map attributeName="capitaleMat"          length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
           		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="riserva"              length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="prestaScad"           length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="utili"           	   length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="impLordo"             length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="oneriProp"            length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="impNetto"             length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="impRendita"           length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
           	<field-map attributeName="dataPagam"            length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="segnoPrest"           length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="intPrestiti"          length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="prestiti"             length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="quota"                length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tpRend"               length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="annice"               length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="revers"               length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			<field-map attributeName="tipoLiquid"          	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="percRenParziale"     		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codiceUTRendita"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- DATI correttivi 710 -->
			<field-map attributeName="datiCorrettivi" length="710">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="710">
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="705" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="47">
							<field-map attributeName="flgCorrettivo"     			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoCorrettivo" nomeKeyERR="erroreLiquidazioneRiscatto"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flgConversione"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flPresenteIntRitPag" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
		
	</rule>
</rules>
    