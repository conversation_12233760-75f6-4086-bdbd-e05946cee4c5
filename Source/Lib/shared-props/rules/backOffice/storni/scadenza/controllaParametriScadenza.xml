<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLA-STORNI-SCADENZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE218</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaRequestDTO" nomeKeyERR="errorePrenotazioneScadenza">
			<!-- 59 + 77 = 136 car -->
            <field-map attributeName="categoria"      	  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"	  	length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     	  	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza" 	  	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"     	 	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        	  	length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  	  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flUnit"         	 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFisc"           	length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="tipoFunz"           	length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="dataContab"       	length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
			   valida="true" natura="Data"  nomeAttributoERR="dataContab"    nomeKeyERR="errorePrenotazioneScadenza" />
			<field-map attributeName="dataStorno"          	length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
			   valida="true" natura="Data"  nomeAttributoERR="dataStorno"    nomeKeyERR="errorePrenotazioneScadenza" />
			<field-map attributeName="idPrenotazione" 		length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="importoInps" nomeKeyERR="errorePrenotazioneScadenza"/>
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" nomeAttributoERR="percInps" nomeKeyERR="errorePrenotazioneScadenza"/>
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" nomeAttributoERR="percIrpef" nomeKeyERR="errorePrenotazioneScadenza"/>
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="imponibileIrpef" nomeKeyERR="errorePrenotazioneScadenza"/>
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" nomeAttributoERR="importoIrpef" nomeKeyERR="errorePrenotazioneScadenza"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaResponseDTO">
		    <!-- Campi di output 136 + 28 =  164 car -->
           	<field-map attributeName="categoria"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"      length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           	<field-map attributeName="numeroColl"          length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"       length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"             length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flUnit"         	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFisc"            length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="tipoFunz"            length="1"  precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""/>
			<field-map attributeName="dataContab"       length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
			     natura="Data"/>
			<field-map attributeName="dataStorno"          length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
			     natura="Data"/>
			<field-map attributeName="idPrenotazione" 		length="9"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" /> 
			<!-- Output 28 -->
			<field-map attributeName="dataContabileCdErr"      		length="4" precision="0" numericScale="0"  align="right"   mandatory="0"  separator=""  occurs="1"  default="0000" offset="" padding=""/>
			<field-map attributeName="dataEffettoStornoCdErr"  		length="4" precision="0" numericScale="0"  align="right"   mandatory="0"  separator=""  occurs="1"  default="0000" offset="" padding=""/>
			<field-map attributeName="importoInpsCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="percInpsCdErr"    			length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>					
			<field-map attributeName="percIrpefCdErr"  			    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="imponibileIrpefCdErr"  		length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoIrpefCdErr"  		    length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			
		</output-mapping>
		
	</rule>
</rules>
    