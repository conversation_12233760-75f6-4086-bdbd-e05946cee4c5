<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLA-PARAMETRI-DECADENZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE649</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.sinistro.dto.SinistroRequestDTO">
			<!-- 78 car -->
            <field-map attributeName="categNum"      			   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" 			   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"    			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza" 			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"       			   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoSinistro"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataContabile"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataMorte"                   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataMorte"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataDenuncia"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataRicevimentoDenuncia"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataRicevimentoDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.sinistro.dto.SinistroResponseDTO">
		    <!-- Totali 78 + 20 = 98 --> 		   
            <!-- input 78 car -->
            <field-map attributeName="categNum"      			   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza" 			   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"    			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza" 			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"       			   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoSinistro"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataContabile"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataMorte"                   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataMorte"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataDenuncia"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataRicevimentoDenuncia"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataRicevimentoDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>		
			<!-- output 20 car -->
			<field-map attributeName="tipoSinistroCdErr"              length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataContabileCdErr"             length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataMorteCdErr"                 length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataDenunciaCdErr"              length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRicevimentoDenunciaCdErr"   length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    