<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-DECADENZA</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE650</program>
	  	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>CONFERMA DECADENZA</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>
		
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.sinistro.dto.SinistroRequestDTO">
		  <!-- Input 92 caratteri  --> 
            <field-map attributeName="categNum"                    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoSinistro"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataContabile"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataMorte"                   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataMorte"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataDenuncia"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataRicevimentoDenuncia"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataRicevimentoDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="dataConsDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataRicezioneDoc"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.sinistro.dto.SinistroResponseDTO">
		    <!-- 92 car -->
		    <!--  Totali 92 + 8 = 100 -->
            <field-map attributeName="categNum"                    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoSinistro"                length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataContabile"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataMorte"                   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataMorte"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataDenuncia"                length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="dataRicevimentoDenuncia"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  valida="true" natura="Data"  nomeAttributoERR="dataRicevimentoDenuncia"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			<field-map attributeName="idPrenotazione" 			   length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="dataConsDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataRicezioneDoc"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
				natura="Data"/>
			<!-- output 8 car -->	
			<field-map attributeName="dataConsDocCdErr"        		length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRicezioneDocCdErr"        length="04"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    