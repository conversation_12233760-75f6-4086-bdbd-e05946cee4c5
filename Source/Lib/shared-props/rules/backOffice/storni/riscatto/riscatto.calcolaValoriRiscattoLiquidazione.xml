<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLA-VALORI-RISCATTO-LIQUIDAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0079</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipientiRequestDTO">
			<!-- Totali 10974 car (150 + 12675) -->
			<!-- 12675 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoPercipienti" length="12303">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.ElencoPercipientiDTO" iterations="0" blockLength="12303">
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="percipiente" length="12300">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipienteDTO" iterations="50" blockLength="246">
			           		<field-map attributeName="descrizioneRuolo"    length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="cognome"             length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"         	   length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="scelta"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipologia"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="modPagamento"        length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="percentuale"         length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importo"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="coordinateBancarie"  length="27" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="beneficiario"        length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>										
							<field-map attributeName="datiAntiriciclaggio" length="64">
								<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="64">
									<field-map attributeName="tipoDocumento"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
									<field-map attributeName="numeroDocumento"    	length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						       		<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
						       		<field-map attributeName="localitaRilascio"     length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
									<field-map attributeName="enteRilascio"     	length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
								</nested-mapping>
							</field-map>
							<field-map attributeName="datiGeneraliPerc" length="27" >
								<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.DatiGeneraliPercipienteDTO" iterations="0" blockLength="27">
									<field-map attributeName="progrTsT075"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="progressivo"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                   	<field-map attributeName="codiceCliente"       length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>	
				                    <field-map attributeName="modificabile"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flAnagrafica"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flContoCorrente"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flAntiriciclaggio"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="tipoPercipiente"     length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flBlocco"            length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="flLiquidazione"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                </nested-mapping>
							</field-map>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoQuietanze" length="350">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="50" blockLength="7">
					<field-map attributeName="numQuietanza"      length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerValorizzato" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flFipPip"   length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipientiResponseDTO">
		    <!-- Totali 24302 car (150 + 25708 + 1824) --> 
		    <!-- Campi di output 25708 car -->
		    <!-- input 12675 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoPercipienti" length="12303">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.ElencoPercipientiDTO" iterations="0" blockLength="12303">
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="percipiente" length="12300">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipienteDTO" iterations="50" blockLength="246">
			           		<field-map attributeName="descrizioneRuolo"    length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="cognome"             length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"         	   length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="scelta"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipologia"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="modPagamento"        length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="percentuale"         length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importo"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="coordinateBancarie"  length="27" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="beneficiario"        length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>										
							<field-map attributeName="datiAntiriciclaggio" length="64">
								<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="64">
									<field-map attributeName="tipoDocumento"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
									<field-map attributeName="numeroDocumento"    	length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						       		<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default=""  offset="" padding=" "/>
						       		<field-map attributeName="localitaRilascio"     length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
									<field-map attributeName="enteRilascio"     	length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>
								</nested-mapping>
							</field-map>
							<field-map attributeName="datiGeneraliPerc" length="27" >
								<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.DatiGeneraliPercipienteDTO" iterations="0" blockLength="27">
									<field-map attributeName="progrTsT075"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="progressivo"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                   	<field-map attributeName="codiceCliente"       length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>	
				                    <field-map attributeName="modificabile"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flAnagrafica"        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flContoCorrente"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flAntiriciclaggio"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="tipoPercipiente"     length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="flBlocco"            length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="flLiquidazione"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                </nested-mapping>
							</field-map>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoQuietanze" length="350">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="50" blockLength="7">
					<field-map attributeName="numQuietanza"      length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="fillerValorizzato" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="flFipPip"   length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=" "/>
			 <!-- output 13033 car -->
			 <field-map attributeName="elencoValoriRiscatto" length="8854" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoValoriRiscattoDTO" iterations="0" blockLength="8854">
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="valoriRiscatto" length="8850" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="50" blockLength="177">
							<field-map attributeName="cognome"          		length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"         	   		length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoLordoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="segnoOneriOperazioni"     length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="oneriOperazione"          length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoNettoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="interessiPrestiti"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="prestiti"                 length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="imposteRedditiCapitale"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="altreImposteRedditi"      length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="importoLiquidato"         length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="indiceTabella"            length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<!-- Solo per riscatto FIP o PIP -->
			<field-map attributeName="valoriRiscattoFipPip" length="179">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoFipPipDTO" iterations="0" blockLength="179">            
					<field-map attributeName="cognomeFip"             	 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="nomeFip"             		 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoLordoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="segnoOneriOperazioniFip"   length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="oneriOperazioneFip"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoNettoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoRiscattiPrecFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiCapitaleFip" length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="altreImposteRedditiFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiPrecFip"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoLiquidatoFip"       length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="numQuietanzaFip"           length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="errorePercipienti" length="3600">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.ErrorePercipienteDTO" iterations="50" blockLength="72">            
					<field-map attributeName="cognomeCdErr"          	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="nomeCdErr"          		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="tipologiaCdErr"           length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="modPagamentoCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="percentualeCdErr"         length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="importoCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="siglaInternazCdErr"       length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="numeriControlloCdErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="cinBancaCdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="abiBancaCdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="cabBancaCdErr"            length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="contoCorrenteCdErr"       length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="beneficiarioCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="tipoDocumentoCdErr"       length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="numeroDocumentoCdErr"     length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="dataRilascioCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="localitaRilascioCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="enteRilascioCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="erroreValoriRiscatto" length="400">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ErroreValoriRiscattoDTO" iterations="50" blockLength="8">            
					<field-map attributeName="importoLiquidatoCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="numQuietanzaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    