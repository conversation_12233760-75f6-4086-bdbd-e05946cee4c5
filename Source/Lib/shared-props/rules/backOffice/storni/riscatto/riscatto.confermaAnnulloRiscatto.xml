<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-ANNULLO-RISCATTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0074</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO">
			<!-- Totali 9324 car (150 + 9174) -->
			<!-- 9174 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoValoriRiscatto" length="18303" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoValoriRiscattoDTO" iterations="0" blockLength="18303">
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="valoriRiscatto" length="18300" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="100" blockLength="183">
							<field-map attributeName="cognome"             		length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"               		length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoLordoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="segnoOneriOperazioni"     length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="oneriOperazione"          length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoNettoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="interessiPrestiti"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="prestiti"                 length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="imposteRedditiCapitale"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="altreImposteRedditi"      length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="importoLiquidato"         length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="numQuietanza"             length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="indiceTabella"            length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		    <!-- Totali 11548 car (150 + 9574 + 1824) --> 
		    <!-- Campi di output 9574 car -->
		    <!-- 9174 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoValoriRiscatto" length="18303" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoValoriRiscattoDTO" iterations="0" blockLength="18303">
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="valoriRiscatto" length="18300" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="100" blockLength="183">
							<field-map attributeName="cognome"             		length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"               		length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoLordoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="segnoOneriOperazioni"     length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="oneriOperazione"          length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoNettoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="interessiPrestiti"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="prestiti"                 length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="imposteRedditiCapitale"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="altreImposteRedditi"      length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="importoLiquidato"         length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
							<field-map attributeName="numQuietanza"             length="6"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="indiceTabella"            length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="erroreValoriRiscatto" length="800">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ErroreValoriRiscattoDTO" iterations="100" blockLength="8">            
					<field-map attributeName="importoLiquidatoCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="numQuietanzaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>