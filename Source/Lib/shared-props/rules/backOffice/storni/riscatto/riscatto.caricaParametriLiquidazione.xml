<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-PARAMETRI-LIQUIDAZIONE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0063</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO">
			<!-- 27 + 17 + 10 + 77 + 12 + 10 + 10 = 163 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"               length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flUnit"                  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="distribuzioneRiscatto"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- DATI VINCOLO 17car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="idPrenotazione"              length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" segnato="false"  numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<!-- Convenzione Piano B  11 + 1-->
			<field-map attributeName="flgPianoB"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="convenzionePianoB"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- dati collettiva  10 car -->
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 valida="true" natura="Data"  nomeAttributoERR="dataPrimaIscrizione"    nomeKeyERR="erroreLiquidazioneSinistro"/>
			 <field-map attributeName="causaleGS"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		 
		    <!-- Campi Totali 1221 + 163 = 1384 car -->
		    <!-- Campi di input 27 + 17 + 10 + 77 + 12 +10 +10  = 163 car -->
		    <field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"               length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flUnit"                  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="distribuzioneRiscatto"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- DATI VINCOLO 17car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="idPrenotazione"              length="09" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" segnato="false"  numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Prodotti particolari 77 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
				<!-- Convenzione Piano B 11 + 1 -->
			<field-map attributeName="flgPianoB"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="convenzionePianoB"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- dati collettiva  10 car -->
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data" />
			<field-map attributeName="causaleGS"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati output 115 + 710 + 63 + 56 + 276 + 1 = 1221 car -->
			<field-map attributeName="capitaleMaturato"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="riservaMaturata"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="riscattoLordo"               length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
            <field-map attributeName="percentualeRiscatto"         length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	natura="Numerico"  segnato="false"  numInteri="3" numDecimali="7" separatoreMigliaia="false"/>
			<field-map attributeName="descOneriRiscattoLordo"      length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="impOneriRiscattoLordo"       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="valoreRiscatto"              length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="importoPercRiscatto"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="datiCorrettivi" length="710">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.DatiCorrettiviDTO" iterations="0" blockLength="710">
					<field-map attributeName="numElementiTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="elencoCorrettivi" length="705" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ElencoCorrettiviDTO" iterations="15" blockLength="47">
							<field-map attributeName="flgCorrettivo"      			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrCorrettivo"    			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgProtezSegnoCorrettivo"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="segnoCorrettivo"    			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="importoCorrettivo"  			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoCorrettivo" nomeKeyERR="erroreLiquidazioneRiscatto"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="importoNettoOperazione"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataPagamento"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="prestiti"                    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="segnoInterPrestiti"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="impInterPrestiti"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>	
			<!-- DATI RENDITA 16 + 40 = 56 -->
			<field-map attributeName="datiInfoAgg" length="16">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiInfoAgg" iterations="0" blockLength="16">
					<field-map attributeName="tipoLiquid"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoRendita"        		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="numAnniRenditaCerta"     	length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="true"/>
	           	 	<field-map attributeName="percReversibilita"     	length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="percRenParziale"     		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codiceUTRendita"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="assegnoSociale"          length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="riscattoNetto"               length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
   				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
   			<field-map attributeName="flgConversione"  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
   			<field-map attributeName="flgOpzione"  				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="flPresenteIntRitPag"  	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Errori 276 -->				
			<field-map attributeName="percentualeRiscattoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoPercRiscattoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="riscattoLordoCdErr"          length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="erroriDatiCorrettivi" length="240" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.dto.ErroriDatiCorrettiviDTO" iterations="15" blockLength="16">
					<field-map attributeName="flgCorrettivoCdErr"      length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="descrCorrettivoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="segnoCorrettivoCdErr"    length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="importoCorrettivoCdErr"  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="dataPagamentoCdErr"          length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="importoNettoOperazioneCdErr" length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="prestitiCdErr"               length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="segnoInterPrestitiCdErr"     length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="impInterPrestitiCdErr"       length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="riscattoNettoCdErr"          length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    