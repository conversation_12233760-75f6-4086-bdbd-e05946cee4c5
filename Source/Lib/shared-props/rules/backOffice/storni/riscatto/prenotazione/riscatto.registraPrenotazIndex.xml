<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-PRENOTAZ-RISCATTO-INDEX</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE394</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>SALVA RISCATTO FONDI INDEX</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO">
			<!-- 100 + 17 + 21 + 1055 + 18 + 266 + 107 = 1584 car -->
			<field-map attributeName="numCategoria"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="faseINDEX"                   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"              length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataContabile" nomeKeyERR="erroriFondiRiscatto"/>
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataStorno" nomeKeyERR="erroriFondiRiscatto"/>				               
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRicevRichiestaRiscatto" nomeKeyERR="erroriFondiRiscatto"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataDisinvestimento" nomeKeyERR="erroriFondiRiscatto"/>	
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="fondoIndex" length="14">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="0" blockLength="14">
					<field-map attributeName="valoreRiscatto"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="valoreRiscatto" nomeKeyERR="erroriFondiRiscatto"/>
				</nested-mapping>
			</field-map>
			<!-- DATI VINCOLO 17 car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="dataConsDoc"       				 length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				valida="true"  natura="Data" nomeAttributoERR="dataConsDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRicezioneDoc"    				 length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				valida="true"  natura="Data" nomeAttributoERR="dataRicezioneDocCdErr" nomeKeyERR="erroriPercipientiNew"/>	
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="datiReimpiego" length="1055">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiReimpiego" iterations="0" blockLength="1055">
	          		<field-map attributeName="flReimpiego"     		        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoReimpiego"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impReimpiego"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="integrazione"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoRapporto"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStessoContraente"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="listaRapportiReimpiego" length="1023">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ListaRapportiReimpiegoDTO" iterations="0" blockLength="1023">            
							<field-map attributeName="numElementiTrovati" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
							<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
							<field-map attributeName="rapportoReimpiego" length="1020">
								<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportoReimpiegoDTO" iterations="20" blockLength="51">
									<field-map attributeName="flagSelRapportoReimpiego"     	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCategoriaRapportoReimpiego" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="agenziaRapportoReimpiego"			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numeroCollettivaReimpiego"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="numeroRapportoReimpiego"			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="convenzioneRapportoReimpiego"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="importoRapportoReimpiego"			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
								</nested-mapping>
							</field-map>					
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiGenerali" length="18">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiGenerali" iterations="0" blockLength="18">
			           		<field-map attributeName="codAnag"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			 				<field-map attributeName="codiceCustomer"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPercipiente" length="266">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="0" blockLength="266">
					<field-map attributeName="cognome"   	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nome"  		 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFiscale"   		  length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoSoggetto"   		  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="indirizzoDiResidenza"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="comuRes"   			  length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="capDiResidenza"   	  length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provRes"   			  length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statoResidenza"         length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoDiNascita"         length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provNascita"            length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDiNascita"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="sesso"                  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgCommerciale"         length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDocumento"           length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRilascio"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="luogoRilascio"          length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="enteRilascio"           length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cab"          		  length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="gruppo"          		  length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottoGruppo"            length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamento" length="107">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="107">
					<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateContoCorrente"     	length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="causale"     		            length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="swift"     		            length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
			<!-- Totali = 1584 +  320 = 1904-->
			<field-map attributeName="numCategoria"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="faseINDEX"                   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="idPrenotazione"              length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>				               
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>	
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			
		    <field-map attributeName="fondoIndex" length="14">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="0" blockLength="14">
					<field-map attributeName="valoreRiscatto"         length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
				</nested-mapping>
			</field-map>
			<!-- DATI VINCOLO 17 car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="dataConsDoc"       				 length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRicezioneDoc"    				 length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" />	
			<field-map attributeName="sottoFunzionePrenotaz"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="datiReimpiego" length="1055">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiReimpiego" iterations="0" blockLength="1055">
	          		<field-map attributeName="flReimpiego"     		        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoReimpiego"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impReimpiego"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="integrazione"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoRapporto"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStessoContraente"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="listaRapportiReimpiego" length="1023">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ListaRapportiReimpiegoDTO" iterations="0" blockLength="1023">            
							<field-map attributeName="numElementiTrovati" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
							<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
							<field-map attributeName="rapportoReimpiego" length="1020">
								<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportoReimpiegoDTO" iterations="20" blockLength="51">
									<field-map attributeName="flagSelRapportoReimpiego"     	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCategoriaRapportoReimpiego" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="agenziaRapportoReimpiego"			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numeroCollettivaReimpiego"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="numeroRapportoReimpiego"			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="convenzioneRapportoReimpiego"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="importoRapportoReimpiego"			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
								</nested-mapping>
							</field-map>					
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiGenerali" length="18">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiGenerali" iterations="0" blockLength="18">
			           		<field-map attributeName="codAnag"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			 				<field-map attributeName="codiceCustomer"         length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPercipiente" length="266">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="0" blockLength="266">
					<field-map attributeName="cognome"   	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nome"  		 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFiscale"   		  length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoSoggetto"   		  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="indirizzoDiResidenza"   length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="comuRes"   			  length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="capDiResidenza"   	  length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provRes"   			  length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="statoResidenza"         length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="luogoDiNascita"         length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="provNascita"            length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDiNascita"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="sesso"                  length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgCommerciale"         length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numDocumento"           length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRilascio"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="luogoRilascio"          length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="enteRilascio"           length="20" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cab"          		  length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="gruppo"          		  length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="sottoGruppo"            length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
				<field-map attributeName="datiPagamento" length="107">
				
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="107">
								<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="coordinateContoCorrente"     	length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				           	 	<field-map attributeName="causale"     		            length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								<field-map attributeName="swift"     		            length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
				</field-map>
         <!-- Output 320 car -->
		
		    <field-map attributeName="valoreRiscattoErr"         	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataConsDocCdErr"        		length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="dataRicezioneDocCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		    <field-map attributeName="datiErr" length="308">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipientiErr" iterations="0" blockLength="308">
					 <field-map attributeName="cognome"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="nome"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="codiceFiscale"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="tipoSoggetto"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="indirizzoDiResidenza"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="comuRes"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="capDiResidenza"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="provRes"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="statoResidenza"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="luogoDiNascita"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	
					 <field-map attributeName="provNascita"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="dataDiNascita"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="sesso"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="modPagamento"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	
					 <field-map attributeName="coordinateSiglaInternaz"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateNumControllo"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateCin"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateAbi"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateCab"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateContoCorrente"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="intestatario"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="causale"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="tipoDocumento"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	
					 <field-map attributeName="numDocumento"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="dataRilascio"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="luogoRilascio"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="enteRilascio"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="cab"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="gruppo"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="sottoGruppo"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="invioDatiCollettore"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="tipoReimpiego"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="impReimpiego"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="integrazione"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="tipoRapporto"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>		
					 <field-map attributeName="stessoContraente" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="elencoRapportiReimpiegoErr" length="160">
					 	<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportiReimpiegoErrDTO" iterations="20" blockLength="8">
					 		<field-map attributeName="numeroRapportoReimpiegoErr"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 		<field-map attributeName="importoRapportoReimpiegoErr" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 	</nested-mapping>
					 </field-map>
					 <field-map attributeName="swift" 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	 
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    