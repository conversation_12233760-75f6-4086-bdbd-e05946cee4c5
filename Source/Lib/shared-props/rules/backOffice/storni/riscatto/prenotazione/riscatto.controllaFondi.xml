<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONTROLLA-FONDI-PRENOTAZ-RISCATTO-UL</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0081</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO" nomeKeyERR="erroriFondiRiscatto">
			<!-- old 89 + 43 + 108 + 1310 + 9 + 32 + 1  = 1592 car -->
			<!-- 89 + 43 + 108 + 13100 + 9 + 32 + 1 + 1  = 13383 car -->
            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numCategoria"                length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRichiestaRiscatto"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="distribuzioneRiscatto"       length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRiscattoFondi"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRichiesto"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="dataDisinvestimento"         length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
			natura="Data"/>
			<!-- Dati Penalità per  RP Multinvest 43 car -->
			<field-map attributeName="impPercRiscattoCalc"                 length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="penaleRiscatto"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="importoErogabile"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flNettoPenalita"            length="1" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantitaInput" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoFondiRiscattoInput" length="13100">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13100">            
					<field-map attributeName="fondiRiscatto" length="13100">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="true" natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"  nomeAttributoERR="listaValoriRiscatto[].valoreRiscatto" nomeKeyERR="erroriFondiRiscatto"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map> 
			<field-map attributeName="idPrenotazione" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<!-- DATI VINCOLO 32car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="valoreDa"           				 length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  valida="true" nomeAttributoERR="valoreDa"  nomeKeyERR="erroriFondiRiscatto"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- 1 car -->
			<field-map attributeName="flPrenotazDispositiva"             length="01" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile" 	 length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		    <!--old Area applicativa 1592 + 1514 = 3106 -->
		    <!--old Campi di input 1592 car -->
		    <!--Area applicativa 13383 + 14024 = 27407 -->
		    <!-- Campi di input 13383 car -->
		    <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numCategoria"                length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRichiestaRiscatto"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			natura="Data"/>
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="distribuzioneRiscatto"       length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoRiscattoFondi"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRichiesto"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="dataDisinvestimento"         length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
			natura="Data"/>
			<!-- Dati Penalità per  RP Multinvest 43 car -->
			<field-map attributeName="impPercRiscattoCalc"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="penaleRiscatto"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="importoErogabile"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flNettoPenalita"            length="1" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantitaInput" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoFondiRiscattoInput" length="13100">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13100">            
					<field-map attributeName="fondiRiscatto" length="13100">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"           length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="idPrenotazione" length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<!-- DATI VINCOLO 32car -->
			<field-map attributeName="tipoVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"    					 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"    				 length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="valoreDa"           				 length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- 1 car -->
			<field-map attributeName="flPrenotazDispositiva"             length="01" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile"	 length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!--old Campi di output 108 + 1314 + 92 = 1514 car -->
			<!-- Campi di output 108 + 13104 + 812 = 14024 car -->
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantita" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoFondiRiscatto" length="13104">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13104">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="fondiRiscatto" length="13100">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<!-- old Area errori 92 -->
			<!-- Area errori 812 -->
			<field-map attributeName="importoRichiestoCdErr"           length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRicevRichiestaRiscattoCdErr" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoFondiRiscattoErr" length="800">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoErr" iterations="100" blockLength="8">
					<field-map attributeName="valoreRiscattoCdErr"         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="valoreMinQuotaCdErr"         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>            
				</nested-mapping>
			</field-map>
			<field-map attributeName="valoreDaCdErr"       			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    