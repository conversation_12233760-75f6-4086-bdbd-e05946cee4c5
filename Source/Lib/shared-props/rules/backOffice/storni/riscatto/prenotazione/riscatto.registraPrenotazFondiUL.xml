<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-PRENOTAZ-RISCATTO-FONDI-UL</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE450</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

		<multipleTransaction>true</multipleTransaction>
		<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
		<pageRequestField/>
		<limitPage>99</limitPage>
		<moreDataField/>
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
		<pastedFields>
			<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
		</pastedFields>	   	

		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO" nomeKeyERR="erroriPercipientiNew">
			
			<!--old 89+ 43 + 108 + 1310 + 63+ 78 + 2 + 10 + 10 = 1713 car -->
			<!-- 89+ 43 + 108 + 13100 + 63+ 78 + 2 + 10 + 10 + 58 + 1 + 110 + 1 + 1 = 13674 car -->
			
            <field-map attributeName="categoria"					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="categNum"						length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>			
			<field-map attributeName="dataRichiestaRiscatto"		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="dataRicevRichiestaRiscatto"	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="dataContabile"				length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="tipoRiscatto"					length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="metodoRiscatto"				length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="distribuzioneRiscatto"		length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoRiscattoFondi"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="importoRichiesto"				length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataDisinvestimento"			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<!-- Dati Penalita' per  RP Multinvest 43 car -->
<!-- 			<field-map attributeName="valoreRiscattoLordo"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" -->
<!-- 				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/> -->
			<field-map attributeName="impPercRiscattoCalc"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="penaleRiscatto"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="importoErogabile"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flNettoPenalita"            length="1" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantitaInput" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>		
			<field-map attributeName="elencoFondiRiscattoInput" length="13100">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13100">            
					<field-map attributeName="fondiRiscatto" length="13100">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map> 
			<field-map attributeName="idPrenotazione"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="tipoVincolo"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico" valida="false" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="valoreDa"           	length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  valida="true" 	nomeAttributoERR="valoreDa" 	 nomeKeyERR="erroriFondiRiscatto"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>	
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flPrenotazDispositiva"             length="01" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" 	nomeAttributoERR="dataConsDocCdErr" 	 nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRicezioneDoc"			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" 	nomeAttributoERR="dataRicezioneDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="sottoFunzionePrenotaz" length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Prodotti particolari 78 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />		
			<field-map attributeName="flCompletaPIP"    	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Flag 2 car -->
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- dati collettiva  10 car -->
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 valida="true" natura="Data"  nomeAttributoERR="dataPrimaIscrizione"    nomeKeyERR="erroriPercipientiNew"/>			
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagArt11"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<!-- Dati LETTERE-RICH-DOCUME 56 car -->
			<field-map attributeName="dataRichDocCliente"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRichDocCliente" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoLiquid"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgOpzione"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProcessiNonConcl"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscatto"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscattoAltro"          length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flErroriNoCompleta" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		    <!-- old Totali 1713 dati Input + 1538 dati output  = 3251 --> 
		     <!-- Totali 13563 dati Input + 14064 dati output  = 27726 -->		    
            <field-map attributeName="categoria"					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"					length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="categNum"						length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>			
			<field-map attributeName="dataRichiestaRiscatto"		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="dataRicevRichiestaRiscatto"	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="dataContabile"				length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="tipoRiscatto"					length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="metodoRiscatto"				length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="distribuzioneRiscatto"		length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="tipoRiscattoFondi"			length="02"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			<field-map attributeName="importoRichiesto"				length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" valida="true" segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="dataDisinvestimento"			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<!-- Dati Penalita' per  RP Multinvest 43 car -->
<!-- 			<field-map attributeName="valoreRiscattoLordo"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" -->
<!-- 				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/> -->
			<field-map attributeName="impPercRiscattoCalc"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="penaleRiscatto"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="importoErogabile"            length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flNettoPenalita"            length="1" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantitaInput" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>				
			<field-map attributeName="elencoFondiRiscattoInput" length="13100">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13100">            
					<field-map attributeName="fondiRiscatto" length="13100">        
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map> 
			<field-map attributeName="idPrenotazione"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
						natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="tipoVincolo"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"			length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoVincolo"		length="14" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
					natura="Numerico" valida="false" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"  />
			<field-map attributeName="valoreDa"           				 length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
			<field-map attributeName="flgAnte"     	 		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flPrenotazDispositiva"             length="01" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					natura="Data" valida="false"/>
			<field-map attributeName="dataRicezioneDoc"			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					natura="Data" valida="false"/>
			<field-map attributeName="sottoFunzionePrenotaz" length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>					
			<!-- Prodotti particolari 78 car -->
			<field-map attributeName="tipoProdottoSpeciale"   length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="penaleTFM"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="true" separatoreMigliaia="true" numInteri="7" numDecimali="3" />
			<field-map attributeName="tipoUscita"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgManleva"    		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgInps"    		      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoInps"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="percInps"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="percIrpef"    		      length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="false" numInteri="3" numDecimali="4" />
			<field-map attributeName="imponibileIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="importoIrpef"    		  length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico" segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2" />
			<field-map attributeName="flCompletaPIP"    	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- dati collettiva  10 car -->
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  natura="Data" />
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagArt11"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<!-- Dati LETTERE-RICH-DOCUME 56 car -->
			<field-map attributeName="dataRichDocCliente"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoLiquid"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgOpzione"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProcessiNonConcl"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>			
			<field-map attributeName="motivoRiscatto"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscattoAltro"          length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flErroriNoCompleta" 			length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!--old campi specifici di Output:108 + 1314 + 100 + 16 = 1538 car -->	
			<!-- campi specifici di Output:108 + 13104 + 820 + 20 = 14052 car -->			
			<!-- Componente garantita per Multinvest 108 car -->
			<field-map attributeName="componenteGarantita" length="108">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO" iterations="0" blockLength="108">
					<field-map attributeName="descrFondoRiv"          		length="42"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="valoreRiscatto"        		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
					natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRivalutato"     			length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="valoreRiscattato"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 	natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
	           	 	<field-map attributeName="dataEffetto"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="rendimento"     		length="14"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
	           	 		natura="Numerico"  segnato="false"  separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoFondiRiscatto" length="13104">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="13104">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="fondiRiscatto" length="13100">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="100" blockLength="131">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="dataDisinvestimentoFondo"   length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="3"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="6" numDecimali="3"/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="5" numDecimali="3"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false" separatoreMigliaia="true" numInteri="9" numDecimali="2"/>
							<field-map attributeName="tipologia"          length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="importoRichiestoCdErr"        length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="filler1Err"        				length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="elencoFondiRiscattoErr" length="800">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoErr" iterations="100" blockLength="8">
					<field-map attributeName="valoreRiscattoCdErr"         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="valoreMinQuotaCdErr"         length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>            
				</nested-mapping>
			</field-map>
			<field-map attributeName="dataConsDocCdErr"        		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRicezioneDocCdErr"       	length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="valoreDaCdErr"       			length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataPrimaIscrizioneCdErr"     length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoIscrittoCdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="aliquitaTfrCdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flagArt11CdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRichDocClienteCdErr"      length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    