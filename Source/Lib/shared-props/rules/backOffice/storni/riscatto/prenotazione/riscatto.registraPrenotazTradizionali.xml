<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-PRENOTAZ-RISCATTO-TRADIZIONALE</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE451</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

		<multipleTransaction>true</multipleTransaction>
		<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
		<pageRequestField/>
		<limitPage>99</limitPage>
		<moreDataField/>
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
		<pastedFields>
			<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
		</pastedFields>	   	

		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO">
			<!-- 58 + 21 + 56 + 110 + 1 car -->
            <field-map attributeName="categoria"					length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="idPrenotazione"				length="09"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="distribuzioneRiscatto"		length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoVincolo"					length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"					length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataConsDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRicezioneDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataRicezioneDocCdErr" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="sottoFunzionePrenotaz" 		length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flCompletaPIP"    	  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 valida="true" natura="Data"  nomeAttributoERR="dataPrimaIscrizione"    nomeKeyERR="erroriPercipientiNew"/>			
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagArt11"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<field-map attributeName="flInoltra"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>			
			<!-- Dati LETTERE-RICH-DOCUME 56 car -->
			<field-map attributeName="dataRichDocCliente"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRichDocCliente" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscatto"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscattoAltro"          length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		    <!-- Totali 58 + 21 + 56 + 110 + 1 dati Input + 8 + 20 dati output = 274 --> 		    
            <!-- Stessi dati input di 58 + 21 + 56 + 110 + 1 car -->
           <field-map attributeName="categoria"						length="01"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"				length="06"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"					length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"				length="07"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="idPrenotazione"				length="09"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="distribuzioneRiscatto"		length="02" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoVincolo"					length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="richiedente"					length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataConsDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="dataRicezioneDoc"					length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="sottoFunzionePrenotaz" 		length="01" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>				
			<field-map attributeName="flCompletaPIP"    	  		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flFacta"     		       		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInserisci"    	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 valida="true" natura="Data"  nomeAttributoERR="dataPrimaIscrizione"    nomeKeyERR="erroriPercipientiNew"/>			
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flagArt11"   		  	  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="flInoltra"     		  length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<!-- Dati LETTERE-RICH-DOCUME 56 car -->
			<field-map attributeName="dataRichDocCliente"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscatto"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="motivoRiscattoAltro"          length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInibizioneBloccoMaxErogabile" length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
			<!-- campi specifici di Output: 8 + 20 car -->			
			<field-map attributeName="dataConsDocCdErr"        		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRicezioneDocCdErr"        length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataPrimaIscrizioneCdErr"     length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoIscrittoCdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="aliquitaTfrCdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flagArt11CdErr"       		length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataRichDocClienteCdErr"      length="04"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    