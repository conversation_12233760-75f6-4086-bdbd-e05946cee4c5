<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-VALORI-RISCATTO-LIQUIDAZIONE-PER-ANNULLO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0135</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO">
			<!-- Totali 238 car (150 + 112) -->
			<!-- 112 car -->
            <field-map attributeName="categoria"      			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"			   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"    			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"            		   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categNum"                    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFiscalita"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flTFR"                       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flUnit"                      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoCateg"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="faseUL"                      length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataContabile" nomeKeyERR="erroreLiquidazioneRiscatto"/>
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataStorno" nomeKeyERR="erroreLiquidazioneRiscatto"/>
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riscattoTotFip"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRichiestaRiscatto"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="percTFR"                     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataFondiRival"              length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="flPreventivo"      		   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="appendice"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data" />		
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO">
		    <!-- Totali 12348 car (150 + 10374 + 1824) --> 
		    <!-- Campi di output 10374 car -->
            <!-- input 112 car -->
            <field-map attributeName="categoria"      			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"			   length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"    			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			   length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"      			   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"            		   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"  			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="categNum"                    length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="tipoFiscalita"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flTFR"                       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flUnit"                      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoCateg"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="faseUL"                      length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataStorno"                  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="tipoRiscatto"                length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="metodoRiscatto"              length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riscattoTotFip"              length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRichiestaRiscatto"       length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="percTFR"                     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataFondiRival"              length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flPreventivo"      		   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="appendice"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataDisinvestimento"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>					
			<field-map attributeName="dataRicevRichiestaRiscatto"  length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			
			<!-- output 1 + 17704 + 24 +1204 +179 -->
			<field-map attributeName="flFipPip"      		   	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			 <field-map attributeName="elencoValoriRiscatto" length="17704" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoValoriRiscattoDTO" iterations="0" blockLength="17704">	
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="valoriRiscatto" length="17700" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoDTO" iterations="100" blockLength="177">
							<field-map attributeName="cognome"               	length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="nome"               		length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="importoLordoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="segnoOneriOperazioni"     length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="oneriOperazione"          length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="importoNettoRiscatto"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="interessiPrestiti"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="prestiti"                 length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="imposteRedditiCapitale"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="altreImposteRedditi"      length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="importoLiquidato"         length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
							<field-map attributeName="indiceTabella"            length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="dataContabileCdErr"          length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataStornoCdErr"             length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<!-- Fondi -->
			<field-map attributeName="tipoRiscattoFondi"           length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRichiesto"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="elencoFondiRiscatto" length="1204">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO" iterations="0" blockLength="1204">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="fondiRiscatto" length="1200">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO" iterations="10" blockLength="120">
							<field-map attributeName="descrFondo"          length="42" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							<field-map attributeName="numeroQuote"         length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="false" natura="Numerico"  segnato="true"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreRiscatto"      length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreMinQuota"      length="11" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreQuota"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="5" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="controvaloreQuota"    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="controvaloreRiscatto" length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<!-- valori riscatto fip pip --> 
			<field-map attributeName="valoriRiscattoFipPip" length="179">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoFipPipDTO" iterations="0" blockLength="179">            
					<field-map attributeName="cognomeFip"                length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="nomeFip"             		 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoLordoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="segnoOneriOperazioniFip"   length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="oneriOperazioneFip"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="importoNettoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="importoRiscattiPrecFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="imposteRedditiCapitaleFip" length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="altreImposteRedditiFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="imposteRedditiPrecFip"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="importoLiquidatoFip"       length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						valida="false" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>						
					<field-map attributeName="numQuietanzaFip"           length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    