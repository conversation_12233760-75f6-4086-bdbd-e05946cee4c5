<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-ANNULLO-RISCATTO-FIP-PIP</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0181</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipientiRequestDTO">
			<!-- Totali 350 car (150 + 200) -->
			<!-- 200 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="valoriRiscattoFipPip" length="179">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoFipPipDTO" iterations="0" blockLength="179">            
					<field-map attributeName="cognomeFip"             	 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="nomeFip"             		 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoLordoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="segnoOneriOperazioniFip"   length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="oneriOperazioneFip"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoNettoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoRiscattiPrecFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiCapitaleFip" length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="altreImposteRedditiFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiPrecFip"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoLiquidatoFip"       length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="numQuietanzaFip"           length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.dto.PercipientiResponseDTO">
		    <!-- Totali 2182 car (150 + 208 + 1824) --> 
		    <!-- Campi di output 208 car -->
		    <!-- 200 car -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="valoriRiscattoFipPip" length="179">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ValoreRiscattoFipPipDTO" iterations="0" blockLength="179">            
					<field-map attributeName="cognomeFip"             	 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="nomeFip"             		 length="30" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoLordoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="segnoOneriOperazioniFip"   length="1"  precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="oneriOperazioneFip"        length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="importoNettoRiscattoFip"   length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoRiscattiPrecFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiCapitaleFip" length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="altreImposteRedditiFip"    length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="imposteRedditiPrecFip"     length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="importoLiquidatoFip"       length="14" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>						
					<field-map attributeName="numQuietanzaFip"           length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="erroreValoriRiscattoFip" length="8">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ErroreValoriRiscattoDTO" iterations="0" blockLength="8">            
					<field-map attributeName="importoLiquidatoCdErr"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset=""  padding="0"/>
					<field-map attributeName="numQuietanzaCdErr"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>