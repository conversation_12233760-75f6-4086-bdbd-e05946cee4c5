<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONVALIDA-DIFFERIMENTO-A-SCADENZA-NEW</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE230</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.differimentoScadenza.dto.DifferimentoScadenzaNewRequestDTO">
			<!-- Totali 177 car (150 + 27) -->
			<!-- 27 car -->
            <field-map attributeName="livello"           		length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="trattabili"  	  			length="5"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottofunzione"  	  		length="1"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>	
			<field-map attributeName="dataContabile"          	length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data"/>
			<field-map attributeName="dataEffettoStorno"      	length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data"/>
			<field-map attributeName="capMaturatoNum"      		length="14" precision="0" numericScale="0" align="right"   mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="durataPolNum"      		length="5" 	precision="0" numericScale="0" align="right"   mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="scadenza"      			length="10" 	precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			<field-map attributeName="riservaMatematica"      length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
			  natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"  />
			<field-map attributeName="durataDiff"             length="2" precision="0"  numericScale="0"  align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>		
			<field-map attributeName="newScadenza"            length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			  natura="Data"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.differimentoScadenza.dto.DifferimentoScadenzaNewResponseDTO">
		    <!-- Totali 2027 car (150 + 81 + 1824) --> 
		    <!-- Campi di output 81 car -->
            <field-map attributeName="livello"           		length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="trattabili"  	  			length="5"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottofunzione"  	  		length="1"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>				
			<field-map attributeName="dataContabile"          	length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data"/>
			<field-map attributeName="dataEffettoStorno"      	length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data"/>
			<field-map attributeName="capMaturatoNum"      		length="14" precision="0" numericScale="0" align="right"   mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="durataPolNum"      		length="5" 	precision="0" numericScale="0" align="right"   mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="scadenza"      			length="10" 	precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Data"/>
			
			<field-map attributeName="riservaMatematica"      	length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
			 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"  />
			<field-map attributeName="durataDiff"             	length="2" precision="0"  numericScale="0"  align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>		
			<field-map attributeName="newScadenza"            	length="10" precision="0" numericScale="0" align="left"   mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data"/>
			 <field-map attributeName="durataDifferimentoCdErr"  length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    