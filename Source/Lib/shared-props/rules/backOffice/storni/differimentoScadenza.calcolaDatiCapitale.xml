<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CALCOLA-CAPITALE-DIFFERIMENTO-A-SCADENZA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSER0110</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.differimentoScadenza.dto.DifferimentoScadenzaRequestDTO">
			<!-- Totali 345 car (150 + 57) -->
			<!-- 225 car -->
            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffettoStorno"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoScadenzaIniz"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoDifferito"            length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="durataDifferimento"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoScadenza"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riscattoMaturato"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="utili"                       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="cumuloPremi"                 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="prestiti"                    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoInterPrestiti"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="impInterPrestiti"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoRitenute"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="ritenute"                    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoRettifiche"             length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descrizRettifiche"           length="19" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="rettifiche"                  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="liquidazione"                length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.differimentoScadenza.dto.DifferimentoScadenzaResponseDTO">
		    <!-- Totali 2251 car (150 + 277 + 1824) --> 
		    <!-- Campi output 277 -->
		    <!-- Campi di input 225 car -->
            <field-map attributeName="categoria"                   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="agenziaPolizza"              length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"                  length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"               length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"                   length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"                     length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="sottoFunzione"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile"               length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataEffettoStorno"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoScadenzaIniz"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoDifferito"            length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="durataDifferimento"          length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoScadenza"             length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="riscattoMaturato"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="utili"                       length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="cumuloPremi"                 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="prestiti"                    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoInterPrestiti"          length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="impInterPrestiti"            length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoRitenute"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="ritenute"                    length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="segnoRettifiche"             length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="descrizRettifiche"           length="19" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="rettifiche"                  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="liquidazione"                length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <!-- Dati output 52 car -->
             <field-map attributeName="importoDifferitoCdErr"      length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataDifferimentoCdErr"     length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="importoScadenzaCdErr"        length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="riscattoMaturatoCdErr"       length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="utiliCdErr"                  length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="cumuloPremiCdErr"            length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="prestitiCdErr"               length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="segnoInterPrestitiCdErr"     length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="impInterPrestitiCdErr"       length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="ritenuteCdErr"               length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="segnoRettificheCdErr"        length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="descrizRettificheCdErr"      length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="rettificheCdErr"             length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    