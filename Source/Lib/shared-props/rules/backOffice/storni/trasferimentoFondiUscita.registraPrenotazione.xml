<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-TRASF-FONDI-OUT-PRENOTAZ</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
		<program>VWLSE438</program>
		<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
		<logApp>true</logApp>
		<logAppServDesc>REGISTRA PRENOTAZ TRASF FONDI</logAppServDesc>
		<areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>

		<multipleTransaction>true</multipleTransaction>
		<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
		<pageRequestField />
		<limitPage>99</limitPage>
		<moreDataField />
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0"
			secondValue="" />
		<pastedFields>
			<fieldToBePaste absoluteInPosition="0"
				absoluteOutPosition="0" length="0" />
		</pastedFields>
		<!-- Definizione commarea: VWLSE438 Lunghezza: 150 + 544 + 1824 = 2518 -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaRequestDTO" nomeKeyERR="erroriTrasferimentoFondiUscita" >
			<!-- area cliente 123 + 17 + 19 + 142 + 70 + 18 + 79 = 468 car -->
			<field-map attributeName="categNum"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataEffettoStorno" 		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataTrasferimento" 		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataDisinvestimento" 	 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" 	occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="flUnit"               	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzione"  			  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="capitaleDaTrasferire"   	  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="idPrenotazione" 			  length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgCompleta"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoVincolo"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRicezioneDoc"      	length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataRicezioneDoc" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="dataConsDoc"      		   length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" valida="true" nomeAttributoERR="dataConsDoc" nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="causaleTrasferimento"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"  	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- DATI Fondo 17 car -->
			<field-map attributeName="datiFondoDestinazione" length="17">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondoDTO" iterations="0" blockLength="17">
					<field-map attributeName="flUnificazioneFondi"   	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codFondoDest"   			length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />
					<field-map attributeName="tipoFondo"   				length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="compartoFondo"			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codSocDest"   			length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- DATI SECONDARI 19 car -->
			<field-map attributeName="datiSecondariPolizza" length="19">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSecondariPolizzaDTO" iterations="0" blockLength="19">
					<field-map attributeName="divisa"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="matricola"			length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="statoPolizza"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="causale"    			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
			</field-map>
			<!-- DATI SOCIETA 142 car -->
			<field-map attributeName="datiSocietaDestinazione" length="142">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSocietaDTO" iterations="0" blockLength="142">
					<field-map attributeName="stato"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provincia"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="cap"    			length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="comune"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrUrbana"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrStrada"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numCivico"    	length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- DATI PAGAMENTO 70 car -->
			<field-map attributeName="datiPagamento" length="70">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="70">
							<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           		<field-map attributeName="intestatario"     	        length="40"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateContoCorrente"     	length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 	natura="Data" valida="true" nomeAttributoERR="dataPrimaIscrizioneCdErr" nomeKeyERR="erroriTrasferimentoFondiUscita" />
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati LETTERE-RICH-DOCUME 78 car -->
			<field-map attributeName="dataRichDocCliente"   	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRichDocCliente" 	 nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRichBenestare"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Data" nomeAttributoERR="dataRichBenestare" 	 nomeKeyERR="erroriTrasferimentoFondiUscita"/>
			<field-map attributeName="dataRichBenestareIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichBenestare"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettadataRichBenestare"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInoltra"          		    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.TrasferimentoFondiUscitaResponseDTO">
			<!-- DATI 371 + 18 + 78 + 56 + 12 + 9 = 544 car -->
			<field-map attributeName="categNum"   				length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
   			<field-map attributeName="agenziaPolizza" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"     			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"  			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="livello"        			length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataContabile" 			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataEffettoStorno" 		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataTrasferimento" 		length="10" precision="0" numericScale="0" align="left" mandatory="0" separator=""  occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="dataDisinvestimento" 	 	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" 	occurs="1" default="" offset="" padding="" 
				natura="Data" />
			<field-map attributeName="flUnit"               	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzione"  			  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="capitaleDaTrasferire"   	  length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
				natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
			<field-map attributeName="idPrenotazione" 			  length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"
				natura="Numerico"  segnato="false"  separatoreMigliaia="false" numInteri="9" numDecimali="0"/>
			<field-map attributeName="sottoFunzionePrenotaz"  	   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgCompleta"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flgVincolo"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoVincolo"    			   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRicezioneDoc"      	length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="dataConsDoc"      		   length="10" precision="0" numericScale="0" align="left"   mandatory="0"  separator=""  occurs="1"  default="" offset="" padding=""
				natura="Data" />
			<field-map attributeName="causaleTrasferimento"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="tipoUtente"  	  			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- DATI Fondo 17 car -->
			<field-map attributeName="datiFondoDestinazione" length="17">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondoDTO" iterations="0" blockLength="17">
					<field-map attributeName="flUnificazioneFondi"   	length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codFondoDest"   			length="7" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""
						natura="Numerico" segnato="false" numInteri="7" numDecimali="0" separatoreMigliaia="false" />
					<field-map attributeName="tipoFondo"   				length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="compartoFondo"			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="codSocDest"   			length="3"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- DATI SECONDARI 19 car -->
			<field-map attributeName="datiSecondariPolizza" length="19">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSecondariPolizzaDTO" iterations="0" blockLength="19">
					<field-map attributeName="divisa"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="matricola"			length="12" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="tipologia"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="statoPolizza"    		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="causale"    			length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            	</nested-mapping>
			</field-map>
			<!-- DATI SOCIETA 142 car -->
			<field-map attributeName="datiSocietaDestinazione" length="142">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiSocietaDTO" iterations="0" blockLength="142">
					<field-map attributeName="stato"   			length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="provincia"		length="2" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					<field-map attributeName="cap"    			length="5"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>     
		            <field-map attributeName="comune"    		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrUrbana"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="descrStrada"    	length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="numCivico"    	length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<!-- DATI PAGAMENTO 70 car -->
			<field-map attributeName="datiPagamento" length="70">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="70">
							<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           		<field-map attributeName="intestatario"     	        length="40"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateContoCorrente"     	length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="dataPrimaIscrizione"    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 	natura="Data"  />
			<field-map attributeName="tipoIscritto"   		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="aliquitaTfr"   		  length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- Dati LETTERE-RICH-DOCUME 78 car -->
			<field-map attributeName="dataRichDocCliente"   		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichDocClienteIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettaRichDocCliente"		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito1"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito1"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataAttivPratica"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataAttivPratica"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataSollecito2"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataSollecito2"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRichBenestare"   			length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="dataRichBenestareIniziale"	length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Data"/>
			<field-map attributeName="flInviataRichBenestare"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flProtettadataRichBenestare"	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flDocumentiMancanti"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flInoltra"   	       	        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<!-- output 16 + 40 + 12 + 8 = 76 car  -->
			<field-map attributeName="dataContabileCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataEffettoStornoCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataTrasferimentoCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataDisinvestimentoCdErr"   length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="datiFondiErr" length="40">
            	<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasferimentoFondiUscita.dto.DatiFondiErrDTO" iterations="0" blockLength="40">
            		<field-map attributeName="flUnificazione"      		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="modPagamento"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	
					 <field-map attributeName="intestatario"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateSiglaInternaz"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateNumControllo"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateCin"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateAbi"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateCab"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="coordinateContoCorrente"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					 <field-map attributeName="capitaleDaTrasferire"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
            	</nested-mapping>
            </field-map>
            <field-map attributeName="dataPrimaIscrizioneCdErr"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="tipoIscrittoCdErr"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="aliquitaTfrCdErr"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRichDocClienteCdErr"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="dataRichBenestareCdErr"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>