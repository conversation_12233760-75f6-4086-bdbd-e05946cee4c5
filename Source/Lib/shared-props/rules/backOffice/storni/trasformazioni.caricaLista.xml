<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-TRASFORMAZIONI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE271</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.storni.trasformazione.dto.ControllaParametriTrasformazioneRequestDTO">
			<!-- Totali 175 car (150 + 27) -->
			<!-- 27 car -->
            <field-map attributeName="funzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="tipoStorno"  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
   			<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="categNum"     length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="paginaCorrente"                   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataContabile"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataContabile" nomeKeyERR="erroriStornoTrasformazione"  />
            <field-map attributeName="dataStorno"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataStorno" nomeKeyERR="erroriStornoTrasformazione"  />
            <field-map attributeName="dataRicevimentoRichiesta"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataRicevimentoRichiesta" nomeKeyERR="erroriStornoTrasformazione"  />
            <field-map attributeName="dataDisinvestimento"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataInvestimento" nomeKeyERR="erroriStornoTrasformazione"  />
            <field-map attributeName="oggTrasformazioneReqArrayList"	length="6000" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasformazione.dto.TrasformazioneReq" iterations="75" blockLength="80">
					<field-map attributeName="ultimaQuietIncassata"  		length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
					<field-map attributeName="posizione"   length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
					<field-map attributeName="codiceUnitTecnica"      length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="premiVersati"      length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="risMatematica"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="recuperoUno"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="oggTrasformazioneDTOArrayListError[].recuperoUno" nomeKeyERR="erroriStornoTrasformazione"/>
					<field-map attributeName="recupero2"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="oggTrasformazioneDTOArrayListError[].recupero2" nomeKeyERR="erroriStornoTrasformazione"/>
				</nested-mapping>
			</field-map>
					
			<field-map attributeName="tipoTR"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="premio"                   length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="premio" nomeKeyERR="erroriStornoTrasformazione"/>
			<field-map attributeName="durata"                     length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="unitat"               length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="abbuono"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" natura="Numerico" segnato="false" numInteri="9" numDecimali="02" separatoreMigliaia="true"/>
			
			<field-map attributeName="totalePagine"              length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="righePagina"                  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroElementiTotali"                  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
			<field-map attributeName="totPre"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totRc1"           length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totRc2"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
			<field-map attributeName="totRis"           length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.storni.trasformazione.dto.CaricaParametriTrasformazioneResponseDTO">
		    <!-- Totali 2011 car (150 + 47 + 1824) --> 
		    <!-- Campi di output 47 car -->
		    
		            <field-map attributeName="funzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="sottoFunzione"  length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		            <field-map attributeName="tipoStorno"  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		            <field-map attributeName="livello"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   			<field-map attributeName="categoria" length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="categNum"     length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="agenziaPolizza"  length="6"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroPolizza"        length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="posizione"        length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					
					<field-map attributeName="paginaCorrente"                   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="dataContabile"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	 natura="Data"   />
		            <field-map attributeName="dataStorno"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	 natura="Data"  />
		            <field-map attributeName="dataRicevimentoRichiesta"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	 natura="Data" />
		            <field-map attributeName="dataDisinvestimento"         length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		            	 natura="Data"   />
		            <field-map attributeName="oggTrasformazioneReqArrayList"	length="6000" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasformazione.dto.TrasformazioneReq" iterations="75" blockLength="80">
							<field-map attributeName="ultimaQuietIncassata"  		length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data"/>
							<field-map attributeName="posizione"   length="04" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Data" />
							<field-map attributeName="codiceUnitTecnica"      length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="premiVersati"      length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
							<field-map attributeName="risMatematica"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
							<field-map attributeName="recuperoUno"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="oggTrasformazioneDTOArrayListError[].recuperoUno" nomeKeyERR="erroriStornoTrasformazione"/>
							<field-map attributeName="recupero2"        	length="14" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="" offset="" padding="" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="oggTrasformazioneDTOArrayListError[].recupero2" nomeKeyERR="erroriStornoTrasformazione"/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="tipoTR"               length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="premio"                   length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" valida="true" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true" nomeAttributoERR="premio" nomeKeyERR="erroriStornoTrasformazione"/>
					<field-map attributeName="durata"                     length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="unitat"               length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="abbuono"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					
					<field-map attributeName="totalePagine"              length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		            <field-map attributeName="righePagina"                  length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numeroElementiTotali"                  length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					
					<field-map attributeName="totPre"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="totRc1"           length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="totRc2"               length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
					<field-map attributeName="totRis"           length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" valida="false" natura="Numerico" segnato="false" numInteri="09" numDecimali="02" separatoreMigliaia="true"/>
				
			<!-- output -->
            <field-map attributeName="dataContabileCdErr" 	  	length="4"  		precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="0000" padding="0"/>
            <field-map attributeName="dataStornoErrore" 	  	length="4"  		precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="0000" padding="0"/>
            <field-map attributeName="dataRicevimentoRichiestaCdErr" 	  	length="4"  		precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
            <field-map attributeName="dataDisinvestimentoCdErr" 	  	length="4"  		precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="0000" offset="" padding="0"/>
            <field-map attributeName="oggTrasformazioneDTOArrayListError"	length="600" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.storni.trasformazione.dto.TrasformazioneResp" iterations="75" blockLength="8">
					<field-map attributeName="recuperoUno"        	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					<field-map attributeName="recupero2"        	length="4" precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				</nested-mapping>
			</field-map>
            
            <field-map attributeName="tipoStornoCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            <field-map attributeName="tipoTRCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="premioCdErr"               length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="durataCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="abbuonoCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="unitatCdErr"               length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totPreCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totRc1CdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totRc2CdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="totRisCdErr"           length="4" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="0000" offset="" padding="0"/>
            	 
			
		</output-mapping>
	</rule>
</rules>
        