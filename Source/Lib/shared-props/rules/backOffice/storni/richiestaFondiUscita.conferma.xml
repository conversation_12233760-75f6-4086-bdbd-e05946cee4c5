<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-RICHIESTA-FONDI-OUT</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
		<program>VWLSE438</program>
		<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

		<multipleTransaction>true</multipleTransaction>
		<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
		<pageRequestField />
		<limitPage>99</limitPage>
		<moreDataField />
		<moreDataEndValue>1</moreDataEndValue>
		<flagFirstTime absolutePosition="0" length="0"
			secondValue="" />
		<pastedFields>
			<fieldToBePaste absoluteInPosition="0"
				absoluteOutPosition="0" length="0" />
		</pastedFields>
		<!-- Definizione commarea: VWLSE438 Lunghezza: 150 + 58 + 1824 = 2032 -->
		<input-mapping
			className="it.sistinf.albedoweb.backoffice.storni.richiestaFondiUscita.dto.RichiestaFondiUscitaRequestDTO">
			<!-- area cliente 42 car -->
			<field-map attributeName="livello" length="01" precision="0"
				numericScale="0" align="left" mandatory="0" separator="" occurs="1"
				default="" offset="" padding="" />
			<field-map attributeName="sottoFunzione" length="01"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataContabile" length="10"
				precision="0" numericScale="0" align="left" mandatory="1" separator=""
				occurs="1" default="" offset="" padding="" valida="true" natura="Data"
				nomeAttributoERR="dataContabile" nomeKeyERR="erroriRichiestaFondiUscita" />
			<field-map attributeName="dataEffettoStorno" length="10"
				precision="0" numericScale="0" align="left" mandatory="1" separator=""
				occurs="1" default="" offset="" padding="" valida="true" natura="Data"
				nomeAttributoERR="dataEffettoStorno" nomeKeyERR="erroriRichiestaFondiUscita" />
			<field-map attributeName="dataTrasferimento" length="10"
				precision="0" numericScale="0" align="left" mandatory="1" separator=""
				occurs="1" default="" offset="" padding="" valida="true" natura="Data"
				nomeAttributoERR="dataTrasferimento" nomeKeyERR="erroriRichiestaFondiUscita" />
			<field-map attributeName="dataDisinvestimento" length="10"
				precision="0" numericScale="0" align="left" mandatory="1" separator=""
				occurs="1" default="" offset="" padding="" valida="true" natura="Data"
				nomeAttributoERR="dataDisinvestimento" nomeKeyERR="erroriRichiestaFondiUscita" />
		</input-mapping>

		<output-mapping
			className="it.sistinf.albedoweb.backoffice.storni.richiestaFondiUscita.dto.RichiestaFondiUscitaResponseDTO">
			<!-- DATI 58 car -->
			<field-map attributeName="livello" length="01" precision="0"
				numericScale="0" align="left" mandatory="0" separator="" occurs="1"
				default="" offset="" padding="" />
			<field-map attributeName="sottoFunzione" length="01"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" />
			<field-map attributeName="dataContabile" length="10"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" natura="Data" />
			<field-map attributeName="dataEffettoStorno" length="10"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" natura="Data" />
			<field-map attributeName="dataTrasferimento" length="10"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" natura="Data" />
			<field-map attributeName="dataDisinvestimento" length="10"
				precision="0" numericScale="0" align="left" mandatory="0" separator=""
				occurs="1" default="" offset="" padding="" natura="Data" />
			<field-map attributeName="dataContabileCdErr"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataEffettoStornoCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataTrasferimentoCdErr"     length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
			<field-map attributeName="dataDisinvestimentoCdErr"   length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000"  offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
