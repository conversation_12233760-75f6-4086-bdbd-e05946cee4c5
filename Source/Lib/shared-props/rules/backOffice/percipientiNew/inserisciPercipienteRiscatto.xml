<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INSERISCI-PERCIPIENTE-NEW-RISCATTO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE296</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>INSERISCI PERCIPIENTE RISCATTO</logAppServDesc>
	    <areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>
	    
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.PercipientiNewRequestDTO">
			<!-- Dati input 25 + 8314 + 34 + 71 + 20 + 1352 + 353 + 17 + 662 + 751 + 1055 + 20 + 3 + 84  = 12761 -->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFipPip"           	   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="flDac2"     		       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="presenteEsecutore"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="elencoPercipienti" length="8314">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipienti" iterations="0" blockLength="8314">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>
					<field-map attributeName="percipienti" length="8310">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.UnPercipiente" iterations="30" blockLength="277">
			           		<field-map attributeName="nominativo"     		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="luogoNascita"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="dataNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Data"/>
			           		<field-map attributeName="percentuale"    		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			           		<field-map attributeName="liquidato"      		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="rendita"    	  		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="scelta"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="nome"            		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="fisGiu"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="modPag"          		length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="impSudd"         		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="progrTst075"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progressivo"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codAnag"         		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                   	<field-map attributeName="modificabile"     	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		                    <field-map attributeName="flagAnagraf"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagContoc"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagAntiric"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="percip"     	    	length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="blocco"           	length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="liquid"      			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="numQuiet"      		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="progrTst080"      	length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="invioDatiCollettore"	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="tipologia"     	    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="soggettoTerzo"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="percTotSucc"   	length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			<field-map attributeName="impTotLiquidato"	length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="impTotRendita" 	length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="datiGenerali" length="71">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiGenerali" iterations="0" blockLength="71">
	           		<field-map attributeName="keypol"       length="18" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="pgmPercip"    length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="pgmTst077"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="pgmTst075"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="pgmTst076"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagAnagr"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagContoc"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagAntir"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="okCodana"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="recValido"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="codFiscCorr"	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagBlocco"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="codAnag"      length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	 				<field-map attributeName="protCodana"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="percSucc"   	 length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.perSucc" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="importoSucc"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.importoSucc" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="elencoPercipienteSoggettoTerzo" length="1352">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipientiDettaglio" iterations="0" blockLength="1352">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>		    
					<field-map attributeName="percipienti" length="1348">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="2" blockLength="674">
							<!-- DATI GENERALI = 371 -->
							<field-map attributeName="tipologia"   			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceCustomer"     	length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   	 		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  		 		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"   		length="16"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSoggetto"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="pressoRecapito"	    length="50"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"	length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comuRes"   			length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capDiResidenza"   	length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provRes"   			length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoResidenza"       length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"       length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provNascita"          length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"        length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="sesso"                length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgCommerciale"       length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="politicamenteEsposta" length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI ANTIRICICLAGGIO = 94 -->
							<field-map attributeName="tipoDocumento"        length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numDocumento"         length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"         length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataScadenza"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="cab"    				length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="statoRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRilascio" 	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"        length="20"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gruppo"    			length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottoGruppo"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="ateco"    			length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI FATCA DAC2 = 86 -->
							<field-map attributeName="tasseEstero"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    		length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    					length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    				length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"	length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI ATTIVITA PREVALENTE = 123 -->
							<field-map attributeName="attPrevalenteCod"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    			length="5"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numMandatoFiduciario" length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intMandatoFiduciario" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgFiduciarioProprioTerzi" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="comportamentoCliente" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flRichiestaDiretta" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="periodoRapporto" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="profiloRischio" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="operazioneCoerente" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="classeBeneficiario"   length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entitaNonFinanziaria" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiSoggettoTerzo" length="353">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiSoggettoTerzo" iterations="0" blockLength="353">
					<field-map attributeName="relazioneBeneficiarioContraente"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="relazioneBeneficiarioContraenteAltro" length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="flagEsecutore"      					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagSoggettoTerzo"       				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipologiaSoggettoTerzo"    	 	    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoEsecutore"     	        		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagDelega"     	        			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzo"	length="10"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzoAltro"	length="100"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoRappresentanza"					length="10"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoRappresentanzaAltro"				length="100"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="cfConiuge"        length="16"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	        <field-map attributeName="coniugeACarico"   length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	        
	        <field-map attributeName="datiBeneficiarioPersonaGiuridica" length="662">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiBeneficiarioPersonaGiuridica" iterations="0" blockLength="662">
					<field-map attributeName="flagPaesiBlackList"					length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="flagPresenzaTitolariEffettivi"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="numElementiTrovatiTitEff" 			length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
	           		<field-map attributeName="flagLimiteTitEff"   		  			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>
	           		<field-map attributeName="elencoTitolariEffettivi" length="656">
	           			<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.TitolareEffettivo" iterations="4" blockLength="164">
	           				<field-map attributeName="codiceCustomerTitEff"			length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nominativoTitEff"				length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascitaTitEff"			length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascitaTitEff"			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="relazioneContraenteTitEff"    length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           			</nested-mapping>
	           		</field-map>
				</nested-mapping>
			</field-map>	        
	        
			<field-map attributeName="datiPagamento" length="751">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="751">
							<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateContoCorrente"     	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="causale"     		            length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="swift"     		            length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numSottorubrica"     		              length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="motivo"     		                      length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="flgCointestazione"     		          length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="cointestatario"     		              length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="relazioneContraenteCointestatario"      length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="relazioneContraenteCointestatarioAltro" length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="paese"     		                      length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiReimpiego" length="1055">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiReimpiego" iterations="0" blockLength="1055">
	          		<field-map attributeName="flReimpiego"     		        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoReimpiego"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impReimpiego"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="integrazione"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoRapporto"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStessoContraente"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="listaRapportiReimpiego" length="1023">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ListaRapportiReimpiegoDTO" iterations="0" blockLength="1023">            
							<field-map attributeName="numElementiTrovati" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
							<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
							<field-map attributeName="rapportoReimpiego" length="1020">
								<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportoReimpiegoDTO" iterations="20" blockLength="51">
									<field-map attributeName="flagSelRapportoReimpiego"     	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCategoriaRapportoReimpiego" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="agenziaRapportoReimpiego"			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numeroCollettivaReimpiego"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="numeroRapportoReimpiego"			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="convenzioneRapportoReimpiego"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="importoRapportoReimpiego"			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
								</nested-mapping>
							</field-map>					
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		    <field-map attributeName="datiLiquidatoInput" length="20">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiLiquidato" iterations="0" blockLength="20">
		   			<field-map attributeName="numQuietanza"     			length="6"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="6" numDecimali="0" separatoreMigliaia="false"  nomeAttributoERR="datiPercErr.numQuiet" nomeKeyERR="erroriPercipientiNew"/>
					<field-map attributeName="rendita"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.rendita" nomeKeyERR="erroriPercipientiNew"/>
				</nested-mapping>
		    </field-map>
			<field-map attributeName="datiRenditaInput" length="3">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiRendita" iterations="0" blockLength="3">
					<field-map attributeName="tipoRendita"        				length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="frazionamento"      			    length="2"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamentoRendita" length="84">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="84">
					<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateContoCorrente"     	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>   
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.PercipientiNewResponseDTO">
		<!-- Dati input 12761 -->
		<!-- Totale 12761 + 1414 = 14175 -->
		
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFipPip"           	   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="flDac2"     		       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="presenteEsecutore"       length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="elencoPercipienti" length="8314">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipienti" iterations="0" blockLength="8314">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>		    
					<field-map attributeName="percipienti" length="8310">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.UnPercipiente" iterations="30" blockLength="277">
			           		<field-map attributeName="nominativo"     		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="luogoNascita"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="dataNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Data"/>
			           		<field-map attributeName="percentuale"    		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			           		<field-map attributeName="liquidato"      		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="rendita"    	  		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="scelta"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="nome"            		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="fisGiu"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="modPag"          		length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="impSudd"         		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="progrTst075"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progressivo"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codAnag"         		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                   	<field-map attributeName="modificabile"     	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		                    <field-map attributeName="flagAnagraf"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagContoc"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagAntiric"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="percip"     	    	length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="blocco"           	length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="liquid"      			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="numQuiet"      		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="progrTst080"      	length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="invioDatiCollettore"	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="tipologia"     	    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="soggettoTerzo"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="percTotSucc"   	 length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			<field-map attributeName="impTotLiquidato"   length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="impTotRendita"   length="14" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			<field-map attributeName="datiGenerali" length="71">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiGenerali" iterations="0" blockLength="71">
	           		<field-map attributeName="keypol"       length="18" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="pgmPercip"    length="8"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="pgmTst077"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="pgmTst075"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="pgmTst076"    length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagAnagr"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagContoc"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagAntir"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="okCodana"     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="recValido"    length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="codFiscCorr"	length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagBlocco"   length="2"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="codAnag"      length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	 				<field-map attributeName="protCodana"   length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="percSucc"   	 length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  numInteri="3" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.perSucc" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="importoSucc"   length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
				valida="true" natura="Numerico"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.importoSucc" nomeKeyERR="erroriPercipientiNew"/>
			<field-map attributeName="elencoPercipienteSoggettoTerzo" length="1352">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipientiDettaglio" iterations="0" blockLength="1352">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>		    
					<field-map attributeName="percipienti" length="1348">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="2" blockLength="674">
							<!-- DATI GENERALI = 321 -->
							<field-map attributeName="tipologia"   			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceCustomer"     	length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="cognome"   	 		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nome"  		 		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codiceFiscale"   		length="16"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tipoSoggetto"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="pressoRecapito"	    length="50"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="indirizzoDiResidenza"	length="60"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comuRes"   			length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capDiResidenza"   	length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provRes"   			length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoResidenza"       length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascita"       length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provNascita"          length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascita"        length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="sesso"                length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgCommerciale"       length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza1"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza2"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittadinanza3"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="politicamenteEsposta" length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI ANTIRICICLAGGIO = 94 -->
							<field-map attributeName="tipoDocumento"        length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numDocumento"         length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="enteRilascio"    		length="20"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataRilascio"         length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data"/>
							<field-map attributeName="dataScadenza"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="cab"    				length="5"   precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="statoRilascio"    	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaRilascio" 	length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoRilascio"        length="20"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gruppo"    			length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sottoGruppo"    		length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="ateco"    			length="3"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI FATCA DAC2 = 86 -->
							<field-map attributeName="tasseEstero"    				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="tasseEsteroCodStato"    		length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="usStatus"    					length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="taxIdNumber"    				length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscaleEstero"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1CodStato"	length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2CodStato"	length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale1Nif"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="residenzaFiscale2Nif"    		length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="gin"    						length="20" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<!-- DATI ATTIVITA PREVALENTE = 123 -->
							<field-map attributeName="attPrevalenteCod"    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="provinciaSav"    		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="cittaSavDesc"    		length="35" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="capSav"    			length="5"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numMandatoFiduciario" length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="intMandatoFiduciario" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flgFiduciarioProprioTerzi" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="comportamentoCliente" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flRichiestaDiretta" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="periodoRapporto" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="profiloRischio" 		length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="operazioneCoerente" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="classeBeneficiario"   length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="entitaNonFinanziaria" length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiSoggettoTerzo" length="353">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiSoggettoTerzo" iterations="0" blockLength="353">
					<field-map attributeName="relazioneBeneficiarioContraente"      length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="relazioneBeneficiarioContraenteAltro" length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="flagEsecutore"      					length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagSoggettoTerzo"       				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipologiaSoggettoTerzo"    	 	    length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoEsecutore"     	        		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="flagDelega"     	        			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzo"	length="10"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzoAltro"	length="100"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoRappresentanza"					length="10"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="tipoRappresentanzaAltro"				length="100"	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	           	 	
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="cfConiuge"        length="16"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	        <field-map attributeName="coniugeACarico"   length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	        
	        <field-map attributeName="datiBeneficiarioPersonaGiuridica" length="662">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiBeneficiarioPersonaGiuridica" iterations="0" blockLength="662">
					<field-map attributeName="flagPaesiBlackList"					length="1" 	precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="flagPresenzaTitolariEffettivi"      	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           		<field-map attributeName="numElementiTrovatiTitEff" 			length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
	           		<field-map attributeName="flagLimiteTitEff"   		  			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>
	           		<field-map attributeName="elencoTitolariEffettivi" length="656">
	           			<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.TitolareEffettivo" iterations="4" blockLength="164">
	           				<field-map attributeName="codiceCustomerTitEff"			length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="nominativoTitEff"				length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="luogoDiNascitaTitEff"			length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDiNascitaTitEff"			length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="relazioneContraenteTitEff"    length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           			</nested-mapping>
	           		</field-map>
				</nested-mapping>
			</field-map>	        
	        
			<field-map attributeName="datiPagamento" length="751">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="751">
							<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
			           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="coordinateContoCorrente"     	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="causale"     		            length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="swift"     		            length="11"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="numSottorubrica"     		              length="12"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="motivo"     		                      length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="flgCointestazione"     		          length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="cointestatario"     		              length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="relazioneContraenteCointestatario"      length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="relazioneContraenteCointestatarioAltro" length="200" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           	 	<field-map attributeName="paese"     		                      length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiReimpiego" length="1055">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiReimpiego" iterations="0" blockLength="1055">
	          		<field-map attributeName="flReimpiego"     		        length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoReimpiego"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="impReimpiego"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="integrazione"     		    length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="tipoRapporto"     		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flStessoContraente"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="listaRapportiReimpiego" length="1023">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ListaRapportiReimpiegoDTO" iterations="0" blockLength="1023">            
							<field-map attributeName="numElementiTrovati" length="2" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
							<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
							<field-map attributeName="rapportoReimpiego" length="1020">
								<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportoReimpiegoDTO" iterations="20" blockLength="51">
									<field-map attributeName="flagSelRapportoReimpiego"     	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCategoriaRapportoReimpiego" 	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="agenziaRapportoReimpiego"			length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numeroCollettivaReimpiego"		length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="numeroRapportoReimpiego"			length="9"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
									<field-map attributeName="convenzioneRapportoReimpiego"		length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="importoRapportoReimpiego"			length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
								</nested-mapping>
							</field-map>					
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiLiquidatoInput" length="20">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiLiquidato" iterations="0" blockLength="20">
		   			<field-map attributeName="numQuietanza"     			length="6"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="false" numInteri="6" numDecimali="0" separatoreMigliaia="false"  nomeAttributoERR="datiPercErr.numQuiet" nomeKeyERR="erroriPercipientiNew"/>
					<field-map attributeName="rendita"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="datiPercErr.rendita" nomeKeyERR="erroriPercipientiNew"/>
				</nested-mapping>
		   </field-map>
			<field-map attributeName="datiRenditaInput" length="3">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiRendita" iterations="0" blockLength="3">
					<field-map attributeName="tipoRendita"        				length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="frazionamento"      			    length="2"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamentoRendita" length="84">				
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="84">
					<field-map attributeName="modPagamento"        			length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           		<field-map attributeName="coordinateSiglaInternaz"      length="2"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
	           	 	<field-map attributeName="coordinateNumControllo"       length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCin"    	 	    length="1"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateAbi"     	        length="5"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateCab"     	        length="5"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="coordinateContoCorrente"     	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	           	 	<field-map attributeName="intestatario"     	        length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="invioDatiCollettore"     		length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>   
			<!-- Dati output 408 + 91 + 3 + 912 = 1414 -->
		 	<field-map attributeName="datiLiquidato" length="408">
 				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiLiquidato" iterations="0" blockLength="408">
						<field-map attributeName="numQuietanza"     			length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="cognome"   					length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="nome"  		 				length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						
						<field-map attributeName="impLordo"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="segnoOneriOperazioni"     	length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="oneriOperazioni"     			length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impNetto"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="interessiPrestiti"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="prestiti"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impIrpef"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impInps"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impSuRedditiCapitale"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="altreImposte"     			length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="importoLiquidato"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="prog80"     		length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						<field-map attributeName="rendita"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="capitale"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impLordoFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="segnoOneriOperazioniFip"     	length="1"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						<field-map attributeName="oneriOperazioniFip"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impNettoFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impIrpefFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impInpsFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impRiscattiPrecFip"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="impSuRedditiCapitaleFip"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impSuRedditiFip"     			length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="impSuRedditiPrecFip"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="importoLiquidatoFip"     		length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							 natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						<field-map attributeName="renditaFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="capitaleFip"     				length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					</nested-mapping>
				</field-map>
				<field-map attributeName="datiRendita" length="91">			
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiRendita" iterations="0" blockLength="91">
						<field-map attributeName="tipoRendita"        				length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
		           		<field-map attributeName="frazionamento"      			    length="2"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
		           	 	<field-map attributeName="impLiqRendita"    	 	    	length="14"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		           	 	<field-map attributeName="numAnniRenditaCerta"     	        length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		           	 		natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="true"/>
		           	 	<field-map attributeName="percReversibilita"     	        length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		           	 	<field-map attributeName="renditaAnnua"     				length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		           	 	<field-map attributeName="renditaATassoZero"     	        length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
		           	 	<field-map attributeName="primaRata"     		            length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						<field-map attributeName="dataPagamento"     				length="10"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
							natura="Data"/>
						<field-map attributeName="premioPuro"    	 	    		length="14"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>							
					</nested-mapping>
				</field-map>
				<field-map attributeName="flTasseEstero"           length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="flUsFisico"              length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="flUsGiuridico"           length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				<field-map attributeName="datiErr" length="912">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipientiErr" iterations="0" blockLength="912">
						<field-map attributeName="percSucc"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>		
						<field-map attributeName="importoSucc"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<!-- 456 -->
						<field-map attributeName="elencoPercipienteSoggettoTerzoErr" length="456">
							<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipiente" iterations="2" blockLength="228">
								<!-- DATI GENERALI = 84 -->
								<field-map attributeName="tipologia"   			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="codiceCustomer"     	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cognome"   	 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="nome"  		 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="codiceFiscale"   		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="tipoSoggetto"   		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="pressoRecapito"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="indirizzoDiResidenza"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="comuRes"   			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="capDiResidenza"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="provRes"   			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="statoResidenza"       length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="luogoDiNascita"       length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="provNascita"          length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="dataDiNascita"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="sesso"                length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="flgCommerciale"       length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cittadinanza1"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cittadinanza2"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cittadinanza3"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="politicamenteEsposta" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<!-- DATI ANTIRICICLAGGIO = 48 -->
								<field-map attributeName="tipoDocumento"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="numDocumento"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="enteRilascio"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="dataRilascio"         length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="dataScadenza"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cab"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="statoRilascio"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="provinciaRilascio" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="luogoRilascio"        length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="gruppo"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="sottoGruppo"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="ateco"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<!-- DATI FATCA DAC2 = 40 -->
								<field-map attributeName="tasseEstero"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="tasseEsteroCodStato"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="usStatus"    					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="taxIdNumber"    				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="residenzaFiscaleEstero"   	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="residenzaFiscale1CodStato"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="residenzaFiscale2CodStato"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="residenzaFiscale1Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="residenzaFiscale2Nif"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="gin"    						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<!-- DATI ATTIVITA PREVALENTE = 52 -->
								<field-map attributeName="attPrevalenteCod"    	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="provinciaSav"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="cittaSavDesc"    		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="capSav"    			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="numMandatoFiduciario" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="intMandatoFiduciario" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="flgFiduciarioProprioTerzi" length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="comportamentoCliente" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="flRichiestaDiretta" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="periodoRapporto" 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="profiloRischio" 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="operazioneCoerente" 	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="classeBeneficiario"   length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="entitaNonFinanziaria" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
							</nested-mapping>
						</field-map>
						<!-- 44 -->
						<field-map attributeName="relazioneBeneficiarioContraente"      length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="relazioneBeneficiarioContraenteAltro" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           		<field-map attributeName="flagEsecutore"      					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="flagSoggettoTerzo"       				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="tipologiaSoggettoTerzo"    	 	    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="tipoEsecutore"     	        		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="flagDelega"     	        			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzo"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="relazioneBeneficiarioSoggettoTerzoAltro"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
	           	 		<field-map attributeName="tipoRappresentanza"					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
	           	 		<field-map attributeName="tipoRappresentanzaAltro"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>		           	 	
		           	 	
						<field-map attributeName="datiBeneficiarioPersonaGiuridicaErr" length="88">
							<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiBeneficiarioPersonaGiuridica" iterations="0" blockLength="88">
								<field-map attributeName="flagPaesiBlackList"					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
								<field-map attributeName="flagPresenzaTitolariEffettivi"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				           		<field-map attributeName="elencoTitolariEffettivi" length="80">
				           			<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.TitolareEffettivo" iterations="4" blockLength="20">
				           				<field-map attributeName="codiceCustomerTitEff"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
										<field-map attributeName="nominativoTitEff"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
										<field-map attributeName="luogoDiNascitaTitEff"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
										<field-map attributeName="dataDiNascitaTitEff"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
										<field-map attributeName="relazioneContraenteTitEff"    length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
				           			</nested-mapping>
				           		</field-map>
							</nested-mapping>
						</field-map>			           	 	   	 
						
						<field-map attributeName="cfConiuge"   				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coniugeACarico"     		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<!-- -248 -->
						<field-map attributeName="modPagamento"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>	
						<field-map attributeName="coordinateSiglaInternaz"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateNumControllo"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateCin"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateAbi"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateCab"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateContoCorrente"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="intestatario"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="causale"					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="tipoReimpiego"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="impReimpiego"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="integrazione"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="tipoRapporto"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>		
						<field-map attributeName="stessoContraente" 		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="elencoRapportiReimpiegoErr" length="160">
							<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.RapportiReimpiegoErrDTO" iterations="20" blockLength="8">
						 		<field-map attributeName="numeroRapportoReimpiegoErr"  length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						 		<field-map attributeName="importoRapportoReimpiegoErr" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						 	</nested-mapping>
						</field-map>
						<field-map attributeName="swift" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="numSottorubrica"     		              length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="motivo"     		                      length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="flgCointestazione"     		          length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="cointestatario"     		              length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="relazioneContraenteCointestatario"      length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="relazioneContraenteCointestatarioAltro" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		           	 	<field-map attributeName="paese"     		                      length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<!-- -60 -->
						<field-map attributeName="impLiq"						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="numQuiet"						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="invioDatiCollettore"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="rendita"						length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="tipoRendita"					length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="frazionamento"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="modPagamentoRend"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateSiglaInternazRend"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateNumControlloRend"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateCinRend"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateAbiRend"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateCabRend"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="coordinateContoCorrenteRend"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="intestatarioRend"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						<field-map attributeName="invioDatiCollettorePagRend"	length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					</nested-mapping>
				</field-map>
		</output-mapping>
	</rule>
</rules>
    