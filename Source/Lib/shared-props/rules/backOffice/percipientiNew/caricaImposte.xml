<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-IMPOSTE</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE415</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteRequestDTO">
			<!-- Totali 153 car (150 + 3) -->
			<field-map attributeName="imposteDTO" length="3" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteDTO" iterations="0" blockLength="3">
					<!-- 3 car -->
				  	<field-map attributeName="tipoPercipiente"	length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
		</input-mapping>
			
		<output-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteResponseDTO">
		    <!-- Totali 3985 car (150 + 2011 + 1824) --> 
		    <!-- 2011 ( 3 input + 2008 output) car -->
		    <!-- Campi di input 3 car -->
			<field-map attributeName="imposteDTO" length="3" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteDTO" iterations="0" blockLength="3">
					<!-- 3 car -->
				  	<field-map attributeName="tipoPercipiente"	length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				</nested-mapping>
			</field-map>
          
          	<!-- Elenco Valori -->
			<!-- capi specifici di output: 2008 car -->
            <field-map attributeName="imposteDTO" length="2008">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteDTO" iterations="0" blockLength="804">
					<!-- 804 -->
					<field-map attributeName="numElementiImposteRedditi" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriImposteRedditi"   	 length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="imposteRedditi" length="800" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="4" blockLength="200">
							<field-map attributeName="tipoImposta"      		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneImposta"   	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneLungaImposta"  length="135" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="imponibile"   	    	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="imposta"   	    		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="aliquota"   	    		length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
					
					<!-- 1204 car -->		    
					<field-map attributeName="numElementiAltreImposte" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriAltreImposte"     length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="altreImposte" length="1200" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="6" blockLength="200">
							<field-map attributeName="tipoImposta"      		length="2"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneImposta"   	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneLungaImposta"  length="135" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="imponibile"   	    	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="imposta"   	    		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="aliquota"   	    		length="5"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="2" numDecimali="2" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    