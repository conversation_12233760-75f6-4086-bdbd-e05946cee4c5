<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>REGISTRA-PERCIPIENTE-NEW-SINISTRI</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE316</program>
	 	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>
	    <logApp>true</logApp>
	    <logAppServDesc>REGISTRA PERCIPIENTE SINISTRI</logAppServDesc>
	    <areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.PercipientiNewRequestDTO">
		    
			<!-- Dati imput 26 + 8314 + 91 + 34 + 3 + 1 = 8469-->
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFipPip"           	   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="rigaSel"           	   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="elencoPercipienti" length="8314">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipienti" iterations="0" blockLength="8314">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>		    
					<field-map attributeName="percipienti" length="8310">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.UnPercipiente" iterations="30" blockLength="277">
			           		<field-map attributeName="nominativo"     		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="luogoNascita"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="dataNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Data"/>
			           		<field-map attributeName="percentuale"    		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			           		<field-map attributeName="liquidato"      		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="rendita"    	  		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="scelta"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="nome"            		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="fisGiu"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="modPag"          		length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="impSudd"         		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="progrTst075"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progressivo"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codAnag"         		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                   	<field-map attributeName="modificabile"     	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		                    <field-map attributeName="flagAnagraf"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagContoc"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagAntiric"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="percip"     	    	length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="blocco"           	length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="liquid"      			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="numQuiet"      		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="progrTst080"      	length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="invioDatiCollettore"	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="tipologia"     	    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="soggettoTerzo"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			<field-map attributeName="datiRendita" length="91">				
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiRendita" iterations="0" blockLength="91">
									<field-map attributeName="tipoRendita"        				length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					           		<field-map attributeName="frazionamento"      			    length="2"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					           	 	
					           	 	<field-map attributeName="impLiqRendita"    	 	    	length="14"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="numAnniRenditaCerta"     	        length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="percReversibilita"     	        length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					           	 	<field-map attributeName="renditaAnnua"     				length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="renditaATassoZero"     	        length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="primaRata"     		            length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
									<field-map attributeName="dataPagamento"     				length="10"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Data"/>
									<field-map attributeName="premioPuro"     					length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           	 				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>								
						</nested-mapping>
			</field-map>
			
			
			<field-map attributeName="datiLiquidatoInput" length="34">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiLiquidato" iterations="0" blockLength="34">
							<field-map attributeName="numQuietanza" length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									valida="true" natura="Numerico"  numInteri="6" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="datiPercErr.numQuiet" nomeKeyERR="erroriPercipientiNew"/>
							<field-map attributeName="impLiqCapitSI"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="impConvRendSI"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamentoRendita" length="3">				
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="3">
							<field-map attributeName="modPagamento"        	 length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							
					</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamento" length="1">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="1">
							<field-map attributeName="invioDatiCollettore"   length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
			</field-map>
			
		</input-mapping>
		<output-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.PercipientiNewResponseDTO">
		<!-- Dati Totali 8469 + 16 = 8485 -->
		<!-- Output = 16  -->
		
			<field-map attributeName="categoria"               length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="agenziaPolizza"          length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="numeroPolizza"           length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFipPip"           	   length="1"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="rigaSel"           	   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="flFacta"     		       length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
		    <field-map attributeName="elencoPercipienti" length="8314">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ElencoPercipienti" iterations="0" blockLength="8314">            
					<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N"    offset="" padding=""/>		    
					<field-map attributeName="percipienti" length="8310">
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.UnPercipiente" iterations="30" blockLength="277">
			           		<field-map attributeName="nominativo"     		length="100" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="luogoNascita"   		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="dataNascita"    		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Data"/>
			           		<field-map attributeName="percentuale"    		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="3" numDecimali="2" separatoreMigliaia="false"/>
			           		<field-map attributeName="liquidato"      		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="rendita"    	  		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="scelta"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="nome"            		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="fisGiu"          		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="modPag"          		length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			           		<field-map attributeName="impSudd"         		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           			natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
			           		<field-map attributeName="progrTst075"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="progressivo"     		length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="codAnag"         		length="9"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                   	<field-map attributeName="modificabile"     	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		                    <field-map attributeName="flagAnagraf"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagContoc"     		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flagAntiric"   		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="percip"     	    	length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="blocco"           	length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="liquid"      			length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="numQuiet"      		length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="progrTst080"      	length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="invioDatiCollettore"	length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="tipologia"     	    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="soggettoTerzo"     	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
						<field-map attributeName="datiRendita" length="91">				
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiRendita" iterations="0" blockLength="91">
									<field-map attributeName="tipoRendita"        				length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					           		<field-map attributeName="frazionamento"      			    length="2"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
					           	 	
					           	 	<field-map attributeName="impLiqRendita"    	 	    	length="14"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="numAnniRenditaCerta"     	        length="2"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="percReversibilita"     	        length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					           	 	<field-map attributeName="renditaAnnua"     				length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="renditaATassoZero"     	        length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
					           	 		natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					           	 	<field-map attributeName="primaRata"     		            length="14"  precision="0" numericScale="0"  align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
									<field-map attributeName="dataPagamento"     				length="10"   precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
										natura="Data"/>
									<field-map attributeName="premioPuro"     					length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			           	 				natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>																		
						</nested-mapping>
			</field-map>
			<field-map attributeName="datiLiquidatoInput" length="34">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiLiquidato" iterations="0" blockLength="34">
							<field-map attributeName="numQuietanza" length="6"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									valida="true" natura="Numerico"  numInteri="6" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="datiPercErr.numQuiet" nomeKeyERR="erroriPercipientiNew"/>
							<field-map attributeName="impLiqCapitSI"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="impConvRendSI"     	length="14"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
									natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
					</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamentoRendita" length="3">				
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="3">
							<field-map attributeName="modPagamento"        	 length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
							
					</nested-mapping>
			</field-map>
			<field-map attributeName="datiPagamento" length="1">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPagamento" iterations="0" blockLength="1">
							<field-map attributeName="invioDatiCollettore"   length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					</nested-mapping>
			</field-map>
			<!-- Dati output 16-->
             <field-map attributeName="datiErr" length="16">
					<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.DatiPercipientiErr" iterations="0" blockLength="16">				         
						 <field-map attributeName="numQuiet"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						 <field-map attributeName="invioDatiCollettore"		length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						 <field-map attributeName="tipoRendita"				length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
						 <field-map attributeName="frazionamento"			length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
					</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    