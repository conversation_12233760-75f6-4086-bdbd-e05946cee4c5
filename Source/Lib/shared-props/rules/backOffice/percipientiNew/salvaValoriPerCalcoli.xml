<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-VALORI-PER-CALCOLI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE431</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>SALVA VALORI PER CALCOLI</logAppServDesc>
	    <areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>
	    
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	 
	   	<!-- Campi di input 25807 car totali -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteRequestDTO">
		  	<!-- 25807 car -->
            <field-map attributeName="imposteDTO" length="25807" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteDTO" iterations="0" blockLength="25804">
					<field-map attributeName="tipoPercipiente" 				length="3" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="000"  offset="" padding=""/>
					<field-map attributeName="numElementiValoriPerCalcoli"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriValoriPerCalcoli"		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<!-- Elenco Valori -->
					<field-map attributeName="valoriPerCalcoli" length="25800" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="120" blockLength="215">
				  			<field-map attributeName="numeroImposta"        length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="progressivo"          length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="tipoImposta"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneImposta"   length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreTesto"    		length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreData"           length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false" />
							<field-map attributeName="valoreImporto"   	    length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreNumero"  		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="valoreTasso"   	    length="8"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="4" separatoreMigliaia="false"/>
							<field-map attributeName="valorePercentuale"    length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
								
							<field-map attributeName="valoreTestoNew"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreDataNew"        length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false" />
							<field-map attributeName="valoreImportoNew"   	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreNumeroNew"  	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="valoreTassoNew"   	length="8"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="4" separatoreMigliaia="false"/>
							<field-map attributeName="valorePercentualeNew" length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteResponseDTO">
		    <!-- Totali 28261 car = 150 + 26287 [25807 + 480] + 1824 --> 
		    
		    <!-- stessi campi di input: 25807 -->
			<field-map attributeName="imposteDTO" length="25807" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImposteDTO" iterations="0" blockLength="25804">
					<field-map attributeName="tipoPercipiente" 				length="3" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="000"  offset="" padding=""/>
					<field-map attributeName="numElementiValoriPerCalcoli"	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flAltriValoriPerCalcoli"		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<!-- Elenco Valori -->
					<field-map attributeName="valoriPerCalcoli" length="25800" >
						<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaDTO" iterations="120" blockLength="215">
				  			<field-map attributeName="numeroImposta"        length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="progressivo"          length="4"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
							<field-map attributeName="tipoImposta"      	length="1"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="descrizioneImposta"   length="40"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreTesto"    		length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreData"           length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false" />
							<field-map attributeName="valoreImporto"   	    length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreNumero"  		length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="valoreTasso"   	    length="8"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="4" separatoreMigliaia="false"/>
							<field-map attributeName="valorePercentuale"    length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
								
							<field-map attributeName="valoreTestoNew"    	length="30"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="valoreDataNew"        length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" valida="false" />
							<field-map attributeName="valoreImportoNew"   	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
							<field-map attributeName="valoreNumeroNew"  	length="14"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="8" numDecimali="3" separatoreMigliaia="true"/>
							<field-map attributeName="valoreTassoNew"   	length="8"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="4" separatoreMigliaia="false"/>
							<field-map attributeName="valorePercentualeNew" length="7"   precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico" valida="false"  segnato="false"  numInteri="3" numDecimali="3" separatoreMigliaia="false"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
			<!-- campi specifici di output: 480   -->
          	<field-map attributeName="valoriPerCalcoliErr"	length="480">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ListaErrori" iterations="120" blockLength="4">
					<field-map attributeName="valoreNewErr"  length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    