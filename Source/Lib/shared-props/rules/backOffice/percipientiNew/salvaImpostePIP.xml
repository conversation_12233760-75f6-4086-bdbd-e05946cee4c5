<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>SALVA-IMPOSTE-PIP</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE497</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
		<logApp>true</logApp>
	    <logAppServDesc>SALVA VALORI PER CALCOLI</logAppServDesc>
	    <areaFunzionale>APRI POLIZZA-STORNI</areaFunzionale>
	    
	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   	 
	   	<!-- Campi di input 2893 car totali -->
		<input-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostePIPRequestDTO" nomeKeyERR="erroriImpostePIP">
		  	<!-- 2888 car -->
           	<field-map attributeName="funzione"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           	<field-map attributeName="tipoPercipiente"    	length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numElementiTrovati" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  	length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="elencoImpostePIP" length="2880" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaPIPDTO" iterations="30" blockLength="96">
					<field-map attributeName="tipoImposta"     					length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneImposta"   			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoAl31122000Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoAl31122000"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoDal01012001Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoDal01012001"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoDal01012007Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoDal01012007"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoTotaleProtetto" 		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoTotale"  					length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="t075Progressivo" length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostePIPResponseDTO">
		    <!-- Totali 5417 car = 150 + 3447 [2893 + 555] + 1824 --> 
		    <!-- stessi campi di input: 2888 -->
			<field-map attributeName="funzione"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
           	<field-map attributeName="tipoPercipiente"    	length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numElementiTrovati" 	length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  	length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
			<field-map attributeName="elencoImpostePIP" length="2880" >
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ImpostaPIPDTO" iterations="30" blockLength="96">
					<field-map attributeName="tipoImposta"     					length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="descrizioneImposta"   			length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoAl31122000Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoAl31122000"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoDal01012001Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoDal01012001"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoDal01012007Protetto" 	length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoDal01012007"  				length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flgImportoTotaleProtetto" 		length="1"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="importoTotale"  					length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="t075Progressivo" 						length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<!-- campi specifici di output: 555 -->
			<field-map attributeName="totaleImposteRedditiCap"  			length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totaleAltreImposte"  					length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totaleImpostaSostitutivaRedditiCap"  	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totaleImposteRedditiCapVisentini"  	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		    <field-map attributeName="totaleLiquidato"  				 	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
          	<field-map attributeName="erroriDatiImpostePIP"	length="480">
				<nested-mapping className="it.sistinf.albedoweb.backoffice.percipienti.dto.ErroriDatiImpostePIPDTO" iterations="30" blockLength="16">
					<field-map attributeName="importoAl31122000CdErr"  	length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="importoDal01012001CdErr"  length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="importoDal01012007CdErr"  length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="importoTotaleCdErr"  		length="04"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    