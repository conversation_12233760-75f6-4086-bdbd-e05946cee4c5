<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-TESTE-COLLETTIVA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE215</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.collettiva.proposta.teste.dto.ElencoTesteCollettivaRequestDTO">
			<!-- Totali 245 car (150 + 95) -->
			<!-- 95 car -->
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaGest"     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <!-- IL FLAG PROP-POLI INDICA SE PROPOSTA (=1) O POLIZZA (=2) -->
            <field-map attributeName="flPropPoli"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <!-- FILTRI AGG -->
            <field-map attributeName="numeroTesta"     length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="cognome"     	   length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="nome"      	   length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataNascita"     length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="statoTesta"      length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.collettiva.proposta.teste.dto.ElencoTesteCollettivaResponseDTO">
		    <!-- Totali 26873 car (150 + 24899 + 1824) -->  
			<!-- Output 24804 -->
			<!-- input 95 car -->
			<field-map attributeName="categoria"       length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="agenziaGest"     length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="numeroColl"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <!-- IL FLAG PROP-POLI INDICA SE PROPOSTA (=1) O POLIZZA (=2) -->
            <field-map attributeName="flPropPoli"      length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <!-- FILTRI AGG -->
            <field-map attributeName="numeroTesta"     length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
            <field-map attributeName="cognome"     	   length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="nome"      	   length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="dataNascita"     length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            <field-map attributeName="statoTesta"      length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            			
		    <field-map attributeName="elencoTesteCollettiva" length="24804">
				<nested-mapping className="it.sistinf.albedoweb.collettiva.proposta.teste.dto.ElencoTesteCollettivaDTO" iterations="0" blockLength="24804">            
					<field-map attributeName="numElementiTrovati"   length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="flLimite"     		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="N" offset="" padding=""/>		    
					<field-map attributeName="dettaglioTestaCollettiva" length="24800">
						<nested-mapping className="it.sistinf.albedoweb.collettiva.proposta.teste.dto.DettaglioTestaCollettivaDTO" iterations="200" blockLength="124">
							<field-map attributeName="numeroTesta"     	length="7"   precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>		    
							<field-map attributeName="categoria"  		length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            				<field-map attributeName="nominativo"      	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
            				<field-map attributeName="sesso"  		    length="1"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataNascita"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="luogo"      		length="35"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="dataDecorrenza"	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="dataScadenza"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Data" />
							<field-map attributeName="stato"  	        length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="statoPolizza"  	length="3"   precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>							
							<field-map attributeName="prestazione" 		length="14"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""
								natura="Numerico"  segnato="false"  numInteri="9" numDecimali="2" separatoreMigliaia="true"/>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>	
					
		</output-mapping>
	</rule>
</rules>
    