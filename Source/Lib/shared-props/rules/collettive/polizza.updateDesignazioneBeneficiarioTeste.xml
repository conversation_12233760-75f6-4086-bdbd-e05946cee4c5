<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>UPDATE-DESIGNAZIONE-BENEFICIARIO-TESTE</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE654</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	    	
	   
		<!-- Totale 15 car -->
		
	   	<!-- Lunghezza input = 15 -->
		<input-mapping className="it.sistinf.albedoweb.collettiva.polizza.dto.DesignazioneBeneficiarioRequestDTO">
			<!-- Lunghezza designazioneBeneficiario = 15 car-->
		    <field-map attributeName="designazioneBeneficiario" length="15">
				<nested-mapping className="it.sistinf.albedoweb.collettiva.polizza.dto.DesignazioneBeneficiario" iterations="0" blockLength="15">
					<!-- 15 car -->		
					<field-map attributeName="categoria"			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"				length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"				length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>					
		</input-mapping>
		
		<!-- Lunghezza output = 15 -->		
		<output-mapping className="it.sistinf.albedoweb.collettiva.polizza.dto.DesignazioneBeneficiarioResponseDTO">	
			<!-- Lunghezza designazioneBeneficiario = 15 car-->
		    <field-map attributeName="designazioneBeneficiario" length="15">
				<nested-mapping className="it.sistinf.albedoweb.collettiva.polizza.dto.DesignazioneBeneficiario" iterations="0" blockLength="15">
					<!-- 15 car -->		
					<field-map attributeName="categoria"			length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"				length="06" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"				length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				</nested-mapping>
			</field-map>	
		</output-mapping>
	</rule>
</rules>
