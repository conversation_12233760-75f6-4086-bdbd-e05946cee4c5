<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>NUOVA-PROPOSTA-COLLETTIVA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE573</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.collettiva.proposta.dto.CollettivaPropostaRequestDTO">
		<!-- Totali 165 car (150 + 15) -->
			<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="username" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.collettiva.proposta.dto.CollettivaPropostaResponseDTO">
		<!-- Totali 1999 car (150 + 25 + 1824) -->		
		<!-- campo aggiunto in quanto ne serve almeno uno nella parte applicativa -->
			<field-map attributeName="username" 	length="15" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataProposta" length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
			 natura="Data" />
		</output-mapping>
	</rule>
</rules>