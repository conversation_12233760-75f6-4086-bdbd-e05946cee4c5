<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>PRODOTTO-SELEZIONATO-PER-COLLETTIVA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE574</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
		<input-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoSelezionatoRequestDTO">
		<!-- Totali 363 car (150 + 213) -->		
			<!-- 64 + 149 = 213 -->
			<field-map attributeName="prodotto" length="64">
				<nested-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoDTO" iterations="0" blockLength="64">
					<field-map attributeName="compagnia" 		length="3" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceProdotto" 	length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoOldProdotto"  length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codAgenzia"     length="6"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
  			<field-map attributeName="descAgenzia" 	  length="40"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	<field-map attributeName="codSubagenzia"  length="6"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descSubagenzia" length="40"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codPromotore"   length="7"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descPromotore"  length="40"	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataProposta"   length="10" 	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		 		natura="Data" />
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoSelezionatoResponseDTO">
		<!-- Totali 2220 car (150 + 246 + 1824) -->		
			<!-- 219 + 11 + 16 = 246 -->
			
			<!-- 64 + 155 = 219 -->
			<field-map attributeName="prodotto" length="64">
				<nested-mapping className="it.sistinf.albedoweb.prodotto.dto.ProdottoDTO" iterations="0" blockLength="64">
					<field-map attributeName="compagnia" 		length="3" 	precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codice"  	      	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceProdotto" 	length="10" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="descrizione"    	length="40" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoOldProdotto"  length="1" 	precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="codAgenzia"     length="6"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
  			<field-map attributeName="descAgenzia" 	  length="40"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
           	<field-map attributeName="codSubagenzia"  length="6"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descSubagenzia" length="40"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codPromotore"   length="7"  	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descPromotore"  length="40"	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataProposta"   length="10" 	precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=""
		 		natura="Data" />
			
			<field-map attributeName="flNewCommarea" 		length="1" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="numCategoria"  		length="2" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="flNumeroNuovo"		length="1" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		   	<field-map attributeName="numeroProposta"		length="7" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		   	<field-map attributeName="agenziaNumProposta"   length="6" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		
			<field-map attributeName="codAgenziaCdErr"	  	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codSubagenziaCdErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="codPromotoreCdErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataPropostaCdErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
		</output-mapping>
	</rule>
</rules>