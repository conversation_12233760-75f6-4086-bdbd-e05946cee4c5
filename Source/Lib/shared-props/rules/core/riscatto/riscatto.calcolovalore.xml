<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>RISCATTO-CALCOLO-VALORE</id> 
  
  	<initialProgram>WNDISPC1</initialProgram>
	<initialTransaction>SBEX</initialTransaction>
	<program>VWLSE557</program>
	<transaction>SBEX</transaction>
	<connectorId>A05TAREEX</connectorId>
  

  	<logApp>true</logApp>
	<logAppServDesc>CORE RISCATTO</logAppServDesc>
	<areaFunzionale>CORE RISCATTO CALCOLO VALORE</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: VWLSE557 Lunghezza: 150 + 144 + 1824 = 2118 -->  
  		<input-mapping className="it.sistinf.albedoweb.core.riscatto.dto.ValoreRiscattoRequestDTO" nomeKeyERR="erroriCalcoloValoreRiscatto"> 
  		 <!-- input : 88 car --> 
	    	
	    	<field-map attributeName="inputRequest" length="88" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.riscatto.dto.InputRequestRiscatto">  
	    	
			    	<field-map attributeName="numeroCategoria" 			length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/> 
			    	<field-map attributeName="agenzia"   				length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="numeroCollettiva" 		length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>	
			    	<field-map attributeName="numeroPolizza"   			length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			    	
			    	<field-map attributeName="flagUnitLinked"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flagIndex"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flagRivalutabili"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagMultiramo"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagPipUnitLinked" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagPipGarantito" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagPipMultiramo" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="metodoRiscatto"   		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoRiscattoParziale" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRichiestaRiscatto"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataDisinvestimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagAnticipazione" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="causalePagamento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importoRiscatto"    		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
					<field-map attributeName="percentualeRiscatto"     	length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="3" numDecimali="7" separatoreMigliaia="false" />
			    	
				</nested-mapping>
			</field-map>
	    	
 	 	</input-mapping> 
		<output-mapping className="it.sistinf.albedoweb.core.riscatto.dto.ValoreRiscattoResponseDTO"> 
	    <!-- output : 144 (88 + 56) car -->   
	   
	  		<field-map attributeName="inputRequest" length="88" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.riscatto.dto.InputRequestRiscatto">  
	    	
			    	<field-map attributeName="numeroCategoria" 			length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			    	<field-map attributeName="agenzia"   				length="6"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="numeroCollettiva" 		length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			    	<field-map attributeName="numeroPolizza"   			length="7"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	
			    	<field-map attributeName="flagUnitLinked"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flagIndex"   				length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="flagRivalutabili"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flagMultiramo"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flagPipUnitLinked" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flagPipGarantito" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flagPipMultiramo" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="metodoRiscatto"   		length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="tipoRiscattoParziale" 	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataRichiestaRiscatto"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="dataDisinvestimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="flagAnticipazione" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="causalePagamento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="importoRiscatto"    		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
					<field-map attributeName="percentualeRiscatto"     	length="11"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="3" numDecimali="7" separatoreMigliaia="false" />
			    	
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="valoreRiscatto"    		length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			
			<field-map attributeName="importoLordoOperazione"   length="14"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			
			
			<field-map attributeName="metodoRiscattoErr"   		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="tipoRiscattoParzialeErr"  length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataRichiestaRiscattoErr" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="dataDisinvestimentoErr" 	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="causalePagamentoErr" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="importoRiscattoErr" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="percentualeRiscattoErr" 	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		       
		</output-mapping> 
 </rule> 
</rules> 