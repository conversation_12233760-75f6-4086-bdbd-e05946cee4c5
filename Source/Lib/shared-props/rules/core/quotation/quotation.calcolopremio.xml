<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>QUOTATION-CALCOLO-PREMIO</id> 
  
  	<initialProgram>WNDISPC1</initialProgram>
	<initialTransaction>SBEX</initialTransaction>
	<program>VWLSE533</program>
	<transaction>SBEX</transaction>
	<connectorId>A05TAREEX</connectorId>
  

  	<logApp>true</logApp>
	<logAppServDesc>CORE QUOTATION</logAppServDesc>
	<areaFunzionale>CORE QUOTATION CALCOLO PREMIO</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: VWLSE533 Lunghezza: 150 + 5645 + 1824 = 7619 -->  
  		<input-mapping className="it.sistinf.albedoweb.core.quotation.dto.CalcoloPremioRequestDTO" nomeKeyERR="erroriCalcoloPremio"> 
  		 <!-- input : 634 car --> 
	    	
	    	<field-map attributeName="inputRequest" length="634" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	<field-map attributeName="userAlbedo" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			    	<field-map attributeName="livello1"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="livello2"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			    	<field-map attributeName="livello3"   		length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProdotto"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoPrimaRata" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoRateSuccessive" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	
			    	<field-map attributeName="multigaranzia" length="115" >
				    	<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Multigaranzia">
					    	<field-map attributeName="opzioneGestionale" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="profiloInvestimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="codice1" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita1"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice2" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita2"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice3" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita3"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice4" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita4"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice5" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita5"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="classePremio" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	</nested-mapping>
			    	</field-map>
			    	
			    	
			    	<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="premioRataComplessivo"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
					
					<field-map attributeName="elementiUT" length="420" > 
					
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="42">            
									<field-map attributeName="codiceUT"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"         length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="prestazioneInput"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="frazionamentoRendita"	length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
								    <field-map attributeName="durata"				length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						</nested-mapping>
				
					</field-map>
					
				</nested-mapping>
			</field-map>
	    	
 	 	</input-mapping> 
		<output-mapping className="it.sistinf.albedoweb.core.quotation.dto.CalcoloPremioResponseDTO"> 
	    <!-- output : 5645 (634 + 5011) car -->   
	   
	   <field-map attributeName="inputRequest" length="634" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	<field-map attributeName="userAlbedo" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			    	<field-map attributeName="livello1"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="livello2"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			    	<field-map attributeName="livello3"   		length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProdotto"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoPrimaRata" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoRateSuccessive" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			    	<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	
			    	<field-map attributeName="multigaranzia" length="115" >
				    	<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Multigaranzia">
					    	<field-map attributeName="opzioneGestionale" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="profiloInvestimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="codice1" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita1"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice2" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita2"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice3" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita3"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice4" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita4"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice5" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita5"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="classePremio" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	</nested-mapping>
			    	</field-map>
			    	
			    	<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="premioRataComplessivo"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
					
					<field-map attributeName="elementiUT" length="420" > 
					
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="28">            
									<field-map attributeName="codiceUT"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="prestazioneInput"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="frazionamentoRendita"				length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="durata"				length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
						</nested-mapping>
				
					</field-map>
					
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="premioAnnuo" length="126" > 
			
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
						    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
				</nested-mapping>
		
			</field-map>
			
			<field-map attributeName="premioRata" length="126" > 
			
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
						    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />	
				</nested-mapping>
		
			</field-map>
			
			<field-map attributeName="premioFirma" length="126" > 
			
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
						    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
						    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
				</nested-mapping>
		
			</field-map>
			
			<field-map attributeName="numeroElementi"   	length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
	    	<field-map attributeName="flagLimite"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			
			<field-map attributeName="unitaTecniche" length="4320" > 
			
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.UnitaTecnica" iterations="10" blockLength="432"> 
				
				<field-map attributeName="unitaTecnica"   		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
				<field-map attributeName="descrizione"   		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			
				<field-map attributeName="dettaglioPremioAnnuo" length="126" > 
					<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
							    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					</nested-mapping>
				</field-map>
				
				<field-map attributeName="dettaglioPremioRata" length="126" > 
					<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
							    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					</nested-mapping>
				</field-map>
				
				<field-map attributeName="dettaglioPremioFirma" length="126" > 
					<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.DettaglioPremio">           
							    <field-map attributeName="premioTotaleAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioLordoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="premioNettoAnnuo"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiAcquisto"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiIncasso"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="caricamentiGestione"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="imposte"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="diritti"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    	natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
							    <field-map attributeName="interessiFrazionamento"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    		natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
					</nested-mapping>
				</field-map>
				
				<field-map attributeName="prestazione"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
					natura="Numerico" segnato="false" numInteri="9" numDecimali="2" separatoreMigliaia="true" />
				
				</nested-mapping>
		
			</field-map>
			
			 <field-map attributeName="inputRequestError" length="308" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	
			    	<field-map attributeName="codiceProdotto"   length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoPrimaRata" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoRateSuccessive" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="convenzione" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="multigaranzia" length="56" >
				    	<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Multigaranzia">
					    	<field-map attributeName="opzioneGestionale" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="profiloInvestimento" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="codice1" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita1"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice2" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita2"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice3" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita3"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice4" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita4"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice5" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita5"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="classePremio" 			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="totalePercentualeInvestita" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	</nested-mapping>
			    	</field-map>
					
					<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="premioRataComplessivo"	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
					
					<field-map attributeName="elementiUT" length="200" > 
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="20">            
									<field-map attributeName="codiceUT"				length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"			length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="prestazioneInput"		length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="frazionamentoRendita"	length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="durata"	length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						</nested-mapping>
					</field-map>
					
				</nested-mapping>
			</field-map>
			 
		       
		</output-mapping> 
 </rule> 
</rules> 