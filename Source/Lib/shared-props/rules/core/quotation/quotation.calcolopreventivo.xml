<?xml version="1.0" encoding="UTF-8"?> 
<rules> 
 <rule> 
  <id>QUOTATION-CALCOLO-PREVENTIVO</id> 
  
  	<initialProgram>WNDISPC1</initialProgram>
	<initialTransaction>SBEX</initialTransaction>
	<program>VWLSE540</program>
	<transaction>SBEX</transaction>
	<connectorId>A05TAREEX</connectorId>
  

  	<logApp>true</logApp>
	<logAppServDesc>CORE QUOTATION</logAppServDesc>
	<areaFunzionale>CORE QUOTATION CALCOLO PREVENTIVO</areaFunzionale>
	
     <multipleTransaction>true</multipleTransaction> 
     <!-- hasMultipleTransaction>false</hasMultipleTransaction --> 
     <pageRequestField/> 
     <limitPage>99</limitPage> 
     <moreDataField/> 
     <moreDataEndValue>1</moreDataEndValue> 
     <flagFirstTime absolutePosition="0" length="0" secondValue="" />    
     <pastedFields> 
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" /> 
     </pastedFields>      
  <!-- Definizione commarea: VWLSE540 Lunghezza: 150 + 24620 + 1824 = 26594 -->  
  		<input-mapping className="it.sistinf.albedoweb.core.quotation.dto.CalcoloPreventivoRequestDTO" nomeKeyERR="erroriCalcoloPreventivo"> 
  		 <!-- input : 924 car --> 
	    	
	    	<field-map attributeName="inputRequest" length="924" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	<field-map attributeName="userAlbedo" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			    	<field-map attributeName="livello1"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="livello2"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			    	<field-map attributeName="livello3"   		length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProdotto"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoPrimaRata" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="frazionamentoRateSuccessive" length="2"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					
					<field-map attributeName="multigaranzia" length="115" >
				    	<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Multigaranzia">
					    	<field-map attributeName="opzioneGestionale" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="profiloInvestimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="codice1" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita1"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice2" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita2"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice3" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita3"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice4" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita4"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="codice5" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita5"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
									    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							<field-map attributeName="classePremio" 					length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	</nested-mapping>
			    	</field-map>
					
					
					
					<field-map attributeName="nomeContraente" 		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeContraente" 	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	
			    	<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="premioRataComplessivo"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
					
					<field-map attributeName="elementiUT" length="650" > 
					
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="65">            
									<field-map attributeName="codiceUT"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="prestazioneInput"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="frazionamentoRendita"	length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
								    <field-map attributeName="durata"				length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
								    <field-map attributeName="anniDifferimento"		length="2" precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
								    <field-map attributeName="tassoRivalutazione"	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							    	<field-map attributeName="retrocessione"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							    	<field-map attributeName="percentualeCosti"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
						</nested-mapping>
				
					</field-map>
					
				</nested-mapping>
			</field-map>
	    	
 	 	</input-mapping> 
		<output-mapping className="it.sistinf.albedoweb.core.quotation.dto.CalcoloPreventivoResponseDTO"> 
	    <!-- output : 24620 (924 + 23696) car -->   
	   
	  		<field-map attributeName="inputRequest" length="924" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	<field-map attributeName="userAlbedo" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
			    	<field-map attributeName="livello1"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="livello2"   		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>	
			    	<field-map attributeName="livello3"   		length="7"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="codiceProdotto"   length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoPrimaRata" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoRateSuccessive" length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="convenzione" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeContraente" 		length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeContraente" 	length="30"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	
			    	<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita"   	length="10"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
					<field-map attributeName="elementiUT" length="650" > 
					
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="65">            
									<field-map attributeName="codiceUT"				length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"          length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="prestazioneInput"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    <field-map attributeName="frazionamentoRendita"	length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="durata"				length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="anniDifferimento"		length="2" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="tassoRivalutazione"	length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							    	<field-map attributeName="retrocessione"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
							    	<field-map attributeName="percentualeCosti"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
							    		natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
						</nested-mapping>
				
					</field-map>
					
				</nested-mapping>
			</field-map>
			
			<field-map attributeName="numPreventiviRendimentoMinimo" length="3" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="preventiviRendimentoMinimo" length="11200" > 
					
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Preventivo" iterations="100" blockLength="112">            
						    <field-map attributeName="premioVersato"        	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="cumuloPremi"         		length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="capitaleCasoMorte"   		length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="valoreRiscatto" 			length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaRidottaAnnuale"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaRidottaScadenza"   length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaAssicurata"        length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaAssicurataDifferita"        length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
				</nested-mapping>
				
			</field-map>
			
			<field-map attributeName="numPreventiviAlTassoFornito" length="3" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
			
			<field-map attributeName="preventiviAlTassoFornito" length="11200" > 
					
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Preventivo" iterations="100" blockLength="112">            
						    <field-map attributeName="premioVersato"        	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="cumuloPremi"         		length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="capitaleCasoMorte"   		length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="valoreRiscatto" 			length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaRidottaAnnuale"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaRidottaScadenza"   length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaAssicurata"        length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="renditaAssicurataDifferita"        length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
				</nested-mapping>
				
			</field-map>
			
			<field-map attributeName="tasso1"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso2"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso3"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso4"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso5"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso6"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso7"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso8"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso9"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="tasso10"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
						    	
			<field-map attributeName="importo1"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo2"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo3"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo4"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo5"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo6"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo7"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo8"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo9"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			<field-map attributeName="importo10"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
			
			<field-map attributeName="numerico1"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico2"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico3"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico4"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico5"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />	
			<field-map attributeName="numerico6"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico7"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico8"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico9"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
			<field-map attributeName="numerico10"     length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="2" separatoreMigliaia="false" />
						    	
			<field-map attributeName="percentuale1"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="0" offset="" padding="0" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale2"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale3"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale4"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale5"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale6"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale7"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale8"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale9"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />
			<field-map attributeName="percentuale10"        length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="3" numDecimali="3" separatoreMigliaia="false" />	    	
			
			<field-map attributeName="premioAnnuoTotale"     length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
								    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
								    	
			<field-map attributeName="riassuntoPremi" length="420" > 
					
				<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.RiassuntoPremio" iterations="10" blockLength="42">            
						    <field-map attributeName="renditaIniziale"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="premioLordoAnnuo"	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						    <field-map attributeName="premioRata"   	length="14"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" 
						    	natura="Numerico" segnato="false" numInteri="7" numDecimali="3" separatoreMigliaia="true" />
						   
				</nested-mapping>
				
			</field-map>
			
			
			<field-map attributeName="inputRequestError" length="416" >
	    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.InputRequestQuotation">  
	    	
			    	
			    	<field-map attributeName="codiceProdotto"   length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
			    	<field-map attributeName="decorrenza"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="durataAnni"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoPrimaRata" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="frazionamentoRateSuccessive" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="convenzione" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="multigaranzia" length="56" >
				    	<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Multigaranzia">
					    	<field-map attributeName="opzioneGestionale" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="profiloInvestimento" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="codice1" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita1"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice2" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita2"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice3" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita3"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice4" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita4"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="codice5" 					length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					    	<field-map attributeName="percentualeInvestita5"    length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
							<field-map attributeName="classePremio" 			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="totalePercentualeInvestita" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				    	</nested-mapping>
			    	</field-map>
					
					<field-map attributeName="nomeContraente" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeContraente" 	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					
					<field-map attributeName="assicurati" length="24" >
			    		<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.Assicurato" iterations="2" blockLength="12"> 
					    	<field-map attributeName="dataNascita" length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="sesso"   			length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
							<field-map attributeName="flagFumatore"   	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
						</nested-mapping>
					</field-map>
					
						<field-map attributeName="premioRataComplessivo"	length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/> 
					
					<field-map attributeName="elementiUT" length="360" > 
					
						<nested-mapping className="it.sistinf.albedoweb.core.quotation.dto.ElementoUT" iterations="10" blockLength="36">            
									<field-map attributeName="codiceUT"				length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="importoInput"			length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="prestazioneInput"		length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="frazionamentoRendita"	length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="durata"				length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="anniDifferimento"		length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
								    <field-map attributeName="tassoRivalutazione"	length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
							    	<field-map attributeName="retrocessione"        length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
							    	<field-map attributeName="percentualeCosti"     length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
						</nested-mapping>
				
					</field-map>
					
				</nested-mapping>
			</field-map>
			 
		       
		</output-mapping> 
 </rule> 
</rules> 