<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>RICERCA-FLUSSO-COMUNICAZIONE</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLBLMB3</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLBLMB3 Lunghezza: 150 + 24470 + 1824 = 26444  -->  
    <input-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.flussoComunicazione.dto.FlussoComunicazioneRequestDTO" nomeKeyERR="erroriFlussoComunicazione">
	<!-- input : 48 car --> 
		<field-map attributeName="contesto" 			length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/> 
	 	<field-map attributeName="codiceSocieta" 		length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/>
	 	<field-map attributeName="operazioneRicerca"	length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="agenziaRicerca" 		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/> 
	 	<field-map attributeName="polizzaRicerca" 		length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
		<field-map attributeName="posizioneRicerca" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
		<field-map attributeName="agenziaGestRicerca" 	length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="dataRichiestaRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding="" 
			valida="true" natura="Data" nomeAttributoERR="dataRichiestaRicerca" nomeKeyERR="erroriFlussoComunicazione"/> 
		<field-map attributeName="riferimentoRicerca"   length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/> 
		<field-map attributeName="elaRicerca" 			length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="flgRisRicerca" 		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default=" " offset="" padding=""/> 
		 
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.flussoComunicazione.dto.FlussoComunicazioneResponseDTO">
    	<!-- output : 45 + 28616  car =  28661 -->
    	<field-map attributeName="contesto" 			length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/> 
	 	<field-map attributeName="codiceSocieta" 		length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/>
	 	<field-map attributeName="operazioneRicerca"	length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="agenziaRicerca" 		length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/> 
	 	<field-map attributeName="polizzaRicerca" 		length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
		<field-map attributeName="posizioneRicerca" 	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
		<field-map attributeName="agenziaGestRicerca" 	length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="dataRichiestaRicerca" length="10"  precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default=" " offset="" padding="" 
			natura="Data"/> 
		<field-map attributeName="riferimentoRicerca"   length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/> 
		<field-map attributeName="elaRicerca" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
		<field-map attributeName="flgRisRicerca" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/> 
    	
    	<field-map attributeName="numElementiTrovati"  		length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	    length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		
    	<field-map attributeName="elencoFlussoComunicazione"       length="24000" > 	
			<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.flussoComunicazione.dto.FlussoComunicazioneValoriDTO" iterations="400" blockLength="61">            
    	        <field-map attributeName="operazione" 		length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="agenzia" 			length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="polizza" 			length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
    	        <field-map attributeName="posizione" 		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0"/> 
    	        <field-map attributeName="agenziaGest"  	length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="dataRichiesta" 	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding="" 
    	        	natura="Data"/>
    	        <field-map attributeName="riferimento" 		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="ela" 				length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="flgCon" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
    	        <field-map attributeName="flgRis" 			length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
    	        <field-map attributeName="flusso"	 		length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
    	        <field-map attributeName="progressivo"		length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
       
        <field-map attributeName="polizzaRicercaCdErr" 			length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
        <field-map attributeName="posizioneRicercaCdErr" 		length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>  
        <field-map attributeName="dataRichiestaRicercaCdErr"	length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
        
    </output-mapping>
  </rule>
</rules>
