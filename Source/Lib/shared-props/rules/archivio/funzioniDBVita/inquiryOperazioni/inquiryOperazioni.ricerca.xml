<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <id>RICERCA-INQUIRY</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLBLMB2</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
    <multipleTransaction>true</multipleTransaction>
    <pageRequestField/>
    <limitPage>99</limitPage>
    <moreDataField/>
    <moreDataEndValue>1</moreDataEndValue>
    <flagFirstTime absolutePosition="0" length="0" secondValue="" />
    <pastedFields>
      <fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
    </pastedFields>
    <!-- Definizione commarea: VWLBLMB2 Lunghezza: 150 + 44 + 28817 [4 + 1 + 450 * 64 + 4 + 4 + 4]= 30835  -->  
    <input-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.inquiryOperazioni.dto.OperazioneRequestDTO" nomeKeyERR="erroriElencoRicercaInquiryArchivio">
	<!-- input : 44 car --> 
		<field-map attributeName="ag" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
	 	<field-map attributeName="poliz" length="7" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="7" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="poliz" nomeKeyERR="erroriElencoRicercaInquiryArchivio"/>
	 	<field-map attributeName="posiz" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="4" numDecimali="0" separatoreMigliaia="false" valida="true" nomeAttributoERR="posiz" nomeKeyERR="erroriElencoRicercaInquiryArchivio"/>
	 	<field-map attributeName="operaz" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="agGes" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="riferim" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="flgRis" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="el" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="tr" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="datRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" valida="true" natura="Data" nomeAttributoERR="datRich" nomeKeyERR="erroriElencoRicercaInquiryArchivio"/> 
    </input-mapping>
    <output-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.inquiryOperazioni.dto.ListaOperazioneResponseDTO">
        <field-map attributeName="ag" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="poliz" length="7" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="7" numDecimali="0" separatoreMigliaia="false" />
	 	<field-map attributeName="posiz" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="0" offset="" padding="0" natura="Numerico" segnato="false" 
		        			numInteri="4" numDecimali="0" separatoreMigliaia="false" />	 	
		<field-map attributeName="operaz" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="agGes" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="riferim" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="flgRis" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="el" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="tr" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/> 
		<field-map attributeName="datRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/> 
    	<!-- output : 44 (stessi dati input) + 28817  car =  28861 -->
    	<field-map attributeName="nmEleTrovati" length="4" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
    	<field-map attributeName="flagLimite" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	<field-map attributeName="listaOperazioneResponse"       length="28800" > 	
			<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.inquiryOperazioni.dto.OperazioneResponseDTO" iterations="450" blockLength="64">            
    	        <field-map attributeName="operazione" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="agenzia" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="polizza" length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/> 
    	        <field-map attributeName="posizione" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/> 
    	        <field-map attributeName="agenziaGes" length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="dataRich" length="10"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding="" natura="Data"/>
    	        <field-map attributeName="riferimento" length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="ela" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="tra" length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="flgRisul" length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="progressivo" length="9"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
    	        <field-map attributeName="progFlc" length="9"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=" " offset="" padding=""/>
            </nested-mapping>
        </field-map>
       
        <field-map attributeName="eDataRichErr" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eNumPolizza" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>
        <field-map attributeName="eNumPosiz" length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"/>  
    </output-mapping>
  </rule>
</rules>
