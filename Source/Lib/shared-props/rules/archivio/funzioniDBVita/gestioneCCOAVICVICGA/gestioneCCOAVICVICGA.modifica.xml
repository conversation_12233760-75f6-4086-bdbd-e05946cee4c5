<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-GESTIONE-CCOAVICVICGA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE275</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
	    <logApp>true</logApp>
	    <logAppServDesc>SALVA GESTIONE CCOAVICVICGA</logAppServDesc>
	    <areaFunzionale>GEST FUNZIONALITA AUSILIARIE-FUNZIONI AUSILIARIE</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLSE275 Lunghezza: 150 + 27304 + 1824 = 29278  -->        
		<input-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGARequestDTO" nomeKeyERR="erroriGestioneCCOAVICVICGA">
			<!-- input : 150 + 24854 = 25004 car -->
            <field-map attributeName="categoriaRicerca"    length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=""  offset="" padding=""/>
            <field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="000"  offset="" padding="0"/>					   
			<field-map attributeName="elencoGestioneCCOAVICVICGA"     length="24850">
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAValori" iterations="350" blockLength="71">
					<field-map attributeName="flRigaModificata"	 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="categoriaNum"      length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="operazione"   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="dataRich"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="agenzia"		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="polizza"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="contraente"   length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="errore"       length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="elaborato"    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="rifiutato"    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="progressivo"  length="9"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAResponseDTO">
		    <!-- output :27654 car -->  
			<field-map attributeName="categoriaRicerca"    length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default=""  offset="" padding=""/>
			<field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="000"  offset="" padding="0"/>
			<field-map attributeName="elencoGestioneCCOAVICVICGA"     length="24850">
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAValori" iterations="350" blockLength="71">
					<field-map attributeName="flRigaModificata"	 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="categoriaNum"      length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="operazione"   length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="dataRich"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="agenzia"		length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="polizza"      length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
						natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="contraente"   length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="errore"       length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="elaborato"    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="rifiutato"    length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
					<field-map attributeName="progressivo"  length="9"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="elencoErroriGestioneCCOAVICVICGA"     length="2800">
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAErrori" iterations="350" blockLength="8">
					<field-map attributeName="elaboratoErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
					<field-map attributeName="rifiutatoErr"  length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="0000" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    