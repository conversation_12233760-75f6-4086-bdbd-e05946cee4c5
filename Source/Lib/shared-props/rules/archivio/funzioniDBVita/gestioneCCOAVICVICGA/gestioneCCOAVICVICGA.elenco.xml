<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-GESTIONE-CCOAVICVICGA</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLBLMB1</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLBLMB1 Lunghezza: 150 + 24897 + 1824 = 26871  -->        
		<input-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGARequestDTO" nomeKeyERR="erroriGestioneCCOAVICVICGA">
			<!-- input : 27 car -->
            <field-map attributeName="categoriaRicerca"		length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="operazioneRicerca"    length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="dataRichRicerca"    	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
            	valida="true" natura="Data"  nomeAttributoERR="dataRichRicerca" nomeKeyERR="erroriGestioneCCOAVICVICGA"/>
            <field-map attributeName="agenziaRicerca"       length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="polizzaRicerca"   	length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=""  offset="" padding=""
            	valida="true" natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="polizzaRicerca" nomeKeyERR="erroriGestioneCCOAVICVICGA"/>            
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAResponseDTO">
		    <!-- output :27 + 24870 = 24897 car -->  
            <field-map attributeName="categoriaRicerca"				length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="operazioneRicerca"    		length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="dataRichRicerca"    			length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""
            	natura="Data"/>
            <field-map attributeName="agenziaRicerca"       		length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="0" default="" offset="" padding=""/>
            <field-map attributeName="polizzaRicerca"   			length="7"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="0" default=""  offset="" padding=""           
				natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false"/>                  
			<field-map attributeName="numElementiTrovati" 			length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="0" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   			length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoGestioneCCOAVICVICGA"     length="24850">
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.gestioneCCOAVICVICGA.dto.GestioneCCOAVICVICGAValori" iterations="350" blockLength="71">
					<field-map attributeName="flRigaModificata"	 	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="categoriaNum" 		length="2"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="false"  numInteri="2" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="operazione"   		length="3"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="dataRich"     		length="10"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="agenzia"				length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="polizza"     	 		length="7"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false"/>
					<field-map attributeName="contraente"   		length="25"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="errore"       		length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="elaborato"    		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="rifiutato"    		length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="0" default="N" offset="" padding=""/>
					<field-map attributeName="progressivo" 			length="9"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="0" default="0" offset="" padding="0"
						natura="Numerico"  segnato="true"  numInteri="9" numDecimali="0" separatoreMigliaia="false"/>
				</nested-mapping>
			</field-map>
			<!-- 16 -->
			<field-map attributeName="operazioneRicCdErr"	length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>			
			<field-map attributeName="dataRichRicCdErr"		length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="agenziaRicCdErr"		length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
			<field-map attributeName="polizzaRicCdErr"		length="4"  precision="0" numericScale="0" align="right"  mandatory="1" separator="" occurs="1" default="0000" offset="" padding="0"/>
		</output-mapping>
	</rule>
</rules>
    