<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CONFERMA-CORREZIONE-MOVIMENTI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSE259</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>
	    <logApp>true</logApp>
	    <logAppServDesc>SALVA MOVIMENTO CORREZIONE</logAppServDesc>
	    <areaFunzionale>GEST FUNZIONALITA AUSILIARIE-FUNZIONI AUSILIARIE</areaFunzionale>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	      <!-- Definizione commarea: VWLSE259 Lunghezza: 150 + 14025 + 1824 = 15999  -->        
		<input-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.movimentoCorrezione.dto.MovimentoCorrezioneRequestDTO" nomeKeyERR="erroriMovimentiCorrezione">
			<!-- input : 14025 car -->
            <field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoMovimentiCorrezione"     length="14000" >
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.movimentoCorrezione.dto.MovimentoCorrezioneValoriDTO" iterations="200" blockLength="70">
					<field-map attributeName="flMovimentoDaCorreggere"          length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="numeroOttico"        				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="progressivo"   					length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
					<field-map attributeName="dataContabilizzazione"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data" />
					<field-map attributeName="dataComunicazioneRCT"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="dataIncasso"   			    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="dataCompetenzaIncasso"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="false" natura="Data"/>
					<field-map attributeName="importoIncasso"  					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="filler"   		   				length="21" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.movimentoCorrezione.dto.MovimentoCorrezioneResponseDTO">
		    <!-- output :14025 car -->  
			<field-map attributeName="numElementiTrovati"  length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flLimite"     	   length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoMovimentiCorrezione"     length="14000" >
				<nested-mapping className="it.sistinf.albedoweb.archivio.funzioniDBVita.movimentoCorrezione.dto.MovimentoCorrezioneValoriDTO" iterations="200" blockLength="70">
					<field-map attributeName="flMovimentoDaCorreggere"          length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>
					<field-map attributeName="numeroOttico"        				length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default=""  offset="" padding=""/>
					<field-map attributeName="progressivo"   					length="4"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default=""  offset="" padding="0"/>
					<field-map attributeName="dataContabilizzazione"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data" />
					<field-map attributeName="dataComunicazioneRCT"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncasso"   			    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataCompetenzaIncasso"   	    	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoIncasso"  					length="15" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="filler"   		   				length="21" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</output-mapping>
	</rule>
</rules>
    