<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ESEGUI-INCASSO-TITOLO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSYN0005</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoRequestDTO">
			<!-- Totali 263 car (150 + 369) -->
			<!-- 369 car -->
			<field-map attributeName="titolo" length="121" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="121">
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="ramoPolizza"          length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"    	length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffetto"   	    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="cognomeContraente"    length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nomeContraente"    	length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="posizHost"   	        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="descrCanale"  		 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codCanaleSecondario"   length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrCanaleSecondario" length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataContEmiss"  		 length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="dataContEsitaz"  		 length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="provvAcquistoDefault"  length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="provvIncassoDefault"   length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<!-- dati incasso -->
			<field-map attributeName="premioLordo"   	    length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvAcquisto"        length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvIncasso"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differPremio"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differAcquisto"       length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differIncasso"        length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataPagamento"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataAccredito"       	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codModalitaPagamento" length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="flProtAgz"            length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
			<field-map attributeName="codCanaleIncassante"   length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="flProtSubage"         length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>												
			<field-map attributeName="codCanaleSecondarioIncassante"   length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoResponseDTO">
		    <!-- Totali 2520 car (150 + 546 + 1824) --> 
		    <!-- 546 car -->
		    <!-- Campi di input 369 car -->
			<field-map attributeName="titolo" length="121" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="121">
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="ramoPolizza"          length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenziaPolizza"   	length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="tipoDocumento"    	length="4"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffetto"   	    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="contraente"           length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="posizHost"   	        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>					
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
			<field-map attributeName="descrCanale"  		 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codCanaleSecondario"   length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrCanaleSecondario" length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataContEmiss"  		 length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="dataContEsitaz"  		 length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="provvAcquistoDefault"  length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<field-map attributeName="provvIncassoDefault"   length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>			
			<!-- dati incasso -->
			<field-map attributeName="premioLordo"   	    length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvAcquisto"        length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvIncasso"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differPremio"         length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differAcquisto"       length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="differIncasso"        length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataPagamento"        length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataAccredito"       	length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codModalitaPagamento" length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="flProtAgz"            length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
			<field-map attributeName="codCanaleIncassante"   length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="flProtSubage"         length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>												
			<field-map attributeName="codCanaleSecondarioIncassante"   length="6" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<!-- dati output  177 car -->
			<field-map attributeName="desCanaleSecondarioIncassante" length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
			<field-map attributeName="descCanaleIncassante"  length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
			<field-map attributeName="descModalitaPagamento" length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="foglioCassa"           length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataFC"        		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="tipoFC"                length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="riga"                  length="4" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
			<!-- errori  -->
			<field-map attributeName="premioLordoCdErr"  				  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="provvAcquistoCdErr"       		  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>						
			<field-map attributeName="provvIncassoCdErr"         		  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>						
			<field-map attributeName="codCanaleIncassanteCdErr"   		  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codCanaleSecondarioIncassanteCdErr" length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataPagamentoCdErr"				  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="dataAccreditoCdErr"      		 	  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			<field-map attributeName="codModalitaPagamentoCdErr"  		  length="4"  precision="0" numericScale="0" align="right"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
			
		</output-mapping>
	</rule>
</rules>
    