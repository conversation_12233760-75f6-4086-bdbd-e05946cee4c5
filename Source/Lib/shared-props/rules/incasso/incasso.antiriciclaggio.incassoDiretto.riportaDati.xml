<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INCASSO-DIRETTO-RIPORTA-DATI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE017</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoRequestDTO">
			<!-- Totali 151 car (150 + 1) -->
			<!-- 1 car -->
			<field-map attributeName="filler" 		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoResponseDTO">
		    <!-- Totali 2538 car (150 + 564 + 1824) --> 
		    <!-- 564 car -->
   			<field-map attributeName="flProtAgz"           		 length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>									
		    <field-map attributeName="codCanaleIncassante"       length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descCanaleIncassante"      length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flProtSubage"        		 length="1" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>															
			<field-map attributeName="codGestoreIncassante"      length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descGestoreIncassante"     length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codModalitaPagamento" 	 length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descModalitaPagamento" 	 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codiceVoce" 	 			 length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descCodiceVoce" 			 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="ramoPolizza" 	 			 length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descRamoPolizza" 			 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codCanaleCompetenza"       length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descCanaleCompetenza"      length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codGestoreCompetenza"      length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="descGestoreCompetenza"     length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="codCanaleSecondario"   	 length="6"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrCanaleSecondario" 	 length="40" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="cognomeContraente"    	 length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="nomeContraente"    	     length="30" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
			<field-map attributeName="premioLordo"   	    	 length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvAcquisto"        	 length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="provvIncasso"        		 length="14" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataRegistrazione"         length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataPagamento"        	 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataAccredito"       		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataEffetto"        		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <field-map attributeName="numColl"               	 length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numPolizza"        	     length="7"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="flProposta"           	 length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
            <!-- Dati Incasso -->
			<field-map attributeName="numOttico"    		     length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="appendice"        	     length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="foglioCassa"        		 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="dataFC"        			 length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="tipoFC"        			 length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>						
			<field-map attributeName="riga"        				 length="4"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		</output-mapping>
	</rule>
</rules>
    