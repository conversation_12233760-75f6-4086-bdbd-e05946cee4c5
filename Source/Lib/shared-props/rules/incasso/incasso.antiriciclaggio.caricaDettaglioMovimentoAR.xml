<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>CARICA-DETTAGLIO-MOVIMENTO-AR-SYNCRO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>WSYN0008</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.MovimentoAntiriciclaggioRequestDTO">
			<!-- Totali 172 car (150 + 22) -->
			<!-- 61 car -->
			<field-map attributeName="tipoIncasso"  		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="chiaveDinamica"  		 length="45" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataOperazione"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="progrMovimento"   	 length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="statoMovimento"  		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.MovimentoAntiriciclaggioResponseDTO">
		    <!-- Totali 7757 car (150 + 5783 + 1824) -->  
		    <!-- Campi output 5783 -->
			<!-- INPUT 61 car -->
			<field-map attributeName="tipoIncasso"  		 length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="chiaveDinamica"  		 length="45" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataOperazione"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="progrMovimento"   	 length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="statoMovimento"  		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<!-- Output 5722 -->
			<field-map attributeName="tipoEvento"  		     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoEvento"       length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="progrMovimento"   	 length="3"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="statoMovimento"  		 length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrStatoMovimento"	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="tipoChiave"  		     length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="descrTipoChiave"  	 length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="chiaveDinamica"  		 length="45" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaProposta"  	 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroProposta"  		 length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="agenziaPolizza"  		 length="6"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numeroPolizza"         length="9"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataEffetto"           length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="dataOperazione"  		 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="operazioneFrazionata"  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="coordinate"  			 length="35" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="numAssegno"  		 	 length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="modPagamento"  	     length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="valuta"        	     length="4"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
			<field-map attributeName="importoAR" 			 length="14" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
		    <field-map attributeName="elencoSoggettiMovimento" length="5463">
				<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.ElencoSoggettiMovimentoDTO" iterations="0" blockLength="5463">            
					<field-map attributeName="numElementiTrovati"   length="3"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
					<field-map attributeName="soggettiMovimento" length="5460">
						<nested-mapping className="it.sistinf.albedoweb.antiriciclaggio.dto.SoggettoMovimentoARDTO" iterations="20" blockLength="273">
							<field-map attributeName="tipoSoggetto"  		  length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
							<field-map attributeName="dettaglioSoggetto" length="194" >
								<nested-mapping className="it.sistinf.albedoweb.backoffice.dto.AnagraficaPercipienteDTO" iterations="0" blockLength="194">
									<field-map attributeName="codiceFiscale"            length="16" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="cognome"              	length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="nome"   		            length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="indirizzoDiResidenza"     length="35" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="luogoDiResidenza"         length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="capResidenza"             length="5"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiResidenza"     length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="stato"                    length="3"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="sesso"                    length="1"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="dataDiNascita"            length="10" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="luogoDiNascita"           length="30" precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
									<field-map attributeName="provinciaDiNascita"       length="2"  precision="0" numericScale="0" align="left" mandatory="0" separator="" occurs="1" default="" offset=""  padding=""/>
								</nested-mapping>
							</field-map>
							<field-map attributeName="datiAntiriciclaggio" length="78">
								<nested-mapping className="it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAntiriciclaggioDTO" iterations="0" blockLength="78">
									<field-map attributeName="numeroDocumento"    	length="15" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="tipoDocumento"        length="2"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
									<field-map attributeName="dataRilascio"         length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=" "/>
						       		<field-map attributeName="localitaRilascio"     length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="numCab"               length="5" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="gruppo"               length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="sottoGruppo"          length="3" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
									<field-map attributeName="enteRilascio"     	length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
								</nested-mapping>
							</field-map>
						</nested-mapping>
					</field-map>
				</nested-mapping>
			</field-map>
			
	</output-mapping>
	</rule>
</rules>
    