<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ESEGUI-STORNA-TITOLO</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>AWLSE025</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoRequestDTO">
			<!-- Totali 267 car (150 + 117) -->
			<!-- 117 car -->
			<field-map attributeName="titolo" length="117" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="117">
					<field-map attributeName="codiceCompagnia"      length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceCategoria"      length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numAgenziaPolizza"    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="nominativo"      		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffetto"   	    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncasso"          length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   	        length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="subagenzia"   	    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="modPagamento"   	    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dupkey"   	        length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="forzatura"   	        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="progressivo"   	    length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.incasso.dto.IncassoResponseDTO">
		    <!-- Totali 2091 car (150 + 117 + 1824) --> 
		    <!-- 117 car -->
		    <!-- Campi di input 117 car -->
			<field-map attributeName="titolo" length="117" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="117">
					<field-map attributeName="codiceCompagnia"      length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceCategoria"      length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numAgenziaPolizza"    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="nominativo"      		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffetto"   	    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncasso"          length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   	        length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="subagenzia"   	    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="modPagamento"   	    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dupkey"   	        length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="forzatura"   	        length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="progressivo"   	    length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>