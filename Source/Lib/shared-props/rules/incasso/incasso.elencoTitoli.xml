<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>ELENCO-TITOLI</id>
		<initialProgram>WNDISPC0</initialProgram>
		<initialTransaction>SB00</initialTransaction>
	   	<program>VWLSLTIT</program>
	   	<transaction>SB00</transaction>
		<connectorId>A05TARE</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue="" />	   
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>	   	
	   
		<input-mapping className="it.sistinf.albedoweb.incasso.dto.ElencoTitoliRequestDTO" nomeKeyERR="erroriElencoTitoli">
			<!-- Totali 290 car (150 + 140) -->
			<!-- 140 car -->
			<field-map attributeName="titolo" length="140" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="140">
		  			<field-map attributeName="codiceCompagnia"      length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		  			<field-map attributeName="codiceCategoria"      length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numAgenziaPolizza"    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=" "/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"
						valida="true" natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numColl" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"
						valida="true" natura="Numerico"  segnato="false"  numInteri="7" numDecimali="0" separatoreMigliaia="false" nomeAttributoERR="numPolizza" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="nominativo"      		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffettoMinima"   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data"  nomeAttributoERR="dataEffettoMinima" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="dataEffettoMassima"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data"  nomeAttributoERR="dataEffettoMassima" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="dataIncassoMinima"   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data"  nomeAttributoERR="dataIncassoMinima" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="dataIncassoMassima"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Data"  nomeAttributoERR="dataIncassoMassima" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						valida="true" natura="Numerico" segnato="true" numInteri="8" numDecimali="2" separatoreMigliaia="true" nomeAttributoERR="importoPremio" nomeKeyERR="erroriElencoTitoli"/>
					<field-map attributeName="agenzia"   	        length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="subagenzia"           length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="promotore"            length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceStatoTitolo"    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flUtenteBatch"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</input-mapping>

		<output-mapping className="it.sistinf.albedoweb.incasso.dto.ElencoTitoliResponseDTO">
		    <!-- Totali 30498 car (150 + 28524 + 1824) --> 
		    <!-- 28524 car -->
		    <!-- Campi di input 140 car -->
			<field-map attributeName="titolo" length="140" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="0" blockLength="140">
		  			<field-map attributeName="codiceCompagnia"      length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		  			<field-map attributeName="codiceCategoria"      length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numAgenziaPolizza"    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="nominativo"      		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffettoMinima"   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataEffettoMassima"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncassoMinima"   	length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncassoMassima"   length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="true" numInteri="8" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="agenzia"   	        length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="subagenzia"           length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="promotore"            length="7"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceStatoTitolo"    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="flUtenteBatch"   		length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
          <!-- Elenco -->
            <!-- 28384 car -->		    
			<field-map attributeName="numElementiTrovati" length="3" precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="000"  offset="" padding="0"/>
			<field-map attributeName="flAltri"     		  length="1" precision="0" numericScale="0" align="left" mandatory="1" separator="" occurs="1" default="N" offset="" padding=""/>		    
			<field-map attributeName="elencoTitoli" length="28380" >
				<nested-mapping className="it.sistinf.albedoweb.incasso.dto.TitoloDTO" iterations="220" blockLength="129">
		  			<field-map attributeName="tipoTitolo"           length="1"  precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="esito"                length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceCompagnia"      length="3"  precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="codiceCategoria"      length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numAgenziaPolizza"    length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numColl"              length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="numPolizza"   	    length="7"  precision="0" numericScale="0" align="right" mandatory="1" separator="" occurs="1" default="" offset="" padding="0"/>
					<field-map attributeName="incassoProposta"   	length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="numOttico"  		    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="agenzia"   	        length="6"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="nominativo"      		length="30" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="codiceFiscale"      	length="16" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dataEffetto"   	    length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="dataIncasso"          length="10" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Data"/>
					<field-map attributeName="importoPremio"   	    length="14" precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""
						natura="Numerico" segnato="true" numInteri="8" numDecimali="2" separatoreMigliaia="true"/>
					<field-map attributeName="modPagamento"   	    length="1"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="dupkey"   	        length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
					<field-map attributeName="progressivo"   	    length="2"  precision="0" numericScale="0" align="left"  mandatory="1" separator="" occurs="1" default="" offset="" padding=""/>
				</nested-mapping>
			</field-map>
		</output-mapping>
	</rule>
</rules>
    