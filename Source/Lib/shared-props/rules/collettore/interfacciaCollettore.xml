<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule>
		<id>INTERFACCIA-COLLETTORE</id>
		<initialProgram>WNDISPC1</initialProgram>
		<initialTransaction>SBEX</initialTransaction>
	   	<program>VWLSE619</program>
	   	<transaction>SBEX</transaction>
		<connectorId>A05TAREEX</connectorId>

	   	<multipleTransaction>true</multipleTransaction>
	   	<!-- hasMultipleTransaction>false</hasMultipleTransaction -->
	   	<pageRequestField/>
	   	<limitPage>99</limitPage>
	   	<moreDataField/>
	   	<moreDataEndValue>1</moreDataEndValue>
	   	<flagFirstTime absolutePosition="0" length="0" secondValue=""/>  
	   	<pastedFields>
	   		<fieldToBePaste absoluteInPosition="0" absoluteOutPosition="0" length="0" />
	   	</pastedFields>
	   
	    
		<input-mapping className="it.sistinf.albedoweb.backoffice.dto.InterfacciaCollettoreRequestDTO">
			<!-- Dati input 94 + 861 + 258 + 1923 = 3136  -->
            <field-map attributeName="collettoreData" length="3136">
                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreDataDTO" iterations="0" blockLength="3136">
					<!-- Dati input 94 + 861 + 258 + 1923 = 3136  -->
					<!-- 94 car -->
		            <field-map attributeName="funzione"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="aggancio"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="compagnia"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="portafoglio"      length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="agenzia"          length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="numeroCollettiva" length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		            <field-map attributeName="numeroPolizza"    length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		            <field-map attributeName="tipoLiquidazione" length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="dataLiquidazione" length="08" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="timestamp"        length="26" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="codReturnCode"    length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		            <field-map attributeName="desReturnCode"    length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		
		            <!-- 861 -->
		            <field-map attributeName="liquidaz" length="861">
		                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreLiquidazDTO" iterations="0" blockLength="861"> 
		                    <field-map attributeName="codDivisa"            length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="contraente"           length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="assicurato"           length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="ageLiquid"            length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="impLordoIna"          length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codProduttore"        length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="impLordoLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impNettoLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impComplInfLi"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRimbPrestLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIntPrestLi"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRateNopagLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impUtilTarifLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codCorrett1Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett1Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codCorrett2Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett2Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccontoLi"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccontoLi"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccontoLi"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccLiM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccLiM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccLiM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccLiM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccLiM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccLiM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponImposta20Li"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquImposta20Li"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impImposta20Li"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponImposta26Li"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquImposta26Li"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impImposta26Li"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitVisentLi"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitVisentLi"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitVisentLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefLi"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefLi"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponVisentLiM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquVisentLiM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impVisentLiM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponVisentLiM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquVisentLiM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impVisentLiM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefLiM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefLiM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefLiM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefLiM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefLiM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefLiM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codCorrett3Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett3Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="abilitazUser"         length="07" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="polizzaFendac"        length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="dataSinFendac"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="dataValFendac"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="ramo"                 length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="tipoPr"               length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="flNuovaFisc"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="imponFp"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquFp"              length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impFp"                length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponFpM2"            length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquFpM2"            length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impFpM2"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponFpM3"            length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquFpM3"            length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impFpM3"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="flThesys"             length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="pip"                  length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="goodJob"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="tipoTax"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="flPtfGest"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="flCompleti"           length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="causaleAnnull"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="impLordoNsQuota"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="dataInser"            length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		                    <field-map attributeName="dataAnnull"           length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		                    <field-map attributeName="pagamAgenzia"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="mod770SiNo"           length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="irpefContrSiNo"       length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="tipoCoass"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="compCoassDel"         length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="flStorniRev"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="tipoAzienda"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="livello"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="psw"                  length="08" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="abilitazione"         length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="scudoFisc"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                </nested-mapping>
		            </field-map>
		                    
		            <!-- 258 -->
		            <field-map attributeName="coassRiass" length="258">
		                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreCoassRiassDTO" iterations="0" blockLength="258">
				            <field-map attributeName="coassList" length="240">
				                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreCoassDTO" iterations="10" blockLength="24"> 
				                    <field-map attributeName="codCoass"       length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
				                    <field-map attributeName="aliquCoass"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="impCoass"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                 </nested-mapping>
				            </field-map>
				            <!-- 18 -->
				            <field-map attributeName="codRiass"                     length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				            <field-map attributeName="impRiass"                     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>				            
		                </nested-mapping>
		            </field-map>
		
		            <!-- 818 + 1105 = 1923 -->
		            <field-map attributeName="percList" length="1923">
		                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettorePercDTO" iterations="1" blockLength="1923">
		                    <field-map attributeName="numPerc"              length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" numInteri="03" />
		                    <field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="flInvioPe"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="tipoPersona"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="nominativo"           length="50" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="nome"                 length="50" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="sesso"                length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="dataDiNascita"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" numInteri="08" />
		                    <field-map attributeName="statoDiNascita"       length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="luogoDiNascita"       length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="provDiNascita"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="statoDiResidenza"     length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="luogoDiResidenza"     length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="provDiResidenza"      length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="capDiResidenza"       length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		                    <field-map attributeName="impLordoPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impNettoPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impComplInfPe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRimbPrestPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impintPrestPe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRateNopagPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impUtilTarifPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codCorrett1Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett1Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codCorrett2Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett2Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccontoPe"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccontoPe"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccontoPe"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccontoPeM2"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccontoPeM2"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccontoPeM2"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitAccontoPeM3"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitAccontoPeM3"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitAccontoPeM3"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponImposta20Pe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquImposta20Pe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impImposta20Pe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponImposta26Pe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquImposta26Pe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impImposta26Pe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponRitVisentPe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquRitVisentPe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impRitVisentPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefPe"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefPe"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponVisentPeM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquVisentPeM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impVisentPeM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponVisentPeM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquVisentPeM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impVisentPeM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefPeM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefPeM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefPeM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="imponIrpefPeM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="aliquIrpefPeM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impIrpefPeM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="tipoSini"             length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="codCorrett3Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                    <field-map attributeName="impCorrett3Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="flRiestrazione"       length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="aliquCompetenzaPe"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="impCompetenzaPe"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                    <field-map attributeName="codiceFiscaleConiuge" length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                    <field-map attributeName="flConiugeCarico"      length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
				            <!-- 825 + 280  = 1105 -->
				            <field-map attributeName="listaPagam" length="1105">
				                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettorePagamDTO" iterations="1" blockLength="1105">
		                            <field-map attributeName="tipoPagamento"        length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                            <field-map attributeName="polPropEmessa"        length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="destinatario"         length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                            <field-map attributeName="abi"                  length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="cab"                  length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="contoCorr"            length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                            <field-map attributeName="ibanNazione"          length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                            <field-map attributeName="ibanCNN"              length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		                            <field-map attributeName="ibanCIN"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                            <field-map attributeName="dataValuta"           length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
		                            <field-map attributeName="impLordoPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impNettoPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impComplInfPa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impRimbPrestPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impIntPrestPa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impRateNopagPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impUtilTarifPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="codCorrett1Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
				                    <field-map attributeName="impCorrett1Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="codCorrett2Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="impCorrett2Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponRitAccontoPa"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="aliquRitAccontoPa"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
				                    <field-map attributeName="impRitAccontoPa"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponRitAccontoPaM2"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquRitAccontoPaM2"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impRitAccontoPaM2"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponRitAccontoPaM3"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquRitAccontoPaM3"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impRitAccontoPaM3"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponImposta20Pa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquImposta20Pa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impImposta20Pa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponImposta26Pa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquImposta26Pa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impImposta26Pa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponRitVisentPa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquRitVisentPa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impRitVisentPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponIrpefPa"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquIrpefPa"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impIrpefPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponVisentPaM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquVisentPaM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impVisentPaM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponVisentPaM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquVisentPaM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impVisentPaM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponIrpefPaM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquIrpefPaM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impIrpefPaM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponIrpefPaM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="aliquIrpefPaM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impIrpefPaM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="dataEsborso"          length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="codCorrett3Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="impCorrett3Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="flPostel"             length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="flConguaglio"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="progrDataEsborso"     length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="aliquCompetenzaPa"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="impCompetenzaPa"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="tipoLiquidPa"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="dscBanca"             length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="dscFiliale"           length="17" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="dscCausalePagam"      length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
		                            <field-map attributeName="dataIniPagFendac"     length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="dataCessPagFendac"    length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <field-map attributeName="imponTfrFendac"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                            <!-- 280 -->
		                            <field-map attributeName="listaIfrs" length="280">
		                                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreIfrsDTO" iterations="5" blockLength="56">
		                                    <field-map attributeName="tariffa"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                                    <field-map attributeName="tipoTariffaFlag"  length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
		                                    <field-map attributeName="impLordoIfrs"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                                    <field-map attributeName="impAssicurato"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                                    <field-map attributeName="impFinanziario"   length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
		                                </nested-mapping>
		                            </field-map>
				                </nested-mapping>
		                    </field-map>
		                 </nested-mapping>
		            </field-map>
                </nested-mapping>
            </field-map>
		</input-mapping>

            
		<output-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreResponseDTO">            
            <!-- Dati input 94 + 861 + 258 + 1905 = 3118  -->  
            <field-map attributeName="collettoreData" length="3118">
                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreDataDTO" iterations="0" blockLength="3118">
                    <!-- Dati input 94 + 861 + 258 + 1905 = 3118  -->           
                    <!-- 94 car -->
                    <field-map attributeName="funzione"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="aggancio"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="compagnia"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="portafoglio"      length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="agenzia"          length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="numeroCollettiva" length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                    <field-map attributeName="numeroPolizza"    length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                    <field-map attributeName="tipoLiquidazione" length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="dataLiquidazione" length="08" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="timestamp"        length="26" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="codReturnCode"    length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
                    <field-map attributeName="desReturnCode"    length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
        
                    <!-- 861 -->
                    <field-map attributeName="liquidaz" length="861">
                        <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreLiquidazDTO" iterations="0" blockLength="861"> 
                            <field-map attributeName="codDivisa"            length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="contraente"           length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="assicurato"           length="40" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="ageLiquid"            length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="impLordoIna"          length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codProduttore"        length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="impLordoLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impNettoLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impComplInfLi"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRimbPrestLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIntPrestLi"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRateNopagLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impUtilTarifLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codCorrett1Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett1Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codCorrett2Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett2Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccontoLi"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccontoLi"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccontoLi"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccLiM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccLiM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccLiM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccLiM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccLiM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccLiM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponImposta20Li"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquImposta20Li"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impImposta20Li"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponImposta26Li"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquImposta26Li"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impImposta26Li"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitVisentLi"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitVisentLi"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitVisentLi"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefLi"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefLi"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefLi"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponVisentLiM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquVisentLiM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impVisentLiM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponVisentLiM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquVisentLiM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impVisentLiM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefLiM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefLiM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefLiM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefLiM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefLiM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefLiM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codCorrett3Li"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett3Li"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="abilitazUser"         length="07" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="polizzaFendac"        length="07" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="dataSinFendac"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="dataValFendac"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="ramo"                 length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="tipoPr"               length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="flNuovaFisc"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="imponFp"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquFp"              length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impFp"                length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponFpM2"            length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquFpM2"            length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impFpM2"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponFpM3"            length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquFpM3"            length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impFpM3"              length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="flThesys"             length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="pip"                  length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="goodJob"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="tipoTax"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="flPtfGest"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="flCompleti"           length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="causaleAnnull"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="impLordoNsQuota"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="dataInser"            length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                            <field-map attributeName="dataAnnull"           length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                            <field-map attributeName="pagamAgenzia"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="mod770SiNo"           length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="irpefContrSiNo"       length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="tipoCoass"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="compCoassDel"         length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="flStorniRev"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="tipoAzienda"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="livello"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="psw"                  length="08" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="abilitazione"         length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="scudoFisc"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                        </nested-mapping>
                    </field-map>
                            
                    <!-- 258 -->
                    <field-map attributeName="coassRiass" length="258">
                        <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreCoassRiassDTO" iterations="0" blockLength="258">                            
                            <field-map attributeName="coassList" length="240">
                                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreCoassDTO" iterations="10" blockLength="24"> 
                                    <field-map attributeName="codCoass"       length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="aliquCoass"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impCoass"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                 </nested-mapping>
                            </field-map>
                            <!-- 18 -->
                            <field-map attributeName="codRiass"               length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRiass"               length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                        </nested-mapping>
                    </field-map>
        
                    <!-- 818 + 1105 = 1923 -->
                    <field-map attributeName="percList" length="1923">
                        <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettorePercDTO" iterations="1" blockLength="1923">
                            <field-map attributeName="numPerc"              length="03" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" numInteri="03" />
                            <field-map attributeName="codiceFiscale"        length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="flInvioPe"            length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="tipoPersona"          length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="nominativo"           length="50" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="nome"                 length="50" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="sesso"                length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="dataDiNascita"        length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" natura="Numerico" numInteri="08" />
                            <field-map attributeName="statoDiNascita"       length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="luogoDiNascita"       length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="provDiNascita"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="statoDiResidenza"     length="03" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="luogoDiResidenza"     length="25" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="provDiResidenza"      length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="indirizzoDiResidenza" length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="capDiResidenza"       length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                            <field-map attributeName="impLordoPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impNettoPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impComplInfPe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRimbPrestPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impintPrestPe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRateNopagPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impUtilTarifPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codCorrett1Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett1Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codCorrett2Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett2Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccontoPe"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccontoPe"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccontoPe"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccontoPeM2"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccontoPeM2"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccontoPeM2"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitAccontoPeM3"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitAccontoPeM3"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitAccontoPeM3"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponImposta20Pe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquImposta20Pe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impImposta20Pe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponImposta26Pe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquImposta26Pe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impImposta26Pe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponRitVisentPe"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquRitVisentPe"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impRitVisentPe"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefPe"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefPe"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefPe"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponVisentPeM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquVisentPeM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impVisentPeM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponVisentPeM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquVisentPeM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impVisentPeM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefPeM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefPeM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefPeM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="imponIrpefPeM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="aliquIrpefPeM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impIrpefPeM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="tipoSini"             length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="codCorrett3Pe"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                            <field-map attributeName="impCorrett3Pe"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="flRiestrazione"       length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="aliquCompetenzaPe"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="impCompetenzaPe"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                            <field-map attributeName="codiceFiscaleConiuge" length="16" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <field-map attributeName="flConiugeCarico"      length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                            <!-- 825 + 280  = 1105 -->
                            <field-map attributeName="listaPagam" length="1105">
                                <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettorePagamDTO" iterations="1" blockLength="1105">
                                    <field-map attributeName="tipoPagamento"        length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="polPropEmessa"        length="13" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="destinatario"         length="60" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="abi"                  length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="cab"                  length="05" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="contoCorr"            length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="ibanNazione"          length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="ibanCNN"              length="02" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                                    <field-map attributeName="ibanCIN"              length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                    <field-map attributeName="dataValuta"           length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0" />
                                    <field-map attributeName="impLordoPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impNettoPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impComplInfPa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRimbPrestPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impIntPrestPa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRateNopagPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impUtilTarifPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="codCorrett1Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="impCorrett1Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="codCorrett2Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="impCorrett2Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponRitAccontoPa"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquRitAccontoPa"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRitAccontoPa"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponRitAccontoPaM2"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquRitAccontoPaM2"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRitAccontoPaM2"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponRitAccontoPaM3"  length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquRitAccontoPaM3"  length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRitAccontoPaM3"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponImposta20Pa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquImposta20Pa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impImposta20Pa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponImposta26Pa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquImposta26Pa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impImposta26Pa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponRitVisentPa"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquRitVisentPa"     length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impRitVisentPa"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponIrpefPa"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquIrpefPa"         length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impIrpefPa"           length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponVisentPaM2"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquVisentPaM2"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impVisentPaM2"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponVisentPaM3"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquVisentPaM3"      length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impVisentPaM3"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponIrpefPaM2"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquIrpefPaM2"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impIrpefPaM2"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponIrpefPaM3"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="aliquIrpefPaM3"       length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impIrpefPaM3"         length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="dataEsborso"          length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="codCorrett3Pa"        length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="impCorrett3Pa"        length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="flPostel"             length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="flConguaglio"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="progrDataEsborso"     length="04" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="aliquCompetenzaPa"    length="06" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="impCompetenzaPa"      length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="tipoLiquidPa"         length="02" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="dscBanca"             length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="dscFiliale"           length="17" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="dscCausalePagam"      length="30" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""/>
                                    <field-map attributeName="dataIniPagFendac"     length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="dataCessPagFendac"    length="08" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <field-map attributeName="imponTfrFendac"       length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                    <!-- 280 -->
                                    <field-map attributeName="listaIfrs" length="280">
                                        <nested-mapping className="it.sistinf.albedoweb.backoffice.collettore.dto.InterfacciaCollettoreIfrsDTO" iterations="5" blockLength="56">
                                            <field-map attributeName="tariffa"          length="10" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                            <field-map attributeName="tipoTariffaFlag"  length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding="" />
                                            <field-map attributeName="impLordoIfrs"     length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                            <field-map attributeName="impAssicurato"    length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                            <field-map attributeName="impFinanziario"   length="15" precision="0" numericScale="0" align="right" mandatory="0" separator="" occurs="1" default="" offset="" padding="0"/>
                                        </nested-mapping>
                                    </field-map>
                                </nested-mapping>
                            </field-map>
                         </nested-mapping>
                    </field-map>
                </nested-mapping>
            </field-map>
            
            <field-map attributeName="filler0"     length="01" precision="0" numericScale="0" align="left"  mandatory="0" separator="" occurs="1" default="" offset="" padding=""  />
		</output-mapping>
	</rule>
</rules>
    