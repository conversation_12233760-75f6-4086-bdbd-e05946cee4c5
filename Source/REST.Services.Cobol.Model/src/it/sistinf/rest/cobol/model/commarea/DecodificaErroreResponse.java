package it.sistinf.rest.cobol.model.commarea;

import it.sistinf.rest.cobol.model.commarea.DecodificaCodiciErrore;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.Size;
import javax.validation.Valid;


import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;


public class DecodificaErroreResponse   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid List<DecodificaCodiciErrore> decodificaCodiciErrore = new ArrayList<DecodificaCodiciErrore>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public DecodificaErroreResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public DecodificaErroreResponse decodificaCodiciErrore(List<DecodificaCodiciErrore> decodificaCodiciErrore) {
    this.decodificaCodiciErrore = decodificaCodiciErrore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("decodificaCodiciErrore")
 @Size(max=100)
  public List<DecodificaCodiciErrore> getDecodificaCodiciErrore() {
    return decodificaCodiciErrore;
  }
  public void setDecodificaCodiciErrore(List<DecodificaCodiciErrore> decodificaCodiciErrore) {
    this.decodificaCodiciErrore = decodificaCodiciErrore;
  }

  /**
   **/
  public DecodificaErroreResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DecodificaErroreResponse decodificaErroreResponse = (DecodificaErroreResponse) o;
    return Objects.equals(headerCobolSrv, decodificaErroreResponse.headerCobolSrv) &&
        Objects.equals(decodificaCodiciErrore, decodificaErroreResponse.decodificaCodiciErrore) &&
        Objects.equals(errori, decodificaErroreResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, decodificaCodiciErrore, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DecodificaErroreResponse {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    decodificaCodiciErrore: ").append(toIndentedString(decodificaCodiciErrore)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
