package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CheckListRequest   {

  private @Valid String idChiamata = null;

  private @Valid String intermediario = null;

  private @Valid CheckListRequestInfo request = null;

  private @Valid String callingService = null;

  /**
   * Identificatore della chiamata es: EM1002001
   **/
  public CheckListRequest idChiamata(String idChiamata) {
    this.idChiamata = idChiamata;
    return this;
  }

  
  @ApiModelProperty(example = "EM1002001", required = true, value = "Identificatore della chiamata es: EM1002001")
  @JsonProperty("idChiamata")
  @NotNull

  public String getIdChiamata() {
    return idChiamata;
  }
  public void setIdChiamata(String idChiamata) {
    this.idChiamata = idChiamata;
  }

  /**
   * Codice fiscale della compagnia
   **/
  public CheckListRequest intermediario(String intermediario) {
    this.intermediario = intermediario;
    return this;
  }

  
  @ApiModelProperty(example = "01234567890", required = true, value = "Codice fiscale della compagnia")
  @JsonProperty("intermediario")
  @NotNull

  public String getIntermediario() {
    return intermediario;
  }
  public void setIntermediario(String intermediario) {
    this.intermediario = intermediario;
  }

  /**
   **/
  public CheckListRequest request(CheckListRequestInfo request) {
    this.request = request;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("request")

  public CheckListRequestInfo getRequest() {
    return request;
  }
  public void setRequest(CheckListRequestInfo request) {
    this.request = request;
  }

  /**
   * Nome del servizio chiamante
   **/
  public CheckListRequest callingService(String callingService) {
    this.callingService = callingService;
    return this;
  }

  
  @ApiModelProperty(value = "Nome del servizio chiamante")
  @JsonProperty("callingService")

  public String getCallingService() {
    return callingService;
  }
  public void setCallingService(String callingService) {
    this.callingService = callingService;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckListRequest checkListRequest = (CheckListRequest) o;
    return Objects.equals(idChiamata, checkListRequest.idChiamata) &&
        Objects.equals(intermediario, checkListRequest.intermediario) &&
        Objects.equals(request, checkListRequest.request) &&
        Objects.equals(callingService, checkListRequest.callingService);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idChiamata, intermediario, request, callingService);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckListRequest {\n");
    
    sb.append("    idChiamata: ").append(toIndentedString(idChiamata)).append("\n");
    sb.append("    intermediario: ").append(toIndentedString(intermediario)).append("\n");
    sb.append("    request: ").append(toIndentedString(request)).append("\n");
    sb.append("    callingService: ").append(toIndentedString(callingService)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
