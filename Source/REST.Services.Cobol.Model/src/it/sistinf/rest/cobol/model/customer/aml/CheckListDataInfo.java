package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CheckListDataInfo   {

  private @Valid String parameter = null;

  private @Valid String value = null;

  /**
   * Valorizzare con query
   **/
  public CheckListDataInfo parameter(String parameter) {
    this.parameter = parameter;
    return this;
  }

  
  @ApiModelProperty(example = "query", required = true, value = "Valorizzare con query")
  @JsonProperty("parameter")
  @NotNull

  public String getParameter() {
    return parameter;
  }
  public void setParameter(String parameter) {
    this.parameter = parameter;
  }

  /**
   * Valorizzare con queryStandard
   **/
  public CheckListDataInfo value(String value) {
    this.value = value;
    return this;
  }

  
  @ApiModelProperty(example = "queryStandard", required = true, value = "Valorizzare con queryStandard")
  @JsonProperty("value")
  @NotNull

  public String getValue() {
    return value;
  }
  public void setValue(String value) {
    this.value = value;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckListDataInfo checkListDataInfo = (CheckListDataInfo) o;
    return Objects.equals(parameter, checkListDataInfo.parameter) &&
        Objects.equals(value, checkListDataInfo.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(parameter, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckListDataInfo {\n");
    
    sb.append("    parameter: ").append(toIndentedString(parameter)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
