package it.sistinf.rest.cobol.model.dominio;

import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class DominioResponse   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid List<ElementoDominio> elementiDominio = new ArrayList<ElementoDominio>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public DominioResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public DominioResponse elementiDominio(List<ElementoDominio> elementiDominio) {
    this.elementiDominio = elementiDominio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elementiDominio")

  public List<ElementoDominio> getElementiDominio() {
    return elementiDominio;
  }
  public void setElementiDominio(List<ElementoDominio> elementiDominio) {
    this.elementiDominio = elementiDominio;
  }

  /**
   **/
  public DominioResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DominioResponse dominioResponse = (DominioResponse) o;
    return Objects.equals(headerCobolSrv, dominioResponse.headerCobolSrv) &&
        Objects.equals(elementiDominio, dominioResponse.elementiDominio) &&
        Objects.equals(errori, dominioResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, elementiDominio, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DominioResponse {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    elementiDominio: ").append(toIndentedString(elementiDominio)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
