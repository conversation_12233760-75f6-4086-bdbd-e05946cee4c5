package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class PercipienteExtendedInfo   {

  private @Valid BigDecimal percentualeSuddivisione = null;

  private @Valid BigDecimal importoSuddivisione = null;

  private @Valid String progrTst075 = null;

  private @Valid String progressivo = null;

  private @Valid String codAnag = null;

  private @Valid String percip = null;

  private @Valid String relazioneBeneficiarioContraente = null;

  private @Valid String relazioneBeneficiarioContraenteAltro = null;

  private @Valid List<BeneficiarioSoggettoTerzoInfo> elencoBeneficiarioSoggettoTerzo = new ArrayList<BeneficiarioSoggettoTerzoInfo>();

  private @Valid DatiSoggettoTerzo datiSoggettoTerzo = null;

  private @Valid DatiBeneficiarioPG datiBeneficiarioPG = null;

  private @Valid DatiPagamento datiPagamento = null;

  private @Valid Reimpiego datiReimpiego = null;

  /**
   **/
  public PercipienteExtendedInfo percentualeSuddivisione(BigDecimal percentualeSuddivisione) {
    this.percentualeSuddivisione = percentualeSuddivisione;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("percentualeSuddivisione")
  

  public BigDecimal getPercentualeSuddivisione() {
    return percentualeSuddivisione;
  }
  public void setPercentualeSuddivisione(BigDecimal percentualeSuddivisione) {
    this.percentualeSuddivisione = percentualeSuddivisione;
  }

  /**
   **/
  public PercipienteExtendedInfo importoSuddivisione(BigDecimal importoSuddivisione) {
    this.importoSuddivisione = importoSuddivisione;
    return this;
  }

  
  @ApiModelProperty(example = "20000000.01", value = "")
  @JsonProperty("importoSuddivisione")
  

  public BigDecimal getImportoSuddivisione() {
    return importoSuddivisione;
  }
  public void setImportoSuddivisione(BigDecimal importoSuddivisione) {
    this.importoSuddivisione = importoSuddivisione;
  }

  /**
   **/
  public PercipienteExtendedInfo progrTst075(String progrTst075) {
    this.progrTst075 = progrTst075;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("progrTst075")
  

  public String getProgrTst075() {
    return progrTst075;
  }
  public void setProgrTst075(String progrTst075) {
    this.progrTst075 = progrTst075;
  }

  /**
   **/
  public PercipienteExtendedInfo progressivo(String progressivo) {
    this.progressivo = progressivo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("progressivo")
  

  public String getProgressivo() {
    return progressivo;
  }
  public void setProgressivo(String progressivo) {
    this.progressivo = progressivo;
  }

  /**
   **/
  public PercipienteExtendedInfo codAnag(String codAnag) {
    this.codAnag = codAnag;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codAnag")
  

  public String getCodAnag() {
    return codAnag;
  }
  public void setCodAnag(String codAnag) {
    this.codAnag = codAnag;
  }

  /**
   **/
  public PercipienteExtendedInfo percip(String percip) {
    this.percip = percip;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("percip")
  

  public String getPercip() {
    return percip;
  }
  public void setPercip(String percip) {
    this.percip = percip;
  }

  /**
   **/
  public PercipienteExtendedInfo relazioneBeneficiarioContraente(String relazioneBeneficiarioContraente) {
    this.relazioneBeneficiarioContraente = relazioneBeneficiarioContraente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioContraente")
  

  public String getRelazioneBeneficiarioContraente() {
    return relazioneBeneficiarioContraente;
  }
  public void setRelazioneBeneficiarioContraente(String relazioneBeneficiarioContraente) {
    this.relazioneBeneficiarioContraente = relazioneBeneficiarioContraente;
  }

  /**
   **/
  public PercipienteExtendedInfo relazioneBeneficiarioContraenteAltro(String relazioneBeneficiarioContraenteAltro) {
    this.relazioneBeneficiarioContraenteAltro = relazioneBeneficiarioContraenteAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioContraenteAltro")
  

  public String getRelazioneBeneficiarioContraenteAltro() {
    return relazioneBeneficiarioContraenteAltro;
  }
  public void setRelazioneBeneficiarioContraenteAltro(String relazioneBeneficiarioContraenteAltro) {
    this.relazioneBeneficiarioContraenteAltro = relazioneBeneficiarioContraenteAltro;
  }

  /**
   **/
  public PercipienteExtendedInfo elencoBeneficiarioSoggettoTerzo(List<BeneficiarioSoggettoTerzoInfo> elencoBeneficiarioSoggettoTerzo) {
    this.elencoBeneficiarioSoggettoTerzo = elencoBeneficiarioSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoBeneficiarioSoggettoTerzo")
  

  public List<BeneficiarioSoggettoTerzoInfo> getElencoBeneficiarioSoggettoTerzo() {
    return elencoBeneficiarioSoggettoTerzo;
  }
  public void setElencoBeneficiarioSoggettoTerzo(List<BeneficiarioSoggettoTerzoInfo> elencoBeneficiarioSoggettoTerzo) {
    this.elencoBeneficiarioSoggettoTerzo = elencoBeneficiarioSoggettoTerzo;
  }

  /**
   **/
  public PercipienteExtendedInfo datiSoggettoTerzo(DatiSoggettoTerzo datiSoggettoTerzo) {
    this.datiSoggettoTerzo = datiSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiSoggettoTerzo")
  

  public DatiSoggettoTerzo getDatiSoggettoTerzo() {
    return datiSoggettoTerzo;
  }
  public void setDatiSoggettoTerzo(DatiSoggettoTerzo datiSoggettoTerzo) {
    this.datiSoggettoTerzo = datiSoggettoTerzo;
  }

  /**
   **/
  public PercipienteExtendedInfo datiBeneficiarioPG(DatiBeneficiarioPG datiBeneficiarioPG) {
    this.datiBeneficiarioPG = datiBeneficiarioPG;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiBeneficiarioPG")
  

  public DatiBeneficiarioPG getDatiBeneficiarioPG() {
    return datiBeneficiarioPG;
  }
  public void setDatiBeneficiarioPG(DatiBeneficiarioPG datiBeneficiarioPG) {
    this.datiBeneficiarioPG = datiBeneficiarioPG;
  }

  /**
   **/
  public PercipienteExtendedInfo datiPagamento(DatiPagamento datiPagamento) {
    this.datiPagamento = datiPagamento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiPagamento")
  

  public DatiPagamento getDatiPagamento() {
    return datiPagamento;
  }
  public void setDatiPagamento(DatiPagamento datiPagamento) {
    this.datiPagamento = datiPagamento;
  }

  /**
   **/
  public PercipienteExtendedInfo datiReimpiego(Reimpiego datiReimpiego) {
    this.datiReimpiego = datiReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiReimpiego")
  

  public Reimpiego getDatiReimpiego() {
    return datiReimpiego;
  }
  public void setDatiReimpiego(Reimpiego datiReimpiego) {
    this.datiReimpiego = datiReimpiego;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PercipienteExtendedInfo percipienteExtendedInfo = (PercipienteExtendedInfo) o;
    return Objects.equals(percentualeSuddivisione, percipienteExtendedInfo.percentualeSuddivisione) &&
        Objects.equals(importoSuddivisione, percipienteExtendedInfo.importoSuddivisione) &&
        Objects.equals(progrTst075, percipienteExtendedInfo.progrTst075) &&
        Objects.equals(progressivo, percipienteExtendedInfo.progressivo) &&
        Objects.equals(codAnag, percipienteExtendedInfo.codAnag) &&
        Objects.equals(percip, percipienteExtendedInfo.percip) &&
        Objects.equals(relazioneBeneficiarioContraente, percipienteExtendedInfo.relazioneBeneficiarioContraente) &&
        Objects.equals(relazioneBeneficiarioContraenteAltro, percipienteExtendedInfo.relazioneBeneficiarioContraenteAltro) &&
        Objects.equals(elencoBeneficiarioSoggettoTerzo, percipienteExtendedInfo.elencoBeneficiarioSoggettoTerzo) &&
        Objects.equals(datiSoggettoTerzo, percipienteExtendedInfo.datiSoggettoTerzo) &&
        Objects.equals(datiBeneficiarioPG, percipienteExtendedInfo.datiBeneficiarioPG) &&
        Objects.equals(datiPagamento, percipienteExtendedInfo.datiPagamento) &&
        Objects.equals(datiReimpiego, percipienteExtendedInfo.datiReimpiego);
  }

  @Override
  public int hashCode() {
    return Objects.hash(percentualeSuddivisione, importoSuddivisione, progrTst075, progressivo, codAnag, percip, relazioneBeneficiarioContraente, relazioneBeneficiarioContraenteAltro, elencoBeneficiarioSoggettoTerzo, datiSoggettoTerzo, datiBeneficiarioPG, datiPagamento, datiReimpiego);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PercipienteExtendedInfo {\n");
    
    sb.append("    percentualeSuddivisione: ").append(toIndentedString(percentualeSuddivisione)).append("\n");
    sb.append("    importoSuddivisione: ").append(toIndentedString(importoSuddivisione)).append("\n");
    sb.append("    progrTst075: ").append(toIndentedString(progrTst075)).append("\n");
    sb.append("    progressivo: ").append(toIndentedString(progressivo)).append("\n");
    sb.append("    codAnag: ").append(toIndentedString(codAnag)).append("\n");
    sb.append("    percip: ").append(toIndentedString(percip)).append("\n");
    sb.append("    relazioneBeneficiarioContraente: ").append(toIndentedString(relazioneBeneficiarioContraente)).append("\n");
    sb.append("    relazioneBeneficiarioContraenteAltro: ").append(toIndentedString(relazioneBeneficiarioContraenteAltro)).append("\n");
    sb.append("    elencoBeneficiarioSoggettoTerzo: ").append(toIndentedString(elencoBeneficiarioSoggettoTerzo)).append("\n");
    sb.append("    datiSoggettoTerzo: ").append(toIndentedString(datiSoggettoTerzo)).append("\n");
    sb.append("    datiBeneficiarioPG: ").append(toIndentedString(datiBeneficiarioPG)).append("\n");
    sb.append("    datiPagamento: ").append(toIndentedString(datiPagamento)).append("\n");
    sb.append("    datiReimpiego: ").append(toIndentedString(datiReimpiego)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
