package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;
import it.sistinf.rest.cobol.model.proposta.common.DatiValutazioneIntermediario;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ControllaOpzContrattualiRequest   {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid RppInfo rppInfo = null;

  private @Valid LifeCycleInfo lifeCycleInfo = null;

  private @Valid TakeProfitInfo takeProfitInfo = null;

  private @Valid Date dataOperazione = null;

  private @Valid DatiValutazioneIntermediario datiValutazioneIntermediario = null;

  private @Valid String utente = null;

  /**
   **/
  public ControllaOpzContrattualiRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest rppInfo(RppInfo rppInfo) {
    this.rppInfo = rppInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("rppInfo")

  public RppInfo getRppInfo() {
    return rppInfo;
  }
  public void setRppInfo(RppInfo rppInfo) {
    this.rppInfo = rppInfo;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest lifeCycleInfo(LifeCycleInfo lifeCycleInfo) {
    this.lifeCycleInfo = lifeCycleInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("lifeCycleInfo")

  public LifeCycleInfo getLifeCycleInfo() {
    return lifeCycleInfo;
  }
  public void setLifeCycleInfo(LifeCycleInfo lifeCycleInfo) {
    this.lifeCycleInfo = lifeCycleInfo;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest takeProfitInfo(TakeProfitInfo takeProfitInfo) {
    this.takeProfitInfo = takeProfitInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("takeProfitInfo")

  public TakeProfitInfo getTakeProfitInfo() {
    return takeProfitInfo;
  }
  public void setTakeProfitInfo(TakeProfitInfo takeProfitInfo) {
    this.takeProfitInfo = takeProfitInfo;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest dataOperazione(Date dataOperazione) {
    this.dataOperazione = dataOperazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataOperazione")

  public Date getDataOperazione() {
    return dataOperazione;
  }
  public void setDataOperazione(Date dataOperazione) {
    this.dataOperazione = dataOperazione;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest datiValutazioneIntermediario(DatiValutazioneIntermediario datiValutazioneIntermediario) {
    this.datiValutazioneIntermediario = datiValutazioneIntermediario;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("datiValutazioneIntermediario")
  @NotNull

  public DatiValutazioneIntermediario getDatiValutazioneIntermediario() {
    return datiValutazioneIntermediario;
  }
  public void setDatiValutazioneIntermediario(DatiValutazioneIntermediario datiValutazioneIntermediario) {
    this.datiValutazioneIntermediario = datiValutazioneIntermediario;
  }

  /**
   **/
  public ControllaOpzContrattualiRequest utente(String utente) {
    this.utente = utente;
    return this;
  }

  
  @ApiModelProperty(example = "C1234567", required = true, value = "")
  @JsonProperty("utente")
  @NotNull

  public String getUtente() {
    return utente;
  }
  public void setUtente(String utente) {
    this.utente = utente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControllaOpzContrattualiRequest controllaOpzContrattualiRequest = (ControllaOpzContrattualiRequest) o;
    return Objects.equals(polizzaInfo, controllaOpzContrattualiRequest.polizzaInfo) &&
        Objects.equals(rppInfo, controllaOpzContrattualiRequest.rppInfo) &&
        Objects.equals(lifeCycleInfo, controllaOpzContrattualiRequest.lifeCycleInfo) &&
        Objects.equals(takeProfitInfo, controllaOpzContrattualiRequest.takeProfitInfo) &&
        Objects.equals(dataOperazione, controllaOpzContrattualiRequest.dataOperazione) &&
        Objects.equals(datiValutazioneIntermediario, controllaOpzContrattualiRequest.datiValutazioneIntermediario) &&
        Objects.equals(utente, controllaOpzContrattualiRequest.utente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, rppInfo, lifeCycleInfo, takeProfitInfo, dataOperazione, datiValutazioneIntermediario, utente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControllaOpzContrattualiRequest {\n");
    
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    rppInfo: ").append(toIndentedString(rppInfo)).append("\n");
    sb.append("    lifeCycleInfo: ").append(toIndentedString(lifeCycleInfo)).append("\n");
    sb.append("    takeProfitInfo: ").append(toIndentedString(takeProfitInfo)).append("\n");
    sb.append("    dataOperazione: ").append(toIndentedString(dataOperazione)).append("\n");
    sb.append("    datiValutazioneIntermediario: ").append(toIndentedString(datiValutazioneIntermediario)).append("\n");
    sb.append("    utente: ").append(toIndentedString(utente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
