package it.sistinf.rest.cobol.model.dominio;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ElementoDominio   {

  private @Valid String codice = null;

  private @Valid String descrizione = null;

  /**
   **/
  public ElementoDominio codice(String codice) {
    this.codice = codice;
    return this;
  }

  
  @ApiModelProperty(example = "DOMINIO1", required = true, value = "")
  @JsonProperty("codice")
  @NotNull

  public String getCodice() {
    return codice;
  }
  public void setCodice(String codice) {
    this.codice = codice;
  }

  /**
   **/
  public ElementoDominio descrizione(String descrizione) {
    this.descrizione = descrizione;
    return this;
  }

  
  @ApiModelProperty(example = "DESCRIZIONE DOMINIO 1", required = true, value = "")
  @JsonProperty("descrizione")
  @NotNull

  public String getDescrizione() {
    return descrizione;
  }
  public void setDescrizione(String descrizione) {
    this.descrizione = descrizione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ElementoDominio elementoDominio = (ElementoDominio) o;
    return Objects.equals(codice, elementoDominio.codice) &&
        Objects.equals(descrizione, elementoDominio.descrizione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codice, descrizione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ElementoDominio {\n");
    
    sb.append("    codice: ").append(toIndentedString(codice)).append("\n");
    sb.append("    descrizione: ").append(toIndentedString(descrizione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
