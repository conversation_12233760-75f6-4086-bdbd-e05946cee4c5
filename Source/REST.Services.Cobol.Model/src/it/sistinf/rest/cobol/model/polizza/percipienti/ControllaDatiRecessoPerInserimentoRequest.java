package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.polizza.recesso.RecessoDatiParametri;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniCommon;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniRimborso;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaSimpleEstesa;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ControllaDatiRecessoPerInserimentoRequest   {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid StorniRimborso storniRimborso = null;

  private @Valid StorniCommon storniCommon = null;

  private @Valid RecessoDatiParametri recessoDatiParametri = null;

  private @Valid PolizzaSimpleEstesa polizzaSimpleEstesa = null;

  private @Valid Boolean checkGestioneBloccoConteggio = null;

  private @Valid String username = null;

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest storniRimborso(StorniRimborso storniRimborso) {
    this.storniRimborso = storniRimborso;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("storniRimborso")
  @NotNull

  public StorniRimborso getStorniRimborso() {
    return storniRimborso;
  }
  public void setStorniRimborso(StorniRimborso storniRimborso) {
    this.storniRimborso = storniRimborso;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest storniCommon(StorniCommon storniCommon) {
    this.storniCommon = storniCommon;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("storniCommon")
  @NotNull

  public StorniCommon getStorniCommon() {
    return storniCommon;
  }
  public void setStorniCommon(StorniCommon storniCommon) {
    this.storniCommon = storniCommon;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest recessoDatiParametri(RecessoDatiParametri recessoDatiParametri) {
    this.recessoDatiParametri = recessoDatiParametri;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("recessoDatiParametri")
  @NotNull

  public RecessoDatiParametri getRecessoDatiParametri() {
    return recessoDatiParametri;
  }
  public void setRecessoDatiParametri(RecessoDatiParametri recessoDatiParametri) {
    this.recessoDatiParametri = recessoDatiParametri;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest polizzaSimpleEstesa(PolizzaSimpleEstesa polizzaSimpleEstesa) {
    this.polizzaSimpleEstesa = polizzaSimpleEstesa;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("polizzaSimpleEstesa")
  @NotNull

  public PolizzaSimpleEstesa getPolizzaSimpleEstesa() {
    return polizzaSimpleEstesa;
  }
  public void setPolizzaSimpleEstesa(PolizzaSimpleEstesa polizzaSimpleEstesa) {
    this.polizzaSimpleEstesa = polizzaSimpleEstesa;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest checkGestioneBloccoConteggio(Boolean checkGestioneBloccoConteggio) {
    this.checkGestioneBloccoConteggio = checkGestioneBloccoConteggio;
    return this;
  }

  
  @ApiModelProperty(example = "true", required = true, value = "")
  @JsonProperty("checkGestioneBloccoConteggio")
  @NotNull

  public Boolean isCheckGestioneBloccoConteggio() {
    return checkGestioneBloccoConteggio;
  }
  public void setCheckGestioneBloccoConteggio(Boolean checkGestioneBloccoConteggio) {
    this.checkGestioneBloccoConteggio = checkGestioneBloccoConteggio;
  }

  /**
   **/
  public ControllaDatiRecessoPerInserimentoRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControllaDatiRecessoPerInserimentoRequest controllaDatiRecessoPerInserimentoRequest = (ControllaDatiRecessoPerInserimentoRequest) o;
    return Objects.equals(polizzaInfo, controllaDatiRecessoPerInserimentoRequest.polizzaInfo) &&
        Objects.equals(storniRimborso, controllaDatiRecessoPerInserimentoRequest.storniRimborso) &&
        Objects.equals(storniCommon, controllaDatiRecessoPerInserimentoRequest.storniCommon) &&
        Objects.equals(recessoDatiParametri, controllaDatiRecessoPerInserimentoRequest.recessoDatiParametri) &&
        Objects.equals(polizzaSimpleEstesa, controllaDatiRecessoPerInserimentoRequest.polizzaSimpleEstesa) &&
        Objects.equals(checkGestioneBloccoConteggio, controllaDatiRecessoPerInserimentoRequest.checkGestioneBloccoConteggio) &&
        Objects.equals(username, controllaDatiRecessoPerInserimentoRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, storniRimborso, storniCommon, recessoDatiParametri, polizzaSimpleEstesa, checkGestioneBloccoConteggio, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControllaDatiRecessoPerInserimentoRequest {\n");
    
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    storniRimborso: ").append(toIndentedString(storniRimborso)).append("\n");
    sb.append("    storniCommon: ").append(toIndentedString(storniCommon)).append("\n");
    sb.append("    recessoDatiParametri: ").append(toIndentedString(recessoDatiParametri)).append("\n");
    sb.append("    polizzaSimpleEstesa: ").append(toIndentedString(polizzaSimpleEstesa)).append("\n");
    sb.append("    checkGestioneBloccoConteggio: ").append(toIndentedString(checkGestioneBloccoConteggio)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
