package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class FiguraAnagraficaRelazionataInfo   {

  private @Valid Long codiceClientePrincipale = null;

  private @Valid Long codiceClienteRelazionato = null;

public enum TipoRelazioneEnum {

    L(String.valueOf("L")), T(String.valueOf("T"));


    private String value;

    TipoRelazioneEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoRelazioneEnum fromValue(String v) {
        for (TipoRelazioneEnum b : TipoRelazioneEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoRelazioneEnum tipoRelazione = null;

  private @Valid String qualificaSoggPrincipale = null;

  private @Valid String qualificaSoggRelazionato = null;

  private @Valid String qualificaAltroSoggRelazionato = null;

public enum RuoloPrincipaleEnum {

    A(String.valueOf("A")), C(String.valueOf("C")), V(String.valueOf("V")), R(String.valueOf("R"));


    private String value;

    RuoloPrincipaleEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static RuoloPrincipaleEnum fromValue(String v) {
        for (RuoloPrincipaleEnum b : RuoloPrincipaleEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid RuoloPrincipaleEnum ruoloPrincipale = null;

  private @Valid BigDecimal percOwnership = null;

  private @Valid Boolean fiducianteAltraPolizza = null;

  /**
   **/
  public FiguraAnagraficaRelazionataInfo codiceClientePrincipale(Long codiceClientePrincipale) {
    this.codiceClientePrincipale = codiceClientePrincipale;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("codiceClientePrincipale")
  @NotNull

  public Long getCodiceClientePrincipale() {
    return codiceClientePrincipale;
  }
  public void setCodiceClientePrincipale(Long codiceClientePrincipale) {
    this.codiceClientePrincipale = codiceClientePrincipale;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo codiceClienteRelazionato(Long codiceClienteRelazionato) {
    this.codiceClienteRelazionato = codiceClienteRelazionato;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("codiceClienteRelazionato")
  @NotNull

  public Long getCodiceClienteRelazionato() {
    return codiceClienteRelazionato;
  }
  public void setCodiceClienteRelazionato(Long codiceClienteRelazionato) {
    this.codiceClienteRelazionato = codiceClienteRelazionato;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo tipoRelazione(TipoRelazioneEnum tipoRelazione) {
    this.tipoRelazione = tipoRelazione;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("tipoRelazione")
  @NotNull

  public TipoRelazioneEnum getTipoRelazione() {
    return tipoRelazione;
  }
  public void setTipoRelazione(TipoRelazioneEnum tipoRelazione) {
    this.tipoRelazione = tipoRelazione;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo qualificaSoggPrincipale(String qualificaSoggPrincipale) {
    this.qualificaSoggPrincipale = qualificaSoggPrincipale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("qualificaSoggPrincipale")

  public String getQualificaSoggPrincipale() {
    return qualificaSoggPrincipale;
  }
  public void setQualificaSoggPrincipale(String qualificaSoggPrincipale) {
    this.qualificaSoggPrincipale = qualificaSoggPrincipale;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo qualificaSoggRelazionato(String qualificaSoggRelazionato) {
    this.qualificaSoggRelazionato = qualificaSoggRelazionato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("qualificaSoggRelazionato")

  public String getQualificaSoggRelazionato() {
    return qualificaSoggRelazionato;
  }
  public void setQualificaSoggRelazionato(String qualificaSoggRelazionato) {
    this.qualificaSoggRelazionato = qualificaSoggRelazionato;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo qualificaAltroSoggRelazionato(String qualificaAltroSoggRelazionato) {
    this.qualificaAltroSoggRelazionato = qualificaAltroSoggRelazionato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("qualificaAltroSoggRelazionato")

  public String getQualificaAltroSoggRelazionato() {
    return qualificaAltroSoggRelazionato;
  }
  public void setQualificaAltroSoggRelazionato(String qualificaAltroSoggRelazionato) {
    this.qualificaAltroSoggRelazionato = qualificaAltroSoggRelazionato;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo ruoloPrincipale(RuoloPrincipaleEnum ruoloPrincipale) {
    this.ruoloPrincipale = ruoloPrincipale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ruoloPrincipale")

  public RuoloPrincipaleEnum getRuoloPrincipale() {
    return ruoloPrincipale;
  }
  public void setRuoloPrincipale(RuoloPrincipaleEnum ruoloPrincipale) {
    this.ruoloPrincipale = ruoloPrincipale;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo percOwnership(BigDecimal percOwnership) {
    this.percOwnership = percOwnership;
    return this;
  }

  
  @ApiModelProperty(example = "99.01", value = "")
  @JsonProperty("percOwnership")

  public BigDecimal getPercOwnership() {
    return percOwnership;
  }
  public void setPercOwnership(BigDecimal percOwnership) {
    this.percOwnership = percOwnership;
  }

  /**
   **/
  public FiguraAnagraficaRelazionataInfo fiducianteAltraPolizza(Boolean fiducianteAltraPolizza) {
    this.fiducianteAltraPolizza = fiducianteAltraPolizza;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("fiducianteAltraPolizza")

  public Boolean isFiducianteAltraPolizza() {
    return fiducianteAltraPolizza;
  }
  public void setFiducianteAltraPolizza(Boolean fiducianteAltraPolizza) {
    this.fiducianteAltraPolizza = fiducianteAltraPolizza;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionataInfo = (FiguraAnagraficaRelazionataInfo) o;
    return Objects.equals(codiceClientePrincipale, figuraAnagraficaRelazionataInfo.codiceClientePrincipale) &&
        Objects.equals(codiceClienteRelazionato, figuraAnagraficaRelazionataInfo.codiceClienteRelazionato) &&
        Objects.equals(tipoRelazione, figuraAnagraficaRelazionataInfo.tipoRelazione) &&
        Objects.equals(qualificaSoggPrincipale, figuraAnagraficaRelazionataInfo.qualificaSoggPrincipale) &&
        Objects.equals(qualificaSoggRelazionato, figuraAnagraficaRelazionataInfo.qualificaSoggRelazionato) &&
        Objects.equals(qualificaAltroSoggRelazionato, figuraAnagraficaRelazionataInfo.qualificaAltroSoggRelazionato) &&
        Objects.equals(ruoloPrincipale, figuraAnagraficaRelazionataInfo.ruoloPrincipale) &&
        Objects.equals(percOwnership, figuraAnagraficaRelazionataInfo.percOwnership) &&
        Objects.equals(fiducianteAltraPolizza, figuraAnagraficaRelazionataInfo.fiducianteAltraPolizza);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceClientePrincipale, codiceClienteRelazionato, tipoRelazione, qualificaSoggPrincipale, qualificaSoggRelazionato, qualificaAltroSoggRelazionato, ruoloPrincipale, percOwnership, fiducianteAltraPolizza);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiguraAnagraficaRelazionataInfo {\n");
    
    sb.append("    codiceClientePrincipale: ").append(toIndentedString(codiceClientePrincipale)).append("\n");
    sb.append("    codiceClienteRelazionato: ").append(toIndentedString(codiceClienteRelazionato)).append("\n");
    sb.append("    tipoRelazione: ").append(toIndentedString(tipoRelazione)).append("\n");
    sb.append("    qualificaSoggPrincipale: ").append(toIndentedString(qualificaSoggPrincipale)).append("\n");
    sb.append("    qualificaSoggRelazionato: ").append(toIndentedString(qualificaSoggRelazionato)).append("\n");
    sb.append("    qualificaAltroSoggRelazionato: ").append(toIndentedString(qualificaAltroSoggRelazionato)).append("\n");
    sb.append("    ruoloPrincipale: ").append(toIndentedString(ruoloPrincipale)).append("\n");
    sb.append("    percOwnership: ").append(toIndentedString(percOwnership)).append("\n");
    sb.append("    fiducianteAltraPolizza: ").append(toIndentedString(fiducianteAltraPolizza)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
