package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class AnagrafiCondiviseRequest   {

  private @Valid List<CodClienteInfo> codiciCustomer = new ArrayList<CodClienteInfo>();

  /**
   **/
  public AnagrafiCondiviseRequest codiciCustomer(List<CodClienteInfo> codiciCustomer) {
    this.codiciCustomer = codiciCustomer;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiciCustomer")

  public List<CodClienteInfo> getCodiciCustomer() {
    return codiciCustomer;
  }
  public void setCodiciCustomer(List<CodClienteInfo> codiciCustomer) {
    this.codiciCustomer = codiciCustomer;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AnagrafiCondiviseRequest anagrafiCondiviseRequest = (AnagrafiCondiviseRequest) o;
    return Objects.equals(codiciCustomer, anagrafiCondiviseRequest.codiciCustomer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiciCustomer);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AnagrafiCondiviseRequest {\n");
    
    sb.append("    codiciCustomer: ").append(toIndentedString(codiciCustomer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
