package it.sistinf.rest.cobol.model.polizza.recesso;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class StorniRimborso   {

  private @Valid BigDecimal totaleRimborso = null;

  private @Valid BigDecimal totalePrestazioneUnit = null;

  private @Valid BigDecimal totaleRimborsoUnit = null;

  private @Valid BigDecimal totalePremioRiferimentoUnit = null;

  private @Valid BigDecimal totaleRimborsoGestSep = null;

  private @Valid BigDecimal totalePremioRiferimentoGestSep = null;

  private @Valid List<StorniRimborsoCompoGestSep> elencoRimborsoCompoGestSep = new ArrayList<StorniRimborsoCompoGestSep>();

  private @Valid List<StorniRimborsoCompoUnit> elencoRimborsoCompoUnit = new ArrayList<StorniRimborsoCompoUnit>();

  private @Valid BigDecimal tassoIntCorr = null;

  private @Valid BigDecimal tassoIntPrec = null;

  private @Valid Integer giorniIntCorr = null;

  private @Valid Integer giorniIntPrec = null;

  private @Valid Date dataLiquidPresunta = null;

  /**
   **/
  public StorniRimborso totaleRimborso(BigDecimal totaleRimborso) {
    this.totaleRimborso = totaleRimborso;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totaleRimborso")

  public BigDecimal getTotaleRimborso() {
    return totaleRimborso;
  }
  public void setTotaleRimborso(BigDecimal totaleRimborso) {
    this.totaleRimborso = totaleRimborso;
  }

  /**
   **/
  public StorniRimborso totalePrestazioneUnit(BigDecimal totalePrestazioneUnit) {
    this.totalePrestazioneUnit = totalePrestazioneUnit;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totalePrestazioneUnit")

  public BigDecimal getTotalePrestazioneUnit() {
    return totalePrestazioneUnit;
  }
  public void setTotalePrestazioneUnit(BigDecimal totalePrestazioneUnit) {
    this.totalePrestazioneUnit = totalePrestazioneUnit;
  }

  /**
   **/
  public StorniRimborso totaleRimborsoUnit(BigDecimal totaleRimborsoUnit) {
    this.totaleRimborsoUnit = totaleRimborsoUnit;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totaleRimborsoUnit")

  public BigDecimal getTotaleRimborsoUnit() {
    return totaleRimborsoUnit;
  }
  public void setTotaleRimborsoUnit(BigDecimal totaleRimborsoUnit) {
    this.totaleRimborsoUnit = totaleRimborsoUnit;
  }

  /**
   **/
  public StorniRimborso totalePremioRiferimentoUnit(BigDecimal totalePremioRiferimentoUnit) {
    this.totalePremioRiferimentoUnit = totalePremioRiferimentoUnit;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totalePremioRiferimentoUnit")

  public BigDecimal getTotalePremioRiferimentoUnit() {
    return totalePremioRiferimentoUnit;
  }
  public void setTotalePremioRiferimentoUnit(BigDecimal totalePremioRiferimentoUnit) {
    this.totalePremioRiferimentoUnit = totalePremioRiferimentoUnit;
  }

  /**
   **/
  public StorniRimborso totaleRimborsoGestSep(BigDecimal totaleRimborsoGestSep) {
    this.totaleRimborsoGestSep = totaleRimborsoGestSep;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totaleRimborsoGestSep")

  public BigDecimal getTotaleRimborsoGestSep() {
    return totaleRimborsoGestSep;
  }
  public void setTotaleRimborsoGestSep(BigDecimal totaleRimborsoGestSep) {
    this.totaleRimborsoGestSep = totaleRimborsoGestSep;
  }

  /**
   **/
  public StorniRimborso totalePremioRiferimentoGestSep(BigDecimal totalePremioRiferimentoGestSep) {
    this.totalePremioRiferimentoGestSep = totalePremioRiferimentoGestSep;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("totalePremioRiferimentoGestSep")

  public BigDecimal getTotalePremioRiferimentoGestSep() {
    return totalePremioRiferimentoGestSep;
  }
  public void setTotalePremioRiferimentoGestSep(BigDecimal totalePremioRiferimentoGestSep) {
    this.totalePremioRiferimentoGestSep = totalePremioRiferimentoGestSep;
  }

  /**
   **/
  public StorniRimborso elencoRimborsoCompoGestSep(List<StorniRimborsoCompoGestSep> elencoRimborsoCompoGestSep) {
    this.elencoRimborsoCompoGestSep = elencoRimborsoCompoGestSep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoRimborsoCompoGestSep")

  public List<StorniRimborsoCompoGestSep> getElencoRimborsoCompoGestSep() {
    return elencoRimborsoCompoGestSep;
  }
  public void setElencoRimborsoCompoGestSep(List<StorniRimborsoCompoGestSep> elencoRimborsoCompoGestSep) {
    this.elencoRimborsoCompoGestSep = elencoRimborsoCompoGestSep;
  }

  /**
   **/
  public StorniRimborso elencoRimborsoCompoUnit(List<StorniRimborsoCompoUnit> elencoRimborsoCompoUnit) {
    this.elencoRimborsoCompoUnit = elencoRimborsoCompoUnit;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoRimborsoCompoUnit")

  public List<StorniRimborsoCompoUnit> getElencoRimborsoCompoUnit() {
    return elencoRimborsoCompoUnit;
  }
  public void setElencoRimborsoCompoUnit(List<StorniRimborsoCompoUnit> elencoRimborsoCompoUnit) {
    this.elencoRimborsoCompoUnit = elencoRimborsoCompoUnit;
  }

  /**
   **/
  public StorniRimborso tassoIntCorr(BigDecimal tassoIntCorr) {
    this.tassoIntCorr = tassoIntCorr;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("tassoIntCorr")

  public BigDecimal getTassoIntCorr() {
    return tassoIntCorr;
  }
  public void setTassoIntCorr(BigDecimal tassoIntCorr) {
    this.tassoIntCorr = tassoIntCorr;
  }

  /**
   **/
  public StorniRimborso tassoIntPrec(BigDecimal tassoIntPrec) {
    this.tassoIntPrec = tassoIntPrec;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("tassoIntPrec")

  public BigDecimal getTassoIntPrec() {
    return tassoIntPrec;
  }
  public void setTassoIntPrec(BigDecimal tassoIntPrec) {
    this.tassoIntPrec = tassoIntPrec;
  }

  /**
   **/
  public StorniRimborso giorniIntCorr(Integer giorniIntCorr) {
    this.giorniIntCorr = giorniIntCorr;
    return this;
  }

  
  @ApiModelProperty(example = "12", value = "")
  @JsonProperty("giorniIntCorr")

  public Integer getGiorniIntCorr() {
    return giorniIntCorr;
  }
  public void setGiorniIntCorr(Integer giorniIntCorr) {
    this.giorniIntCorr = giorniIntCorr;
  }

  /**
   **/
  public StorniRimborso giorniIntPrec(Integer giorniIntPrec) {
    this.giorniIntPrec = giorniIntPrec;
    return this;
  }

  
  @ApiModelProperty(example = "12", value = "")
  @JsonProperty("giorniIntPrec")

  public Integer getGiorniIntPrec() {
    return giorniIntPrec;
  }
  public void setGiorniIntPrec(Integer giorniIntPrec) {
    this.giorniIntPrec = giorniIntPrec;
  }

  /**
   **/
  public StorniRimborso dataLiquidPresunta(Date dataLiquidPresunta) {
    this.dataLiquidPresunta = dataLiquidPresunta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataLiquidPresunta")

  public Date getDataLiquidPresunta() {
    return dataLiquidPresunta;
  }
  public void setDataLiquidPresunta(Date dataLiquidPresunta) {
    this.dataLiquidPresunta = dataLiquidPresunta;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StorniRimborso storniRimborso = (StorniRimborso) o;
    return Objects.equals(totaleRimborso, storniRimborso.totaleRimborso) &&
        Objects.equals(totalePrestazioneUnit, storniRimborso.totalePrestazioneUnit) &&
        Objects.equals(totaleRimborsoUnit, storniRimborso.totaleRimborsoUnit) &&
        Objects.equals(totalePremioRiferimentoUnit, storniRimborso.totalePremioRiferimentoUnit) &&
        Objects.equals(totaleRimborsoGestSep, storniRimborso.totaleRimborsoGestSep) &&
        Objects.equals(totalePremioRiferimentoGestSep, storniRimborso.totalePremioRiferimentoGestSep) &&
        Objects.equals(elencoRimborsoCompoGestSep, storniRimborso.elencoRimborsoCompoGestSep) &&
        Objects.equals(elencoRimborsoCompoUnit, storniRimborso.elencoRimborsoCompoUnit) &&
        Objects.equals(tassoIntCorr, storniRimborso.tassoIntCorr) &&
        Objects.equals(tassoIntPrec, storniRimborso.tassoIntPrec) &&
        Objects.equals(giorniIntCorr, storniRimborso.giorniIntCorr) &&
        Objects.equals(giorniIntPrec, storniRimborso.giorniIntPrec) &&
        Objects.equals(dataLiquidPresunta, storniRimborso.dataLiquidPresunta);
  }

  @Override
  public int hashCode() {
    return Objects.hash(totaleRimborso, totalePrestazioneUnit, totaleRimborsoUnit, totalePremioRiferimentoUnit, totaleRimborsoGestSep, totalePremioRiferimentoGestSep, elencoRimborsoCompoGestSep, elencoRimborsoCompoUnit, tassoIntCorr, tassoIntPrec, giorniIntCorr, giorniIntPrec, dataLiquidPresunta);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StorniRimborso {\n");
    
    sb.append("    totaleRimborso: ").append(toIndentedString(totaleRimborso)).append("\n");
    sb.append("    totalePrestazioneUnit: ").append(toIndentedString(totalePrestazioneUnit)).append("\n");
    sb.append("    totaleRimborsoUnit: ").append(toIndentedString(totaleRimborsoUnit)).append("\n");
    sb.append("    totalePremioRiferimentoUnit: ").append(toIndentedString(totalePremioRiferimentoUnit)).append("\n");
    sb.append("    totaleRimborsoGestSep: ").append(toIndentedString(totaleRimborsoGestSep)).append("\n");
    sb.append("    totalePremioRiferimentoGestSep: ").append(toIndentedString(totalePremioRiferimentoGestSep)).append("\n");
    sb.append("    elencoRimborsoCompoGestSep: ").append(toIndentedString(elencoRimborsoCompoGestSep)).append("\n");
    sb.append("    elencoRimborsoCompoUnit: ").append(toIndentedString(elencoRimborsoCompoUnit)).append("\n");
    sb.append("    tassoIntCorr: ").append(toIndentedString(tassoIntCorr)).append("\n");
    sb.append("    tassoIntPrec: ").append(toIndentedString(tassoIntPrec)).append("\n");
    sb.append("    giorniIntCorr: ").append(toIndentedString(giorniIntCorr)).append("\n");
    sb.append("    giorniIntPrec: ").append(toIndentedString(giorniIntPrec)).append("\n");
    sb.append("    dataLiquidPresunta: ").append(toIndentedString(dataLiquidPresunta)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
