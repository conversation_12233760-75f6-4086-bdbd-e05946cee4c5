package it.sistinf.rest.cobol.model.blocco.autorizzativo;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.proposta.common.RapportoInfo;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class AutorizzaBlocchiRequest   {

  private @Valid RapportoInfo rapportoInfo = null;

  private @Valid Long idPrenotazione = null;

  private @Valid InfoPerAutorizzazioneBlocco infoPerAutorizzazioneBlocco = null;

  private @Valid String username = null;

  /**
   **/
  public AutorizzaBlocchiRequest rapportoInfo(RapportoInfo rapportoInfo) {
    this.rapportoInfo = rapportoInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("rapportoInfo")
  @NotNull

  public RapportoInfo getRapportoInfo() {
    return rapportoInfo;
  }
  public void setRapportoInfo(RapportoInfo rapportoInfo) {
    this.rapportoInfo = rapportoInfo;
  }

  /**
   **/
  public AutorizzaBlocchiRequest idPrenotazione(Long idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("idPrenotazione")
  @NotNull

  public Long getIdPrenotazione() {
    return idPrenotazione;
  }
  public void setIdPrenotazione(Long idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
  }

  /**
   **/
  public AutorizzaBlocchiRequest infoPerAutorizzazioneBlocco(InfoPerAutorizzazioneBlocco infoPerAutorizzazioneBlocco) {
    this.infoPerAutorizzazioneBlocco = infoPerAutorizzazioneBlocco;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("infoPerAutorizzazioneBlocco")
  @NotNull

  public InfoPerAutorizzazioneBlocco getInfoPerAutorizzazioneBlocco() {
    return infoPerAutorizzazioneBlocco;
  }
  public void setInfoPerAutorizzazioneBlocco(InfoPerAutorizzazioneBlocco infoPerAutorizzazioneBlocco) {
    this.infoPerAutorizzazioneBlocco = infoPerAutorizzazioneBlocco;
  }

  /**
   **/
  public AutorizzaBlocchiRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AutorizzaBlocchiRequest autorizzaBlocchiRequest = (AutorizzaBlocchiRequest) o;
    return Objects.equals(rapportoInfo, autorizzaBlocchiRequest.rapportoInfo) &&
        Objects.equals(idPrenotazione, autorizzaBlocchiRequest.idPrenotazione) &&
        Objects.equals(infoPerAutorizzazioneBlocco, autorizzaBlocchiRequest.infoPerAutorizzazioneBlocco) &&
        Objects.equals(username, autorizzaBlocchiRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rapportoInfo, idPrenotazione, infoPerAutorizzazioneBlocco, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AutorizzaBlocchiRequest {\n");
    
    sb.append("    rapportoInfo: ").append(toIndentedString(rapportoInfo)).append("\n");
    sb.append("    idPrenotazione: ").append(toIndentedString(idPrenotazione)).append("\n");
    sb.append("    infoPerAutorizzazioneBlocco: ").append(toIndentedString(infoPerAutorizzazioneBlocco)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
