package it.sistinf.rest.cobol.model.commarea;

import javax.validation.constraints.Size;
import javax.validation.Valid;


import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;


public class DecodificaCodiciErrore   {

  private @Valid String codiceErrore = "0000";

  private @Valid String descrizioneErrore = null;

  /**
   **/
  public DecodificaCodiciErrore codiceErrore(String codiceErrore) {
    this.codiceErrore = codiceErrore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceErrore")
 @Size(min=4,max=4)
  public String getCodiceErrore() {
    return codiceErrore;
  }
  public void setCodiceErrore(String codiceErrore) {
    this.codiceErrore = codiceErrore;
  }

  /**
   **/
  public DecodificaCodiciErrore descrizioneErrore(String descrizioneErrore) {
    this.descrizioneErrore = descrizioneErrore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrizioneErrore")
 @Size(max=80)
  public String getDescrizioneErrore() {
    return descrizioneErrore;
  }
  public void setDescrizioneErrore(String descrizioneErrore) {
    this.descrizioneErrore = descrizioneErrore;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DecodificaCodiciErrore decodificaCodiciErrore = (DecodificaCodiciErrore) o;
    return Objects.equals(codiceErrore, decodificaCodiciErrore.codiceErrore) &&
        Objects.equals(descrizioneErrore, decodificaCodiciErrore.descrizioneErrore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceErrore, descrizioneErrore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DecodificaCodiciErrore {\n");
    
    sb.append("    codiceErrore: ").append(toIndentedString(codiceErrore)).append("\n");
    sb.append("    descrizioneErrore: ").append(toIndentedString(descrizioneErrore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
