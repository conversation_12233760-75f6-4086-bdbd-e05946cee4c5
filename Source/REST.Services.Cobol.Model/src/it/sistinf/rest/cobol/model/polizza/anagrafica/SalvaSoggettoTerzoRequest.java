package it.sistinf.rest.cobol.model.polizza.anagrafica;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class SalvaSoggettoTerzoRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid DatiCustomer datiCustomer = null;

  /**
   **/
  public SalvaSoggettoTerzoRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public SalvaSoggettoTerzoRequest datiCustomer(DatiCustomer datiCustomer) {
    this.datiCustomer = datiCustomer;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("datiCustomer")
  @NotNull

  public DatiCustomer getDatiCustomer() {
    return datiCustomer;
  }
  public void setDatiCustomer(DatiCustomer datiCustomer) {
    this.datiCustomer = datiCustomer;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SalvaSoggettoTerzoRequest salvaSoggettoTerzoRequest = (SalvaSoggettoTerzoRequest) o;
    return Objects.equals(headerCobolSrv, salvaSoggettoTerzoRequest.headerCobolSrv) &&
        Objects.equals(datiCustomer, salvaSoggettoTerzoRequest.datiCustomer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, datiCustomer);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SalvaSoggettoTerzoRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    datiCustomer: ").append(toIndentedString(datiCustomer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
