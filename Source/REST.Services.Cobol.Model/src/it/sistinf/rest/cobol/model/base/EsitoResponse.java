package it.sistinf.rest.cobol.model.base;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class EsitoResponse extends GenericResponse  {

  private @Valid Boolean esito = null;

  /**
   **/
  public EsitoResponse esito(Boolean esito) {
    this.esito = esito;
    return this;
  }

  
  @ApiModelProperty(example = "true", value = "")
  @JsonProperty("esito")

  public Boolean isEsito() {
    return esito;
  }
  public void setEsito(Boolean esito) {
    this.esito = esito;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EsitoResponse esitoResponse = (EsitoResponse) o;
    return Objects.equals(esito, esitoResponse.esito);
  }

  @Override
  public int hashCode() {
    return Objects.hash(esito);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EsitoResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    esito: ").append(toIndentedString(esito)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
