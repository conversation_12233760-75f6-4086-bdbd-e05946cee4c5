package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiSoggettoTerzo   {

  private @Valid String flagEsecutore = null;

  private @Valid String flagSoggettoTerzo = null;

  private @Valid String tipologiaSoggettoTerzo = null;

  private @Valid String tipoEsecutore = null;

  private @Valid String flagDelega = null;

  private @Valid String relazioneBeneficiarioSoggettoTerzo = null;

  private @Valid String relazioneBeneficiarioSoggettoTerzoAltro = null;

  private @Valid String tipoRappresentanza = null;

  private @Valid String tipoRappresentanzaAltro = null;

  /**
   **/
  public DatiSoggettoTerzo flagEsecutore(String flagEsecutore) {
    this.flagEsecutore = flagEsecutore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagEsecutore")
  @NotNull

  public String getFlagEsecutore() {
    return flagEsecutore;
  }
  public void setFlagEsecutore(String flagEsecutore) {
    this.flagEsecutore = flagEsecutore;
  }

  /**
   **/
  public DatiSoggettoTerzo flagSoggettoTerzo(String flagSoggettoTerzo) {
    this.flagSoggettoTerzo = flagSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagSoggettoTerzo")
  @NotNull

  public String getFlagSoggettoTerzo() {
    return flagSoggettoTerzo;
  }
  public void setFlagSoggettoTerzo(String flagSoggettoTerzo) {
    this.flagSoggettoTerzo = flagSoggettoTerzo;
  }

  /**
   **/
  public DatiSoggettoTerzo tipologiaSoggettoTerzo(String tipologiaSoggettoTerzo) {
    this.tipologiaSoggettoTerzo = tipologiaSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipologiaSoggettoTerzo")
  @NotNull

  public String getTipologiaSoggettoTerzo() {
    return tipologiaSoggettoTerzo;
  }
  public void setTipologiaSoggettoTerzo(String tipologiaSoggettoTerzo) {
    this.tipologiaSoggettoTerzo = tipologiaSoggettoTerzo;
  }

  /**
   **/
  public DatiSoggettoTerzo tipoEsecutore(String tipoEsecutore) {
    this.tipoEsecutore = tipoEsecutore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoEsecutore")
  @NotNull

  public String getTipoEsecutore() {
    return tipoEsecutore;
  }
  public void setTipoEsecutore(String tipoEsecutore) {
    this.tipoEsecutore = tipoEsecutore;
  }

  /**
   **/
  public DatiSoggettoTerzo flagDelega(String flagDelega) {
    this.flagDelega = flagDelega;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagDelega")
  @NotNull

  public String getFlagDelega() {
    return flagDelega;
  }
  public void setFlagDelega(String flagDelega) {
    this.flagDelega = flagDelega;
  }

  /**
   **/
  public DatiSoggettoTerzo relazioneBeneficiarioSoggettoTerzo(String relazioneBeneficiarioSoggettoTerzo) {
    this.relazioneBeneficiarioSoggettoTerzo = relazioneBeneficiarioSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioSoggettoTerzo")
  @NotNull

  public String getRelazioneBeneficiarioSoggettoTerzo() {
    return relazioneBeneficiarioSoggettoTerzo;
  }
  public void setRelazioneBeneficiarioSoggettoTerzo(String relazioneBeneficiarioSoggettoTerzo) {
    this.relazioneBeneficiarioSoggettoTerzo = relazioneBeneficiarioSoggettoTerzo;
  }

  /**
   **/
  public DatiSoggettoTerzo relazioneBeneficiarioSoggettoTerzoAltro(String relazioneBeneficiarioSoggettoTerzoAltro) {
    this.relazioneBeneficiarioSoggettoTerzoAltro = relazioneBeneficiarioSoggettoTerzoAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioSoggettoTerzoAltro")
  @NotNull

  public String getRelazioneBeneficiarioSoggettoTerzoAltro() {
    return relazioneBeneficiarioSoggettoTerzoAltro;
  }
  public void setRelazioneBeneficiarioSoggettoTerzoAltro(String relazioneBeneficiarioSoggettoTerzoAltro) {
    this.relazioneBeneficiarioSoggettoTerzoAltro = relazioneBeneficiarioSoggettoTerzoAltro;
  }

  /**
   **/
  public DatiSoggettoTerzo tipoRappresentanza(String tipoRappresentanza) {
    this.tipoRappresentanza = tipoRappresentanza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRappresentanza")
  @NotNull

  public String getTipoRappresentanza() {
    return tipoRappresentanza;
  }
  public void setTipoRappresentanza(String tipoRappresentanza) {
    this.tipoRappresentanza = tipoRappresentanza;
  }

  /**
   **/
  public DatiSoggettoTerzo tipoRappresentanzaAltro(String tipoRappresentanzaAltro) {
    this.tipoRappresentanzaAltro = tipoRappresentanzaAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRappresentanzaAltro")
  @NotNull

  public String getTipoRappresentanzaAltro() {
    return tipoRappresentanzaAltro;
  }
  public void setTipoRappresentanzaAltro(String tipoRappresentanzaAltro) {
    this.tipoRappresentanzaAltro = tipoRappresentanzaAltro;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiSoggettoTerzo datiSoggettoTerzo = (DatiSoggettoTerzo) o;
    return Objects.equals(flagEsecutore, datiSoggettoTerzo.flagEsecutore) &&
        Objects.equals(flagSoggettoTerzo, datiSoggettoTerzo.flagSoggettoTerzo) &&
        Objects.equals(tipologiaSoggettoTerzo, datiSoggettoTerzo.tipologiaSoggettoTerzo) &&
        Objects.equals(tipoEsecutore, datiSoggettoTerzo.tipoEsecutore) &&
        Objects.equals(flagDelega, datiSoggettoTerzo.flagDelega) &&
        Objects.equals(relazioneBeneficiarioSoggettoTerzo, datiSoggettoTerzo.relazioneBeneficiarioSoggettoTerzo) &&
        Objects.equals(relazioneBeneficiarioSoggettoTerzoAltro, datiSoggettoTerzo.relazioneBeneficiarioSoggettoTerzoAltro) &&
        Objects.equals(tipoRappresentanza, datiSoggettoTerzo.tipoRappresentanza) &&
        Objects.equals(tipoRappresentanzaAltro, datiSoggettoTerzo.tipoRappresentanzaAltro);
  }

  @Override
  public int hashCode() {
    return Objects.hash(flagEsecutore, flagSoggettoTerzo, tipologiaSoggettoTerzo, tipoEsecutore, flagDelega, relazioneBeneficiarioSoggettoTerzo, relazioneBeneficiarioSoggettoTerzoAltro, tipoRappresentanza, tipoRappresentanzaAltro);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiSoggettoTerzo {\n");
    
    sb.append("    flagEsecutore: ").append(toIndentedString(flagEsecutore)).append("\n");
    sb.append("    flagSoggettoTerzo: ").append(toIndentedString(flagSoggettoTerzo)).append("\n");
    sb.append("    tipologiaSoggettoTerzo: ").append(toIndentedString(tipologiaSoggettoTerzo)).append("\n");
    sb.append("    tipoEsecutore: ").append(toIndentedString(tipoEsecutore)).append("\n");
    sb.append("    flagDelega: ").append(toIndentedString(flagDelega)).append("\n");
    sb.append("    relazioneBeneficiarioSoggettoTerzo: ").append(toIndentedString(relazioneBeneficiarioSoggettoTerzo)).append("\n");
    sb.append("    relazioneBeneficiarioSoggettoTerzoAltro: ").append(toIndentedString(relazioneBeneficiarioSoggettoTerzoAltro)).append("\n");
    sb.append("    tipoRappresentanza: ").append(toIndentedString(tipoRappresentanza)).append("\n");
    sb.append("    tipoRappresentanzaAltro: ").append(toIndentedString(tipoRappresentanzaAltro)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
