package it.sistinf.rest.cobol.model.blocco.autorizzativo;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class InfoPerAutorizzazioneBlocco   {

  private @Valid Date dataAutorizzazione = null;

  private @Valid String utenteAutorizzatore = null;

  /**
   **/
  public InfoPerAutorizzazioneBlocco dataAutorizzazione(Date dataAutorizzazione) {
    this.dataAutorizzazione = dataAutorizzazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataAutorizzazione")

  public Date getDataAutorizzazione() {
    return dataAutorizzazione;
  }
  public void setDataAutorizzazione(Date dataAutorizzazione) {
    this.dataAutorizzazione = dataAutorizzazione;
  }

  /**
   **/
  public InfoPerAutorizzazioneBlocco utenteAutorizzatore(String utenteAutorizzatore) {
    this.utenteAutorizzatore = utenteAutorizzatore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("utenteAutorizzatore")

  public String getUtenteAutorizzatore() {
    return utenteAutorizzatore;
  }
  public void setUtenteAutorizzatore(String utenteAutorizzatore) {
    this.utenteAutorizzatore = utenteAutorizzatore;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoPerAutorizzazioneBlocco infoPerAutorizzazioneBlocco = (InfoPerAutorizzazioneBlocco) o;
    return Objects.equals(dataAutorizzazione, infoPerAutorizzazioneBlocco.dataAutorizzazione) &&
        Objects.equals(utenteAutorizzatore, infoPerAutorizzazioneBlocco.utenteAutorizzatore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dataAutorizzazione, utenteAutorizzatore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoPerAutorizzazioneBlocco {\n");
    
    sb.append("    dataAutorizzazione: ").append(toIndentedString(dataAutorizzazione)).append("\n");
    sb.append("    utenteAutorizzatore: ").append(toIndentedString(utenteAutorizzatore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
