package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;

public class PepCrimeCustomerInfo   {

  private @Valid String ateco = null;

  private @Valid String autoritaDoc = null;

  private @Valid String capDomicilio = null;

  private @Valid String capResidenza = null;

  private @Valid String codiceFiscale = null;

  private @Valid String cognome = null;

  private @Valid String comuneDomicilio = null;

  private @Valid String comuneNascita = null;

  private @Valid String comuneResidenza = null;

  private @Valid String dataNascita = null;

  private @Valid String indirizzoDomicilio = null;

  private @Valid String indirizzoResidenza = null;

  private @Valid String nazioneResidenza = null;

  private @Valid String nome = null;

  private @Valid String numCivicoDomicilio = null;

  private @Valid String numCivicoResidenza = null;

  private @Valid String sae = null;

public enum SessoEnum {

    M(String.valueOf("M")), F(String.valueOf("F")), _U(String.valueOf(" "));


    private String value;

    SessoEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static SessoEnum fromValue(String v) {
        for (SessoEnum b : SessoEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid SessoEnum sesso = null;

public enum TipoClienteEnum {

    G(String.valueOf("G")), F(String.valueOf("F"));


    private String value;

    TipoClienteEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoClienteEnum fromValue(String v) {
        for (TipoClienteEnum b : TipoClienteEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoClienteEnum tipoCliente = null;

  private @Valid Long codiceCliente = null;

  /**
   * Codice ateco
   **/
  public PepCrimeCustomerInfo ateco(String ateco) {
    this.ateco = ateco;
    return this;
  }

  
  @ApiModelProperty(value = "Codice ateco")
  @JsonProperty("ateco")
 @Size(max=6)
  public String getAteco() {
    return ateco;
  }
  public void setAteco(String ateco) {
    this.ateco = ateco;
  }

  /**
   * Ente di rilascio del documento
   **/
  public PepCrimeCustomerInfo autoritaDoc(String autoritaDoc) {
    this.autoritaDoc = autoritaDoc;
    return this;
  }

  
  @ApiModelProperty(value = "Ente di rilascio del documento")
  @JsonProperty("autoritaDoc")
 @Size(max=30)
  public String getAutoritaDoc() {
    return autoritaDoc;
  }
  public void setAutoritaDoc(String autoritaDoc) {
    this.autoritaDoc = autoritaDoc;
  }

  /**
   * CAP di domicilio
   **/
  public PepCrimeCustomerInfo capDomicilio(String capDomicilio) {
    this.capDomicilio = capDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "CAP di domicilio")
  @JsonProperty("capDomicilio")
 @Size(max=5)
  public String getCapDomicilio() {
    return capDomicilio;
  }
  public void setCapDomicilio(String capDomicilio) {
    this.capDomicilio = capDomicilio;
  }

  /**
   * CAP di Residenza
   **/
  public PepCrimeCustomerInfo capResidenza(String capResidenza) {
    this.capResidenza = capResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "CAP di Residenza")
  @JsonProperty("capResidenza")
 @Size(max=5)
  public String getCapResidenza() {
    return capResidenza;
  }
  public void setCapResidenza(String capResidenza) {
    this.capResidenza = capResidenza;
  }

  /**
   * Codice Fiscale
   **/
  public PepCrimeCustomerInfo codiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
    return this;
  }

  
  @ApiModelProperty(example = "****************", required = true, value = "Codice Fiscale")
  @JsonProperty("codiceFiscale")
  @NotNull
 @Size(max=16)
  public String getCodiceFiscale() {
    return codiceFiscale;
  }
  public void setCodiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
  }

  /**
   * Cognome persona Fisica / Ragione Sociale persona Giuridica.
   **/
  public PepCrimeCustomerInfo cognome(String cognome) {
    this.cognome = cognome;
    return this;
  }

  
  @ApiModelProperty(example = "CONTINI", required = true, value = "Cognome persona Fisica / Ragione Sociale persona Giuridica.")
  @JsonProperty("cognome")
  @NotNull
 @Size(max=70)
  public String getCognome() {
    return cognome;
  }
  public void setCognome(String cognome) {
    this.cognome = cognome;
  }

  /**
   * Comune di domicilio
   **/
  public PepCrimeCustomerInfo comuneDomicilio(String comuneDomicilio) {
    this.comuneDomicilio = comuneDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "Comune di domicilio")
  @JsonProperty("comuneDomicilio")
 @Size(max=70)
  public String getComuneDomicilio() {
    return comuneDomicilio;
  }
  public void setComuneDomicilio(String comuneDomicilio) {
    this.comuneDomicilio = comuneDomicilio;
  }

  /**
   * Comune di nascita
   **/
  public PepCrimeCustomerInfo comuneNascita(String comuneNascita) {
    this.comuneNascita = comuneNascita;
    return this;
  }

  
  @ApiModelProperty(value = "Comune di nascita")
  @JsonProperty("comuneNascita")
 @Size(max=70)
  public String getComuneNascita() {
    return comuneNascita;
  }
  public void setComuneNascita(String comuneNascita) {
    this.comuneNascita = comuneNascita;
  }

  /**
   * Comune di residenza
   **/
  public PepCrimeCustomerInfo comuneResidenza(String comuneResidenza) {
    this.comuneResidenza = comuneResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "Comune di residenza")
  @JsonProperty("comuneResidenza")
 @Size(max=70)
  public String getComuneResidenza() {
    return comuneResidenza;
  }
  public void setComuneResidenza(String comuneResidenza) {
    this.comuneResidenza = comuneResidenza;
  }

  /**
   * Data di Nascita, formato yyyyMMdd
   **/
  public PepCrimeCustomerInfo dataNascita(String dataNascita) {
    this.dataNascita = dataNascita;
    return this;
  }

  
  @ApiModelProperty(example = "20211130", value = "Data di Nascita, formato yyyyMMdd")
  @JsonProperty("dataNascita")
 @Size(max=8)
  public String getDataNascita() {
    return dataNascita;
  }
  public void setDataNascita(String dataNascita) {
    this.dataNascita = dataNascita;
  }

  /**
   * Indirizzo di domicilio.
   **/
  public PepCrimeCustomerInfo indirizzoDomicilio(String indirizzoDomicilio) {
    this.indirizzoDomicilio = indirizzoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "Indirizzo di domicilio.")
  @JsonProperty("indirizzoDomicilio")
 @Size(max=30)
  public String getIndirizzoDomicilio() {
    return indirizzoDomicilio;
  }
  public void setIndirizzoDomicilio(String indirizzoDomicilio) {
    this.indirizzoDomicilio = indirizzoDomicilio;
  }

  /**
   * Indirizzo di residenza.
   **/
  public PepCrimeCustomerInfo indirizzoResidenza(String indirizzoResidenza) {
    this.indirizzoResidenza = indirizzoResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "Indirizzo di residenza.")
  @JsonProperty("indirizzoResidenza")
 @Size(max=50)
  public String getIndirizzoResidenza() {
    return indirizzoResidenza;
  }
  public void setIndirizzoResidenza(String indirizzoResidenza) {
    this.indirizzoResidenza = indirizzoResidenza;
  }

  /**
   * Codice nazione di residenza.
   **/
  public PepCrimeCustomerInfo nazioneResidenza(String nazioneResidenza) {
    this.nazioneResidenza = nazioneResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "Codice nazione di residenza.")
  @JsonProperty("nazioneResidenza")
 @Size(max=3)
  public String getNazioneResidenza() {
    return nazioneResidenza;
  }
  public void setNazioneResidenza(String nazioneResidenza) {
    this.nazioneResidenza = nazioneResidenza;
  }

  /**
   * Nome persona Fisica.
   **/
  public PepCrimeCustomerInfo nome(String nome) {
    this.nome = nome;
    return this;
  }

  
  @ApiModelProperty(value = "Nome persona Fisica.")
  @JsonProperty("nome")
 @Size(max=50)
  public String getNome() {
    return nome;
  }
  public void setNome(String nome) {
    this.nome = nome;
  }

  /**
   * Numero Civico Domicilio.
   **/
  public PepCrimeCustomerInfo numCivicoDomicilio(String numCivicoDomicilio) {
    this.numCivicoDomicilio = numCivicoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "Numero Civico Domicilio.")
  @JsonProperty("numCivicoDomicilio")
 @Size(max=4)
  public String getNumCivicoDomicilio() {
    return numCivicoDomicilio;
  }
  public void setNumCivicoDomicilio(String numCivicoDomicilio) {
    this.numCivicoDomicilio = numCivicoDomicilio;
  }

  /**
   * Numero Civico Residenza.
   **/
  public PepCrimeCustomerInfo numCivicoResidenza(String numCivicoResidenza) {
    this.numCivicoResidenza = numCivicoResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "Numero Civico Residenza.")
  @JsonProperty("numCivicoResidenza")
 @Size(max=10)
  public String getNumCivicoResidenza() {
    return numCivicoResidenza;
  }
  public void setNumCivicoResidenza(String numCivicoResidenza) {
    this.numCivicoResidenza = numCivicoResidenza;
  }

  /**
   * Codice Sae.
   **/
  public PepCrimeCustomerInfo sae(String sae) {
    this.sae = sae;
    return this;
  }

  
  @ApiModelProperty(value = "Codice Sae.")
  @JsonProperty("sae")
 @Size(max=3)
  public String getSae() {
    return sae;
  }
  public void setSae(String sae) {
    this.sae = sae;
  }

  /**
   **/
  public PepCrimeCustomerInfo sesso(SessoEnum sesso) {
    this.sesso = sesso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sesso")

  public SessoEnum getSesso() {
    return sesso;
  }
  public void setSesso(SessoEnum sesso) {
    this.sesso = sesso;
  }

  /**
   **/
  public PepCrimeCustomerInfo tipoCliente(TipoClienteEnum tipoCliente) {
    this.tipoCliente = tipoCliente;
    return this;
  }

  
  @ApiModelProperty(example = "F", required = true, value = "")
  @JsonProperty("tipoCliente")
  @NotNull

  public TipoClienteEnum getTipoCliente() {
    return tipoCliente;
  }
  public void setTipoCliente(TipoClienteEnum tipoCliente) {
    this.tipoCliente = tipoCliente;
  }

  /**
   * Codice Cliente NAW.
   **/
  public PepCrimeCustomerInfo codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PepCrimeCustomerInfo pepCrimeCustomerInfo = (PepCrimeCustomerInfo) o;
    return Objects.equals(ateco, pepCrimeCustomerInfo.ateco) &&
        Objects.equals(autoritaDoc, pepCrimeCustomerInfo.autoritaDoc) &&
        Objects.equals(capDomicilio, pepCrimeCustomerInfo.capDomicilio) &&
        Objects.equals(capResidenza, pepCrimeCustomerInfo.capResidenza) &&
        Objects.equals(codiceFiscale, pepCrimeCustomerInfo.codiceFiscale) &&
        Objects.equals(cognome, pepCrimeCustomerInfo.cognome) &&
        Objects.equals(comuneDomicilio, pepCrimeCustomerInfo.comuneDomicilio) &&
        Objects.equals(comuneNascita, pepCrimeCustomerInfo.comuneNascita) &&
        Objects.equals(comuneResidenza, pepCrimeCustomerInfo.comuneResidenza) &&
        Objects.equals(dataNascita, pepCrimeCustomerInfo.dataNascita) &&
        Objects.equals(indirizzoDomicilio, pepCrimeCustomerInfo.indirizzoDomicilio) &&
        Objects.equals(indirizzoResidenza, pepCrimeCustomerInfo.indirizzoResidenza) &&
        Objects.equals(nazioneResidenza, pepCrimeCustomerInfo.nazioneResidenza) &&
        Objects.equals(nome, pepCrimeCustomerInfo.nome) &&
        Objects.equals(numCivicoDomicilio, pepCrimeCustomerInfo.numCivicoDomicilio) &&
        Objects.equals(numCivicoResidenza, pepCrimeCustomerInfo.numCivicoResidenza) &&
        Objects.equals(sae, pepCrimeCustomerInfo.sae) &&
        Objects.equals(sesso, pepCrimeCustomerInfo.sesso) &&
        Objects.equals(tipoCliente, pepCrimeCustomerInfo.tipoCliente) &&
        Objects.equals(codiceCliente, pepCrimeCustomerInfo.codiceCliente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ateco, autoritaDoc, capDomicilio, capResidenza, codiceFiscale, cognome, comuneDomicilio, comuneNascita, comuneResidenza, dataNascita, indirizzoDomicilio, indirizzoResidenza, nazioneResidenza, nome, numCivicoDomicilio, numCivicoResidenza, sae, sesso, tipoCliente, codiceCliente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PepCrimeCustomerInfo {\n");
    
    sb.append("    ateco: ").append(toIndentedString(ateco)).append("\n");
    sb.append("    autoritaDoc: ").append(toIndentedString(autoritaDoc)).append("\n");
    sb.append("    capDomicilio: ").append(toIndentedString(capDomicilio)).append("\n");
    sb.append("    capResidenza: ").append(toIndentedString(capResidenza)).append("\n");
    sb.append("    codiceFiscale: ").append(toIndentedString(codiceFiscale)).append("\n");
    sb.append("    cognome: ").append(toIndentedString(cognome)).append("\n");
    sb.append("    comuneDomicilio: ").append(toIndentedString(comuneDomicilio)).append("\n");
    sb.append("    comuneNascita: ").append(toIndentedString(comuneNascita)).append("\n");
    sb.append("    comuneResidenza: ").append(toIndentedString(comuneResidenza)).append("\n");
    sb.append("    dataNascita: ").append(toIndentedString(dataNascita)).append("\n");
    sb.append("    indirizzoDomicilio: ").append(toIndentedString(indirizzoDomicilio)).append("\n");
    sb.append("    indirizzoResidenza: ").append(toIndentedString(indirizzoResidenza)).append("\n");
    sb.append("    nazioneResidenza: ").append(toIndentedString(nazioneResidenza)).append("\n");
    sb.append("    nome: ").append(toIndentedString(nome)).append("\n");
    sb.append("    numCivicoDomicilio: ").append(toIndentedString(numCivicoDomicilio)).append("\n");
    sb.append("    numCivicoResidenza: ").append(toIndentedString(numCivicoResidenza)).append("\n");
    sb.append("    sae: ").append(toIndentedString(sae)).append("\n");
    sb.append("    sesso: ").append(toIndentedString(sesso)).append("\n");
    sb.append("    tipoCliente: ").append(toIndentedString(tipoCliente)).append("\n");
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
