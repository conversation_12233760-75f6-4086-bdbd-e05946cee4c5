package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;

public class PepCrimeCustomerResponseInfo   {

  private @Valid String codiceFiscale = null;

public enum StatoPepEnum {

    I(String.valueOf("I")), V(String.valueOf("V")), S(String.valueOf("S")), N(String.valueOf("N"));


    private String value;

    StatoPepEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static StatoPepEnum fromValue(String v) {
        for (StatoPepEnum b : StatoPepEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid StatoPepEnum statoPep = null;

public enum StatoCrimeEnum {

    I(String.valueOf("I")), V(String.valueOf("V")), S(String.valueOf("S")), N(String.valueOf("N"));


    private String value;

    StatoCrimeEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static StatoCrimeEnum fromValue(String v) {
        for (StatoCrimeEnum b : StatoCrimeEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid StatoCrimeEnum statoCrime = null;

  /**
   * Codice Fiscale
   **/
  public PepCrimeCustomerResponseInfo codiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
    return this;
  }

  
  @ApiModelProperty(value = "Codice Fiscale")
  @JsonProperty("codiceFiscale")
 @Size(max=16)
  public String getCodiceFiscale() {
    return codiceFiscale;
  }
  public void setCodiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
  }

  /**
   **/
  public PepCrimeCustomerResponseInfo statoPep(StatoPepEnum statoPep) {
    this.statoPep = statoPep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoPep")

  public StatoPepEnum getStatoPep() {
    return statoPep;
  }
  public void setStatoPep(StatoPepEnum statoPep) {
    this.statoPep = statoPep;
  }

  /**
   **/
  public PepCrimeCustomerResponseInfo statoCrime(StatoCrimeEnum statoCrime) {
    this.statoCrime = statoCrime;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoCrime")

  public StatoCrimeEnum getStatoCrime() {
    return statoCrime;
  }
  public void setStatoCrime(StatoCrimeEnum statoCrime) {
    this.statoCrime = statoCrime;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PepCrimeCustomerResponseInfo pepCrimeCustomerResponseInfo = (PepCrimeCustomerResponseInfo) o;
    return Objects.equals(codiceFiscale, pepCrimeCustomerResponseInfo.codiceFiscale) &&
        Objects.equals(statoPep, pepCrimeCustomerResponseInfo.statoPep) &&
        Objects.equals(statoCrime, pepCrimeCustomerResponseInfo.statoCrime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceFiscale, statoPep, statoCrime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PepCrimeCustomerResponseInfo {\n");
    
    sb.append("    codiceFiscale: ").append(toIndentedString(codiceFiscale)).append("\n");
    sb.append("    statoPep: ").append(toIndentedString(statoPep)).append("\n");
    sb.append("    statoCrime: ").append(toIndentedString(statoCrime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
