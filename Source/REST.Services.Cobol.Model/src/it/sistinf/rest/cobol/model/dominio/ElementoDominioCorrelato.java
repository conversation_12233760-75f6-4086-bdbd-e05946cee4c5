package it.sistinf.rest.cobol.model.dominio;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ElementoDominioCorrelato extends ElementoDominio  {

  private @Valid String codiceCorrelato = null;

  /**
   **/
  public ElementoDominioCorrelato codiceCorrelato(String codiceCorrelato) {
    this.codiceCorrelato = codiceCorrelato;
    return this;
  }

  
  @ApiModelProperty(example = "DOMINIO1", value = "")
  @JsonProperty("codiceCorrelato")

  public String getCodiceCorrelato() {
    return codiceCorrelato;
  }
  public void setCodiceCorrelato(String codiceCorrelato) {
    this.codiceCorrelato = codiceCorrelato;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ElementoDominioCorrelato elementoDominioCorrelato = (ElementoDominioCorrelato) o;
    return Objects.equals(codiceCorrelato, elementoDominioCorrelato.codiceCorrelato);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCorrelato);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ElementoDominioCorrelato {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    codiceCorrelato: ").append(toIndentedString(codiceCorrelato)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
