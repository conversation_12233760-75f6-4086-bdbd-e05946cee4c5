package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class OpzioniContrattualiResponse   {

  private @Valid RppInfo rppInfo = null;

  private @Valid LifeCycleInfo lifeCycleInfo = null;

  private @Valid TakeProfitInfo takeProfitInfo = null;

  private @Valid Date dataEffetto = null;

  private @Valid Date dataAdesione = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public OpzioniContrattualiResponse rppInfo(RppInfo rppInfo) {
    this.rppInfo = rppInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("rppInfo")

  public RppInfo getRppInfo() {
    return rppInfo;
  }
  public void setRppInfo(RppInfo rppInfo) {
    this.rppInfo = rppInfo;
  }

  /**
   **/
  public OpzioniContrattualiResponse lifeCycleInfo(LifeCycleInfo lifeCycleInfo) {
    this.lifeCycleInfo = lifeCycleInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("lifeCycleInfo")

  public LifeCycleInfo getLifeCycleInfo() {
    return lifeCycleInfo;
  }
  public void setLifeCycleInfo(LifeCycleInfo lifeCycleInfo) {
    this.lifeCycleInfo = lifeCycleInfo;
  }

  /**
   **/
  public OpzioniContrattualiResponse takeProfitInfo(TakeProfitInfo takeProfitInfo) {
    this.takeProfitInfo = takeProfitInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("takeProfitInfo")

  public TakeProfitInfo getTakeProfitInfo() {
    return takeProfitInfo;
  }
  public void setTakeProfitInfo(TakeProfitInfo takeProfitInfo) {
    this.takeProfitInfo = takeProfitInfo;
  }

  /**
   **/
  public OpzioniContrattualiResponse dataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataEffetto")

  public Date getDataEffetto() {
    return dataEffetto;
  }
  public void setDataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
  }

  /**
   **/
  public OpzioniContrattualiResponse dataAdesione(Date dataAdesione) {
    this.dataAdesione = dataAdesione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataAdesione")

  public Date getDataAdesione() {
    return dataAdesione;
  }
  public void setDataAdesione(Date dataAdesione) {
    this.dataAdesione = dataAdesione;
  }

  /**
   **/
  public OpzioniContrattualiResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OpzioniContrattualiResponse opzioniContrattualiResponse = (OpzioniContrattualiResponse) o;
    return Objects.equals(rppInfo, opzioniContrattualiResponse.rppInfo) &&
        Objects.equals(lifeCycleInfo, opzioniContrattualiResponse.lifeCycleInfo) &&
        Objects.equals(takeProfitInfo, opzioniContrattualiResponse.takeProfitInfo) &&
        Objects.equals(dataEffetto, opzioniContrattualiResponse.dataEffetto) &&
        Objects.equals(dataAdesione, opzioniContrattualiResponse.dataAdesione) &&
        Objects.equals(errori, opzioniContrattualiResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rppInfo, lifeCycleInfo, takeProfitInfo, dataEffetto, dataAdesione, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OpzioniContrattualiResponse {\n");
    
    sb.append("    rppInfo: ").append(toIndentedString(rppInfo)).append("\n");
    sb.append("    lifeCycleInfo: ").append(toIndentedString(lifeCycleInfo)).append("\n");
    sb.append("    takeProfitInfo: ").append(toIndentedString(takeProfitInfo)).append("\n");
    sb.append("    dataEffetto: ").append(toIndentedString(dataEffetto)).append("\n");
    sb.append("    dataAdesione: ").append(toIndentedString(dataAdesione)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}