package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DettaglioPep   {

  private @Valid Boolean isPepInCarica = null;

  private @Valid String caricaPep = null;

  /**
   **/
  public DettaglioPep isPepInCarica(Boolean isPepInCarica) {
    this.isPepInCarica = isPepInCarica;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("isPepInCarica")

  public Boolean isIsPepInCarica() {
    return isPepInCarica;
  }
  public void setIsPepInCarica(Boolean isPepInCarica) {
    this.isPepInCarica = isPepInCarica;
  }

  /**
   **/
  public DettaglioPep caricaPep(String caricaPep) {
    this.caricaPep = caricaPep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("caricaPep")

  public String getCaricaPep() {
    return caricaPep;
  }
  public void setCaricaPep(String caricaPep) {
    this.caricaPep = caricaPep;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DettaglioPep dettaglioPep = (DettaglioPep) o;
    return Objects.equals(isPepInCarica, dettaglioPep.isPepInCarica) &&
        Objects.equals(caricaPep, dettaglioPep.caricaPep);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isPepInCarica, caricaPep);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DettaglioPep {\n");
    
    sb.append("    isPepInCarica: ").append(toIndentedString(isPepInCarica)).append("\n");
    sb.append("    caricaPep: ").append(toIndentedString(caricaPep)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
