package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class InserisciPercipientiRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid String funzione = null;

  private @Valid BigDecimal importoRichiesto = null;

  private @Valid Boolean flFacta = null;

  private @Valid Boolean flDac2 = null;

  private @Valid Boolean flFipPip = null;

  private @Valid List<PercipienteInfo> elencoPercipientiDefault = new ArrayList<PercipienteInfo>();

  private @Valid List<PercipienteExtendedInfo> elencoPercipienti = new ArrayList<PercipienteExtendedInfo>();

  /**
   **/
  public InserisciPercipientiRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public InserisciPercipientiRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public InserisciPercipientiRequest funzione(String funzione) {
    this.funzione = funzione;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("funzione")
  @NotNull

  public String getFunzione() {
    return funzione;
  }
  public void setFunzione(String funzione) {
    this.funzione = funzione;
  }

  /**
   **/
  public InserisciPercipientiRequest importoRichiesto(BigDecimal importoRichiesto) {
    this.importoRichiesto = importoRichiesto;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoRichiesto")
  

  public BigDecimal getImportoRichiesto() {
    return importoRichiesto;
  }
  public void setImportoRichiesto(BigDecimal importoRichiesto) {
    this.importoRichiesto = importoRichiesto;
  }

  /**
   **/
  public InserisciPercipientiRequest flFacta(Boolean flFacta) {
    this.flFacta = flFacta;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flFacta")
  

  public Boolean isFlFacta() {
    return flFacta;
  }
  public void setFlFacta(Boolean flFacta) {
    this.flFacta = flFacta;
  }

  /**
   **/
  public InserisciPercipientiRequest flDac2(Boolean flDac2) {
    this.flDac2 = flDac2;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flDac2")
  

  public Boolean isFlDac2() {
    return flDac2;
  }
  public void setFlDac2(Boolean flDac2) {
    this.flDac2 = flDac2;
  }

  /**
   **/
  public InserisciPercipientiRequest flFipPip(Boolean flFipPip) {
    this.flFipPip = flFipPip;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flFipPip")
  

  public Boolean isFlFipPip() {
    return flFipPip;
  }
  public void setFlFipPip(Boolean flFipPip) {
    this.flFipPip = flFipPip;
  }

  /**
   **/
  public InserisciPercipientiRequest elencoPercipientiDefault(List<PercipienteInfo> elencoPercipientiDefault) {
    this.elencoPercipientiDefault = elencoPercipientiDefault;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipientiDefault")
  

  public List<PercipienteInfo> getElencoPercipientiDefault() {
    return elencoPercipientiDefault;
  }
  public void setElencoPercipientiDefault(List<PercipienteInfo> elencoPercipientiDefault) {
    this.elencoPercipientiDefault = elencoPercipientiDefault;
  }

  /**
   **/
  public InserisciPercipientiRequest elencoPercipienti(List<PercipienteExtendedInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipienti")
  

  public List<PercipienteExtendedInfo> getElencoPercipienti() {
    return elencoPercipienti;
  }
  public void setElencoPercipienti(List<PercipienteExtendedInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InserisciPercipientiRequest inserisciPercipientiRequest = (InserisciPercipientiRequest) o;
    return Objects.equals(headerCobolSrv, inserisciPercipientiRequest.headerCobolSrv) &&
        Objects.equals(polizzaInfo, inserisciPercipientiRequest.polizzaInfo) &&
        Objects.equals(funzione, inserisciPercipientiRequest.funzione) &&
        Objects.equals(importoRichiesto, inserisciPercipientiRequest.importoRichiesto) &&
        Objects.equals(flFacta, inserisciPercipientiRequest.flFacta) &&
        Objects.equals(flDac2, inserisciPercipientiRequest.flDac2) &&
        Objects.equals(flFipPip, inserisciPercipientiRequest.flFipPip) &&
        Objects.equals(elencoPercipientiDefault, inserisciPercipientiRequest.elencoPercipientiDefault) &&
        Objects.equals(elencoPercipienti, inserisciPercipientiRequest.elencoPercipienti);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, polizzaInfo, funzione, importoRichiesto, flFacta, flDac2, flFipPip, elencoPercipientiDefault, elencoPercipienti);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InserisciPercipientiRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    funzione: ").append(toIndentedString(funzione)).append("\n");
    sb.append("    importoRichiesto: ").append(toIndentedString(importoRichiesto)).append("\n");
    sb.append("    flFacta: ").append(toIndentedString(flFacta)).append("\n");
    sb.append("    flDac2: ").append(toIndentedString(flDac2)).append("\n");
    sb.append("    flFipPip: ").append(toIndentedString(flFipPip)).append("\n");
    sb.append("    elencoPercipientiDefault: ").append(toIndentedString(elencoPercipientiDefault)).append("\n");
    sb.append("    elencoPercipienti: ").append(toIndentedString(elencoPercipienti)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
