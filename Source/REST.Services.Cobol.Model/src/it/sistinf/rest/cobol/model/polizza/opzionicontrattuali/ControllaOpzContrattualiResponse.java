package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ControllaOpzContrattualiResponse   {

  private @Valid Boolean esito = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public ControllaOpzContrattualiResponse esito(Boolean esito) {
    this.esito = esito;
    return this;
  }

  
  @ApiModelProperty(example = "true", value = "")
  @JsonProperty("esito")

  public Boolean isEsito() {
    return esito;
  }
  public void setEsito(Boolean esito) {
    this.esito = esito;
  }

  /**
   **/
  public ControllaOpzContrattualiResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControllaOpzContrattualiResponse controllaOpzContrattualiResponse = (ControllaOpzContrattualiResponse) o;
    return Objects.equals(esito, controllaOpzContrattualiResponse.esito) &&
        Objects.equals(errori, controllaOpzContrattualiResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(esito, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControllaOpzContrattualiResponse {\n");
    
    sb.append("    esito: ").append(toIndentedString(esito)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
