package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiBeneficiarioPG   {

  private @Valid String flagPaesiBlackList = null;

  private @Valid String flagPresenzaTitolariEffettivi = null;

  private @Valid String flagLimiteTitEff = null;

  private @Valid List<TitolariEffettiviInfo> elencoTitolariEffettivi = new ArrayList<TitolariEffettiviInfo>();

  /**
   **/
  public DatiBeneficiarioPG flagPaesiBlackList(String flagPaesiBlackList) {
    this.flagPaesiBlackList = flagPaesiBlackList;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagPaesiBlackList")
  

  public String getFlagPaesiBlackList() {
    return flagPaesiBlackList;
  }
  public void setFlagPaesiBlackList(String flagPaesiBlackList) {
    this.flagPaesiBlackList = flagPaesiBlackList;
  }

  /**
   **/
  public DatiBeneficiarioPG flagPresenzaTitolariEffettivi(String flagPresenzaTitolariEffettivi) {
    this.flagPresenzaTitolariEffettivi = flagPresenzaTitolariEffettivi;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagPresenzaTitolariEffettivi")
  

  public String getFlagPresenzaTitolariEffettivi() {
    return flagPresenzaTitolariEffettivi;
  }
  public void setFlagPresenzaTitolariEffettivi(String flagPresenzaTitolariEffettivi) {
    this.flagPresenzaTitolariEffettivi = flagPresenzaTitolariEffettivi;
  }

  /**
   **/
  public DatiBeneficiarioPG flagLimiteTitEff(String flagLimiteTitEff) {
    this.flagLimiteTitEff = flagLimiteTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagLimiteTitEff")
  

  public String getFlagLimiteTitEff() {
    return flagLimiteTitEff;
  }
  public void setFlagLimiteTitEff(String flagLimiteTitEff) {
    this.flagLimiteTitEff = flagLimiteTitEff;
  }

  /**
   **/
  public DatiBeneficiarioPG elencoTitolariEffettivi(List<TitolariEffettiviInfo> elencoTitolariEffettivi) {
    this.elencoTitolariEffettivi = elencoTitolariEffettivi;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoTitolariEffettivi")
  

  public List<TitolariEffettiviInfo> getElencoTitolariEffettivi() {
    return elencoTitolariEffettivi;
  }
  public void setElencoTitolariEffettivi(List<TitolariEffettiviInfo> elencoTitolariEffettivi) {
    this.elencoTitolariEffettivi = elencoTitolariEffettivi;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiBeneficiarioPG datiBeneficiarioPG = (DatiBeneficiarioPG) o;
    return Objects.equals(flagPaesiBlackList, datiBeneficiarioPG.flagPaesiBlackList) &&
        Objects.equals(flagPresenzaTitolariEffettivi, datiBeneficiarioPG.flagPresenzaTitolariEffettivi) &&
        Objects.equals(flagLimiteTitEff, datiBeneficiarioPG.flagLimiteTitEff) &&
        Objects.equals(elencoTitolariEffettivi, datiBeneficiarioPG.elencoTitolariEffettivi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(flagPaesiBlackList, flagPresenzaTitolariEffettivi, flagLimiteTitEff, elencoTitolariEffettivi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiBeneficiarioPG {\n");
    
    sb.append("    flagPaesiBlackList: ").append(toIndentedString(flagPaesiBlackList)).append("\n");
    sb.append("    flagPresenzaTitolariEffettivi: ").append(toIndentedString(flagPresenzaTitolariEffettivi)).append("\n");
    sb.append("    flagLimiteTitEff: ").append(toIndentedString(flagLimiteTitEff)).append("\n");
    sb.append("    elencoTitolariEffettivi: ").append(toIndentedString(elencoTitolariEffettivi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
