package it.sistinf.rest.cobol.model.portafoglio.polizza;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class QuietanzeResponse   {

  private @Valid List<QuietanzaInfo> listaQuietanze = new ArrayList<QuietanzaInfo>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public QuietanzeResponse listaQuietanze(List<QuietanzaInfo> listaQuietanze) {
    this.listaQuietanze = listaQuietanze;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("listaQuietanze")

  public List<QuietanzaInfo> getListaQuietanze() {
    return listaQuietanze;
  }
  public void setListaQuietanze(List<QuietanzaInfo> listaQuietanze) {
    this.listaQuietanze = listaQuietanze;
  }

  /**
   **/
  public QuietanzeResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QuietanzeResponse quietanzeResponse = (QuietanzeResponse) o;
    return Objects.equals(listaQuietanze, quietanzeResponse.listaQuietanze) &&
        Objects.equals(errori, quietanzeResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(listaQuietanze, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QuietanzeResponse {\n");
    
    sb.append("    listaQuietanze: ").append(toIndentedString(listaQuietanze)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
