package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class SelectInfoFinanziarieRequest   {

  private @Valid Long codiceCliente = null;

  private @Valid Date dataRif = null;

  /**
   **/
  public SelectInfoFinanziarieRequest codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("codiceCliente")
  @NotNull

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public SelectInfoFinanziarieRequest dataRif(Date dataRif) {
    this.dataRif = dataRif;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRif")

  public Date getDataRif() {
    return dataRif;
  }
  public void setDataRif(Date dataRif) {
    this.dataRif = dataRif;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SelectInfoFinanziarieRequest selectInfoFinanziarieRequest = (SelectInfoFinanziarieRequest) o;
    return Objects.equals(codiceCliente, selectInfoFinanziarieRequest.codiceCliente) &&
        Objects.equals(dataRif, selectInfoFinanziarieRequest.dataRif);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, dataRif);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SelectInfoFinanziarieRequest {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    dataRif: ").append(toIndentedString(dataRif)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
