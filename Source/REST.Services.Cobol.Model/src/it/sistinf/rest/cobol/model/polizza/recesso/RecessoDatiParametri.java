package it.sistinf.rest.cobol.model.polizza.recesso;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class RecessoDatiParametri   {

  private @Valid Date dataSistema = null;

  private @Valid Date dataPrenotazione = null;

  private @Valid Date dataRichiestaCliente = null;

  private @Valid Date dataRicevimentoRichiesta = null;

  private @Valid Date dataDisinvestimento = null;

  private @Valid Date dataDisinvestimentoCalcolata = null;

  private @Valid Date dataRichiestaCompleta = null;

  private @Valid Date dataQuietanzaIncassata = null;

  private @Valid Boolean existQuoteToInvest = null;

public enum TipoOperazioneEnum {

    RECESSO(String.valueOf("RECESSO")), RIMBORSO(String.valueOf("RIMBORSO")), RECESSO_SENZA_COSTI(String.valueOf("RECESSO SENZA COSTI"));


    private String value;

    TipoOperazioneEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoOperazioneEnum fromValue(String v) {
        for (TipoOperazioneEnum b : TipoOperazioneEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoOperazioneEnum tipoOperazione = null;

  private @Valid String motivazioneRecesso = null;

  private @Valid Boolean recessoSenzaCostiAttivo = null;

  private @Valid Date dataDocumento = null;

  private @Valid Date dataInvioFU = null;

  private @Valid Date dataUltimaRicezioneDocFU = null;

  private @Valid String eventoRibilNonConcluso = null;

  private @Valid Date dataInizioEventoPrior = null;

  private @Valid String dscrEventoRibilNonConcluso = null;

  private @Valid String esisteRigaFU = null;

  /**
   **/
  public RecessoDatiParametri dataSistema(Date dataSistema) {
    this.dataSistema = dataSistema;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataSistema") 

  public Date getDataSistema() {
    return dataSistema;
  }
  public void setDataSistema(Date dataSistema) {
    this.dataSistema = dataSistema;
  }

  /**
   **/
  public RecessoDatiParametri dataPrenotazione(Date dataPrenotazione) {
    this.dataPrenotazione = dataPrenotazione;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("dataPrenotazione") 

  public Date getDataPrenotazione() {
    return dataPrenotazione;
  }
  public void setDataPrenotazione(Date dataPrenotazione) {
    this.dataPrenotazione = dataPrenotazione;
  }

  /**
   **/
  public RecessoDatiParametri dataRichiestaCliente(Date dataRichiestaCliente) {
    this.dataRichiestaCliente = dataRichiestaCliente;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("dataRichiestaCliente") 

  public Date getDataRichiestaCliente() {
    return dataRichiestaCliente;
  }
  public void setDataRichiestaCliente(Date dataRichiestaCliente) {
    this.dataRichiestaCliente = dataRichiestaCliente;
  }

  /**
   **/
  public RecessoDatiParametri dataRicevimentoRichiesta(Date dataRicevimentoRichiesta) {
    this.dataRicevimentoRichiesta = dataRicevimentoRichiesta;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("dataRicevimentoRichiesta") 

  public Date getDataRicevimentoRichiesta() {
    return dataRicevimentoRichiesta;
  }
  public void setDataRicevimentoRichiesta(Date dataRicevimentoRichiesta) {
    this.dataRicevimentoRichiesta = dataRicevimentoRichiesta;
  }

  /**
   **/
  public RecessoDatiParametri dataDisinvestimento(Date dataDisinvestimento) {
    this.dataDisinvestimento = dataDisinvestimento;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("dataDisinvestimento") 

  public Date getDataDisinvestimento() {
    return dataDisinvestimento;
  }
  public void setDataDisinvestimento(Date dataDisinvestimento) {
    this.dataDisinvestimento = dataDisinvestimento;
  }

  /**
   **/
  public RecessoDatiParametri dataDisinvestimentoCalcolata(Date dataDisinvestimentoCalcolata) {
    this.dataDisinvestimentoCalcolata = dataDisinvestimentoCalcolata;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataDisinvestimentoCalcolata") 

  public Date getDataDisinvestimentoCalcolata() {
    return dataDisinvestimentoCalcolata;
  }
  public void setDataDisinvestimentoCalcolata(Date dataDisinvestimentoCalcolata) {
    this.dataDisinvestimentoCalcolata = dataDisinvestimentoCalcolata;
  }

  /**
   **/
  public RecessoDatiParametri dataRichiestaCompleta(Date dataRichiestaCompleta) {
    this.dataRichiestaCompleta = dataRichiestaCompleta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRichiestaCompleta") 

  public Date getDataRichiestaCompleta() {
    return dataRichiestaCompleta;
  }
  public void setDataRichiestaCompleta(Date dataRichiestaCompleta) {
    this.dataRichiestaCompleta = dataRichiestaCompleta;
  }

  /**
   **/
  public RecessoDatiParametri dataQuietanzaIncassata(Date dataQuietanzaIncassata) {
    this.dataQuietanzaIncassata = dataQuietanzaIncassata;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataQuietanzaIncassata") 

  public Date getDataQuietanzaIncassata() {
    return dataQuietanzaIncassata;
  }
  public void setDataQuietanzaIncassata(Date dataQuietanzaIncassata) {
    this.dataQuietanzaIncassata = dataQuietanzaIncassata;
  }

  /**
   **/
  public RecessoDatiParametri existQuoteToInvest(Boolean existQuoteToInvest) {
    this.existQuoteToInvest = existQuoteToInvest;
    return this;
  }

  
  @ApiModelProperty(example = "true", value = "")
  @JsonProperty("existQuoteToInvest") 

  public Boolean isExistQuoteToInvest() {
    return existQuoteToInvest;
  }
  public void setExistQuoteToInvest(Boolean existQuoteToInvest) {
    this.existQuoteToInvest = existQuoteToInvest;
  }

  /**
   **/
  public RecessoDatiParametri tipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoOperazione") 

  public TipoOperazioneEnum getTipoOperazione() {
    return tipoOperazione;
  }
  public void setTipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
  }

  /**
   **/
  public RecessoDatiParametri motivazioneRecesso(String motivazioneRecesso) {
    this.motivazioneRecesso = motivazioneRecesso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("motivazioneRecesso") 

  public String getMotivazioneRecesso() {
    return motivazioneRecesso;
  }
  public void setMotivazioneRecesso(String motivazioneRecesso) {
    this.motivazioneRecesso = motivazioneRecesso;
  }

  /**
   **/
  public RecessoDatiParametri recessoSenzaCostiAttivo(Boolean recessoSenzaCostiAttivo) {
    this.recessoSenzaCostiAttivo = recessoSenzaCostiAttivo;
    return this;
  }

  
  @ApiModelProperty(example = "true", value = "")
  @JsonProperty("recessoSenzaCostiAttivo") 

  public Boolean isRecessoSenzaCostiAttivo() {
    return recessoSenzaCostiAttivo;
  }
  public void setRecessoSenzaCostiAttivo(Boolean recessoSenzaCostiAttivo) {
    this.recessoSenzaCostiAttivo = recessoSenzaCostiAttivo;
  }

  /**
   **/
  public RecessoDatiParametri dataDocumento(Date dataDocumento) {
    this.dataDocumento = dataDocumento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataDocumento") 

  public Date getDataDocumento() {
    return dataDocumento;
  }
  public void setDataDocumento(Date dataDocumento) {
    this.dataDocumento = dataDocumento;
  }

  /**
   **/
  public RecessoDatiParametri dataInvioFU(Date dataInvioFU) {
    this.dataInvioFU = dataInvioFU;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataInvioFU") 

  public Date getDataInvioFU() {
    return dataInvioFU;
  }
  public void setDataInvioFU(Date dataInvioFU) {
    this.dataInvioFU = dataInvioFU;
  }

  /**
   **/
  public RecessoDatiParametri dataUltimaRicezioneDocFU(Date dataUltimaRicezioneDocFU) {
    this.dataUltimaRicezioneDocFU = dataUltimaRicezioneDocFU;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataUltimaRicezioneDocFU") 

  public Date getDataUltimaRicezioneDocFU() {
    return dataUltimaRicezioneDocFU;
  }
  public void setDataUltimaRicezioneDocFU(Date dataUltimaRicezioneDocFU) {
    this.dataUltimaRicezioneDocFU = dataUltimaRicezioneDocFU;
  }

  /**
   **/
  public RecessoDatiParametri eventoRibilNonConcluso(String eventoRibilNonConcluso) {
    this.eventoRibilNonConcluso = eventoRibilNonConcluso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("eventoRibilNonConcluso") 

  public String getEventoRibilNonConcluso() {
    return eventoRibilNonConcluso;
  }
  public void setEventoRibilNonConcluso(String eventoRibilNonConcluso) {
    this.eventoRibilNonConcluso = eventoRibilNonConcluso;
  }

  /**
   **/
  public RecessoDatiParametri dataInizioEventoPrior(Date dataInizioEventoPrior) {
    this.dataInizioEventoPrior = dataInizioEventoPrior;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataInizioEventoPrior") 

  public Date getDataInizioEventoPrior() {
    return dataInizioEventoPrior;
  }
  public void setDataInizioEventoPrior(Date dataInizioEventoPrior) {
    this.dataInizioEventoPrior = dataInizioEventoPrior;
  }

  /**
   **/
  public RecessoDatiParametri dscrEventoRibilNonConcluso(String dscrEventoRibilNonConcluso) {
    this.dscrEventoRibilNonConcluso = dscrEventoRibilNonConcluso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dscrEventoRibilNonConcluso") 

  public String getDscrEventoRibilNonConcluso() {
    return dscrEventoRibilNonConcluso;
  }
  public void setDscrEventoRibilNonConcluso(String dscrEventoRibilNonConcluso) {
    this.dscrEventoRibilNonConcluso = dscrEventoRibilNonConcluso;
  }

  /**
   **/
  public RecessoDatiParametri esisteRigaFU(String esisteRigaFU) {
    this.esisteRigaFU = esisteRigaFU;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("esisteRigaFU") 

  public String getEsisteRigaFU() {
    return esisteRigaFU;
  }
  public void setEsisteRigaFU(String esisteRigaFU) {
    this.esisteRigaFU = esisteRigaFU;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RecessoDatiParametri recessoDatiParametri = (RecessoDatiParametri) o;
    return Objects.equals(dataSistema, recessoDatiParametri.dataSistema) &&
        Objects.equals(dataPrenotazione, recessoDatiParametri.dataPrenotazione) &&
        Objects.equals(dataRichiestaCliente, recessoDatiParametri.dataRichiestaCliente) &&
        Objects.equals(dataRicevimentoRichiesta, recessoDatiParametri.dataRicevimentoRichiesta) &&
        Objects.equals(dataDisinvestimento, recessoDatiParametri.dataDisinvestimento) &&
        Objects.equals(dataDisinvestimentoCalcolata, recessoDatiParametri.dataDisinvestimentoCalcolata) &&
        Objects.equals(dataRichiestaCompleta, recessoDatiParametri.dataRichiestaCompleta) &&
        Objects.equals(dataQuietanzaIncassata, recessoDatiParametri.dataQuietanzaIncassata) &&
        Objects.equals(existQuoteToInvest, recessoDatiParametri.existQuoteToInvest) &&
        Objects.equals(tipoOperazione, recessoDatiParametri.tipoOperazione) &&
        Objects.equals(motivazioneRecesso, recessoDatiParametri.motivazioneRecesso) &&
        Objects.equals(recessoSenzaCostiAttivo, recessoDatiParametri.recessoSenzaCostiAttivo) &&
        Objects.equals(dataDocumento, recessoDatiParametri.dataDocumento) &&
        Objects.equals(dataInvioFU, recessoDatiParametri.dataInvioFU) &&
        Objects.equals(dataUltimaRicezioneDocFU, recessoDatiParametri.dataUltimaRicezioneDocFU) &&
        Objects.equals(eventoRibilNonConcluso, recessoDatiParametri.eventoRibilNonConcluso) &&
        Objects.equals(dataInizioEventoPrior, recessoDatiParametri.dataInizioEventoPrior) &&
        Objects.equals(dscrEventoRibilNonConcluso, recessoDatiParametri.dscrEventoRibilNonConcluso) &&
        Objects.equals(esisteRigaFU, recessoDatiParametri.esisteRigaFU);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dataSistema, dataPrenotazione, dataRichiestaCliente, dataRicevimentoRichiesta, dataDisinvestimento, dataDisinvestimentoCalcolata, dataRichiestaCompleta, dataQuietanzaIncassata, existQuoteToInvest, tipoOperazione, motivazioneRecesso, recessoSenzaCostiAttivo, dataDocumento, dataInvioFU, dataUltimaRicezioneDocFU, eventoRibilNonConcluso, dataInizioEventoPrior, dscrEventoRibilNonConcluso, esisteRigaFU);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RecessoDatiParametri {\n");
    
    sb.append("    dataSistema: ").append(toIndentedString(dataSistema)).append("\n");
    sb.append("    dataPrenotazione: ").append(toIndentedString(dataPrenotazione)).append("\n");
    sb.append("    dataRichiestaCliente: ").append(toIndentedString(dataRichiestaCliente)).append("\n");
    sb.append("    dataRicevimentoRichiesta: ").append(toIndentedString(dataRicevimentoRichiesta)).append("\n");
    sb.append("    dataDisinvestimento: ").append(toIndentedString(dataDisinvestimento)).append("\n");
    sb.append("    dataDisinvestimentoCalcolata: ").append(toIndentedString(dataDisinvestimentoCalcolata)).append("\n");
    sb.append("    dataRichiestaCompleta: ").append(toIndentedString(dataRichiestaCompleta)).append("\n");
    sb.append("    dataQuietanzaIncassata: ").append(toIndentedString(dataQuietanzaIncassata)).append("\n");
    sb.append("    existQuoteToInvest: ").append(toIndentedString(existQuoteToInvest)).append("\n");
    sb.append("    tipoOperazione: ").append(toIndentedString(tipoOperazione)).append("\n");
    sb.append("    motivazioneRecesso: ").append(toIndentedString(motivazioneRecesso)).append("\n");
    sb.append("    recessoSenzaCostiAttivo: ").append(toIndentedString(recessoSenzaCostiAttivo)).append("\n");
    sb.append("    dataDocumento: ").append(toIndentedString(dataDocumento)).append("\n");
    sb.append("    dataInvioFU: ").append(toIndentedString(dataInvioFU)).append("\n");
    sb.append("    dataUltimaRicezioneDocFU: ").append(toIndentedString(dataUltimaRicezioneDocFU)).append("\n");
    sb.append("    eventoRibilNonConcluso: ").append(toIndentedString(eventoRibilNonConcluso)).append("\n");
    sb.append("    dataInizioEventoPrior: ").append(toIndentedString(dataInizioEventoPrior)).append("\n");
    sb.append("    dscrEventoRibilNonConcluso: ").append(toIndentedString(dscrEventoRibilNonConcluso)).append("\n");
    sb.append("    esisteRigaFU: ").append(toIndentedString(esisteRigaFU)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
