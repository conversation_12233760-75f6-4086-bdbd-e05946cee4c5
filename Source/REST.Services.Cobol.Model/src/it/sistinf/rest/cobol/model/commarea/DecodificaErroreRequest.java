package it.sistinf.rest.cobol.model.commarea;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DecodificaErroreRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid List<CodiciErrore> elencoErrori = new ArrayList<CodiciErrore>();

  /**
   **/
  public DecodificaErroreRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public DecodificaErroreRequest elencoErrori(List<CodiciErrore> elencoErrori) {
    this.elencoErrori = elencoErrori;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("elencoErrori")
  @NotNull

  public List<CodiciErrore> getElencoErrori() {
    return elencoErrori;
  }
  public void setElencoErrori(List<CodiciErrore> elencoErrori) {
    this.elencoErrori = elencoErrori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DecodificaErroreRequest decodificaErroreRequest = (DecodificaErroreRequest) o;
    return Objects.equals(headerCobolSrv, decodificaErroreRequest.headerCobolSrv) &&
        Objects.equals(elencoErrori, decodificaErroreRequest.elencoErrori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, elencoErrori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DecodificaErroreRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    elencoErrori: ").append(toIndentedString(elencoErrori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
