package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class BeneficiarioInfo   {

  private @Valid String beneficiario = null;

  private @Valid String modPagamento = null;

  private @Valid String codiceIban = null;

  /**
   **/
  public BeneficiarioInfo beneficiario(String beneficiario) {
    this.beneficiario = beneficiario;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("beneficiario")

  public String getBeneficiario() {
    return beneficiario;
  }
  public void setBeneficiario(String beneficiario) {
    this.beneficiario = beneficiario;
  }

  /**
   **/
  public BeneficiarioInfo modPagamento(String modPagamento) {
    this.modPagamento = modPagamento;
    return this;
  }

  
  @ApiModelProperty(example = "1", value = "")
  @JsonProperty("modPagamento")

  public String getModPagamento() {
    return modPagamento;
  }
  public void setModPagamento(String modPagamento) {
    this.modPagamento = modPagamento;
  }

  /**
   **/
  public BeneficiarioInfo codiceIban(String codiceIban) {
    this.codiceIban = codiceIban;
    return this;
  }

  
  @ApiModelProperty(example = "CROCE ROSSA INTERNAZIONALE : ***************************", value = "")
  @JsonProperty("codiceIban")

  public String getCodiceIban() {
    return codiceIban;
  }
  public void setCodiceIban(String codiceIban) {
    this.codiceIban = codiceIban;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BeneficiarioInfo beneficiarioInfo = (BeneficiarioInfo) o;
    return Objects.equals(beneficiario, beneficiarioInfo.beneficiario) &&
        Objects.equals(modPagamento, beneficiarioInfo.modPagamento) &&
        Objects.equals(codiceIban, beneficiarioInfo.codiceIban);
  }

  @Override
  public int hashCode() {
    return Objects.hash(beneficiario, modPagamento, codiceIban);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BeneficiarioInfo {\n");
    
    sb.append("    beneficiario: ").append(toIndentedString(beneficiario)).append("\n");
    sb.append("    modPagamento: ").append(toIndentedString(modPagamento)).append("\n");
    sb.append("    codiceIban: ").append(toIndentedString(codiceIban)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
