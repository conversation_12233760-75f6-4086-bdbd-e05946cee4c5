package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.ModPagamentoInfoBase;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiPagamento extends ModPagamentoInfoBase  {

  private @Valid String causale = null;

  private @Valid String invioDatiCollettore = null;

  /**
   **/
  public DatiPagamento causale(String causale) {
    this.causale = causale;
    return this;
  }

  
  @ApiModelProperty(example = "1", value = "")
  @JsonProperty("causale")

  public String getCausale() {
    return causale;
  }
  public void setCausale(String causale) {
    this.causale = causale;
  }

  /**
   **/
  public DatiPagamento invioDatiCollettore(String invioDatiCollettore) {
    this.invioDatiCollettore = invioDatiCollettore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("invioDatiCollettore")

  public String getInvioDatiCollettore() {
    return invioDatiCollettore;
  }
  public void setInvioDatiCollettore(String invioDatiCollettore) {
    this.invioDatiCollettore = invioDatiCollettore;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiPagamento datiPagamento = (DatiPagamento) o;
    return Objects.equals(causale, datiPagamento.causale) &&
        Objects.equals(invioDatiCollettore, datiPagamento.invioDatiCollettore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(causale, invioDatiCollettore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiPagamento {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    causale: ").append(toIndentedString(causale)).append("\n");
    sb.append("    invioDatiCollettore: ").append(toIndentedString(invioDatiCollettore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
