package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class Reimpiego   {

  private @Valid String flReimpiego = null;

  private @Valid String tipoReimpiego = null;

  private @Valid BigDecimal impReimpiego = null;

  private @Valid BigDecimal integrazione = null;

  private @Valid String tipoRapporto = null;

  private @Valid String flStessoContraente = null;

  private @Valid List<RapportoReimpiego> elencoRapportoReimpiego = new ArrayList<RapportoReimpiego>();

  /**
   **/
  public Reimpiego flReimpiego(String flReimpiego) {
    this.flReimpiego = flReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flReimpiego")
  

  public String getFlReimpiego() {
    return flReimpiego;
  }
  public void setFlReimpiego(String flReimpiego) {
    this.flReimpiego = flReimpiego;
  }

  /**
   **/
  public Reimpiego tipoReimpiego(String tipoReimpiego) {
    this.tipoReimpiego = tipoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoReimpiego")
  

  public String getTipoReimpiego() {
    return tipoReimpiego;
  }
  public void setTipoReimpiego(String tipoReimpiego) {
    this.tipoReimpiego = tipoReimpiego;
  }

  /**
   **/
  public Reimpiego impReimpiego(BigDecimal impReimpiego) {
    this.impReimpiego = impReimpiego;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("impReimpiego")
  

  public BigDecimal getImpReimpiego() {
    return impReimpiego;
  }
  public void setImpReimpiego(BigDecimal impReimpiego) {
    this.impReimpiego = impReimpiego;
  }

  /**
   **/
  public Reimpiego integrazione(BigDecimal integrazione) {
    this.integrazione = integrazione;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("integrazione")
  

  public BigDecimal getIntegrazione() {
    return integrazione;
  }
  public void setIntegrazione(BigDecimal integrazione) {
    this.integrazione = integrazione;
  }

  /**
   **/
  public Reimpiego tipoRapporto(String tipoRapporto) {
    this.tipoRapporto = tipoRapporto;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRapporto")
  

  public String getTipoRapporto() {
    return tipoRapporto;
  }
  public void setTipoRapporto(String tipoRapporto) {
    this.tipoRapporto = tipoRapporto;
  }

  /**
   **/
  public Reimpiego flStessoContraente(String flStessoContraente) {
    this.flStessoContraente = flStessoContraente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flStessoContraente")
  

  public String getFlStessoContraente() {
    return flStessoContraente;
  }
  public void setFlStessoContraente(String flStessoContraente) {
    this.flStessoContraente = flStessoContraente;
  }

  /**
   **/
  public Reimpiego elencoRapportoReimpiego(List<RapportoReimpiego> elencoRapportoReimpiego) {
    this.elencoRapportoReimpiego = elencoRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoRapportoReimpiego")
  

  public List<RapportoReimpiego> getElencoRapportoReimpiego() {
    return elencoRapportoReimpiego;
  }
  public void setElencoRapportoReimpiego(List<RapportoReimpiego> elencoRapportoReimpiego) {
    this.elencoRapportoReimpiego = elencoRapportoReimpiego;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Reimpiego reimpiego = (Reimpiego) o;
    return Objects.equals(flReimpiego, reimpiego.flReimpiego) &&
        Objects.equals(tipoReimpiego, reimpiego.tipoReimpiego) &&
        Objects.equals(impReimpiego, reimpiego.impReimpiego) &&
        Objects.equals(integrazione, reimpiego.integrazione) &&
        Objects.equals(tipoRapporto, reimpiego.tipoRapporto) &&
        Objects.equals(flStessoContraente, reimpiego.flStessoContraente) &&
        Objects.equals(elencoRapportoReimpiego, reimpiego.elencoRapportoReimpiego);
  }

  @Override
  public int hashCode() {
    return Objects.hash(flReimpiego, tipoReimpiego, impReimpiego, integrazione, tipoRapporto, flStessoContraente, elencoRapportoReimpiego);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Reimpiego {\n");
    
    sb.append("    flReimpiego: ").append(toIndentedString(flReimpiego)).append("\n");
    sb.append("    tipoReimpiego: ").append(toIndentedString(tipoReimpiego)).append("\n");
    sb.append("    impReimpiego: ").append(toIndentedString(impReimpiego)).append("\n");
    sb.append("    integrazione: ").append(toIndentedString(integrazione)).append("\n");
    sb.append("    tipoRapporto: ").append(toIndentedString(tipoRapporto)).append("\n");
    sb.append("    flStessoContraente: ").append(toIndentedString(flStessoContraente)).append("\n");
    sb.append("    elencoRapportoReimpiego: ").append(toIndentedString(elencoRapportoReimpiego)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
