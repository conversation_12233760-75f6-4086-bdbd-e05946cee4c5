package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class RicercaFigureAnagraficheRelazionateResponse   {

  private @Valid List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionateInfo = new ArrayList<FiguraAnagraficaRelazionataInfo>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public RicercaFigureAnagraficheRelazionateResponse listaFigureAnagraficheRelazionateInfo(List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionateInfo) {
    this.listaFigureAnagraficheRelazionateInfo = listaFigureAnagraficheRelazionateInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("listaFigureAnagraficheRelazionateInfo")

  public List<FiguraAnagraficaRelazionataInfo> getListaFigureAnagraficheRelazionateInfo() {
    return listaFigureAnagraficheRelazionateInfo;
  }
  public void setListaFigureAnagraficheRelazionateInfo(List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionateInfo) {
    this.listaFigureAnagraficheRelazionateInfo = listaFigureAnagraficheRelazionateInfo;
  }

  /**
   **/
  public RicercaFigureAnagraficheRelazionateResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RicercaFigureAnagraficheRelazionateResponse ricercaFigureAnagraficheRelazionateResponse = (RicercaFigureAnagraficheRelazionateResponse) o;
    return Objects.equals(listaFigureAnagraficheRelazionateInfo, ricercaFigureAnagraficheRelazionateResponse.listaFigureAnagraficheRelazionateInfo) &&
        Objects.equals(errori, ricercaFigureAnagraficheRelazionateResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(listaFigureAnagraficheRelazionateInfo, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RicercaFigureAnagraficheRelazionateResponse {\n");
    
    sb.append("    listaFigureAnagraficheRelazionateInfo: ").append(toIndentedString(listaFigureAnagraficheRelazionateInfo)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
