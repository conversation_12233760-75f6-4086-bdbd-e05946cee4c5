package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.EsitoResponse;
import it.sistinf.rest.cobol.model.customer.AllCustomerInfo;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class PercipienteInfo extends EsitoResponse  {

  private @Valid Long codiceCliente = null;

  private @Valid BigDecimal percentuale = null;

  private @Valid BigDecimal liquidato = null;

  private @Valid BigDecimal rendita = null;

  private @Valid String scelta = null;

  private @Valid String modPag = null;

  private @Valid String impSudd = null;

  private @Valid String progrTst075 = null;

  private @Valid String progressivo = null;

  private @Valid String codAnag = null;

  private @Valid String modificabile = null;

  private @Valid String flagAnagraf = null;

  private @Valid String flagContoc = null;

  private @Valid String flagAntiric = null;

  private @Valid String percip = null;

  private @Valid String blocco = null;

  private @Valid String liquid = null;

  private @Valid String numQuiet = null;

  private @Valid String progrTst080 = null;

  private @Valid String invioDatiCollettore = null;

  private @Valid AllCustomerInfo allCustomerInfo = null;

  /**
   **/
  public PercipienteInfo codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceCliente")
  

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public PercipienteInfo percentuale(BigDecimal percentuale) {
    this.percentuale = percentuale;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("percentuale")
  

  public BigDecimal getPercentuale() {
    return percentuale;
  }
  public void setPercentuale(BigDecimal percentuale) {
    this.percentuale = percentuale;
  }

  /**
   **/
  public PercipienteInfo liquidato(BigDecimal liquidato) {
    this.liquidato = liquidato;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("liquidato")
  

  public BigDecimal getLiquidato() {
    return liquidato;
  }
  public void setLiquidato(BigDecimal liquidato) {
    this.liquidato = liquidato;
  }

  /**
   **/
  public PercipienteInfo rendita(BigDecimal rendita) {
    this.rendita = rendita;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("rendita")
  

  public BigDecimal getRendita() {
    return rendita;
  }
  public void setRendita(BigDecimal rendita) {
    this.rendita = rendita;
  }

  /**
   **/
  public PercipienteInfo scelta(String scelta) {
    this.scelta = scelta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("scelta")
  

  public String getScelta() {
    return scelta;
  }
  public void setScelta(String scelta) {
    this.scelta = scelta;
  }

  /**
   **/
  public PercipienteInfo modPag(String modPag) {
    this.modPag = modPag;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("modPag")
  

  public String getModPag() {
    return modPag;
  }
  public void setModPag(String modPag) {
    this.modPag = modPag;
  }

  /**
   **/
  public PercipienteInfo impSudd(String impSudd) {
    this.impSudd = impSudd;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("impSudd")
  

  public String getImpSudd() {
    return impSudd;
  }
  public void setImpSudd(String impSudd) {
    this.impSudd = impSudd;
  }

  /**
   **/
  public PercipienteInfo progrTst075(String progrTst075) {
    this.progrTst075 = progrTst075;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("progrTst075")
  

  public String getProgrTst075() {
    return progrTst075;
  }
  public void setProgrTst075(String progrTst075) {
    this.progrTst075 = progrTst075;
  }

  /**
   **/
  public PercipienteInfo progressivo(String progressivo) {
    this.progressivo = progressivo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("progressivo")
  

  public String getProgressivo() {
    return progressivo;
  }
  public void setProgressivo(String progressivo) {
    this.progressivo = progressivo;
  }

  /**
   **/
  public PercipienteInfo codAnag(String codAnag) {
    this.codAnag = codAnag;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codAnag")
  

  public String getCodAnag() {
    return codAnag;
  }
  public void setCodAnag(String codAnag) {
    this.codAnag = codAnag;
  }

  /**
   **/
  public PercipienteInfo modificabile(String modificabile) {
    this.modificabile = modificabile;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("modificabile")
  

  public String getModificabile() {
    return modificabile;
  }
  public void setModificabile(String modificabile) {
    this.modificabile = modificabile;
  }

  /**
   **/
  public PercipienteInfo flagAnagraf(String flagAnagraf) {
    this.flagAnagraf = flagAnagraf;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagAnagraf")
  

  public String getFlagAnagraf() {
    return flagAnagraf;
  }
  public void setFlagAnagraf(String flagAnagraf) {
    this.flagAnagraf = flagAnagraf;
  }

  /**
   **/
  public PercipienteInfo flagContoc(String flagContoc) {
    this.flagContoc = flagContoc;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagContoc")
  

  public String getFlagContoc() {
    return flagContoc;
  }
  public void setFlagContoc(String flagContoc) {
    this.flagContoc = flagContoc;
  }

  /**
   **/
  public PercipienteInfo flagAntiric(String flagAntiric) {
    this.flagAntiric = flagAntiric;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagAntiric")
  

  public String getFlagAntiric() {
    return flagAntiric;
  }
  public void setFlagAntiric(String flagAntiric) {
    this.flagAntiric = flagAntiric;
  }

  /**
   **/
  public PercipienteInfo percip(String percip) {
    this.percip = percip;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("percip")
  

  public String getPercip() {
    return percip;
  }
  public void setPercip(String percip) {
    this.percip = percip;
  }

  /**
   **/
  public PercipienteInfo blocco(String blocco) {
    this.blocco = blocco;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("blocco")
  

  public String getBlocco() {
    return blocco;
  }
  public void setBlocco(String blocco) {
    this.blocco = blocco;
  }

  /**
   **/
  public PercipienteInfo liquid(String liquid) {
    this.liquid = liquid;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("liquid")
  

  public String getLiquid() {
    return liquid;
  }
  public void setLiquid(String liquid) {
    this.liquid = liquid;
  }

  /**
   **/
  public PercipienteInfo numQuiet(String numQuiet) {
    this.numQuiet = numQuiet;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numQuiet")
  

  public String getNumQuiet() {
    return numQuiet;
  }
  public void setNumQuiet(String numQuiet) {
    this.numQuiet = numQuiet;
  }

  /**
   **/
  public PercipienteInfo progrTst080(String progrTst080) {
    this.progrTst080 = progrTst080;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("progrTst080")
  

  public String getProgrTst080() {
    return progrTst080;
  }
  public void setProgrTst080(String progrTst080) {
    this.progrTst080 = progrTst080;
  }

  /**
   **/
  public PercipienteInfo invioDatiCollettore(String invioDatiCollettore) {
    this.invioDatiCollettore = invioDatiCollettore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("invioDatiCollettore")
  

  public String getInvioDatiCollettore() {
    return invioDatiCollettore;
  }
  public void setInvioDatiCollettore(String invioDatiCollettore) {
    this.invioDatiCollettore = invioDatiCollettore;
  }

  /**
   **/
  public PercipienteInfo allCustomerInfo(AllCustomerInfo allCustomerInfo) {
    this.allCustomerInfo = allCustomerInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("allCustomerInfo")
  

  public AllCustomerInfo getAllCustomerInfo() {
    return allCustomerInfo;
  }
  public void setAllCustomerInfo(AllCustomerInfo allCustomerInfo) {
    this.allCustomerInfo = allCustomerInfo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PercipienteInfo percipienteInfo = (PercipienteInfo) o;
    return Objects.equals(codiceCliente, percipienteInfo.codiceCliente) &&
        Objects.equals(percentuale, percipienteInfo.percentuale) &&
        Objects.equals(liquidato, percipienteInfo.liquidato) &&
        Objects.equals(rendita, percipienteInfo.rendita) &&
        Objects.equals(scelta, percipienteInfo.scelta) &&
        Objects.equals(modPag, percipienteInfo.modPag) &&
        Objects.equals(impSudd, percipienteInfo.impSudd) &&
        Objects.equals(progrTst075, percipienteInfo.progrTst075) &&
        Objects.equals(progressivo, percipienteInfo.progressivo) &&
        Objects.equals(codAnag, percipienteInfo.codAnag) &&
        Objects.equals(modificabile, percipienteInfo.modificabile) &&
        Objects.equals(flagAnagraf, percipienteInfo.flagAnagraf) &&
        Objects.equals(flagContoc, percipienteInfo.flagContoc) &&
        Objects.equals(flagAntiric, percipienteInfo.flagAntiric) &&
        Objects.equals(percip, percipienteInfo.percip) &&
        Objects.equals(blocco, percipienteInfo.blocco) &&
        Objects.equals(liquid, percipienteInfo.liquid) &&
        Objects.equals(numQuiet, percipienteInfo.numQuiet) &&
        Objects.equals(progrTst080, percipienteInfo.progrTst080) &&
        Objects.equals(invioDatiCollettore, percipienteInfo.invioDatiCollettore) &&
        Objects.equals(allCustomerInfo, percipienteInfo.allCustomerInfo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, percentuale, liquidato, rendita, scelta, modPag, impSudd, progrTst075, progressivo, codAnag, modificabile, flagAnagraf, flagContoc, flagAntiric, percip, blocco, liquid, numQuiet, progrTst080, invioDatiCollettore, allCustomerInfo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PercipienteInfo {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    percentuale: ").append(toIndentedString(percentuale)).append("\n");
    sb.append("    liquidato: ").append(toIndentedString(liquidato)).append("\n");
    sb.append("    rendita: ").append(toIndentedString(rendita)).append("\n");
    sb.append("    scelta: ").append(toIndentedString(scelta)).append("\n");
    sb.append("    modPag: ").append(toIndentedString(modPag)).append("\n");
    sb.append("    impSudd: ").append(toIndentedString(impSudd)).append("\n");
    sb.append("    progrTst075: ").append(toIndentedString(progrTst075)).append("\n");
    sb.append("    progressivo: ").append(toIndentedString(progressivo)).append("\n");
    sb.append("    codAnag: ").append(toIndentedString(codAnag)).append("\n");
    sb.append("    modificabile: ").append(toIndentedString(modificabile)).append("\n");
    sb.append("    flagAnagraf: ").append(toIndentedString(flagAnagraf)).append("\n");
    sb.append("    flagContoc: ").append(toIndentedString(flagContoc)).append("\n");
    sb.append("    flagAntiric: ").append(toIndentedString(flagAntiric)).append("\n");
    sb.append("    percip: ").append(toIndentedString(percip)).append("\n");
    sb.append("    blocco: ").append(toIndentedString(blocco)).append("\n");
    sb.append("    liquid: ").append(toIndentedString(liquid)).append("\n");
    sb.append("    numQuiet: ").append(toIndentedString(numQuiet)).append("\n");
    sb.append("    progrTst080: ").append(toIndentedString(progrTst080)).append("\n");
    sb.append("    invioDatiCollettore: ").append(toIndentedString(invioDatiCollettore)).append("\n");
    sb.append("    allCustomerInfo: ").append(toIndentedString(allCustomerInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
