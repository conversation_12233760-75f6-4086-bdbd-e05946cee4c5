package it.sistinf.rest.cobol.model.base;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Errore   {

  private @Valid String codErrore = null;

  private @Valid String tipoErrore = null;

  private @Valid String componente = null;

  private @Valid String metodo = null;

  private @Valid String sqlCode = null;

  private @Valid String sqlState = null;

  private @Valid String dispFisico = null;

  private @Valid String abendCode = null;

  private @Valid String descErrore = null;

  private @Valid String descUtente = null;

  private @Valid List<ErroreCampo> erroriCampo = new ArrayList<ErroreCampo>();

  /**
   **/
  public Errore codErrore(String codErrore) {
    this.codErrore = codErrore;
    return this;
  }

  
  @ApiModelProperty(example = "SYSINF0001", required = true, value = "")
  @JsonProperty("codErrore")
  @NotNull

  public String getCodErrore() {
    return codErrore;
  }
  public void setCodErrore(String codErrore) {
    this.codErrore = codErrore;
  }

  /**
   **/
  public Errore tipoErrore(String tipoErrore) {
    this.tipoErrore = tipoErrore;
    return this;
  }

  
  @ApiModelProperty(example = "I", required = true, value = "")
  @JsonProperty("tipoErrore")
  @NotNull

  public String getTipoErrore() {
    return tipoErrore;
  }
  public void setTipoErrore(String tipoErrore) {
    this.tipoErrore = tipoErrore;
  }

  /**
   **/
  public Errore componente(String componente) {
    this.componente = componente;
    return this;
  }

  
  @ApiModelProperty(example = "12345678", value = "")
  @JsonProperty("componente")

  public String getComponente() {
    return componente;
  }
  public void setComponente(String componente) {
    this.componente = componente;
  }

  /**
   **/
  public Errore metodo(String metodo) {
    this.metodo = metodo;
    return this;
  }

  
  @ApiModelProperty(example = "123456789012345678901234567890", value = "")
  @JsonProperty("metodo")

  public String getMetodo() {
    return metodo;
  }
  public void setMetodo(String metodo) {
    this.metodo = metodo;
  }

  /**
   **/
  public Errore sqlCode(String sqlCode) {
    this.sqlCode = sqlCode;
    return this;
  }

  
  @ApiModelProperty(example = "123456", value = "")
  @JsonProperty("sqlCode")

  public String getSqlCode() {
    return sqlCode;
  }
  public void setSqlCode(String sqlCode) {
    this.sqlCode = sqlCode;
  }

  /**
   **/
  public Errore sqlState(String sqlState) {
    this.sqlState = sqlState;
    return this;
  }

  
  @ApiModelProperty(example = "12345", value = "")
  @JsonProperty("sqlState")

  public String getSqlState() {
    return sqlState;
  }
  public void setSqlState(String sqlState) {
    this.sqlState = sqlState;
  }

  /**
   **/
  public Errore dispFisico(String dispFisico) {
    this.dispFisico = dispFisico;
    return this;
  }

  
  @ApiModelProperty(example = "123456789012345678", value = "")
  @JsonProperty("dispFisico")

  public String getDispFisico() {
    return dispFisico;
  }
  public void setDispFisico(String dispFisico) {
    this.dispFisico = dispFisico;
  }

  /**
   **/
  public Errore abendCode(String abendCode) {
    this.abendCode = abendCode;
    return this;
  }

  
  @ApiModelProperty(example = "1234", value = "")
  @JsonProperty("abendCode")

  public String getAbendCode() {
    return abendCode;
  }
  public void setAbendCode(String abendCode) {
    this.abendCode = abendCode;
  }

  /**
   **/
  public Errore descErrore(String descErrore) {
    this.descErrore = descErrore;
    return this;
  }

  
  @ApiModelProperty(example = "12345678901234567890123456789012345678901234567890123456789012345678901234567890", value = "")
  @JsonProperty("descErrore")

  public String getDescErrore() {
    return descErrore;
  }
  public void setDescErrore(String descErrore) {
    this.descErrore = descErrore;
  }

  /**
   **/
  public Errore descUtente(String descUtente) {
    this.descUtente = descUtente;
    return this;
  }

  
  @ApiModelProperty(example = "123456789012345678901234567890", value = "")
  @JsonProperty("descUtente")

  public String getDescUtente() {
    return descUtente;
  }
  public void setDescUtente(String descUtente) {
    this.descUtente = descUtente;
  }

  /**
   **/
  public Errore erroriCampo(List<ErroreCampo> erroriCampo) {
    this.erroriCampo = erroriCampo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("erroriCampo")

  public List<ErroreCampo> getErroriCampo() {
    return erroriCampo;
  }
  public void setErroriCampo(List<ErroreCampo> erroriCampo) {
    this.erroriCampo = erroriCampo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Errore errore = (Errore) o;
    return Objects.equals(codErrore, errore.codErrore) &&
        Objects.equals(tipoErrore, errore.tipoErrore) &&
        Objects.equals(componente, errore.componente) &&
        Objects.equals(metodo, errore.metodo) &&
        Objects.equals(sqlCode, errore.sqlCode) &&
        Objects.equals(sqlState, errore.sqlState) &&
        Objects.equals(dispFisico, errore.dispFisico) &&
        Objects.equals(abendCode, errore.abendCode) &&
        Objects.equals(descErrore, errore.descErrore) &&
        Objects.equals(descUtente, errore.descUtente) &&
        Objects.equals(erroriCampo, errore.erroriCampo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codErrore, tipoErrore, componente, metodo, sqlCode, sqlState, dispFisico, abendCode, descErrore, descUtente, erroriCampo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Errore {\n");
    
    sb.append("    codErrore: ").append(toIndentedString(codErrore)).append("\n");
    sb.append("    tipoErrore: ").append(toIndentedString(tipoErrore)).append("\n");
    sb.append("    componente: ").append(toIndentedString(componente)).append("\n");
    sb.append("    metodo: ").append(toIndentedString(metodo)).append("\n");
    sb.append("    sqlCode: ").append(toIndentedString(sqlCode)).append("\n");
    sb.append("    sqlState: ").append(toIndentedString(sqlState)).append("\n");
    sb.append("    dispFisico: ").append(toIndentedString(dispFisico)).append("\n");
    sb.append("    abendCode: ").append(toIndentedString(abendCode)).append("\n");
    sb.append("    descErrore: ").append(toIndentedString(descErrore)).append("\n");
    sb.append("    descUtente: ").append(toIndentedString(descUtente)).append("\n");
    sb.append("    erroriCampo: ").append(toIndentedString(erroriCampo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
