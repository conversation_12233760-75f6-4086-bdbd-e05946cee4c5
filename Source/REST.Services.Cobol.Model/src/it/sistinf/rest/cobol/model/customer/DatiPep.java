package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiPep   {

  private @Valid Boolean isPep = null;

  private @Valid DettaglioPep dettaglioPep = null;

  /**
   **/
  public DatiPep isPep(Boolean isPep) {
    this.isPep = isPep;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("isPep")
  @NotNull

  public Boolean isIsPep() {
    return isPep;
  }
  public void setIsPep(Boolean isPep) {
    this.isPep = isPep;
  }

  /**
   **/
  public DatiPep dettaglioPep(DettaglioPep dettaglioPep) {
    this.dettaglioPep = dettaglioPep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dettaglioPep")

  public DettaglioPep getDettaglioPep() {
    return dettaglioPep;
  }
  public void setDettaglioPep(DettaglioPep dettaglioPep) {
    this.dettaglioPep = dettaglioPep;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiPep datiPep = (DatiPep) o;
    return Objects.equals(isPep, datiPep.isPep) &&
        Objects.equals(dettaglioPep, datiPep.dettaglioPep);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isPep, dettaglioPep);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiPep {\n");
    
    sb.append("    isPep: ").append(toIndentedString(isPep)).append("\n");
    sb.append("    dettaglioPep: ").append(toIndentedString(dettaglioPep)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
