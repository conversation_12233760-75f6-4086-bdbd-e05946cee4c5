package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class InfoFinanziarieRequest   {

  private @Valid String username = null;

  private @Valid Long codiceCliente = null;

  private @Valid SituazioneFinanziariaInfo situazioneFinanziariaInfo = null;

  /**
   **/
  public InfoFinanziarieRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }

  /**
   * Codice Cliente NAW.
   **/
  public InfoFinanziarieRequest codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")
  @NotNull

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public InfoFinanziarieRequest situazioneFinanziariaInfo(SituazioneFinanziariaInfo situazioneFinanziariaInfo) {
    this.situazioneFinanziariaInfo = situazioneFinanziariaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("situazioneFinanziariaInfo")
  @NotNull

  public SituazioneFinanziariaInfo getSituazioneFinanziariaInfo() {
    return situazioneFinanziariaInfo;
  }
  public void setSituazioneFinanziariaInfo(SituazioneFinanziariaInfo situazioneFinanziariaInfo) {
    this.situazioneFinanziariaInfo = situazioneFinanziariaInfo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoFinanziarieRequest infoFinanziarieRequest = (InfoFinanziarieRequest) o;
    return Objects.equals(username, infoFinanziarieRequest.username) &&
        Objects.equals(codiceCliente, infoFinanziarieRequest.codiceCliente) &&
        Objects.equals(situazioneFinanziariaInfo, infoFinanziarieRequest.situazioneFinanziariaInfo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(username, codiceCliente, situazioneFinanziariaInfo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoFinanziarieRequest {\n");
    
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    situazioneFinanziariaInfo: ").append(toIndentedString(situazioneFinanziariaInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
