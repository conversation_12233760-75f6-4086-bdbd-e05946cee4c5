package it.sistinf.rest.cobol.model.polizza.anagrafica;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class DatiCustomer   {

  private @Valid Integer codiceCliente = null;

  private @Valid String cognome = null;

  private @Valid String nome = null;

  private @Valid String codiceFiscale = null;

  private @Valid Date dataDiNascita = null;

  private @Valid String codProvinciaDiNascita = null;

  private @Valid String luogoDiNascita = null;

public enum CodSessoEnum {

    M(String.valueOf("M")), F(String.valueOf("F")), _U(String.valueOf(" "));


    private String value;

    CodSessoEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static CodSessoEnum fromValue(String v) {
        for (CodSessoEnum b : CodSessoEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid CodSessoEnum codSesso = null;

  private @Valid String codiceProfessione = null;

public enum TipoPersonaEnum {

    F(String.valueOf("F")), G(String.valueOf("G"));


    private String value;

    TipoPersonaEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoPersonaEnum fromValue(String v) {
        for (TipoPersonaEnum b : TipoPersonaEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoPersonaEnum tipoPersona = null;

  private @Valid String cittadinanza1 = null;

  private @Valid String cittadinanza2 = null;

  private @Valid String cittadinanza3 = null;

  private @Valid String codProvinciaDiResidenza = null;

  private @Valid String comDiResidenza = null;

  private @Valid String indirizzoDiResidenza = null;

  private @Valid String capResidenza = null;

  private @Valid String codStato = null;

  private @Valid String titoloStudio = null;

  private @Valid String codTipoDocumento = null;

  private @Valid String numeroDocumento = null;

  private @Valid String luogoRilascio = null;

  private @Valid Date dataRilascio = null;

  private @Valid String enteRilascio = null;

  private @Valid Integer cab = null;

  private @Valid String gruppoAR = null;

  private @Valid String sottogruppoAR = null;

  private @Valid String atecoAR = null;

  private @Valid String politicamenteEsposta = null;

  private @Valid String tasseEstero = null;

  private @Valid String tasseEsteroCodStato = null;

  private @Valid String usStatus = null;

  private @Valid String taxIdNumber = null;

  private @Valid String attPrevalenteCod = null;

  private @Valid String origineFondi = null;

  private @Valid String origineFondiAltro = null;

  private @Valid String codStatoRilascio = null;

  private @Valid String codProvinciaRilascio = null;

  private @Valid Date dataScadenza = null;

  private @Valid String altroRelazione = null;

  private @Valid String tipoRelazione = null;

  private @Valid Boolean flDelega = null;

  private @Valid String tipoRappresentanza = null;

  private @Valid String altroTipoRappresentanza = null;

  /**
   **/
  public DatiCustomer codiceCliente(Integer codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(example = "*********", value = "")
  @JsonProperty("codiceCliente")
  

  public Integer getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Integer codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public DatiCustomer cognome(String cognome) {
    this.cognome = cognome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cognome")
  

  public String getCognome() {
    return cognome;
  }
  public void setCognome(String cognome) {
    this.cognome = cognome;
  }

  /**
   **/
  public DatiCustomer nome(String nome) {
    this.nome = nome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nome")
  

  public String getNome() {
    return nome;
  }
  public void setNome(String nome) {
    this.nome = nome;
  }

  /**
   **/
  public DatiCustomer codiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceFiscale")
  

  public String getCodiceFiscale() {
    return codiceFiscale;
  }
  public void setCodiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
  }

  /**
   **/
  public DatiCustomer dataDiNascita(Date dataDiNascita) {
    this.dataDiNascita = dataDiNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataDiNascita")
  

  public Date getDataDiNascita() {
    return dataDiNascita;
  }
  public void setDataDiNascita(Date dataDiNascita) {
    this.dataDiNascita = dataDiNascita;
  }

  /**
   **/
  public DatiCustomer codProvinciaDiNascita(String codProvinciaDiNascita) {
    this.codProvinciaDiNascita = codProvinciaDiNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codProvinciaDiNascita")
  

  public String getCodProvinciaDiNascita() {
    return codProvinciaDiNascita;
  }
  public void setCodProvinciaDiNascita(String codProvinciaDiNascita) {
    this.codProvinciaDiNascita = codProvinciaDiNascita;
  }

  /**
   **/
  public DatiCustomer luogoDiNascita(String luogoDiNascita) {
    this.luogoDiNascita = luogoDiNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoDiNascita")
  

  public String getLuogoDiNascita() {
    return luogoDiNascita;
  }
  public void setLuogoDiNascita(String luogoDiNascita) {
    this.luogoDiNascita = luogoDiNascita;
  }

  /**
   **/
  public DatiCustomer codSesso(CodSessoEnum codSesso) {
    this.codSesso = codSesso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codSesso")
  

  public CodSessoEnum getCodSesso() {
    return codSesso;
  }
  public void setCodSesso(CodSessoEnum codSesso) {
    this.codSesso = codSesso;
  }

  /**
   **/
  public DatiCustomer codiceProfessione(String codiceProfessione) {
    this.codiceProfessione = codiceProfessione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceProfessione")
  

  public String getCodiceProfessione() {
    return codiceProfessione;
  }
  public void setCodiceProfessione(String codiceProfessione) {
    this.codiceProfessione = codiceProfessione;
  }

  /**
   **/
  public DatiCustomer tipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoPersona")
  

  public TipoPersonaEnum getTipoPersona() {
    return tipoPersona;
  }
  public void setTipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
  }

  /**
   **/
  public DatiCustomer cittadinanza1(String cittadinanza1) {
    this.cittadinanza1 = cittadinanza1;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza1")
  

  public String getCittadinanza1() {
    return cittadinanza1;
  }
  public void setCittadinanza1(String cittadinanza1) {
    this.cittadinanza1 = cittadinanza1;
  }

  /**
   **/
  public DatiCustomer cittadinanza2(String cittadinanza2) {
    this.cittadinanza2 = cittadinanza2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza2")
  

  public String getCittadinanza2() {
    return cittadinanza2;
  }
  public void setCittadinanza2(String cittadinanza2) {
    this.cittadinanza2 = cittadinanza2;
  }

  /**
   **/
  public DatiCustomer cittadinanza3(String cittadinanza3) {
    this.cittadinanza3 = cittadinanza3;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza3")
  

  public String getCittadinanza3() {
    return cittadinanza3;
  }
  public void setCittadinanza3(String cittadinanza3) {
    this.cittadinanza3 = cittadinanza3;
  }

  /**
   **/
  public DatiCustomer codProvinciaDiResidenza(String codProvinciaDiResidenza) {
    this.codProvinciaDiResidenza = codProvinciaDiResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codProvinciaDiResidenza")
  

  public String getCodProvinciaDiResidenza() {
    return codProvinciaDiResidenza;
  }
  public void setCodProvinciaDiResidenza(String codProvinciaDiResidenza) {
    this.codProvinciaDiResidenza = codProvinciaDiResidenza;
  }

  /**
   **/
  public DatiCustomer comDiResidenza(String comDiResidenza) {
    this.comDiResidenza = comDiResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("comDiResidenza")
  

  public String getComDiResidenza() {
    return comDiResidenza;
  }
  public void setComDiResidenza(String comDiResidenza) {
    this.comDiResidenza = comDiResidenza;
  }

  /**
   **/
  public DatiCustomer indirizzoDiResidenza(String indirizzoDiResidenza) {
    this.indirizzoDiResidenza = indirizzoDiResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("indirizzoDiResidenza")
  

  public String getIndirizzoDiResidenza() {
    return indirizzoDiResidenza;
  }
  public void setIndirizzoDiResidenza(String indirizzoDiResidenza) {
    this.indirizzoDiResidenza = indirizzoDiResidenza;
  }

  /**
   **/
  public DatiCustomer capResidenza(String capResidenza) {
    this.capResidenza = capResidenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("capResidenza")
  

  public String getCapResidenza() {
    return capResidenza;
  }
  public void setCapResidenza(String capResidenza) {
    this.capResidenza = capResidenza;
  }

  /**
   **/
  public DatiCustomer codStato(String codStato) {
    this.codStato = codStato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codStato")
  

  public String getCodStato() {
    return codStato;
  }
  public void setCodStato(String codStato) {
    this.codStato = codStato;
  }

  /**
   **/
  public DatiCustomer titoloStudio(String titoloStudio) {
    this.titoloStudio = titoloStudio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("titoloStudio")
  

  public String getTitoloStudio() {
    return titoloStudio;
  }
  public void setTitoloStudio(String titoloStudio) {
    this.titoloStudio = titoloStudio;
  }

  /**
   **/
  public DatiCustomer codTipoDocumento(String codTipoDocumento) {
    this.codTipoDocumento = codTipoDocumento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codTipoDocumento")
  

  public String getCodTipoDocumento() {
    return codTipoDocumento;
  }
  public void setCodTipoDocumento(String codTipoDocumento) {
    this.codTipoDocumento = codTipoDocumento;
  }

  /**
   **/
  public DatiCustomer numeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numeroDocumento")
  

  public String getNumeroDocumento() {
    return numeroDocumento;
  }
  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  /**
   **/
  public DatiCustomer luogoRilascio(String luogoRilascio) {
    this.luogoRilascio = luogoRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoRilascio")
  

  public String getLuogoRilascio() {
    return luogoRilascio;
  }
  public void setLuogoRilascio(String luogoRilascio) {
    this.luogoRilascio = luogoRilascio;
  }

  /**
   **/
  public DatiCustomer dataRilascio(Date dataRilascio) {
    this.dataRilascio = dataRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRilascio")
  

  public Date getDataRilascio() {
    return dataRilascio;
  }
  public void setDataRilascio(Date dataRilascio) {
    this.dataRilascio = dataRilascio;
  }

  /**
   **/
  public DatiCustomer enteRilascio(String enteRilascio) {
    this.enteRilascio = enteRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("enteRilascio")
  

  public String getEnteRilascio() {
    return enteRilascio;
  }
  public void setEnteRilascio(String enteRilascio) {
    this.enteRilascio = enteRilascio;
  }

  /**
   **/
  public DatiCustomer cab(Integer cab) {
    this.cab = cab;
    return this;
  }

  
  @ApiModelProperty(example = "12345", value = "")
  @JsonProperty("cab")
  

  public Integer getCab() {
    return cab;
  }
  public void setCab(Integer cab) {
    this.cab = cab;
  }

  /**
   **/
  public DatiCustomer gruppoAR(String gruppoAR) {
    this.gruppoAR = gruppoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("gruppoAR")
  

  public String getGruppoAR() {
    return gruppoAR;
  }
  public void setGruppoAR(String gruppoAR) {
    this.gruppoAR = gruppoAR;
  }

  /**
   **/
  public DatiCustomer sottogruppoAR(String sottogruppoAR) {
    this.sottogruppoAR = sottogruppoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sottogruppoAR")
  

  public String getSottogruppoAR() {
    return sottogruppoAR;
  }
  public void setSottogruppoAR(String sottogruppoAR) {
    this.sottogruppoAR = sottogruppoAR;
  }

  /**
   **/
  public DatiCustomer atecoAR(String atecoAR) {
    this.atecoAR = atecoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("atecoAR")
  

  public String getAtecoAR() {
    return atecoAR;
  }
  public void setAtecoAR(String atecoAR) {
    this.atecoAR = atecoAR;
  }

  /**
   **/
  public DatiCustomer politicamenteEsposta(String politicamenteEsposta) {
    this.politicamenteEsposta = politicamenteEsposta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("politicamenteEsposta")
  

  public String getPoliticamenteEsposta() {
    return politicamenteEsposta;
  }
  public void setPoliticamenteEsposta(String politicamenteEsposta) {
    this.politicamenteEsposta = politicamenteEsposta;
  }

  /**
   **/
  public DatiCustomer tasseEstero(String tasseEstero) {
    this.tasseEstero = tasseEstero;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tasseEstero")
  

  public String getTasseEstero() {
    return tasseEstero;
  }
  public void setTasseEstero(String tasseEstero) {
    this.tasseEstero = tasseEstero;
  }

  /**
   **/
  public DatiCustomer tasseEsteroCodStato(String tasseEsteroCodStato) {
    this.tasseEsteroCodStato = tasseEsteroCodStato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tasseEsteroCodStato")
  

  public String getTasseEsteroCodStato() {
    return tasseEsteroCodStato;
  }
  public void setTasseEsteroCodStato(String tasseEsteroCodStato) {
    this.tasseEsteroCodStato = tasseEsteroCodStato;
  }

  /**
   **/
  public DatiCustomer usStatus(String usStatus) {
    this.usStatus = usStatus;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("usStatus")
  

  public String getUsStatus() {
    return usStatus;
  }
  public void setUsStatus(String usStatus) {
    this.usStatus = usStatus;
  }

  /**
   **/
  public DatiCustomer taxIdNumber(String taxIdNumber) {
    this.taxIdNumber = taxIdNumber;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("taxIdNumber")
  

  public String getTaxIdNumber() {
    return taxIdNumber;
  }
  public void setTaxIdNumber(String taxIdNumber) {
    this.taxIdNumber = taxIdNumber;
  }

  /**
   **/
  public DatiCustomer attPrevalenteCod(String attPrevalenteCod) {
    this.attPrevalenteCod = attPrevalenteCod;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attPrevalenteCod")
  

  public String getAttPrevalenteCod() {
    return attPrevalenteCod;
  }
  public void setAttPrevalenteCod(String attPrevalenteCod) {
    this.attPrevalenteCod = attPrevalenteCod;
  }

  /**
   **/
  public DatiCustomer origineFondi(String origineFondi) {
    this.origineFondi = origineFondi;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("origineFondi")
  

  public String getOrigineFondi() {
    return origineFondi;
  }
  public void setOrigineFondi(String origineFondi) {
    this.origineFondi = origineFondi;
  }

  /**
   **/
  public DatiCustomer origineFondiAltro(String origineFondiAltro) {
    this.origineFondiAltro = origineFondiAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("origineFondiAltro")
  

  public String getOrigineFondiAltro() {
    return origineFondiAltro;
  }
  public void setOrigineFondiAltro(String origineFondiAltro) {
    this.origineFondiAltro = origineFondiAltro;
  }

  /**
   **/
  public DatiCustomer codStatoRilascio(String codStatoRilascio) {
    this.codStatoRilascio = codStatoRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codStatoRilascio")
  

  public String getCodStatoRilascio() {
    return codStatoRilascio;
  }
  public void setCodStatoRilascio(String codStatoRilascio) {
    this.codStatoRilascio = codStatoRilascio;
  }

  /**
   **/
  public DatiCustomer codProvinciaRilascio(String codProvinciaRilascio) {
    this.codProvinciaRilascio = codProvinciaRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codProvinciaRilascio")
  

  public String getCodProvinciaRilascio() {
    return codProvinciaRilascio;
  }
  public void setCodProvinciaRilascio(String codProvinciaRilascio) {
    this.codProvinciaRilascio = codProvinciaRilascio;
  }

  /**
   **/
  public DatiCustomer dataScadenza(Date dataScadenza) {
    this.dataScadenza = dataScadenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataScadenza")
  

  public Date getDataScadenza() {
    return dataScadenza;
  }
  public void setDataScadenza(Date dataScadenza) {
    this.dataScadenza = dataScadenza;
  }

  /**
   **/
  public DatiCustomer altroRelazione(String altroRelazione) {
    this.altroRelazione = altroRelazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("altroRelazione")
  

  public String getAltroRelazione() {
    return altroRelazione;
  }
  public void setAltroRelazione(String altroRelazione) {
    this.altroRelazione = altroRelazione;
  }

  /**
   **/
  public DatiCustomer tipoRelazione(String tipoRelazione) {
    this.tipoRelazione = tipoRelazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRelazione")
  

  public String getTipoRelazione() {
    return tipoRelazione;
  }
  public void setTipoRelazione(String tipoRelazione) {
    this.tipoRelazione = tipoRelazione;
  }

  /**
   **/
  public DatiCustomer flDelega(Boolean flDelega) {
    this.flDelega = flDelega;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flDelega")
  

  public Boolean isFlDelega() {
    return flDelega;
  }
  public void setFlDelega(Boolean flDelega) {
    this.flDelega = flDelega;
  }

  /**
   **/
  public DatiCustomer tipoRappresentanza(String tipoRappresentanza) {
    this.tipoRappresentanza = tipoRappresentanza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRappresentanza")
  

  public String getTipoRappresentanza() {
    return tipoRappresentanza;
  }
  public void setTipoRappresentanza(String tipoRappresentanza) {
    this.tipoRappresentanza = tipoRappresentanza;
  }

  /**
   **/
  public DatiCustomer altroTipoRappresentanza(String altroTipoRappresentanza) {
    this.altroTipoRappresentanza = altroTipoRappresentanza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("altroTipoRappresentanza")
  

  public String getAltroTipoRappresentanza() {
    return altroTipoRappresentanza;
  }
  public void setAltroTipoRappresentanza(String altroTipoRappresentanza) {
    this.altroTipoRappresentanza = altroTipoRappresentanza;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiCustomer datiCustomer = (DatiCustomer) o;
    return Objects.equals(codiceCliente, datiCustomer.codiceCliente) &&
        Objects.equals(cognome, datiCustomer.cognome) &&
        Objects.equals(nome, datiCustomer.nome) &&
        Objects.equals(codiceFiscale, datiCustomer.codiceFiscale) &&
        Objects.equals(dataDiNascita, datiCustomer.dataDiNascita) &&
        Objects.equals(codProvinciaDiNascita, datiCustomer.codProvinciaDiNascita) &&
        Objects.equals(luogoDiNascita, datiCustomer.luogoDiNascita) &&
        Objects.equals(codSesso, datiCustomer.codSesso) &&
        Objects.equals(codiceProfessione, datiCustomer.codiceProfessione) &&
        Objects.equals(tipoPersona, datiCustomer.tipoPersona) &&
        Objects.equals(cittadinanza1, datiCustomer.cittadinanza1) &&
        Objects.equals(cittadinanza2, datiCustomer.cittadinanza2) &&
        Objects.equals(cittadinanza3, datiCustomer.cittadinanza3) &&
        Objects.equals(codProvinciaDiResidenza, datiCustomer.codProvinciaDiResidenza) &&
        Objects.equals(comDiResidenza, datiCustomer.comDiResidenza) &&
        Objects.equals(indirizzoDiResidenza, datiCustomer.indirizzoDiResidenza) &&
        Objects.equals(capResidenza, datiCustomer.capResidenza) &&
        Objects.equals(codStato, datiCustomer.codStato) &&
        Objects.equals(titoloStudio, datiCustomer.titoloStudio) &&
        Objects.equals(codTipoDocumento, datiCustomer.codTipoDocumento) &&
        Objects.equals(numeroDocumento, datiCustomer.numeroDocumento) &&
        Objects.equals(luogoRilascio, datiCustomer.luogoRilascio) &&
        Objects.equals(dataRilascio, datiCustomer.dataRilascio) &&
        Objects.equals(enteRilascio, datiCustomer.enteRilascio) &&
        Objects.equals(cab, datiCustomer.cab) &&
        Objects.equals(gruppoAR, datiCustomer.gruppoAR) &&
        Objects.equals(sottogruppoAR, datiCustomer.sottogruppoAR) &&
        Objects.equals(atecoAR, datiCustomer.atecoAR) &&
        Objects.equals(politicamenteEsposta, datiCustomer.politicamenteEsposta) &&
        Objects.equals(tasseEstero, datiCustomer.tasseEstero) &&
        Objects.equals(tasseEsteroCodStato, datiCustomer.tasseEsteroCodStato) &&
        Objects.equals(usStatus, datiCustomer.usStatus) &&
        Objects.equals(taxIdNumber, datiCustomer.taxIdNumber) &&
        Objects.equals(attPrevalenteCod, datiCustomer.attPrevalenteCod) &&
        Objects.equals(origineFondi, datiCustomer.origineFondi) &&
        Objects.equals(origineFondiAltro, datiCustomer.origineFondiAltro) &&
        Objects.equals(codStatoRilascio, datiCustomer.codStatoRilascio) &&
        Objects.equals(codProvinciaRilascio, datiCustomer.codProvinciaRilascio) &&
        Objects.equals(dataScadenza, datiCustomer.dataScadenza) &&
        Objects.equals(altroRelazione, datiCustomer.altroRelazione) &&
        Objects.equals(tipoRelazione, datiCustomer.tipoRelazione) &&
        Objects.equals(flDelega, datiCustomer.flDelega) &&
        Objects.equals(tipoRappresentanza, datiCustomer.tipoRappresentanza) &&
        Objects.equals(altroTipoRappresentanza, datiCustomer.altroTipoRappresentanza);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, cognome, nome, codiceFiscale, dataDiNascita, codProvinciaDiNascita, luogoDiNascita, codSesso, codiceProfessione, tipoPersona, cittadinanza1, cittadinanza2, cittadinanza3, codProvinciaDiResidenza, comDiResidenza, indirizzoDiResidenza, capResidenza, codStato, titoloStudio, codTipoDocumento, numeroDocumento, luogoRilascio, dataRilascio, enteRilascio, cab, gruppoAR, sottogruppoAR, atecoAR, politicamenteEsposta, tasseEstero, tasseEsteroCodStato, usStatus, taxIdNumber, attPrevalenteCod, origineFondi, origineFondiAltro, codStatoRilascio, codProvinciaRilascio, dataScadenza, altroRelazione, tipoRelazione, flDelega, tipoRappresentanza, altroTipoRappresentanza);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiCustomer {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    cognome: ").append(toIndentedString(cognome)).append("\n");
    sb.append("    nome: ").append(toIndentedString(nome)).append("\n");
    sb.append("    codiceFiscale: ").append(toIndentedString(codiceFiscale)).append("\n");
    sb.append("    dataDiNascita: ").append(toIndentedString(dataDiNascita)).append("\n");
    sb.append("    codProvinciaDiNascita: ").append(toIndentedString(codProvinciaDiNascita)).append("\n");
    sb.append("    luogoDiNascita: ").append(toIndentedString(luogoDiNascita)).append("\n");
    sb.append("    codSesso: ").append(toIndentedString(codSesso)).append("\n");
    sb.append("    codiceProfessione: ").append(toIndentedString(codiceProfessione)).append("\n");
    sb.append("    tipoPersona: ").append(toIndentedString(tipoPersona)).append("\n");
    sb.append("    cittadinanza1: ").append(toIndentedString(cittadinanza1)).append("\n");
    sb.append("    cittadinanza2: ").append(toIndentedString(cittadinanza2)).append("\n");
    sb.append("    cittadinanza3: ").append(toIndentedString(cittadinanza3)).append("\n");
    sb.append("    codProvinciaDiResidenza: ").append(toIndentedString(codProvinciaDiResidenza)).append("\n");
    sb.append("    comDiResidenza: ").append(toIndentedString(comDiResidenza)).append("\n");
    sb.append("    indirizzoDiResidenza: ").append(toIndentedString(indirizzoDiResidenza)).append("\n");
    sb.append("    capResidenza: ").append(toIndentedString(capResidenza)).append("\n");
    sb.append("    codStato: ").append(toIndentedString(codStato)).append("\n");
    sb.append("    titoloStudio: ").append(toIndentedString(titoloStudio)).append("\n");
    sb.append("    codTipoDocumento: ").append(toIndentedString(codTipoDocumento)).append("\n");
    sb.append("    numeroDocumento: ").append(toIndentedString(numeroDocumento)).append("\n");
    sb.append("    luogoRilascio: ").append(toIndentedString(luogoRilascio)).append("\n");
    sb.append("    dataRilascio: ").append(toIndentedString(dataRilascio)).append("\n");
    sb.append("    enteRilascio: ").append(toIndentedString(enteRilascio)).append("\n");
    sb.append("    cab: ").append(toIndentedString(cab)).append("\n");
    sb.append("    gruppoAR: ").append(toIndentedString(gruppoAR)).append("\n");
    sb.append("    sottogruppoAR: ").append(toIndentedString(sottogruppoAR)).append("\n");
    sb.append("    atecoAR: ").append(toIndentedString(atecoAR)).append("\n");
    sb.append("    politicamenteEsposta: ").append(toIndentedString(politicamenteEsposta)).append("\n");
    sb.append("    tasseEstero: ").append(toIndentedString(tasseEstero)).append("\n");
    sb.append("    tasseEsteroCodStato: ").append(toIndentedString(tasseEsteroCodStato)).append("\n");
    sb.append("    usStatus: ").append(toIndentedString(usStatus)).append("\n");
    sb.append("    taxIdNumber: ").append(toIndentedString(taxIdNumber)).append("\n");
    sb.append("    attPrevalenteCod: ").append(toIndentedString(attPrevalenteCod)).append("\n");
    sb.append("    origineFondi: ").append(toIndentedString(origineFondi)).append("\n");
    sb.append("    origineFondiAltro: ").append(toIndentedString(origineFondiAltro)).append("\n");
    sb.append("    codStatoRilascio: ").append(toIndentedString(codStatoRilascio)).append("\n");
    sb.append("    codProvinciaRilascio: ").append(toIndentedString(codProvinciaRilascio)).append("\n");
    sb.append("    dataScadenza: ").append(toIndentedString(dataScadenza)).append("\n");
    sb.append("    altroRelazione: ").append(toIndentedString(altroRelazione)).append("\n");
    sb.append("    tipoRelazione: ").append(toIndentedString(tipoRelazione)).append("\n");
    sb.append("    flDelega: ").append(toIndentedString(flDelega)).append("\n");
    sb.append("    tipoRappresentanza: ").append(toIndentedString(tipoRappresentanza)).append("\n");
    sb.append("    altroTipoRappresentanza: ").append(toIndentedString(altroTipoRappresentanza)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
