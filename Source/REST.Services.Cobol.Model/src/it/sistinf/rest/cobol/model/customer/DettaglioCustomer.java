package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DettaglioCustomer   {

  private @Valid Long codiceCliente = null;

  private @Valid Boolean esito = null;

  private @Valid List<CompagnieInfo> compagnie = new ArrayList<CompagnieInfo>();

  /**
   * Codice Cliente NAW.
   **/
  public DettaglioCustomer codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public DettaglioCustomer esito(Boolean esito) {
    this.esito = esito;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("esito")

  public Boolean isEsito() {
    return esito;
  }
  public void setEsito(Boolean esito) {
    this.esito = esito;
  }

  /**
   **/
  public DettaglioCustomer compagnie(List<CompagnieInfo> compagnie) {
    this.compagnie = compagnie;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("compagnie")

  public List<CompagnieInfo> getCompagnie() {
    return compagnie;
  }
  public void setCompagnie(List<CompagnieInfo> compagnie) {
    this.compagnie = compagnie;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DettaglioCustomer dettaglioCustomer = (DettaglioCustomer) o;
    return Objects.equals(codiceCliente, dettaglioCustomer.codiceCliente) &&
        Objects.equals(esito, dettaglioCustomer.esito) &&
        Objects.equals(compagnie, dettaglioCustomer.compagnie);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, esito, compagnie);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DettaglioCustomer {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    esito: ").append(toIndentedString(esito)).append("\n");
    sb.append("    compagnie: ").append(toIndentedString(compagnie)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
