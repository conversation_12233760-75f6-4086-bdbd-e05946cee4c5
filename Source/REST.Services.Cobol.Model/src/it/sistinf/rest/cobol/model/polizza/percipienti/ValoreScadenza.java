package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ValoreScadenza   {

  private @Valid BigDecimal impNettoOperaz = null;

  private @Valid DatiInfoAgg datiInfoAgg = null;

  /**
   **/
  public ValoreScadenza impNettoOperaz(BigDecimal impNettoOperaz) {
    this.impNettoOperaz = impNettoOperaz;
    return this;
  }

  
  @ApiModelProperty(example = "200000000.01", value = "")
  @JsonProperty("impNettoOperaz")
  @NotNull

  public BigDecimal getImpNettoOperaz() {
    return impNettoOperaz;
  }
  public void setImpNettoOperaz(BigDecimal impNettoOperaz) {
    this.impNettoOperaz = impNettoOperaz;
  }

  /**
   **/
  public ValoreScadenza datiInfoAgg(DatiInfoAgg datiInfoAgg) {
    this.datiInfoAgg = datiInfoAgg;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiInfoAgg")
  @NotNull

  public DatiInfoAgg getDatiInfoAgg() {
    return datiInfoAgg;
  }
  public void setDatiInfoAgg(DatiInfoAgg datiInfoAgg) {
    this.datiInfoAgg = datiInfoAgg;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValoreScadenza valoreScadenza = (ValoreScadenza) o;
    return Objects.equals(impNettoOperaz, valoreScadenza.impNettoOperaz) &&
        Objects.equals(datiInfoAgg, valoreScadenza.datiInfoAgg);
  }

  @Override
  public int hashCode() {
    return Objects.hash(impNettoOperaz, datiInfoAgg);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValoreScadenza {\n");
    
    sb.append("    impNettoOperaz: ").append(toIndentedString(impNettoOperaz)).append("\n");
    sb.append("    datiInfoAgg: ").append(toIndentedString(datiInfoAgg)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
