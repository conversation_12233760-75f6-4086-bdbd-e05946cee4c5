package it.sistinf.rest.cobol.model.base;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HeaderCobolSrv   {

  private @Valid String returnCode = null;

  private @Valid Integer codSoc = null;

  private @Valid String canaleLogico = null;

  private @Valid String idTerminale = null;

  private @Valid String idUtente = null;

  private @Valid String idLingua = null;

  private @Valid String permKey = null;

  private @Valid String permKeyOld = null;

  private @Valid String permKeyCustomer = null;

  private @Valid String transactionToMirror = null;

  private @Valid String flRecuperoPolizza = null;

  private @Valid String idRecuperoPolizza = null;

  private @Valid String livelloGestione = null;

  /**
   **/
  public HeaderCobolSrv returnCode(String returnCode) {
    this.returnCode = returnCode;
    return this;
  }

  
  @ApiModelProperty(example = "00", value = "")
  @JsonProperty("returnCode")

  public String getReturnCode() {
    return returnCode;
  }
  public void setReturnCode(String returnCode) {
    this.returnCode = returnCode;
  }

  /**
   **/
  public HeaderCobolSrv codSoc(Integer codSoc) {
    this.codSoc = codSoc;
    return this;
  }

  
  @ApiModelProperty(example = "143", required = true, value = "")
  @JsonProperty("codSoc")
  @NotNull

  public Integer getCodSoc() {
    return codSoc;
  }
  public void setCodSoc(Integer codSoc) {
    this.codSoc = codSoc;
  }

  /**
   **/
  public HeaderCobolSrv canaleLogico(String canaleLogico) {
    this.canaleLogico = canaleLogico;
    return this;
  }

  
  @ApiModelProperty(example = "BANCA", value = "")
  @JsonProperty("canaleLogico")

  public String getCanaleLogico() {
    return canaleLogico;
  }
  public void setCanaleLogico(String canaleLogico) {
    this.canaleLogico = canaleLogico;
  }

  /**
   **/
  public HeaderCobolSrv idTerminale(String idTerminale) {
    this.idTerminale = idTerminale;
    return this;
  }

  
  @ApiModelProperty(example = "0:0:0:0:0:0:0:1", value = "")
  @JsonProperty("idTerminale")

  public String getIdTerminale() {
    return idTerminale;
  }
  public void setIdTerminale(String idTerminale) {
    this.idTerminale = idTerminale;
  }

  /**
   **/
  public HeaderCobolSrv idUtente(String idUtente) {
    this.idUtente = idUtente;
    return this;
  }

  
  @ApiModelProperty(example = "C20286  ", required = true, value = "")
  @JsonProperty("idUtente")
  @NotNull
  @Size(min=1,max=8)
  public String getIdUtente() {
    return idUtente;
  }
  public void setIdUtente(String idUtente) {
    this.idUtente = idUtente;
  }

  /**
   **/
  public HeaderCobolSrv idLingua(String idLingua) {
    this.idLingua = idLingua;
    return this;
  }

  
  @ApiModelProperty(example = "ITA", value = "")
  @JsonProperty("idLingua")

  public String getIdLingua() {
    return idLingua;
  }
  public void setIdLingua(String idLingua) {
    this.idLingua = idLingua;
  }

  /**
   **/
  public HeaderCobolSrv permKey(String permKey) {
    this.permKey = permKey;
    return this;
  }

  
  @ApiModelProperty(example = "2022-12-13-18.35.25.711687", value = "")
  @JsonProperty("permKey")

  public String getPermKey() {
    return permKey;
  }
  public void setPermKey(String permKey) {
    this.permKey = permKey;
  }

  /**
   **/
  public HeaderCobolSrv permKeyOld(String permKeyOld) {
    this.permKeyOld = permKeyOld;
    return this;
  }

  
  @ApiModelProperty(example = "2022-12-13-18.35.25.711687", value = "")
  @JsonProperty("permKeyOld")

  public String getPermKeyOld() {
    return permKeyOld;
  }
  public void setPermKeyOld(String permKeyOld) {
    this.permKeyOld = permKeyOld;
  }

  /**
   **/
  public HeaderCobolSrv permKeyCustomer(String permKeyCustomer) {
    this.permKeyCustomer = permKeyCustomer;
    return this;
  }

  
  @ApiModelProperty(example = "2022-12-13-18.35.25.711687", value = "")
  @JsonProperty("permKeyCustomer")

  public String getPermKeyCustomer() {
    return permKeyCustomer;
  }
  public void setPermKeyCustomer(String permKeyCustomer) {
    this.permKeyCustomer = permKeyCustomer;
  }

  /**
   **/
  public HeaderCobolSrv transactionToMirror(String transactionToMirror) {
    this.transactionToMirror = transactionToMirror;
    return this;
  }

  
  @ApiModelProperty(example = " ", value = "")
  @JsonProperty("transactionToMirror")

  public String getTransactionToMirror() {
    return transactionToMirror;
  }
  public void setTransactionToMirror(String transactionToMirror) {
    this.transactionToMirror = transactionToMirror;
  }

  /**
   **/
  public HeaderCobolSrv flRecuperoPolizza(String flRecuperoPolizza) {
    this.flRecuperoPolizza = flRecuperoPolizza;
    return this;
  }

  
  @ApiModelProperty(example = " ", value = "")
  @JsonProperty("flRecuperoPolizza")

  public String getFlRecuperoPolizza() {
    return flRecuperoPolizza;
  }
  public void setFlRecuperoPolizza(String flRecuperoPolizza) {
    this.flRecuperoPolizza = flRecuperoPolizza;
  }

  /**
   **/
  public HeaderCobolSrv idRecuperoPolizza(String idRecuperoPolizza) {
    this.idRecuperoPolizza = idRecuperoPolizza;
    return this;
  }

  
  @ApiModelProperty(example = "123456789", value = "")
  @JsonProperty("idRecuperoPolizza")

  public String getIdRecuperoPolizza() {
    return idRecuperoPolizza;
  }
  public void setIdRecuperoPolizza(String idRecuperoPolizza) {
    this.idRecuperoPolizza = idRecuperoPolizza;
  }

  /**
   **/
  public HeaderCobolSrv livelloGestione(String livelloGestione) {
    this.livelloGestione = livelloGestione;
    return this;
  }

  
  @ApiModelProperty(example = " ", value = "")
  @JsonProperty("livelloGestione")

  public String getLivelloGestione() {
    return livelloGestione;
  }
  public void setLivelloGestione(String livelloGestione) {
    this.livelloGestione = livelloGestione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HeaderCobolSrv headerCobolSrv = (HeaderCobolSrv) o;
    return Objects.equals(returnCode, headerCobolSrv.returnCode) &&
        Objects.equals(codSoc, headerCobolSrv.codSoc) &&
        Objects.equals(canaleLogico, headerCobolSrv.canaleLogico) &&
        Objects.equals(idTerminale, headerCobolSrv.idTerminale) &&
        Objects.equals(idUtente, headerCobolSrv.idUtente) &&
        Objects.equals(idLingua, headerCobolSrv.idLingua) &&
        Objects.equals(permKey, headerCobolSrv.permKey) &&
        Objects.equals(permKeyOld, headerCobolSrv.permKeyOld) &&
        Objects.equals(permKeyCustomer, headerCobolSrv.permKeyCustomer) &&
        Objects.equals(transactionToMirror, headerCobolSrv.transactionToMirror) &&
        Objects.equals(flRecuperoPolizza, headerCobolSrv.flRecuperoPolizza) &&
        Objects.equals(idRecuperoPolizza, headerCobolSrv.idRecuperoPolizza) &&
        Objects.equals(livelloGestione, headerCobolSrv.livelloGestione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(returnCode, codSoc, canaleLogico, idTerminale, idUtente, idLingua, permKey, permKeyOld, permKeyCustomer, transactionToMirror, flRecuperoPolizza, idRecuperoPolizza, livelloGestione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HeaderCobolSrv {\n");
    
    sb.append("    returnCode: ").append(toIndentedString(returnCode)).append("\n");
    sb.append("    codSoc: ").append(toIndentedString(codSoc)).append("\n");
    sb.append("    canaleLogico: ").append(toIndentedString(canaleLogico)).append("\n");
    sb.append("    idTerminale: ").append(toIndentedString(idTerminale)).append("\n");
    sb.append("    idUtente: ").append(toIndentedString(idUtente)).append("\n");
    sb.append("    idLingua: ").append(toIndentedString(idLingua)).append("\n");
    sb.append("    permKey: ").append(toIndentedString(permKey)).append("\n");
    sb.append("    permKeyOld: ").append(toIndentedString(permKeyOld)).append("\n");
    sb.append("    permKeyCustomer: ").append(toIndentedString(permKeyCustomer)).append("\n");
    sb.append("    transactionToMirror: ").append(toIndentedString(transactionToMirror)).append("\n");
    sb.append("    flRecuperoPolizza: ").append(toIndentedString(flRecuperoPolizza)).append("\n");
    sb.append("    idRecuperoPolizza: ").append(toIndentedString(idRecuperoPolizza)).append("\n");
    sb.append("    livelloGestione: ").append(toIndentedString(livelloGestione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
