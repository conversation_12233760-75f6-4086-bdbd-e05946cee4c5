package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class Dac2Info   {

  private @Valid String giin = null;

  private @Valid Boolean flgTasseEsteroDac2 = null;

  private @Valid String naz1TasseEsteroDac2 = null;

  private @Valid String naz2TasseEsteroDac2 = null;

  private @Valid String cod1NifDac2 = null;

  private @Valid String cod2NifDac2 = null;

  private @Valid String descNaz1TasseEsteroDac2 = null;

  private @Valid String descNaz2TasseEsteroDac2 = null;

  /**
   **/
  public Dac2Info giin(String giin) {
    this.giin = giin;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("giin")

  public String getGiin() {
    return giin;
  }
  public void setGiin(String giin) {
    this.giin = giin;
  }

  /**
   **/
  public Dac2Info flgTasseEsteroDac2(Boolean flgTasseEsteroDac2) {
    this.flgTasseEsteroDac2 = flgTasseEsteroDac2;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flgTasseEsteroDac2")

  public Boolean isFlgTasseEsteroDac2() {
    return flgTasseEsteroDac2;
  }
  public void setFlgTasseEsteroDac2(Boolean flgTasseEsteroDac2) {
    this.flgTasseEsteroDac2 = flgTasseEsteroDac2;
  }

  /**
   **/
  public Dac2Info naz1TasseEsteroDac2(String naz1TasseEsteroDac2) {
    this.naz1TasseEsteroDac2 = naz1TasseEsteroDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("naz1TasseEsteroDac2")

  public String getNaz1TasseEsteroDac2() {
    return naz1TasseEsteroDac2;
  }
  public void setNaz1TasseEsteroDac2(String naz1TasseEsteroDac2) {
    this.naz1TasseEsteroDac2 = naz1TasseEsteroDac2;
  }

  /**
   **/
  public Dac2Info naz2TasseEsteroDac2(String naz2TasseEsteroDac2) {
    this.naz2TasseEsteroDac2 = naz2TasseEsteroDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("naz2TasseEsteroDac2")

  public String getNaz2TasseEsteroDac2() {
    return naz2TasseEsteroDac2;
  }
  public void setNaz2TasseEsteroDac2(String naz2TasseEsteroDac2) {
    this.naz2TasseEsteroDac2 = naz2TasseEsteroDac2;
  }

  /**
   **/
  public Dac2Info cod1NifDac2(String cod1NifDac2) {
    this.cod1NifDac2 = cod1NifDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cod1NifDac2")

  public String getCod1NifDac2() {
    return cod1NifDac2;
  }
  public void setCod1NifDac2(String cod1NifDac2) {
    this.cod1NifDac2 = cod1NifDac2;
  }

  /**
   **/
  public Dac2Info cod2NifDac2(String cod2NifDac2) {
    this.cod2NifDac2 = cod2NifDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cod2NifDac2")

  public String getCod2NifDac2() {
    return cod2NifDac2;
  }
  public void setCod2NifDac2(String cod2NifDac2) {
    this.cod2NifDac2 = cod2NifDac2;
  }

  /**
   **/
  public Dac2Info descNaz1TasseEsteroDac2(String descNaz1TasseEsteroDac2) {
    this.descNaz1TasseEsteroDac2 = descNaz1TasseEsteroDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descNaz1TasseEsteroDac2")

  public String getDescNaz1TasseEsteroDac2() {
    return descNaz1TasseEsteroDac2;
  }
  public void setDescNaz1TasseEsteroDac2(String descNaz1TasseEsteroDac2) {
    this.descNaz1TasseEsteroDac2 = descNaz1TasseEsteroDac2;
  }

  /**
   **/
  public Dac2Info descNaz2TasseEsteroDac2(String descNaz2TasseEsteroDac2) {
    this.descNaz2TasseEsteroDac2 = descNaz2TasseEsteroDac2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descNaz2TasseEsteroDac2")

  public String getDescNaz2TasseEsteroDac2() {
    return descNaz2TasseEsteroDac2;
  }
  public void setDescNaz2TasseEsteroDac2(String descNaz2TasseEsteroDac2) {
    this.descNaz2TasseEsteroDac2 = descNaz2TasseEsteroDac2;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Dac2Info dac2Info = (Dac2Info) o;
    return Objects.equals(giin, dac2Info.giin) &&
        Objects.equals(flgTasseEsteroDac2, dac2Info.flgTasseEsteroDac2) &&
        Objects.equals(naz1TasseEsteroDac2, dac2Info.naz1TasseEsteroDac2) &&
        Objects.equals(naz2TasseEsteroDac2, dac2Info.naz2TasseEsteroDac2) &&
        Objects.equals(cod1NifDac2, dac2Info.cod1NifDac2) &&
        Objects.equals(cod2NifDac2, dac2Info.cod2NifDac2) &&
        Objects.equals(descNaz1TasseEsteroDac2, dac2Info.descNaz1TasseEsteroDac2) &&
        Objects.equals(descNaz2TasseEsteroDac2, dac2Info.descNaz2TasseEsteroDac2);
  }

  @Override
  public int hashCode() {
    return Objects.hash(giin, flgTasseEsteroDac2, naz1TasseEsteroDac2, naz2TasseEsteroDac2, cod1NifDac2, cod2NifDac2, descNaz1TasseEsteroDac2, descNaz2TasseEsteroDac2);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Dac2Info {\n");
    
    sb.append("    giin: ").append(toIndentedString(giin)).append("\n");
    sb.append("    flgTasseEsteroDac2: ").append(toIndentedString(flgTasseEsteroDac2)).append("\n");
    sb.append("    naz1TasseEsteroDac2: ").append(toIndentedString(naz1TasseEsteroDac2)).append("\n");
    sb.append("    naz2TasseEsteroDac2: ").append(toIndentedString(naz2TasseEsteroDac2)).append("\n");
    sb.append("    cod1NifDac2: ").append(toIndentedString(cod1NifDac2)).append("\n");
    sb.append("    cod2NifDac2: ").append(toIndentedString(cod2NifDac2)).append("\n");
    sb.append("    descNaz1TasseEsteroDac2: ").append(toIndentedString(descNaz1TasseEsteroDac2)).append("\n");
    sb.append("    descNaz2TasseEsteroDac2: ").append(toIndentedString(descNaz2TasseEsteroDac2)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
