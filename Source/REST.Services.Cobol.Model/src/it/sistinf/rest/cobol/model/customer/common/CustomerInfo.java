package it.sistinf.rest.cobol.model.customer.common;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.customer.Dac2Info;
import it.sistinf.rest.cobol.model.customer.FiguraAnagraficaRelazionataInfo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class CustomerInfo   {

  private @Valid Long codiceCliente = null;

  private @Valid String nominativo = null;

  private @Valid String nome = null;

  private @Valid String sesso = null;

  private @Valid String cognomeNome = null;

  private @Valid String indirizzoRes = null;

  private @Valid String statoNascita = null;

  private @Valid String desStatoNascita = null;

  private @Valid String provNascita = null;

  private @Valid String luogoNascita = null;

  private @Valid String codiceStato = null;

  private @Valid String siglaProvincia = null;

  private @Valid String citta = null;

  private @Valid String cap = null;

  private @Valid String desUrbanaInd = null;

  private @Valid String desStradaInd = null;

  private @Valid String numCivico = null;

  private @Valid String desStato = null;

  private @Valid Date dataNascita = null;

  private @Valid String codiceFiscale = null;

public enum TipoPersonaEnum {

    F(String.valueOf("F")), G(String.valueOf("G"));


    private String value;

    TipoPersonaEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoPersonaEnum fromValue(String v) {
        for (TipoPersonaEnum b : TipoPersonaEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoPersonaEnum tipoPersona = null;

  private @Valid Boolean codiceFiscaleForzatura = null;

  private @Valid String desProvinciaNascita = null;

  private @Valid String desProvincia = null;

  private @Valid Boolean personaEspostaPoliticamente = null;

  private @Valid Boolean pepInCarica = null;

  private @Valid String caricaPep = null;

  private @Valid String partitaIva = null;

  private @Valid String codiceFiscaleAzienda = null;

  private @Valid Dac2Info dac2Info = null;

  private @Valid List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionate = new ArrayList<FiguraAnagraficaRelazionataInfo>();

  private @Valid String tipoSocieta = null;

  /**
   * Codice Cliente NAW.
   **/
  public CustomerInfo codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public CustomerInfo nominativo(String nominativo) {
    this.nominativo = nominativo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nominativo")

  public String getNominativo() {
    return nominativo;
  }
  public void setNominativo(String nominativo) {
    this.nominativo = nominativo;
  }

  /**
   **/
  public CustomerInfo nome(String nome) {
    this.nome = nome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nome")

  public String getNome() {
    return nome;
  }
  public void setNome(String nome) {
    this.nome = nome;
  }

  /**
   **/
  public CustomerInfo sesso(String sesso) {
    this.sesso = sesso;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sesso")

  public String getSesso() {
    return sesso;
  }
  public void setSesso(String sesso) {
    this.sesso = sesso;
  }

  /**
   **/
  public CustomerInfo cognomeNome(String cognomeNome) {
    this.cognomeNome = cognomeNome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cognomeNome")

  public String getCognomeNome() {
    return cognomeNome;
  }
  public void setCognomeNome(String cognomeNome) {
    this.cognomeNome = cognomeNome;
  }

  /**
   **/
  public CustomerInfo indirizzoRes(String indirizzoRes) {
    this.indirizzoRes = indirizzoRes;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("indirizzoRes")

  public String getIndirizzoRes() {
    return indirizzoRes;
  }
  public void setIndirizzoRes(String indirizzoRes) {
    this.indirizzoRes = indirizzoRes;
  }

  /**
   **/
  public CustomerInfo statoNascita(String statoNascita) {
    this.statoNascita = statoNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoNascita")

  public String getStatoNascita() {
    return statoNascita;
  }
  public void setStatoNascita(String statoNascita) {
    this.statoNascita = statoNascita;
  }

  /**
   **/
  public CustomerInfo desStatoNascita(String desStatoNascita) {
    this.desStatoNascita = desStatoNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStatoNascita")

  public String getDesStatoNascita() {
    return desStatoNascita;
  }
  public void setDesStatoNascita(String desStatoNascita) {
    this.desStatoNascita = desStatoNascita;
  }

  /**
   **/
  public CustomerInfo provNascita(String provNascita) {
    this.provNascita = provNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("provNascita")

  public String getProvNascita() {
    return provNascita;
  }
  public void setProvNascita(String provNascita) {
    this.provNascita = provNascita;
  }

  /**
   **/
  public CustomerInfo luogoNascita(String luogoNascita) {
    this.luogoNascita = luogoNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoNascita")

  public String getLuogoNascita() {
    return luogoNascita;
  }
  public void setLuogoNascita(String luogoNascita) {
    this.luogoNascita = luogoNascita;
  }

  /**
   **/
  public CustomerInfo codiceStato(String codiceStato) {
    this.codiceStato = codiceStato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceStato")

  public String getCodiceStato() {
    return codiceStato;
  }
  public void setCodiceStato(String codiceStato) {
    this.codiceStato = codiceStato;
  }

  /**
   **/
  public CustomerInfo siglaProvincia(String siglaProvincia) {
    this.siglaProvincia = siglaProvincia;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("siglaProvincia")

  public String getSiglaProvincia() {
    return siglaProvincia;
  }
  public void setSiglaProvincia(String siglaProvincia) {
    this.siglaProvincia = siglaProvincia;
  }

  /**
   **/
  public CustomerInfo citta(String citta) {
    this.citta = citta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("citta")

  public String getCitta() {
    return citta;
  }
  public void setCitta(String citta) {
    this.citta = citta;
  }

  /**
   **/
  public CustomerInfo cap(String cap) {
    this.cap = cap;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cap")

  public String getCap() {
    return cap;
  }
  public void setCap(String cap) {
    this.cap = cap;
  }

  /**
   **/
  public CustomerInfo desUrbanaInd(String desUrbanaInd) {
    this.desUrbanaInd = desUrbanaInd;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desUrbanaInd")

  public String getDesUrbanaInd() {
    return desUrbanaInd;
  }
  public void setDesUrbanaInd(String desUrbanaInd) {
    this.desUrbanaInd = desUrbanaInd;
  }

  /**
   **/
  public CustomerInfo desStradaInd(String desStradaInd) {
    this.desStradaInd = desStradaInd;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStradaInd")

  public String getDesStradaInd() {
    return desStradaInd;
  }
  public void setDesStradaInd(String desStradaInd) {
    this.desStradaInd = desStradaInd;
  }

  /**
   **/
  public CustomerInfo numCivico(String numCivico) {
    this.numCivico = numCivico;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numCivico")

  public String getNumCivico() {
    return numCivico;
  }
  public void setNumCivico(String numCivico) {
    this.numCivico = numCivico;
  }

  /**
   **/
  public CustomerInfo desStato(String desStato) {
    this.desStato = desStato;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStato")

  public String getDesStato() {
    return desStato;
  }
  public void setDesStato(String desStato) {
    this.desStato = desStato;
  }

  /**
   **/
  public CustomerInfo dataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataNascita")

  public Date getDataNascita() {
    return dataNascita;
  }
  public void setDataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
  }

  /**
   **/
  public CustomerInfo codiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceFiscale")

  public String getCodiceFiscale() {
    return codiceFiscale;
  }
  public void setCodiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
  }

  /**
   **/
  public CustomerInfo tipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoPersona")

  public TipoPersonaEnum getTipoPersona() {
    return tipoPersona;
  }
  public void setTipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
  }

  /**
   **/
  public CustomerInfo codiceFiscaleForzatura(Boolean codiceFiscaleForzatura) {
    this.codiceFiscaleForzatura = codiceFiscaleForzatura;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("codiceFiscaleForzatura")

  public Boolean isCodiceFiscaleForzatura() {
    return codiceFiscaleForzatura;
  }
  public void setCodiceFiscaleForzatura(Boolean codiceFiscaleForzatura) {
    this.codiceFiscaleForzatura = codiceFiscaleForzatura;
  }

  /**
   **/
  public CustomerInfo desProvinciaNascita(String desProvinciaNascita) {
    this.desProvinciaNascita = desProvinciaNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desProvinciaNascita")

  public String getDesProvinciaNascita() {
    return desProvinciaNascita;
  }
  public void setDesProvinciaNascita(String desProvinciaNascita) {
    this.desProvinciaNascita = desProvinciaNascita;
  }

  /**
   **/
  public CustomerInfo desProvincia(String desProvincia) {
    this.desProvincia = desProvincia;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desProvincia")

  public String getDesProvincia() {
    return desProvincia;
  }
  public void setDesProvincia(String desProvincia) {
    this.desProvincia = desProvincia;
  }

  /**
   **/
  public CustomerInfo personaEspostaPoliticamente(Boolean personaEspostaPoliticamente) {
    this.personaEspostaPoliticamente = personaEspostaPoliticamente;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("personaEspostaPoliticamente")

  public Boolean isPersonaEspostaPoliticamente() {
    return personaEspostaPoliticamente;
  }
  public void setPersonaEspostaPoliticamente(Boolean personaEspostaPoliticamente) {
    this.personaEspostaPoliticamente = personaEspostaPoliticamente;
  }

  /**
   **/
  public CustomerInfo pepInCarica(Boolean pepInCarica) {
    this.pepInCarica = pepInCarica;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("pepInCarica")

  public Boolean isPepInCarica() {
    return pepInCarica;
  }
  public void setPepInCarica(Boolean pepInCarica) {
    this.pepInCarica = pepInCarica;
  }

  /**
   **/
  public CustomerInfo caricaPep(String caricaPep) {
    this.caricaPep = caricaPep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("caricaPep")

  public String getCaricaPep() {
    return caricaPep;
  }
  public void setCaricaPep(String caricaPep) {
    this.caricaPep = caricaPep;
  }

  /**
   **/
  public CustomerInfo partitaIva(String partitaIva) {
    this.partitaIva = partitaIva;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("partitaIva")

  public String getPartitaIva() {
    return partitaIva;
  }
  public void setPartitaIva(String partitaIva) {
    this.partitaIva = partitaIva;
  }

  /**
   **/
  public CustomerInfo codiceFiscaleAzienda(String codiceFiscaleAzienda) {
    this.codiceFiscaleAzienda = codiceFiscaleAzienda;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceFiscaleAzienda")

  public String getCodiceFiscaleAzienda() {
    return codiceFiscaleAzienda;
  }
  public void setCodiceFiscaleAzienda(String codiceFiscaleAzienda) {
    this.codiceFiscaleAzienda = codiceFiscaleAzienda;
  }

  /**
   **/
  public CustomerInfo dac2Info(Dac2Info dac2Info) {
    this.dac2Info = dac2Info;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dac2Info")

  public Dac2Info getDac2Info() {
    return dac2Info;
  }
  public void setDac2Info(Dac2Info dac2Info) {
    this.dac2Info = dac2Info;
  }

  /**
   **/
  public CustomerInfo listaFigureAnagraficheRelazionate(List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionate) {
    this.listaFigureAnagraficheRelazionate = listaFigureAnagraficheRelazionate;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("listaFigureAnagraficheRelazionate")

  public List<FiguraAnagraficaRelazionataInfo> getListaFigureAnagraficheRelazionate() {
    return listaFigureAnagraficheRelazionate;
  }
  public void setListaFigureAnagraficheRelazionate(List<FiguraAnagraficaRelazionataInfo> listaFigureAnagraficheRelazionate) {
    this.listaFigureAnagraficheRelazionate = listaFigureAnagraficheRelazionate;
  }

  /**
   **/
  public CustomerInfo tipoSocieta(String tipoSocieta) {
    this.tipoSocieta = tipoSocieta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoSocieta")

  public String getTipoSocieta() {
    return tipoSocieta;
  }
  public void setTipoSocieta(String tipoSocieta) {
    this.tipoSocieta = tipoSocieta;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerInfo customerInfo = (CustomerInfo) o;
    return Objects.equals(codiceCliente, customerInfo.codiceCliente) &&
        Objects.equals(nominativo, customerInfo.nominativo) &&
        Objects.equals(nome, customerInfo.nome) &&
        Objects.equals(sesso, customerInfo.sesso) &&
        Objects.equals(cognomeNome, customerInfo.cognomeNome) &&
        Objects.equals(indirizzoRes, customerInfo.indirizzoRes) &&
        Objects.equals(statoNascita, customerInfo.statoNascita) &&
        Objects.equals(desStatoNascita, customerInfo.desStatoNascita) &&
        Objects.equals(provNascita, customerInfo.provNascita) &&
        Objects.equals(luogoNascita, customerInfo.luogoNascita) &&
        Objects.equals(codiceStato, customerInfo.codiceStato) &&
        Objects.equals(siglaProvincia, customerInfo.siglaProvincia) &&
        Objects.equals(citta, customerInfo.citta) &&
        Objects.equals(cap, customerInfo.cap) &&
        Objects.equals(desUrbanaInd, customerInfo.desUrbanaInd) &&
        Objects.equals(desStradaInd, customerInfo.desStradaInd) &&
        Objects.equals(numCivico, customerInfo.numCivico) &&
        Objects.equals(desStato, customerInfo.desStato) &&
        Objects.equals(dataNascita, customerInfo.dataNascita) &&
        Objects.equals(codiceFiscale, customerInfo.codiceFiscale) &&
        Objects.equals(tipoPersona, customerInfo.tipoPersona) &&
        Objects.equals(codiceFiscaleForzatura, customerInfo.codiceFiscaleForzatura) &&
        Objects.equals(desProvinciaNascita, customerInfo.desProvinciaNascita) &&
        Objects.equals(desProvincia, customerInfo.desProvincia) &&
        Objects.equals(personaEspostaPoliticamente, customerInfo.personaEspostaPoliticamente) &&
        Objects.equals(pepInCarica, customerInfo.pepInCarica) &&
        Objects.equals(caricaPep, customerInfo.caricaPep) &&
        Objects.equals(partitaIva, customerInfo.partitaIva) &&
        Objects.equals(codiceFiscaleAzienda, customerInfo.codiceFiscaleAzienda) &&
        Objects.equals(dac2Info, customerInfo.dac2Info) &&
        Objects.equals(listaFigureAnagraficheRelazionate, customerInfo.listaFigureAnagraficheRelazionate) &&
        Objects.equals(tipoSocieta, customerInfo.tipoSocieta);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, nominativo, nome, sesso, cognomeNome, indirizzoRes, statoNascita, desStatoNascita, provNascita, luogoNascita, codiceStato, siglaProvincia, citta, cap, desUrbanaInd, desStradaInd, numCivico, desStato, dataNascita, codiceFiscale, tipoPersona, codiceFiscaleForzatura, desProvinciaNascita, desProvincia, personaEspostaPoliticamente, pepInCarica, caricaPep, partitaIva, codiceFiscaleAzienda, dac2Info, listaFigureAnagraficheRelazionate, tipoSocieta);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerInfo {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    nominativo: ").append(toIndentedString(nominativo)).append("\n");
    sb.append("    nome: ").append(toIndentedString(nome)).append("\n");
    sb.append("    sesso: ").append(toIndentedString(sesso)).append("\n");
    sb.append("    cognomeNome: ").append(toIndentedString(cognomeNome)).append("\n");
    sb.append("    indirizzoRes: ").append(toIndentedString(indirizzoRes)).append("\n");
    sb.append("    statoNascita: ").append(toIndentedString(statoNascita)).append("\n");
    sb.append("    desStatoNascita: ").append(toIndentedString(desStatoNascita)).append("\n");
    sb.append("    provNascita: ").append(toIndentedString(provNascita)).append("\n");
    sb.append("    luogoNascita: ").append(toIndentedString(luogoNascita)).append("\n");
    sb.append("    codiceStato: ").append(toIndentedString(codiceStato)).append("\n");
    sb.append("    siglaProvincia: ").append(toIndentedString(siglaProvincia)).append("\n");
    sb.append("    citta: ").append(toIndentedString(citta)).append("\n");
    sb.append("    cap: ").append(toIndentedString(cap)).append("\n");
    sb.append("    desUrbanaInd: ").append(toIndentedString(desUrbanaInd)).append("\n");
    sb.append("    desStradaInd: ").append(toIndentedString(desStradaInd)).append("\n");
    sb.append("    numCivico: ").append(toIndentedString(numCivico)).append("\n");
    sb.append("    desStato: ").append(toIndentedString(desStato)).append("\n");
    sb.append("    dataNascita: ").append(toIndentedString(dataNascita)).append("\n");
    sb.append("    codiceFiscale: ").append(toIndentedString(codiceFiscale)).append("\n");
    sb.append("    tipoPersona: ").append(toIndentedString(tipoPersona)).append("\n");
    sb.append("    codiceFiscaleForzatura: ").append(toIndentedString(codiceFiscaleForzatura)).append("\n");
    sb.append("    desProvinciaNascita: ").append(toIndentedString(desProvinciaNascita)).append("\n");
    sb.append("    desProvincia: ").append(toIndentedString(desProvincia)).append("\n");
    sb.append("    personaEspostaPoliticamente: ").append(toIndentedString(personaEspostaPoliticamente)).append("\n");
    sb.append("    pepInCarica: ").append(toIndentedString(pepInCarica)).append("\n");
    sb.append("    caricaPep: ").append(toIndentedString(caricaPep)).append("\n");
    sb.append("    partitaIva: ").append(toIndentedString(partitaIva)).append("\n");
    sb.append("    codiceFiscaleAzienda: ").append(toIndentedString(codiceFiscaleAzienda)).append("\n");
    sb.append("    dac2Info: ").append(toIndentedString(dac2Info)).append("\n");
    sb.append("    listaFigureAnagraficheRelazionate: ").append(toIndentedString(listaFigureAnagraficheRelazionate)).append("\n");
    sb.append("    tipoSocieta: ").append(toIndentedString(tipoSocieta)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}