package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class AggiornaFigureAnagraficheRelazionateRequest   {

  private @Valid FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata = null;

  private @Valid String username = null;

  /**
   **/
  public AggiornaFigureAnagraficheRelazionateRequest figuraAnagraficaRelazionata(FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata) {
    this.figuraAnagraficaRelazionata = figuraAnagraficaRelazionata;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("figuraAnagraficaRelazionata")
  @NotNull

  public FiguraAnagraficaRelazionataInfo getFiguraAnagraficaRelazionata() {
    return figuraAnagraficaRelazionata;
  }
  public void setFiguraAnagraficaRelazionata(FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata) {
    this.figuraAnagraficaRelazionata = figuraAnagraficaRelazionata;
  }

  /**
   **/
  public AggiornaFigureAnagraficheRelazionateRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AggiornaFigureAnagraficheRelazionateRequest aggiornaFigureAnagraficheRelazionateRequest = (AggiornaFigureAnagraficheRelazionateRequest) o;
    return Objects.equals(figuraAnagraficaRelazionata, aggiornaFigureAnagraficheRelazionateRequest.figuraAnagraficaRelazionata) &&
        Objects.equals(username, aggiornaFigureAnagraficheRelazionateRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(figuraAnagraficaRelazionata, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AggiornaFigureAnagraficheRelazionateRequest {\n");
    
    sb.append("    figuraAnagraficaRelazionata: ").append(toIndentedString(figuraAnagraficaRelazionata)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
