package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class LifeCycleInfo   {

public enum TipoOperazioneEnum {

    ATTIVAZIONE(String.valueOf("ATTIVAZIONE")), DISATTIVAZIONE(String.valueOf("DISATTIVAZIONE"));


    private String value;

    TipoOperazioneEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoOperazioneEnum fromValue(String v) {
        for (TipoOperazioneEnum b : TipoOperazioneEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoOperazioneEnum tipoOperazione = null;

  /**
   **/
  public LifeCycleInfo tipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoOperazione")

  public TipoOperazioneEnum getTipoOperazione() {
    return tipoOperazione;
  }
  public void setTipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LifeCycleInfo lifeCycleInfo = (LifeCycleInfo) o;
    return Objects.equals(tipoOperazione, lifeCycleInfo.tipoOperazione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tipoOperazione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LifeCycleInfo {\n");
    
    sb.append("    tipoOperazione: ").append(toIndentedString(tipoOperazione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
