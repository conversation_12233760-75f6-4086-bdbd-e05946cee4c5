package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class RicercaFigureAnagraficheRelazionateRequest   {

  private @Valid FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata = null;

  /**
   **/
  public RicercaFigureAnagraficheRelazionateRequest figuraAnagraficaRelazionata(FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata) {
    this.figuraAnagraficaRelazionata = figuraAnagraficaRelazionata;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("figuraAnagraficaRelazionata")
  @NotNull

  public FiguraAnagraficaRelazionataInfo getFiguraAnagraficaRelazionata() {
    return figuraAnagraficaRelazionata;
  }
  public void setFiguraAnagraficaRelazionata(FiguraAnagraficaRelazionataInfo figuraAnagraficaRelazionata) {
    this.figuraAnagraficaRelazionata = figuraAnagraficaRelazionata;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RicercaFigureAnagraficheRelazionateRequest ricercaFigureAnagraficheRelazionateRequest = (RicercaFigureAnagraficheRelazionateRequest) o;
    return Objects.equals(figuraAnagraficaRelazionata, ricercaFigureAnagraficheRelazionateRequest.figuraAnagraficaRelazionata);
  }

  @Override
  public int hashCode() {
    return Objects.hash(figuraAnagraficaRelazionata);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RicercaFigureAnagraficheRelazionateRequest {\n");
    
    sb.append("    figuraAnagraficaRelazionata: ").append(toIndentedString(figuraAnagraficaRelazionata)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
