package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class VerificaRafforzataResponse   {

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public VerificaRafforzataResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VerificaRafforzataResponse verificaRafforzataResponse = (VerificaRafforzataResponse) o;
    return Objects.equals(errori, verificaRafforzataResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VerificaRafforzataResponse {\n");
    
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
