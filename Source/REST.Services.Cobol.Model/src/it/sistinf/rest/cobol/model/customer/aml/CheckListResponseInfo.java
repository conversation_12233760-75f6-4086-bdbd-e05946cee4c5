package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CheckListResponseInfo   {

  private @Valid List<PepCrimeCustomerResponseInfo> customers = new ArrayList<PepCrimeCustomerResponseInfo>();

  /**
   **/
  public CheckListResponseInfo customers(List<PepCrimeCustomerResponseInfo> customers) {
    this.customers = customers;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("customers")

  public List<PepCrimeCustomerResponseInfo> getCustomers() {
    return customers;
  }
  public void setCustomers(List<PepCrimeCustomerResponseInfo> customers) {
    this.customers = customers;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckListResponseInfo checkListResponseInfo = (CheckListResponseInfo) o;
    return Objects.equals(customers, checkListResponseInfo.customers);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customers);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckListResponseInfo {\n");
    
    sb.append("    customers: ").append(toIndentedString(customers)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
