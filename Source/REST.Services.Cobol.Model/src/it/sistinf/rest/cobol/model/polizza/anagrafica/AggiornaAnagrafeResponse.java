package it.sistinf.rest.cobol.model.polizza.anagrafica;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.EsitoResponse;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;
import it.sistinf.rest.cobol.model.proposta.common.AnagraficaRapportoCustomer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class AggiornaAnagrafeResponse extends EsitoResponse  {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid AnagraficaRapportoCustomer anagraficaRapportoCustomer = null;

  /**
   **/
  public AggiornaAnagrafeResponse polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("polizzaInfo")

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public AggiornaAnagrafeResponse anagraficaRapportoCustomer(AnagraficaRapportoCustomer anagraficaRapportoCustomer) {
    this.anagraficaRapportoCustomer = anagraficaRapportoCustomer;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("anagraficaRapportoCustomer")

  public AnagraficaRapportoCustomer getAnagraficaRapportoCustomer() {
    return anagraficaRapportoCustomer;
  }
  public void setAnagraficaRapportoCustomer(AnagraficaRapportoCustomer anagraficaRapportoCustomer) {
    this.anagraficaRapportoCustomer = anagraficaRapportoCustomer;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AggiornaAnagrafeResponse aggiornaAnagrafeResponse = (AggiornaAnagrafeResponse) o;
    return Objects.equals(polizzaInfo, aggiornaAnagrafeResponse.polizzaInfo) &&
        Objects.equals(anagraficaRapportoCustomer, aggiornaAnagrafeResponse.anagraficaRapportoCustomer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, anagraficaRapportoCustomer);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AggiornaAnagrafeResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    anagraficaRapportoCustomer: ").append(toIndentedString(anagraficaRapportoCustomer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
