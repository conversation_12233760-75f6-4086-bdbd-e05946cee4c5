package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.EsitoCobolResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class InserisciPercipientiResponse extends EsitoCobolResponse  {

  private @Valid List<PercipienteInfo> elencoPercipientiDefault = new ArrayList<PercipienteInfo>();

  private @Valid List<PercipienteExtendedInfo> elencoPercipienti = new ArrayList<PercipienteExtendedInfo>();

  /**
   **/
  public InserisciPercipientiResponse elencoPercipientiDefault(List<PercipienteInfo> elencoPercipientiDefault) {
    this.elencoPercipientiDefault = elencoPercipientiDefault;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipientiDefault")
  @NotNull

  public List<PercipienteInfo> getElencoPercipientiDefault() {
    return elencoPercipientiDefault;
  }
  public void setElencoPercipientiDefault(List<PercipienteInfo> elencoPercipientiDefault) {
    this.elencoPercipientiDefault = elencoPercipientiDefault;
  }

  /**
   **/
  public InserisciPercipientiResponse elencoPercipienti(List<PercipienteExtendedInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipienti")
  @NotNull

  public List<PercipienteExtendedInfo> getElencoPercipienti() {
    return elencoPercipienti;
  }
  public void setElencoPercipienti(List<PercipienteExtendedInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InserisciPercipientiResponse inserisciPercipientiResponse = (InserisciPercipientiResponse) o;
    return Objects.equals(elencoPercipientiDefault, inserisciPercipientiResponse.elencoPercipientiDefault) &&
        Objects.equals(elencoPercipienti, inserisciPercipientiResponse.elencoPercipienti);
  }

  @Override
  public int hashCode() {
    return Objects.hash(elencoPercipientiDefault, elencoPercipienti);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InserisciPercipientiResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    elencoPercipientiDefault: ").append(toIndentedString(elencoPercipientiDefault)).append("\n");
    sb.append("    elencoPercipienti: ").append(toIndentedString(elencoPercipienti)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
