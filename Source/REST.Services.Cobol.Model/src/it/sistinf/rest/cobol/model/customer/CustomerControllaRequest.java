package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.user.ReteVendita;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class CustomerControllaRequest   {

  private @Valid String username = null;

  private @Valid Integer codSoc = null;

  private @Valid ReteVendita reteVendita = null;

  private @Valid AllCustomerInfo customerData = null;

public enum RuoloEnum {

    J(String.valueOf("J")), N(String.valueOf("N")), A(String.valueOf("A")), D(String.valueOf("D")), L(String.valueOf("L")), M(String.valueOf("M")), B(String.valueOf("B")), S(String.valueOf("S")), C(String.valueOf("C")), V(String.valueOf("V")), R(String.valueOf("R")), T(String.valueOf("T")), F(String.valueOf("F")), E(String.valueOf("E")), Z(String.valueOf("Z")), P(String.valueOf("P")), K(String.valueOf("K"));


    private String value;

    RuoloEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static RuoloEnum fromValue(String v) {
        for (RuoloEnum b : RuoloEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid RuoloEnum ruolo = null;

public enum ProcessoEnum {

    CUS(String.valueOf("CUS")), CLI(String.valueOf("CLI")), ASS(String.valueOf("ASS")), PSV(String.valueOf("PSV")), LIQ(String.valueOf("LIQ")), BAA(String.valueOf("BAA"));


    private String value;

    ProcessoEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static ProcessoEnum fromValue(String v) {
        for (ProcessoEnum b : ProcessoEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid ProcessoEnum processo = null;

  /**
   **/
  public CustomerControllaRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }

  /**
   **/
  public CustomerControllaRequest codSoc(Integer codSoc) {
    this.codSoc = codSoc;
    return this;
  }

  
  @ApiModelProperty(example = "143", required = true, value = "")
  @JsonProperty("codSoc")
  @NotNull

  public Integer getCodSoc() {
    return codSoc;
  }
  public void setCodSoc(Integer codSoc) {
    this.codSoc = codSoc;
  }

  /**
   **/
  public CustomerControllaRequest reteVendita(ReteVendita reteVendita) {
    this.reteVendita = reteVendita;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("reteVendita")
  @NotNull

  public ReteVendita getReteVendita() {
    return reteVendita;
  }
  public void setReteVendita(ReteVendita reteVendita) {
    this.reteVendita = reteVendita;
  }

  /**
   **/
  public CustomerControllaRequest customerData(AllCustomerInfo customerData) {
    this.customerData = customerData;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("customerData")
  @NotNull

  public AllCustomerInfo getCustomerData() {
    return customerData;
  }
  public void setCustomerData(AllCustomerInfo customerData) {
    this.customerData = customerData;
  }

  /**
   **/
  public CustomerControllaRequest ruolo(RuoloEnum ruolo) {
    this.ruolo = ruolo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("ruolo")
  @NotNull

  public RuoloEnum getRuolo() {
    return ruolo;
  }
  public void setRuolo(RuoloEnum ruolo) {
    this.ruolo = ruolo;
  }

  /**
   **/
  public CustomerControllaRequest processo(ProcessoEnum processo) {
    this.processo = processo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("processo")
  @NotNull

  public ProcessoEnum getProcesso() {
    return processo;
  }
  public void setProcesso(ProcessoEnum processo) {
    this.processo = processo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerControllaRequest customerControllaRequest = (CustomerControllaRequest) o;
    return Objects.equals(username, customerControllaRequest.username) &&
        Objects.equals(codSoc, customerControllaRequest.codSoc) &&
        Objects.equals(reteVendita, customerControllaRequest.reteVendita) &&
        Objects.equals(customerData, customerControllaRequest.customerData) &&
        Objects.equals(ruolo, customerControllaRequest.ruolo) &&
        Objects.equals(processo, customerControllaRequest.processo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(username, codSoc, reteVendita, customerData, ruolo, processo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerControllaRequest {\n");
    
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    codSoc: ").append(toIndentedString(codSoc)).append("\n");
    sb.append("    reteVendita: ").append(toIndentedString(reteVendita)).append("\n");
    sb.append("    customerData: ").append(toIndentedString(customerData)).append("\n");
    sb.append("    ruolo: ").append(toIndentedString(ruolo)).append("\n");
    sb.append("    processo: ").append(toIndentedString(processo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
