package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CheckListResponse   {

  private @Valid String status = null;

  private @Valid String message = null;

  private @Valid CheckListResponseInfo response = null;

  private @Valid String error = null;

  private @Valid String path = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   * http response code
   **/
  public CheckListResponse status(String status) {
    this.status = status;
    return this;
  }

  
  @ApiModelProperty(value = "http response code")
  @JsonProperty("status")

  public String getStatus() {
    return status;
  }
  public void setStatus(String status) {
    this.status = status;
  }

  /**
   * Messaggio di risposta
   **/
  public CheckListResponse message(String message) {
    this.message = message;
    return this;
  }

  
  @ApiModelProperty(value = "Messaggio di risposta")
  @JsonProperty("message")

  public String getMessage() {
    return message;
  }
  public void setMessage(String message) {
    this.message = message;
  }

  /**
   **/
  public CheckListResponse response(CheckListResponseInfo response) {
    this.response = response;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("response")

  public CheckListResponseInfo getResponse() {
    return response;
  }
  public void setResponse(CheckListResponseInfo response) {
    this.response = response;
  }

  /**
   * Messaggio di risposta
   **/
  public CheckListResponse error(String error) {
    this.error = error;
    return this;
  }

  
  @ApiModelProperty(value = "Messaggio di risposta")
  @JsonProperty("error")

  public String getError() {
    return error;
  }
  public void setError(String error) {
    this.error = error;
  }

  /**
   * Path servizio
   **/
  public CheckListResponse path(String path) {
    this.path = path;
    return this;
  }

  
  @ApiModelProperty(value = "Path servizio")
  @JsonProperty("path")

  public String getPath() {
    return path;
  }
  public void setPath(String path) {
    this.path = path;
  }

  /**
   **/
  public CheckListResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckListResponse checkListResponse = (CheckListResponse) o;
    return Objects.equals(status, checkListResponse.status) &&
        Objects.equals(message, checkListResponse.message) &&
        Objects.equals(response, checkListResponse.response) &&
        Objects.equals(error, checkListResponse.error) &&
        Objects.equals(path, checkListResponse.path) &&
        Objects.equals(errori, checkListResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, message, response, error, path, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckListResponse {\n");
    
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    error: ").append(toIndentedString(error)).append("\n");
    sb.append("    path: ").append(toIndentedString(path)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
