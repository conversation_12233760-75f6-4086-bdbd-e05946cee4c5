package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.dominio.ElementoDominio;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class IbanDetailResponse   {

  private @Valid String codiceIban = null;

  private @Valid ElementoDominio banca = null;

  private @Valid ElementoDominio filiale = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public IbanDetailResponse codiceIban(String codiceIban) {
    this.codiceIban = codiceIban;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceIban")

  public String getCodiceIban() {
    return codiceIban;
  }
  public void setCodiceIban(String codiceIban) {
    this.codiceIban = codiceIban;
  }

  /**
   **/
  public IbanDetailResponse banca(ElementoDominio banca) {
    this.banca = banca;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("banca")

  public ElementoDominio getBanca() {
    return banca;
  }
  public void setBanca(ElementoDominio banca) {
    this.banca = banca;
  }

  /**
   **/
  public IbanDetailResponse filiale(ElementoDominio filiale) {
    this.filiale = filiale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("filiale")

  public ElementoDominio getFiliale() {
    return filiale;
  }
  public void setFiliale(ElementoDominio filiale) {
    this.filiale = filiale;
  }

  /**
   **/
  public IbanDetailResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IbanDetailResponse ibanDetailResponse = (IbanDetailResponse) o;
    return Objects.equals(codiceIban, ibanDetailResponse.codiceIban) &&
        Objects.equals(banca, ibanDetailResponse.banca) &&
        Objects.equals(filiale, ibanDetailResponse.filiale) &&
        Objects.equals(errori, ibanDetailResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceIban, banca, filiale, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IbanDetailResponse {\n");
    
    sb.append("    codiceIban: ").append(toIndentedString(codiceIban)).append("\n");
    sb.append("    banca: ").append(toIndentedString(banca)).append("\n");
    sb.append("    filiale: ").append(toIndentedString(filiale)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
