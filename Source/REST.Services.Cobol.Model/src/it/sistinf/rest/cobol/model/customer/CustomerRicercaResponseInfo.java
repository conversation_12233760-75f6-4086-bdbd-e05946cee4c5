package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;

public class CustomerRicercaResponseInfo   {

  private @Valid Long codiceCliente = null;

  private @Valid String codiceFiscale = null;

  private @Valid String partitaIva = null;

  private @Valid String nominativo = null;

  private @Valid String nome = null;

  private @Valid String indirizzoRes = null;

  private @Valid String citta = null;

  private @Valid Date dataNascita = null;

  private @Valid String provinciaRes = null;

public enum TipoPersonaEnum {

    F(String.valueOf("F")), G(String.valueOf("G"));


    private String value;

    TipoPersonaEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoPersonaEnum fromValue(String v) {
        for (TipoPersonaEnum b : TipoPersonaEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoPersonaEnum tipoPersona = null;

  /**
   **/
  public CustomerRicercaResponseInfo codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceCliente")

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public CustomerRicercaResponseInfo codiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceFiscale")

  public String getCodiceFiscale() {
    return codiceFiscale;
  }
  public void setCodiceFiscale(String codiceFiscale) {
    this.codiceFiscale = codiceFiscale;
  }

  /**
   **/
  public CustomerRicercaResponseInfo partitaIva(String partitaIva) {
    this.partitaIva = partitaIva;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("partitaIva")

  public String getPartitaIva() {
    return partitaIva;
  }
  public void setPartitaIva(String partitaIva) {
    this.partitaIva = partitaIva;
  }

  /**
   **/
  public CustomerRicercaResponseInfo nominativo(String nominativo) {
    this.nominativo = nominativo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nominativo")

  public String getNominativo() {
    return nominativo;
  }
  public void setNominativo(String nominativo) {
    this.nominativo = nominativo;
  }

  /**
   **/
  public CustomerRicercaResponseInfo nome(String nome) {
    this.nome = nome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nome")

  public String getNome() {
    return nome;
  }
  public void setNome(String nome) {
    this.nome = nome;
  }

  /**
   **/
  public CustomerRicercaResponseInfo indirizzoRes(String indirizzoRes) {
    this.indirizzoRes = indirizzoRes;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("indirizzoRes")

  public String getIndirizzoRes() {
    return indirizzoRes;
  }
  public void setIndirizzoRes(String indirizzoRes) {
    this.indirizzoRes = indirizzoRes;
  }

  /**
   **/
  public CustomerRicercaResponseInfo citta(String citta) {
    this.citta = citta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("citta")

  public String getCitta() {
    return citta;
  }
  public void setCitta(String citta) {
    this.citta = citta;
  }

  /**
   **/
  public CustomerRicercaResponseInfo dataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataNascita")

  public Date getDataNascita() {
    return dataNascita;
  }
  public void setDataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
  }

  /**
   **/
  public CustomerRicercaResponseInfo provinciaRes(String provinciaRes) {
    this.provinciaRes = provinciaRes;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("provinciaRes")

  public String getProvinciaRes() {
    return provinciaRes;
  }
  public void setProvinciaRes(String provinciaRes) {
    this.provinciaRes = provinciaRes;
  }

  /**
   **/
  public CustomerRicercaResponseInfo tipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoPersona")

  public TipoPersonaEnum getTipoPersona() {
    return tipoPersona;
  }
  public void setTipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerRicercaResponseInfo customerRicercaResponseInfo = (CustomerRicercaResponseInfo) o;
    return Objects.equals(codiceCliente, customerRicercaResponseInfo.codiceCliente) &&
        Objects.equals(codiceFiscale, customerRicercaResponseInfo.codiceFiscale) &&
        Objects.equals(partitaIva, customerRicercaResponseInfo.partitaIva) &&
        Objects.equals(nominativo, customerRicercaResponseInfo.nominativo) &&
        Objects.equals(nome, customerRicercaResponseInfo.nome) &&
        Objects.equals(indirizzoRes, customerRicercaResponseInfo.indirizzoRes) &&
        Objects.equals(citta, customerRicercaResponseInfo.citta) &&
        Objects.equals(dataNascita, customerRicercaResponseInfo.dataNascita) &&
        Objects.equals(provinciaRes, customerRicercaResponseInfo.provinciaRes) &&
        Objects.equals(tipoPersona, customerRicercaResponseInfo.tipoPersona);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, codiceFiscale, partitaIva, nominativo, nome, indirizzoRes, citta, dataNascita, provinciaRes, tipoPersona);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerRicercaResponseInfo {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    codiceFiscale: ").append(toIndentedString(codiceFiscale)).append("\n");
    sb.append("    partitaIva: ").append(toIndentedString(partitaIva)).append("\n");
    sb.append("    nominativo: ").append(toIndentedString(nominativo)).append("\n");
    sb.append("    nome: ").append(toIndentedString(nome)).append("\n");
    sb.append("    indirizzoRes: ").append(toIndentedString(indirizzoRes)).append("\n");
    sb.append("    citta: ").append(toIndentedString(citta)).append("\n");
    sb.append("    dataNascita: ").append(toIndentedString(dataNascita)).append("\n");
    sb.append("    provinciaRes: ").append(toIndentedString(provinciaRes)).append("\n");
    sb.append("    tipoPersona: ").append(toIndentedString(tipoPersona)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
