package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class CompagnieInfo   {

  private @Valid String codCompagnia = null;

  private @Valid String descrizioneCompagnia = null;

  /**
   **/
  public CompagnieInfo codCompagnia(String codCompagnia) {
    this.codCompagnia = codCompagnia;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codCompagnia")

  public String getCodCompagnia() {
    return codCompagnia;
  }
  public void setCodCompagnia(String codCompagnia) {
    this.codCompagnia = codCompagnia;
  }

  /**
   **/
  public CompagnieInfo descrizioneCompagnia(String descrizioneCompagnia) {
    this.descrizioneCompagnia = descrizioneCompagnia;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrizioneCompagnia")

  public String getDescrizioneCompagnia() {
    return descrizioneCompagnia;
  }
  public void setDescrizioneCompagnia(String descrizioneCompagnia) {
    this.descrizioneCompagnia = descrizioneCompagnia;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CompagnieInfo compagnieInfo = (CompagnieInfo) o;
    return Objects.equals(codCompagnia, compagnieInfo.codCompagnia) &&
        Objects.equals(descrizioneCompagnia, compagnieInfo.descrizioneCompagnia);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCompagnia, descrizioneCompagnia);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CompagnieInfo {\n");
    
    sb.append("    codCompagnia: ").append(toIndentedString(codCompagnia)).append("\n");
    sb.append("    descrizioneCompagnia: ").append(toIndentedString(descrizioneCompagnia)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
