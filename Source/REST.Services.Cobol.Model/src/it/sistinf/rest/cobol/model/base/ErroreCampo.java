package it.sistinf.rest.cobol.model.base;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ErroreCampo   {

  private @Valid String codErrore = null;

  private @Valid String nomeCampo = null;

  private @Valid String descrizione = null;

  /**
   **/
  public ErroreCampo codErrore(String codErrore) {
    this.codErrore = codErrore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codErrore")

  public String getCodErrore() {
    return codErrore;
  }
  public void setCodErrore(String codErrore) {
    this.codErrore = codErrore;
  }

  /**
   **/
  public ErroreCampo nomeCampo(String nomeCampo) {
    this.nomeCampo = nomeCampo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nomeCampo")

  public String getNomeCampo() {
    return nomeCampo;
  }
  public void setNomeCampo(String nomeCampo) {
    this.nomeCampo = nomeCampo;
  }

  /**
   **/
  public ErroreCampo descrizione(String descrizione) {
    this.descrizione = descrizione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrizione")

  public String getDescrizione() {
    return descrizione;
  }
  public void setDescrizione(String descrizione) {
    this.descrizione = descrizione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ErroreCampo erroreCampo = (ErroreCampo) o;
    return Objects.equals(codErrore, erroreCampo.codErrore) &&
        Objects.equals(nomeCampo, erroreCampo.nomeCampo) &&
        Objects.equals(descrizione, erroreCampo.descrizione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codErrore, nomeCampo, descrizione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ErroreCampo {\n");
    
    sb.append("    codErrore: ").append(toIndentedString(codErrore)).append("\n");
    sb.append("    nomeCampo: ").append(toIndentedString(nomeCampo)).append("\n");
    sb.append("    descrizione: ").append(toIndentedString(descrizione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
