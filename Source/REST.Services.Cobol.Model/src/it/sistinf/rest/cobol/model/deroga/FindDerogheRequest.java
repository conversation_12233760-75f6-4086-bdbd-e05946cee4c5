package it.sistinf.rest.cobol.model.deroga;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.prodotto.ReteVenditaL1L3;
import it.sistinf.rest.cobol.model.proposta.common.PropostaInfo;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class FindDerogheRequest   {

  private @Valid PropostaInfo propostaInfo = null;

  private @Valid String codProdotto = null;

  private @Valid ReteVenditaL1L3 reteVendita = null;

  /**
   **/
  public FindDerogheRequest propostaInfo(PropostaInfo propostaInfo) {
    this.propostaInfo = propostaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("propostaInfo")
  @NotNull

  public PropostaInfo getPropostaInfo() {
    return propostaInfo;
  }
  public void setPropostaInfo(PropostaInfo propostaInfo) {
    this.propostaInfo = propostaInfo;
  }

  /**
   **/
  public FindDerogheRequest codProdotto(String codProdotto) {
    this.codProdotto = codProdotto;
    return this;
  }

  
  @ApiModelProperty(example = "M101", required = true, value = "")
  @JsonProperty("codProdotto")
  @NotNull
 @Size(max=10)
  public String getCodProdotto() {
    return codProdotto;
  }
  public void setCodProdotto(String codProdotto) {
    this.codProdotto = codProdotto;
  }

  /**
   **/
  public FindDerogheRequest reteVendita(ReteVenditaL1L3 reteVendita) {
    this.reteVendita = reteVendita;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("reteVendita")
  @NotNull

  public ReteVenditaL1L3 getReteVendita() {
    return reteVendita;
  }
  public void setReteVendita(ReteVenditaL1L3 reteVendita) {
    this.reteVendita = reteVendita;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FindDerogheRequest findDerogheRequest = (FindDerogheRequest) o;
    return Objects.equals(propostaInfo, findDerogheRequest.propostaInfo) &&
        Objects.equals(codProdotto, findDerogheRequest.codProdotto) &&
        Objects.equals(reteVendita, findDerogheRequest.reteVendita);
  }

  @Override
  public int hashCode() {
    return Objects.hash(propostaInfo, codProdotto, reteVendita);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FindDerogheRequest {\n");
    
    sb.append("    propostaInfo: ").append(toIndentedString(propostaInfo)).append("\n");
    sb.append("    codProdotto: ").append(toIndentedString(codProdotto)).append("\n");
    sb.append("    reteVendita: ").append(toIndentedString(reteVendita)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
