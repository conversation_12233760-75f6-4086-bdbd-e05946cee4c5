package it.sistinf.rest.cobol.model.dominio;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DominioValoriPrestazioneRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid String categoria = null;

  private @Valid String codiceTariffa = null;

  /**
   **/
  public DominioValoriPrestazioneRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public DominioValoriPrestazioneRequest categoria(String categoria) {
    this.categoria = categoria;
    return this;
  }

  
  @ApiModelProperty(example = "I", value = "")
  @JsonProperty("categoria")

  public String getCategoria() {
    return categoria;
  }
  public void setCategoria(String categoria) {
    this.categoria = categoria;
  }

  /**
   **/
  public DominioValoriPrestazioneRequest codiceTariffa(String codiceTariffa) {
    this.codiceTariffa = codiceTariffa;
    return this;
  }

  
  @ApiModelProperty(example = "U101", value = "")
  @JsonProperty("codiceTariffa")

  public String getCodiceTariffa() {
    return codiceTariffa;
  }
  public void setCodiceTariffa(String codiceTariffa) {
    this.codiceTariffa = codiceTariffa;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DominioValoriPrestazioneRequest dominioValoriPrestazioneRequest = (DominioValoriPrestazioneRequest) o;
    return Objects.equals(headerCobolSrv, dominioValoriPrestazioneRequest.headerCobolSrv) &&
        Objects.equals(categoria, dominioValoriPrestazioneRequest.categoria) &&
        Objects.equals(codiceTariffa, dominioValoriPrestazioneRequest.codiceTariffa);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, categoria, codiceTariffa);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DominioValoriPrestazioneRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    categoria: ").append(toIndentedString(categoria)).append("\n");
    sb.append("    codiceTariffa: ").append(toIndentedString(codiceTariffa)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
