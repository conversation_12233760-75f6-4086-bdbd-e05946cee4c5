package it.sistinf.rest.cobol.model.polizza.recesso;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class StorniRimborsoCompoUnit   {

  private @Valid String ulFondo = null;

  private @Valid String descrUlFondo = null;

  private @Valid Date dataDisinv = null;

  private @Valid BigDecimal prestazione = null;

  private @Valid BigDecimal ultValore = null;

  private @Valid BigDecimal controvalore = null;

  private @Valid List<Integer> elencoNumPosiz = new ArrayList<Integer>();

  /**
   **/
  public StorniRimborsoCompoUnit ulFondo(String ulFondo) {
    this.ulFondo = ulFondo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ulFondo")

  public String getUlFondo() {
    return ulFondo;
  }
  public void setUlFondo(String ulFondo) {
    this.ulFondo = ulFondo;
  }

  /**
   **/
  public StorniRimborsoCompoUnit descrUlFondo(String descrUlFondo) {
    this.descrUlFondo = descrUlFondo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrUlFondo")

  public String getDescrUlFondo() {
    return descrUlFondo;
  }
  public void setDescrUlFondo(String descrUlFondo) {
    this.descrUlFondo = descrUlFondo;
  }

  /**
   **/
  public StorniRimborsoCompoUnit dataDisinv(Date dataDisinv) {
    this.dataDisinv = dataDisinv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataDisinv")

  public Date getDataDisinv() {
    return dataDisinv;
  }
  public void setDataDisinv(Date dataDisinv) {
    this.dataDisinv = dataDisinv;
  }

  /**
   **/
  public StorniRimborsoCompoUnit prestazione(BigDecimal prestazione) {
    this.prestazione = prestazione;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("prestazione")

  public BigDecimal getPrestazione() {
    return prestazione;
  }
  public void setPrestazione(BigDecimal prestazione) {
    this.prestazione = prestazione;
  }

  /**
   **/
  public StorniRimborsoCompoUnit ultValore(BigDecimal ultValore) {
    this.ultValore = ultValore;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("ultValore")

  public BigDecimal getUltValore() {
    return ultValore;
  }
  public void setUltValore(BigDecimal ultValore) {
    this.ultValore = ultValore;
  }

  /**
   **/
  public StorniRimborsoCompoUnit controvalore(BigDecimal controvalore) {
    this.controvalore = controvalore;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("controvalore")

  public BigDecimal getControvalore() {
    return controvalore;
  }
  public void setControvalore(BigDecimal controvalore) {
    this.controvalore = controvalore;
  }

  /**
   **/
  public StorniRimborsoCompoUnit elencoNumPosiz(List<Integer> elencoNumPosiz) {
    this.elencoNumPosiz = elencoNumPosiz;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoNumPosiz")

  public List<Integer> getElencoNumPosiz() {
    return elencoNumPosiz;
  }
  public void setElencoNumPosiz(List<Integer> elencoNumPosiz) {
    this.elencoNumPosiz = elencoNumPosiz;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StorniRimborsoCompoUnit storniRimborsoCompoUnit = (StorniRimborsoCompoUnit) o;
    return Objects.equals(ulFondo, storniRimborsoCompoUnit.ulFondo) &&
        Objects.equals(descrUlFondo, storniRimborsoCompoUnit.descrUlFondo) &&
        Objects.equals(dataDisinv, storniRimborsoCompoUnit.dataDisinv) &&
        Objects.equals(prestazione, storniRimborsoCompoUnit.prestazione) &&
        Objects.equals(ultValore, storniRimborsoCompoUnit.ultValore) &&
        Objects.equals(controvalore, storniRimborsoCompoUnit.controvalore) &&
        Objects.equals(elencoNumPosiz, storniRimborsoCompoUnit.elencoNumPosiz);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ulFondo, descrUlFondo, dataDisinv, prestazione, ultValore, controvalore, elencoNumPosiz);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StorniRimborsoCompoUnit {\n");
    
    sb.append("    ulFondo: ").append(toIndentedString(ulFondo)).append("\n");
    sb.append("    descrUlFondo: ").append(toIndentedString(descrUlFondo)).append("\n");
    sb.append("    dataDisinv: ").append(toIndentedString(dataDisinv)).append("\n");
    sb.append("    prestazione: ").append(toIndentedString(prestazione)).append("\n");
    sb.append("    ultValore: ").append(toIndentedString(ultValore)).append("\n");
    sb.append("    controvalore: ").append(toIndentedString(controvalore)).append("\n");
    sb.append("    elencoNumPosiz: ").append(toIndentedString(elencoNumPosiz)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
