package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class RppInfo   {

  private @Valid String durata = null;

  private @Valid String periodicita = null;

  private @Valid BigDecimal importo = null;

  private @Valid BeneficiarioInfo beneficiarioInfo = null;

public enum TipoOperazioneEnum {

    ATTIVAZIONE(String.valueOf("ATTIVAZIONE")), DISATTIVAZIONE(String.valueOf("DISATTIVAZIONE"));


    private String value;

    TipoOperazioneEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoOperazioneEnum fromValue(String v) {
        for (TipoOperazioneEnum b : TipoOperazioneEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoOperazioneEnum tipoOperazione = null;

  /**
   **/
  public RppInfo durata(String durata) {
    this.durata = durata;
    return this;
  }

  
  @ApiModelProperty(example = "05", value = "")
  @JsonProperty("durata")

  public String getDurata() {
    return durata;
  }
  public void setDurata(String durata) {
    this.durata = durata;
  }

  /**
   **/
  public RppInfo periodicita(String periodicita) {
    this.periodicita = periodicita;
    return this;
  }

  
  @ApiModelProperty(example = "01", value = "")
  @JsonProperty("periodicita")

  public String getPeriodicita() {
    return periodicita;
  }
  public void setPeriodicita(String periodicita) {
    this.periodicita = periodicita;
  }

  /**
   **/
  public RppInfo importo(BigDecimal importo) {
    this.importo = importo;
    return this;
  }

  
  @ApiModelProperty(example = "3.01", value = "")
  @JsonProperty("importo")

  public BigDecimal getImporto() {
    return importo;
  }
  public void setImporto(BigDecimal importo) {
    this.importo = importo;
  }

  /**
   **/
  public RppInfo beneficiarioInfo(BeneficiarioInfo beneficiarioInfo) {
    this.beneficiarioInfo = beneficiarioInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("beneficiarioInfo")

  public BeneficiarioInfo getBeneficiarioInfo() {
    return beneficiarioInfo;
  }
  public void setBeneficiarioInfo(BeneficiarioInfo beneficiarioInfo) {
    this.beneficiarioInfo = beneficiarioInfo;
  }

  /**
   **/
  public RppInfo tipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoOperazione")

  public TipoOperazioneEnum getTipoOperazione() {
    return tipoOperazione;
  }
  public void setTipoOperazione(TipoOperazioneEnum tipoOperazione) {
    this.tipoOperazione = tipoOperazione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RppInfo rppInfo = (RppInfo) o;
    return Objects.equals(durata, rppInfo.durata) &&
        Objects.equals(periodicita, rppInfo.periodicita) &&
        Objects.equals(importo, rppInfo.importo) &&
        Objects.equals(beneficiarioInfo, rppInfo.beneficiarioInfo) &&
        Objects.equals(tipoOperazione, rppInfo.tipoOperazione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(durata, periodicita, importo, beneficiarioInfo, tipoOperazione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RppInfo {\n");
    
    sb.append("    durata: ").append(toIndentedString(durata)).append("\n");
    sb.append("    periodicita: ").append(toIndentedString(periodicita)).append("\n");
    sb.append("    importo: ").append(toIndentedString(importo)).append("\n");
    sb.append("    beneficiarioInfo: ").append(toIndentedString(beneficiarioInfo)).append("\n");
    sb.append("    tipoOperazione: ").append(toIndentedString(tipoOperazione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
