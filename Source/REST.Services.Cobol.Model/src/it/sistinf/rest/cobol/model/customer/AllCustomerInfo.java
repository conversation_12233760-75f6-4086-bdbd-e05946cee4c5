package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.customer.common.CustomerInfo;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class AllCustomerInfo extends CustomerInfo  {

  private @Valid String nazioneRilascio = null;

  private @Valid String provRilascio = null;

  private @Valid String localRilascio = null;

  private @Valid String pressoRep = null;

  private @Valid String statoRep = null;

  private @Valid String proviRep = null;

  private @Valid String localRep = null;

  private @Valid String capRep = null;

  private @Valid String desUrbanaIndRep = null;

  private @Valid String desStradaIndRep = null;

  private @Valid String numCivicoRep = null;

  private @Valid String telefRep = null;

  private @Valid String telefono = null;

  private @Valid String attivitaProfessio = null;

  private @Valid String gruppo = null;

  private @Valid String sottoGruppo = null;

  private @Valid String tipoDocumentoAR = null;

  private @Valid String descTipoDocumento = null;

  private @Valid String numDocumentoAR = null;

  private @Valid String enteRilascio = null;

  private @Valid Date dataRilascio = null;

  private @Valid String cittadinanza = null;

  private @Valid String desCittadinanza = null;

  private @Valid String titoloStudio = null;

  private @Valid Boolean flagTasseEstero = null;

  private @Valid String nazTasseEstero = null;

  private @Valid String desNazTasseEstero = null;

  private @Valid String tin = null;

  private @Valid String usStatus = null;

  private @Valid String cab = null;

  private @Valid Boolean marketing = null;

  private @Valid Boolean ricercheMercato = null;

  private @Valid Boolean comunicazioniSoggettiTerzi = null;

  private @Valid String easyClientCode = null;

  private @Valid String easyClientCodePrincipale = null;

  private @Valid String numCellulare = null;

  private @Valid String cittadinanza2 = null;

  private @Valid String cittadinanza3 = null;

  private @Valid Date dataScadenzaAR = null;

  private @Valid String desSottogruppoAR = null;

  private @Valid String desGruppoAR = null;

  private @Valid String codEnteRilascioAR = null;

  private @Valid String desNazioneRilascioAR = null;

  private @Valid String desProvinciaRilascioAR = null;

  private @Valid Boolean flLuogoRilascioEsteroAR = null;

  private @Valid Boolean flDocumentiAllegatiAR = null;

  private @Valid String desDocumentiAllegatiAR = null;

  private @Valid String desNazioneRep = null;

  private @Valid String desProvinciaRep = null;

  private @Valid String indirizzoRep = null;

  private @Valid String email = null;

  private @Valid String emailRep = null;

  private @Valid String emailCertificata = null;

  private @Valid String condizioneProfessionale = null;

  private @Valid String statoCivile = null;

  private @Valid String entePubblico = null;

  private @Valid Boolean flCommerciale = null;

  private @Valid String societaQuotata = null;

  private @Valid String tipoSocieta = null;

  private @Valid String descTipoSocieta = null;

  private @Valid Date dataPrimaIscrizione = null;

  private @Valid Boolean greenCard = null;

  private @Valid Boolean flForzaturaUSStatus = null;

  private @Valid String motivoForzaturaUsStatus = null;

  private @Valid Boolean flTitolareEffettivo = null;

  private @Valid String provAttivitaPrevalente = null;

  private @Valid String descProvAttivitaPrevalente = null;

  private @Valid String luogoAttivitaPrevalente = null;

  private @Valid String capAttivitaPrevalente = null;

  private @Valid String statoCliente = null;

  private @Valid String tipoCliente = null;

  private @Valid String ultProgStorico = null;

  private @Valid Boolean flagOrigineFondi = null;

  private @Valid String sedeAttivitaPrevalente = null;

  private @Valid String attivitaPrevalente = null;

  private @Valid String descAttivitaPrevalente = null;

  private @Valid String codRegione = null;

  private @Valid String descRegione = null;

  private @Valid Boolean flEntitaNonFinanziaria = null;

  private @Valid Boolean flagConsComunicazioniElettroniche = null;

  private @Valid Date dataConsComunicazioniElettroniche = null;

  private @Valid Date dataRevocaConsComunicazioniElettroniche = null;

  private @Valid Date dataUpdMailComunicazioniElettroniche = null;

  private @Valid Boolean flBeneficiariDisabili = null;

  private @Valid Date dataAttivazBeneficiariDisabili = null;

  private @Valid String ateco = null;

  private @Valid Date dataUltimaAdeguataVerifica = null;

  private @Valid Boolean fondoPensione = null;

  private @Valid Boolean fiduciaria = null;

  private @Valid Boolean intermediarioFinanziario = null;

  private @Valid Boolean flPaesiBlackListAR = null;

  private @Valid Boolean flgDomicilio = null;

  private @Valid String statoDomicilio = null;

  private @Valid String provDomicilio = null;

  private @Valid String luogoDomicilio = null;

  private @Valid String toponimoDomicilio = null;

  private @Valid String descrizioneDomicilio = null;

  private @Valid String numCivicoDomicilio = null;

  private @Valid String capDomicilio = null;

  private @Valid Boolean flgCorrispondenza = null;

  private @Valid Boolean flgCorrispondenzaIntestataSoggetto = null;

  private @Valid String autorita = null;

  private @Valid String albo = null;

  private @Valid String numeroIscrizioneAlbo = null;

  private @Valid String registroIscrizione = null;

  private @Valid String numeroIscrizioneRegistro = null;

  private @Valid Date dataIscrizione = null;

  private @Valid String societaAzionariatoFiduciario = null;

  private @Valid String classeBeneficiari = null;

  private @Valid String livelloIstruzione = null;

  private @Valid String attivitaPrevalenteAltro = null;

  private @Valid String attivitaPrecedentePensionamento = null;

  private @Valid String descAttivitaPrecedentePensionamento = null;

  private @Valid String attivitaPrecedentePensionamentoAltro = null;

  private @Valid String statoAttivitaPrevalente = null;

  private @Valid String ambitoAttivita = null;

  private @Valid String descAmbitoAttivita = null;

  private @Valid String altraCaricaPubblicaNonPep = null;

  private @Valid Boolean flgRelazPaesiAltoRischio = null;

  private @Valid Date dataRif = null;

  private @Valid CustomerOtherInfo customerOtherInfo = null;

  private @Valid String desCittadinanza2 = null;

  private @Valid String desCittadinanza3 = null;

  private @Valid String desStatoDomicilio = null;

  private @Valid String desStatoAttivitaPrevalente = null;

  private @Valid String desNazioneRilascio = null;

  private @Valid String tipoPensionamento = null;

  private @Valid Date dataVerificaRafforzata = null;

  private @Valid Date dataVerificaRafforzataPrecedente = null;

  private @Valid String profiloRischio = null;

  private @Valid String paeseBlackList1 = null;

  private @Valid String paeseBlackList2 = null;

  private @Valid String paeseBlackList3 = null;

  private @Valid String motivoRelazione = null;

public enum FlgFiduciarioProprioTerziEnum {

    P(String.valueOf("P")), T(String.valueOf("T"));


    private String value;

    FlgFiduciarioProprioTerziEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static FlgFiduciarioProprioTerziEnum fromValue(String v) {
        for (FlgFiduciarioProprioTerziEnum b : FlgFiduciarioProprioTerziEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid FlgFiduciarioProprioTerziEnum flgFiduciarioProprioTerzi = null;

  /**
   **/
  public AllCustomerInfo nazioneRilascio(String nazioneRilascio) {
    this.nazioneRilascio = nazioneRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nazioneRilascio")

  public String getNazioneRilascio() {
    return nazioneRilascio;
  }
  public void setNazioneRilascio(String nazioneRilascio) {
    this.nazioneRilascio = nazioneRilascio;
  }

  /**
   **/
  public AllCustomerInfo provRilascio(String provRilascio) {
    this.provRilascio = provRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("provRilascio")

  public String getProvRilascio() {
    return provRilascio;
  }
  public void setProvRilascio(String provRilascio) {
    this.provRilascio = provRilascio;
  }

  /**
   **/
  public AllCustomerInfo localRilascio(String localRilascio) {
    this.localRilascio = localRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("localRilascio")

  public String getLocalRilascio() {
    return localRilascio;
  }
  public void setLocalRilascio(String localRilascio) {
    this.localRilascio = localRilascio;
  }

  /**
   **/
  public AllCustomerInfo pressoRep(String pressoRep) {
    this.pressoRep = pressoRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("pressoRep")

  public String getPressoRep() {
    return pressoRep;
  }
  public void setPressoRep(String pressoRep) {
    this.pressoRep = pressoRep;
  }

  /**
   **/
  public AllCustomerInfo statoRep(String statoRep) {
    this.statoRep = statoRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoRep")

  public String getStatoRep() {
    return statoRep;
  }
  public void setStatoRep(String statoRep) {
    this.statoRep = statoRep;
  }

  /**
   **/
  public AllCustomerInfo proviRep(String proviRep) {
    this.proviRep = proviRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("proviRep")

  public String getProviRep() {
    return proviRep;
  }
  public void setProviRep(String proviRep) {
    this.proviRep = proviRep;
  }

  /**
   **/
  public AllCustomerInfo localRep(String localRep) {
    this.localRep = localRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("localRep")

  public String getLocalRep() {
    return localRep;
  }
  public void setLocalRep(String localRep) {
    this.localRep = localRep;
  }

  /**
   **/
  public AllCustomerInfo capRep(String capRep) {
    this.capRep = capRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("capRep")

  public String getCapRep() {
    return capRep;
  }
  public void setCapRep(String capRep) {
    this.capRep = capRep;
  }

  /**
   **/
  public AllCustomerInfo desUrbanaIndRep(String desUrbanaIndRep) {
    this.desUrbanaIndRep = desUrbanaIndRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desUrbanaIndRep")

  public String getDesUrbanaIndRep() {
    return desUrbanaIndRep;
  }
  public void setDesUrbanaIndRep(String desUrbanaIndRep) {
    this.desUrbanaIndRep = desUrbanaIndRep;
  }

  /**
   **/
  public AllCustomerInfo desStradaIndRep(String desStradaIndRep) {
    this.desStradaIndRep = desStradaIndRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStradaIndRep")

  public String getDesStradaIndRep() {
    return desStradaIndRep;
  }
  public void setDesStradaIndRep(String desStradaIndRep) {
    this.desStradaIndRep = desStradaIndRep;
  }

  /**
   **/
  public AllCustomerInfo numCivicoRep(String numCivicoRep) {
    this.numCivicoRep = numCivicoRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numCivicoRep")

  public String getNumCivicoRep() {
    return numCivicoRep;
  }
  public void setNumCivicoRep(String numCivicoRep) {
    this.numCivicoRep = numCivicoRep;
  }

  /**
   **/
  public AllCustomerInfo telefRep(String telefRep) {
    this.telefRep = telefRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("telefRep")

  public String getTelefRep() {
    return telefRep;
  }
  public void setTelefRep(String telefRep) {
    this.telefRep = telefRep;
  }

  /**
   **/
  public AllCustomerInfo telefono(String telefono) {
    this.telefono = telefono;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("telefono")

  public String getTelefono() {
    return telefono;
  }
  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  /**
   **/
  public AllCustomerInfo attivitaProfessio(String attivitaProfessio) {
    this.attivitaProfessio = attivitaProfessio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attivitaProfessio")

  public String getAttivitaProfessio() {
    return attivitaProfessio;
  }
  public void setAttivitaProfessio(String attivitaProfessio) {
    this.attivitaProfessio = attivitaProfessio;
  }

  /**
   **/
  public AllCustomerInfo gruppo(String gruppo) {
    this.gruppo = gruppo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("gruppo")

  public String getGruppo() {
    return gruppo;
  }
  public void setGruppo(String gruppo) {
    this.gruppo = gruppo;
  }

  /**
   **/
  public AllCustomerInfo sottoGruppo(String sottoGruppo) {
    this.sottoGruppo = sottoGruppo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sottoGruppo")

  public String getSottoGruppo() {
    return sottoGruppo;
  }
  public void setSottoGruppo(String sottoGruppo) {
    this.sottoGruppo = sottoGruppo;
  }

  /**
   **/
  public AllCustomerInfo tipoDocumentoAR(String tipoDocumentoAR) {
    this.tipoDocumentoAR = tipoDocumentoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoDocumentoAR")

  public String getTipoDocumentoAR() {
    return tipoDocumentoAR;
  }
  public void setTipoDocumentoAR(String tipoDocumentoAR) {
    this.tipoDocumentoAR = tipoDocumentoAR;
  }

  /**
   **/
  public AllCustomerInfo descTipoDocumento(String descTipoDocumento) {
    this.descTipoDocumento = descTipoDocumento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descTipoDocumento")

  public String getDescTipoDocumento() {
    return descTipoDocumento;
  }
  public void setDescTipoDocumento(String descTipoDocumento) {
    this.descTipoDocumento = descTipoDocumento;
  }

  /**
   **/
  public AllCustomerInfo numDocumentoAR(String numDocumentoAR) {
    this.numDocumentoAR = numDocumentoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numDocumentoAR")

  public String getNumDocumentoAR() {
    return numDocumentoAR;
  }
  public void setNumDocumentoAR(String numDocumentoAR) {
    this.numDocumentoAR = numDocumentoAR;
  }

  /**
   **/
  public AllCustomerInfo enteRilascio(String enteRilascio) {
    this.enteRilascio = enteRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("enteRilascio")

  public String getEnteRilascio() {
    return enteRilascio;
  }
  public void setEnteRilascio(String enteRilascio) {
    this.enteRilascio = enteRilascio;
  }

  /**
   **/
  public AllCustomerInfo dataRilascio(Date dataRilascio) {
    this.dataRilascio = dataRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRilascio")

  public Date getDataRilascio() {
    return dataRilascio;
  }
  public void setDataRilascio(Date dataRilascio) {
    this.dataRilascio = dataRilascio;
  }

  /**
   **/
  public AllCustomerInfo cittadinanza(String cittadinanza) {
    this.cittadinanza = cittadinanza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza")

  public String getCittadinanza() {
    return cittadinanza;
  }
  public void setCittadinanza(String cittadinanza) {
    this.cittadinanza = cittadinanza;
  }

  /**
   **/
  public AllCustomerInfo desCittadinanza(String desCittadinanza) {
    this.desCittadinanza = desCittadinanza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desCittadinanza")

  public String getDesCittadinanza() {
    return desCittadinanza;
  }
  public void setDesCittadinanza(String desCittadinanza) {
    this.desCittadinanza = desCittadinanza;
  }

  /**
   **/
  public AllCustomerInfo titoloStudio(String titoloStudio) {
    this.titoloStudio = titoloStudio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("titoloStudio")

  public String getTitoloStudio() {
    return titoloStudio;
  }
  public void setTitoloStudio(String titoloStudio) {
    this.titoloStudio = titoloStudio;
  }

  /**
   **/
  public AllCustomerInfo flagTasseEstero(Boolean flagTasseEstero) {
    this.flagTasseEstero = flagTasseEstero;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flagTasseEstero")

  public Boolean isFlagTasseEstero() {
    return flagTasseEstero;
  }
  public void setFlagTasseEstero(Boolean flagTasseEstero) {
    this.flagTasseEstero = flagTasseEstero;
  }

  /**
   **/
  public AllCustomerInfo nazTasseEstero(String nazTasseEstero) {
    this.nazTasseEstero = nazTasseEstero;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nazTasseEstero")

  public String getNazTasseEstero() {
    return nazTasseEstero;
  }
  public void setNazTasseEstero(String nazTasseEstero) {
    this.nazTasseEstero = nazTasseEstero;
  }

  /**
   **/
  public AllCustomerInfo desNazTasseEstero(String desNazTasseEstero) {
    this.desNazTasseEstero = desNazTasseEstero;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desNazTasseEstero")

  public String getDesNazTasseEstero() {
    return desNazTasseEstero;
  }
  public void setDesNazTasseEstero(String desNazTasseEstero) {
    this.desNazTasseEstero = desNazTasseEstero;
  }

  /**
   **/
  public AllCustomerInfo tin(String tin) {
    this.tin = tin;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tin")

  public String getTin() {
    return tin;
  }
  public void setTin(String tin) {
    this.tin = tin;
  }

  /**
   **/
  public AllCustomerInfo usStatus(String usStatus) {
    this.usStatus = usStatus;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("usStatus")

  public String getUsStatus() {
    return usStatus;
  }
  public void setUsStatus(String usStatus) {
    this.usStatus = usStatus;
  }

  /**
   **/
  public AllCustomerInfo cab(String cab) {
    this.cab = cab;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cab")

  public String getCab() {
    return cab;
  }
  public void setCab(String cab) {
    this.cab = cab;
  }

  /**
   **/
  public AllCustomerInfo marketing(Boolean marketing) {
    this.marketing = marketing;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("marketing")

  public Boolean isMarketing() {
    return marketing;
  }
  public void setMarketing(Boolean marketing) {
    this.marketing = marketing;
  }

  /**
   **/
  public AllCustomerInfo ricercheMercato(Boolean ricercheMercato) {
    this.ricercheMercato = ricercheMercato;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("ricercheMercato")

  public Boolean isRicercheMercato() {
    return ricercheMercato;
  }
  public void setRicercheMercato(Boolean ricercheMercato) {
    this.ricercheMercato = ricercheMercato;
  }

  /**
   **/
  public AllCustomerInfo comunicazioniSoggettiTerzi(Boolean comunicazioniSoggettiTerzi) {
    this.comunicazioniSoggettiTerzi = comunicazioniSoggettiTerzi;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("comunicazioniSoggettiTerzi")

  public Boolean isComunicazioniSoggettiTerzi() {
    return comunicazioniSoggettiTerzi;
  }
  public void setComunicazioniSoggettiTerzi(Boolean comunicazioniSoggettiTerzi) {
    this.comunicazioniSoggettiTerzi = comunicazioniSoggettiTerzi;
  }

  /**
   **/
  public AllCustomerInfo easyClientCode(String easyClientCode) {
    this.easyClientCode = easyClientCode;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("easyClientCode")

  public String getEasyClientCode() {
    return easyClientCode;
  }
  public void setEasyClientCode(String easyClientCode) {
    this.easyClientCode = easyClientCode;
  }

  /**
   **/
  public AllCustomerInfo easyClientCodePrincipale(String easyClientCodePrincipale) {
    this.easyClientCodePrincipale = easyClientCodePrincipale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("easyClientCodePrincipale")

  public String getEasyClientCodePrincipale() {
    return easyClientCodePrincipale;
  }
  public void setEasyClientCodePrincipale(String easyClientCodePrincipale) {
    this.easyClientCodePrincipale = easyClientCodePrincipale;
  }

  /**
   **/
  public AllCustomerInfo numCellulare(String numCellulare) {
    this.numCellulare = numCellulare;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numCellulare")

  public String getNumCellulare() {
    return numCellulare;
  }
  public void setNumCellulare(String numCellulare) {
    this.numCellulare = numCellulare;
  }

  /**
   **/
  public AllCustomerInfo cittadinanza2(String cittadinanza2) {
    this.cittadinanza2 = cittadinanza2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza2")

  public String getCittadinanza2() {
    return cittadinanza2;
  }
  public void setCittadinanza2(String cittadinanza2) {
    this.cittadinanza2 = cittadinanza2;
  }

  /**
   **/
  public AllCustomerInfo cittadinanza3(String cittadinanza3) {
    this.cittadinanza3 = cittadinanza3;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cittadinanza3")

  public String getCittadinanza3() {
    return cittadinanza3;
  }
  public void setCittadinanza3(String cittadinanza3) {
    this.cittadinanza3 = cittadinanza3;
  }

  /**
   **/
  public AllCustomerInfo dataScadenzaAR(Date dataScadenzaAR) {
    this.dataScadenzaAR = dataScadenzaAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataScadenzaAR")

  public Date getDataScadenzaAR() {
    return dataScadenzaAR;
  }
  public void setDataScadenzaAR(Date dataScadenzaAR) {
    this.dataScadenzaAR = dataScadenzaAR;
  }

  /**
   **/
  public AllCustomerInfo desSottogruppoAR(String desSottogruppoAR) {
    this.desSottogruppoAR = desSottogruppoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desSottogruppoAR")

  public String getDesSottogruppoAR() {
    return desSottogruppoAR;
  }
  public void setDesSottogruppoAR(String desSottogruppoAR) {
    this.desSottogruppoAR = desSottogruppoAR;
  }

  /**
   **/
  public AllCustomerInfo desGruppoAR(String desGruppoAR) {
    this.desGruppoAR = desGruppoAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desGruppoAR")

  public String getDesGruppoAR() {
    return desGruppoAR;
  }
  public void setDesGruppoAR(String desGruppoAR) {
    this.desGruppoAR = desGruppoAR;
  }

  /**
   **/
  public AllCustomerInfo codEnteRilascioAR(String codEnteRilascioAR) {
    this.codEnteRilascioAR = codEnteRilascioAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codEnteRilascioAR")

  public String getCodEnteRilascioAR() {
    return codEnteRilascioAR;
  }
  public void setCodEnteRilascioAR(String codEnteRilascioAR) {
    this.codEnteRilascioAR = codEnteRilascioAR;
  }

  /**
   **/
  public AllCustomerInfo desNazioneRilascioAR(String desNazioneRilascioAR) {
    this.desNazioneRilascioAR = desNazioneRilascioAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desNazioneRilascioAR")

  public String getDesNazioneRilascioAR() {
    return desNazioneRilascioAR;
  }
  public void setDesNazioneRilascioAR(String desNazioneRilascioAR) {
    this.desNazioneRilascioAR = desNazioneRilascioAR;
  }

  /**
   **/
  public AllCustomerInfo desProvinciaRilascioAR(String desProvinciaRilascioAR) {
    this.desProvinciaRilascioAR = desProvinciaRilascioAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desProvinciaRilascioAR")

  public String getDesProvinciaRilascioAR() {
    return desProvinciaRilascioAR;
  }
  public void setDesProvinciaRilascioAR(String desProvinciaRilascioAR) {
    this.desProvinciaRilascioAR = desProvinciaRilascioAR;
  }

  /**
   **/
  public AllCustomerInfo flLuogoRilascioEsteroAR(Boolean flLuogoRilascioEsteroAR) {
    this.flLuogoRilascioEsteroAR = flLuogoRilascioEsteroAR;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flLuogoRilascioEsteroAR")

  public Boolean isFlLuogoRilascioEsteroAR() {
    return flLuogoRilascioEsteroAR;
  }
  public void setFlLuogoRilascioEsteroAR(Boolean flLuogoRilascioEsteroAR) {
    this.flLuogoRilascioEsteroAR = flLuogoRilascioEsteroAR;
  }

  /**
   **/
  public AllCustomerInfo flDocumentiAllegatiAR(Boolean flDocumentiAllegatiAR) {
    this.flDocumentiAllegatiAR = flDocumentiAllegatiAR;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flDocumentiAllegatiAR")

  public Boolean isFlDocumentiAllegatiAR() {
    return flDocumentiAllegatiAR;
  }
  public void setFlDocumentiAllegatiAR(Boolean flDocumentiAllegatiAR) {
    this.flDocumentiAllegatiAR = flDocumentiAllegatiAR;
  }

  /**
   **/
  public AllCustomerInfo desDocumentiAllegatiAR(String desDocumentiAllegatiAR) {
    this.desDocumentiAllegatiAR = desDocumentiAllegatiAR;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desDocumentiAllegatiAR")

  public String getDesDocumentiAllegatiAR() {
    return desDocumentiAllegatiAR;
  }
  public void setDesDocumentiAllegatiAR(String desDocumentiAllegatiAR) {
    this.desDocumentiAllegatiAR = desDocumentiAllegatiAR;
  }

  /**
   **/
  public AllCustomerInfo desNazioneRep(String desNazioneRep) {
    this.desNazioneRep = desNazioneRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desNazioneRep")

  public String getDesNazioneRep() {
    return desNazioneRep;
  }
  public void setDesNazioneRep(String desNazioneRep) {
    this.desNazioneRep = desNazioneRep;
  }

  /**
   **/
  public AllCustomerInfo desProvinciaRep(String desProvinciaRep) {
    this.desProvinciaRep = desProvinciaRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desProvinciaRep")

  public String getDesProvinciaRep() {
    return desProvinciaRep;
  }
  public void setDesProvinciaRep(String desProvinciaRep) {
    this.desProvinciaRep = desProvinciaRep;
  }

  /**
   **/
  public AllCustomerInfo indirizzoRep(String indirizzoRep) {
    this.indirizzoRep = indirizzoRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("indirizzoRep")

  public String getIndirizzoRep() {
    return indirizzoRep;
  }
  public void setIndirizzoRep(String indirizzoRep) {
    this.indirizzoRep = indirizzoRep;
  }

  /**
   **/
  public AllCustomerInfo email(String email) {
    this.email = email;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("email")

  public String getEmail() {
    return email;
  }
  public void setEmail(String email) {
    this.email = email;
  }

  /**
   **/
  public AllCustomerInfo emailRep(String emailRep) {
    this.emailRep = emailRep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("emailRep")

  public String getEmailRep() {
    return emailRep;
  }
  public void setEmailRep(String emailRep) {
    this.emailRep = emailRep;
  }

  /**
   **/
  public AllCustomerInfo emailCertificata(String emailCertificata) {
    this.emailCertificata = emailCertificata;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("emailCertificata")

  public String getEmailCertificata() {
    return emailCertificata;
  }
  public void setEmailCertificata(String emailCertificata) {
    this.emailCertificata = emailCertificata;
  }

  /**
   **/
  public AllCustomerInfo condizioneProfessionale(String condizioneProfessionale) {
    this.condizioneProfessionale = condizioneProfessionale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("condizioneProfessionale")

  public String getCondizioneProfessionale() {
    return condizioneProfessionale;
  }
  public void setCondizioneProfessionale(String condizioneProfessionale) {
    this.condizioneProfessionale = condizioneProfessionale;
  }

  /**
   **/
  public AllCustomerInfo statoCivile(String statoCivile) {
    this.statoCivile = statoCivile;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoCivile")

  public String getStatoCivile() {
    return statoCivile;
  }
  public void setStatoCivile(String statoCivile) {
    this.statoCivile = statoCivile;
  }

  /**
   **/
  public AllCustomerInfo entePubblico(String entePubblico) {
    this.entePubblico = entePubblico;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("entePubblico")

  public String getEntePubblico() {
    return entePubblico;
  }
  public void setEntePubblico(String entePubblico) {
    this.entePubblico = entePubblico;
  }

  /**
   **/
  public AllCustomerInfo flCommerciale(Boolean flCommerciale) {
    this.flCommerciale = flCommerciale;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flCommerciale")

  public Boolean isFlCommerciale() {
    return flCommerciale;
  }
  public void setFlCommerciale(Boolean flCommerciale) {
    this.flCommerciale = flCommerciale;
  }

  /**
   **/
  public AllCustomerInfo societaQuotata(String societaQuotata) {
    this.societaQuotata = societaQuotata;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("societaQuotata")

  public String getSocietaQuotata() {
    return societaQuotata;
  }
  public void setSocietaQuotata(String societaQuotata) {
    this.societaQuotata = societaQuotata;
  }

  /**
   **/
  public AllCustomerInfo tipoSocieta(String tipoSocieta) {
    this.tipoSocieta = tipoSocieta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoSocieta")

  public String getTipoSocieta() {
    return tipoSocieta;
  }
  public void setTipoSocieta(String tipoSocieta) {
    this.tipoSocieta = tipoSocieta;
  }

  /**
   **/
  public AllCustomerInfo descTipoSocieta(String descTipoSocieta) {
    this.descTipoSocieta = descTipoSocieta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descTipoSocieta")
  

  public String getDescTipoSocieta() {
    return descTipoSocieta;
  }
  public void setDescTipoSocieta(String descTipoSocieta) {
    this.descTipoSocieta = descTipoSocieta;
  }

  /**
   **/
  public AllCustomerInfo dataPrimaIscrizione(Date dataPrimaIscrizione) {
    this.dataPrimaIscrizione = dataPrimaIscrizione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataPrimaIscrizione")
  

  public Date getDataPrimaIscrizione() {
    return dataPrimaIscrizione;
  }
  public void setDataPrimaIscrizione(Date dataPrimaIscrizione) {
    this.dataPrimaIscrizione = dataPrimaIscrizione;
  }

  /**
   **/
  public AllCustomerInfo greenCard(Boolean greenCard) {
    this.greenCard = greenCard;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("greenCard")
  

  public Boolean isGreenCard() {
    return greenCard;
  }
  public void setGreenCard(Boolean greenCard) {
    this.greenCard = greenCard;
  }

  /**
   **/
  public AllCustomerInfo flForzaturaUSStatus(Boolean flForzaturaUSStatus) {
    this.flForzaturaUSStatus = flForzaturaUSStatus;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flForzaturaUSStatus")
  

  public Boolean isFlForzaturaUSStatus() {
    return flForzaturaUSStatus;
  }
  public void setFlForzaturaUSStatus(Boolean flForzaturaUSStatus) {
    this.flForzaturaUSStatus = flForzaturaUSStatus;
  }

  /**
   **/
  public AllCustomerInfo motivoForzaturaUsStatus(String motivoForzaturaUsStatus) {
    this.motivoForzaturaUsStatus = motivoForzaturaUsStatus;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("motivoForzaturaUsStatus")
  

  public String getMotivoForzaturaUsStatus() {
    return motivoForzaturaUsStatus;
  }
  public void setMotivoForzaturaUsStatus(String motivoForzaturaUsStatus) {
    this.motivoForzaturaUsStatus = motivoForzaturaUsStatus;
  }

  /**
   **/
  public AllCustomerInfo flTitolareEffettivo(Boolean flTitolareEffettivo) {
    this.flTitolareEffettivo = flTitolareEffettivo;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flTitolareEffettivo")
  

  public Boolean isFlTitolareEffettivo() {
    return flTitolareEffettivo;
  }
  public void setFlTitolareEffettivo(Boolean flTitolareEffettivo) {
    this.flTitolareEffettivo = flTitolareEffettivo;
  }

  /**
   **/
  public AllCustomerInfo provAttivitaPrevalente(String provAttivitaPrevalente) {
    this.provAttivitaPrevalente = provAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("provAttivitaPrevalente")
  

  public String getProvAttivitaPrevalente() {
    return provAttivitaPrevalente;
  }
  public void setProvAttivitaPrevalente(String provAttivitaPrevalente) {
    this.provAttivitaPrevalente = provAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo descProvAttivitaPrevalente(String descProvAttivitaPrevalente) {
    this.descProvAttivitaPrevalente = descProvAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descProvAttivitaPrevalente")
  

  public String getDescProvAttivitaPrevalente() {
    return descProvAttivitaPrevalente;
  }
  public void setDescProvAttivitaPrevalente(String descProvAttivitaPrevalente) {
    this.descProvAttivitaPrevalente = descProvAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo luogoAttivitaPrevalente(String luogoAttivitaPrevalente) {
    this.luogoAttivitaPrevalente = luogoAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoAttivitaPrevalente")
  

  public String getLuogoAttivitaPrevalente() {
    return luogoAttivitaPrevalente;
  }
  public void setLuogoAttivitaPrevalente(String luogoAttivitaPrevalente) {
    this.luogoAttivitaPrevalente = luogoAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo capAttivitaPrevalente(String capAttivitaPrevalente) {
    this.capAttivitaPrevalente = capAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("capAttivitaPrevalente")
  

  public String getCapAttivitaPrevalente() {
    return capAttivitaPrevalente;
  }
  public void setCapAttivitaPrevalente(String capAttivitaPrevalente) {
    this.capAttivitaPrevalente = capAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo statoCliente(String statoCliente) {
    this.statoCliente = statoCliente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoCliente")
  

  public String getStatoCliente() {
    return statoCliente;
  }
  public void setStatoCliente(String statoCliente) {
    this.statoCliente = statoCliente;
  }

  /**
   **/
  public AllCustomerInfo tipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoCliente")
  

  public String getTipoCliente() {
    return tipoCliente;
  }
  public void setTipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
  }

  /**
   **/
  public AllCustomerInfo ultProgStorico(String ultProgStorico) {
    this.ultProgStorico = ultProgStorico;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ultProgStorico")
  

  public String getUltProgStorico() {
    return ultProgStorico;
  }
  public void setUltProgStorico(String ultProgStorico) {
    this.ultProgStorico = ultProgStorico;
  }

  /**
   **/
  public AllCustomerInfo flagOrigineFondi(Boolean flagOrigineFondi) {
    this.flagOrigineFondi = flagOrigineFondi;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flagOrigineFondi")
  

  public Boolean isFlagOrigineFondi() {
    return flagOrigineFondi;
  }
  public void setFlagOrigineFondi(Boolean flagOrigineFondi) {
    this.flagOrigineFondi = flagOrigineFondi;
  }

  /**
   **/
  public AllCustomerInfo sedeAttivitaPrevalente(String sedeAttivitaPrevalente) {
    this.sedeAttivitaPrevalente = sedeAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sedeAttivitaPrevalente")
  

  public String getSedeAttivitaPrevalente() {
    return sedeAttivitaPrevalente;
  }
  public void setSedeAttivitaPrevalente(String sedeAttivitaPrevalente) {
    this.sedeAttivitaPrevalente = sedeAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo attivitaPrevalente(String attivitaPrevalente) {
    this.attivitaPrevalente = attivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attivitaPrevalente")
  

  public String getAttivitaPrevalente() {
    return attivitaPrevalente;
  }
  public void setAttivitaPrevalente(String attivitaPrevalente) {
    this.attivitaPrevalente = attivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo descAttivitaPrevalente(String descAttivitaPrevalente) {
    this.descAttivitaPrevalente = descAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descAttivitaPrevalente")
  

  public String getDescAttivitaPrevalente() {
    return descAttivitaPrevalente;
  }
  public void setDescAttivitaPrevalente(String descAttivitaPrevalente) {
    this.descAttivitaPrevalente = descAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo codRegione(String codRegione) {
    this.codRegione = codRegione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codRegione")
  

  public String getCodRegione() {
    return codRegione;
  }
  public void setCodRegione(String codRegione) {
    this.codRegione = codRegione;
  }

  /**
   **/
  public AllCustomerInfo descRegione(String descRegione) {
    this.descRegione = descRegione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descRegione")
  

  public String getDescRegione() {
    return descRegione;
  }
  public void setDescRegione(String descRegione) {
    this.descRegione = descRegione;
  }

  /**
   **/
  public AllCustomerInfo flEntitaNonFinanziaria(Boolean flEntitaNonFinanziaria) {
    this.flEntitaNonFinanziaria = flEntitaNonFinanziaria;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flEntitaNonFinanziaria")
  

  public Boolean isFlEntitaNonFinanziaria() {
    return flEntitaNonFinanziaria;
  }
  public void setFlEntitaNonFinanziaria(Boolean flEntitaNonFinanziaria) {
    this.flEntitaNonFinanziaria = flEntitaNonFinanziaria;
  }

  /**
   **/
  public AllCustomerInfo flagConsComunicazioniElettroniche(Boolean flagConsComunicazioniElettroniche) {
    this.flagConsComunicazioniElettroniche = flagConsComunicazioniElettroniche;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flagConsComunicazioniElettroniche")
  

  public Boolean isFlagConsComunicazioniElettroniche() {
    return flagConsComunicazioniElettroniche;
  }
  public void setFlagConsComunicazioniElettroniche(Boolean flagConsComunicazioniElettroniche) {
    this.flagConsComunicazioniElettroniche = flagConsComunicazioniElettroniche;
  }

  /**
   **/
  public AllCustomerInfo dataConsComunicazioniElettroniche(Date dataConsComunicazioniElettroniche) {
    this.dataConsComunicazioniElettroniche = dataConsComunicazioniElettroniche;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataConsComunicazioniElettroniche")
  

  public Date getDataConsComunicazioniElettroniche() {
    return dataConsComunicazioniElettroniche;
  }
  public void setDataConsComunicazioniElettroniche(Date dataConsComunicazioniElettroniche) {
    this.dataConsComunicazioniElettroniche = dataConsComunicazioniElettroniche;
  }

  /**
   **/
  public AllCustomerInfo dataRevocaConsComunicazioniElettroniche(Date dataRevocaConsComunicazioniElettroniche) {
    this.dataRevocaConsComunicazioniElettroniche = dataRevocaConsComunicazioniElettroniche;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRevocaConsComunicazioniElettroniche")
  

  public Date getDataRevocaConsComunicazioniElettroniche() {
    return dataRevocaConsComunicazioniElettroniche;
  }
  public void setDataRevocaConsComunicazioniElettroniche(Date dataRevocaConsComunicazioniElettroniche) {
    this.dataRevocaConsComunicazioniElettroniche = dataRevocaConsComunicazioniElettroniche;
  }

  /**
   **/
  public AllCustomerInfo dataUpdMailComunicazioniElettroniche(Date dataUpdMailComunicazioniElettroniche) {
    this.dataUpdMailComunicazioniElettroniche = dataUpdMailComunicazioniElettroniche;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataUpdMailComunicazioniElettroniche")
  

  public Date getDataUpdMailComunicazioniElettroniche() {
    return dataUpdMailComunicazioniElettroniche;
  }
  public void setDataUpdMailComunicazioniElettroniche(Date dataUpdMailComunicazioniElettroniche) {
    this.dataUpdMailComunicazioniElettroniche = dataUpdMailComunicazioniElettroniche;
  }

  /**
   **/
  public AllCustomerInfo flBeneficiariDisabili(Boolean flBeneficiariDisabili) {
    this.flBeneficiariDisabili = flBeneficiariDisabili;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flBeneficiariDisabili")
  

  public Boolean isFlBeneficiariDisabili() {
    return flBeneficiariDisabili;
  }
  public void setFlBeneficiariDisabili(Boolean flBeneficiariDisabili) {
    this.flBeneficiariDisabili = flBeneficiariDisabili;
  }

  /**
   **/
  public AllCustomerInfo dataAttivazBeneficiariDisabili(Date dataAttivazBeneficiariDisabili) {
    this.dataAttivazBeneficiariDisabili = dataAttivazBeneficiariDisabili;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataAttivazBeneficiariDisabili")
  

  public Date getDataAttivazBeneficiariDisabili() {
    return dataAttivazBeneficiariDisabili;
  }
  public void setDataAttivazBeneficiariDisabili(Date dataAttivazBeneficiariDisabili) {
    this.dataAttivazBeneficiariDisabili = dataAttivazBeneficiariDisabili;
  }

  /**
   **/
  public AllCustomerInfo ateco(String ateco) {
    this.ateco = ateco;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ateco")
  

  public String getAteco() {
    return ateco;
  }
  public void setAteco(String ateco) {
    this.ateco = ateco;
  }

  /**
   **/
  public AllCustomerInfo dataUltimaAdeguataVerifica(Date dataUltimaAdeguataVerifica) {
    this.dataUltimaAdeguataVerifica = dataUltimaAdeguataVerifica;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataUltimaAdeguataVerifica")
  

  public Date getDataUltimaAdeguataVerifica() {
    return dataUltimaAdeguataVerifica;
  }
  public void setDataUltimaAdeguataVerifica(Date dataUltimaAdeguataVerifica) {
    this.dataUltimaAdeguataVerifica = dataUltimaAdeguataVerifica;
  }

  /**
   **/
  public AllCustomerInfo fondoPensione(Boolean fondoPensione) {
    this.fondoPensione = fondoPensione;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("fondoPensione")
  

  public Boolean isFondoPensione() {
    return fondoPensione;
  }
  public void setFondoPensione(Boolean fondoPensione) {
    this.fondoPensione = fondoPensione;
  }

  /**
   **/
  public AllCustomerInfo fiduciaria(Boolean fiduciaria) {
    this.fiduciaria = fiduciaria;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("fiduciaria")
  

  public Boolean isFiduciaria() {
    return fiduciaria;
  }
  public void setFiduciaria(Boolean fiduciaria) {
    this.fiduciaria = fiduciaria;
  }

  /**
   **/
  public AllCustomerInfo intermediarioFinanziario(Boolean intermediarioFinanziario) {
    this.intermediarioFinanziario = intermediarioFinanziario;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("intermediarioFinanziario")
  

  public Boolean isIntermediarioFinanziario() {
    return intermediarioFinanziario;
  }
  public void setIntermediarioFinanziario(Boolean intermediarioFinanziario) {
    this.intermediarioFinanziario = intermediarioFinanziario;
  }

  /**
   **/
  public AllCustomerInfo flPaesiBlackListAR(Boolean flPaesiBlackListAR) {
    this.flPaesiBlackListAR = flPaesiBlackListAR;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flPaesiBlackListAR")
  

  public Boolean isFlPaesiBlackListAR() {
    return flPaesiBlackListAR;
  }
  public void setFlPaesiBlackListAR(Boolean flPaesiBlackListAR) {
    this.flPaesiBlackListAR = flPaesiBlackListAR;
  }

  /**
   **/
  public AllCustomerInfo flgDomicilio(Boolean flgDomicilio) {
    this.flgDomicilio = flgDomicilio;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flgDomicilio")
  

  public Boolean isFlgDomicilio() {
    return flgDomicilio;
  }
  public void setFlgDomicilio(Boolean flgDomicilio) {
    this.flgDomicilio = flgDomicilio;
  }

  /**
   **/
  public AllCustomerInfo statoDomicilio(String statoDomicilio) {
    this.statoDomicilio = statoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoDomicilio")
  

  public String getStatoDomicilio() {
    return statoDomicilio;
  }
  public void setStatoDomicilio(String statoDomicilio) {
    this.statoDomicilio = statoDomicilio;
  }

  /**
   **/
  public AllCustomerInfo provDomicilio(String provDomicilio) {
    this.provDomicilio = provDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("provDomicilio")
  

  public String getProvDomicilio() {
    return provDomicilio;
  }
  public void setProvDomicilio(String provDomicilio) {
    this.provDomicilio = provDomicilio;
  }

  /**
   **/
  public AllCustomerInfo luogoDomicilio(String luogoDomicilio) {
    this.luogoDomicilio = luogoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoDomicilio")
  

  public String getLuogoDomicilio() {
    return luogoDomicilio;
  }
  public void setLuogoDomicilio(String luogoDomicilio) {
    this.luogoDomicilio = luogoDomicilio;
  }

  /**
   **/
  public AllCustomerInfo toponimoDomicilio(String toponimoDomicilio) {
    this.toponimoDomicilio = toponimoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("toponimoDomicilio")
  

  public String getToponimoDomicilio() {
    return toponimoDomicilio;
  }
  public void setToponimoDomicilio(String toponimoDomicilio) {
    this.toponimoDomicilio = toponimoDomicilio;
  }

  /**
   **/
  public AllCustomerInfo descrizioneDomicilio(String descrizioneDomicilio) {
    this.descrizioneDomicilio = descrizioneDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrizioneDomicilio")
  

  public String getDescrizioneDomicilio() {
    return descrizioneDomicilio;
  }
  public void setDescrizioneDomicilio(String descrizioneDomicilio) {
    this.descrizioneDomicilio = descrizioneDomicilio;
  }

  /**
   **/
  public AllCustomerInfo numCivicoDomicilio(String numCivicoDomicilio) {
    this.numCivicoDomicilio = numCivicoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numCivicoDomicilio")
  

  public String getNumCivicoDomicilio() {
    return numCivicoDomicilio;
  }
  public void setNumCivicoDomicilio(String numCivicoDomicilio) {
    this.numCivicoDomicilio = numCivicoDomicilio;
  }

  /**
   **/
  public AllCustomerInfo capDomicilio(String capDomicilio) {
    this.capDomicilio = capDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("capDomicilio")
  

  public String getCapDomicilio() {
    return capDomicilio;
  }
  public void setCapDomicilio(String capDomicilio) {
    this.capDomicilio = capDomicilio;
  }

  /**
   **/
  public AllCustomerInfo flgCorrispondenza(Boolean flgCorrispondenza) {
    this.flgCorrispondenza = flgCorrispondenza;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flgCorrispondenza")
  

  public Boolean isFlgCorrispondenza() {
    return flgCorrispondenza;
  }
  public void setFlgCorrispondenza(Boolean flgCorrispondenza) {
    this.flgCorrispondenza = flgCorrispondenza;
  }

  /**
   **/
  public AllCustomerInfo flgCorrispondenzaIntestataSoggetto(Boolean flgCorrispondenzaIntestataSoggetto) {
    this.flgCorrispondenzaIntestataSoggetto = flgCorrispondenzaIntestataSoggetto;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flgCorrispondenzaIntestataSoggetto")
  

  public Boolean isFlgCorrispondenzaIntestataSoggetto() {
    return flgCorrispondenzaIntestataSoggetto;
  }
  public void setFlgCorrispondenzaIntestataSoggetto(Boolean flgCorrispondenzaIntestataSoggetto) {
    this.flgCorrispondenzaIntestataSoggetto = flgCorrispondenzaIntestataSoggetto;
  }

  /**
   **/
  public AllCustomerInfo autorita(String autorita) {
    this.autorita = autorita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("autorita")
  

  public String getAutorita() {
    return autorita;
  }
  public void setAutorita(String autorita) {
    this.autorita = autorita;
  }

  /**
   **/
  public AllCustomerInfo albo(String albo) {
    this.albo = albo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("albo")
  

  public String getAlbo() {
    return albo;
  }
  public void setAlbo(String albo) {
    this.albo = albo;
  }

  /**
   **/
  public AllCustomerInfo numeroIscrizioneAlbo(String numeroIscrizioneAlbo) {
    this.numeroIscrizioneAlbo = numeroIscrizioneAlbo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numeroIscrizioneAlbo")
  

  public String getNumeroIscrizioneAlbo() {
    return numeroIscrizioneAlbo;
  }
  public void setNumeroIscrizioneAlbo(String numeroIscrizioneAlbo) {
    this.numeroIscrizioneAlbo = numeroIscrizioneAlbo;
  }

  /**
   **/
  public AllCustomerInfo registroIscrizione(String registroIscrizione) {
    this.registroIscrizione = registroIscrizione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("registroIscrizione")
  

  public String getRegistroIscrizione() {
    return registroIscrizione;
  }
  public void setRegistroIscrizione(String registroIscrizione) {
    this.registroIscrizione = registroIscrizione;
  }

  /**
   **/
  public AllCustomerInfo numeroIscrizioneRegistro(String numeroIscrizioneRegistro) {
    this.numeroIscrizioneRegistro = numeroIscrizioneRegistro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numeroIscrizioneRegistro")
  

  public String getNumeroIscrizioneRegistro() {
    return numeroIscrizioneRegistro;
  }
  public void setNumeroIscrizioneRegistro(String numeroIscrizioneRegistro) {
    this.numeroIscrizioneRegistro = numeroIscrizioneRegistro;
  }

  /**
   **/
  public AllCustomerInfo dataIscrizione(Date dataIscrizione) {
    this.dataIscrizione = dataIscrizione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataIscrizione")
  

  public Date getDataIscrizione() {
    return dataIscrizione;
  }
  public void setDataIscrizione(Date dataIscrizione) {
    this.dataIscrizione = dataIscrizione;
  }

  /**
   **/
  public AllCustomerInfo societaAzionariatoFiduciario(String societaAzionariatoFiduciario) {
    this.societaAzionariatoFiduciario = societaAzionariatoFiduciario;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("societaAzionariatoFiduciario")
  

  public String getSocietaAzionariatoFiduciario() {
    return societaAzionariatoFiduciario;
  }
  public void setSocietaAzionariatoFiduciario(String societaAzionariatoFiduciario) {
    this.societaAzionariatoFiduciario = societaAzionariatoFiduciario;
  }

  /**
   **/
  public AllCustomerInfo classeBeneficiari(String classeBeneficiari) {
    this.classeBeneficiari = classeBeneficiari;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("classeBeneficiari")
  

  public String getClasseBeneficiari() {
    return classeBeneficiari;
  }
  public void setClasseBeneficiari(String classeBeneficiari) {
    this.classeBeneficiari = classeBeneficiari;
  }

  /**
   **/
  public AllCustomerInfo livelloIstruzione(String livelloIstruzione) {
    this.livelloIstruzione = livelloIstruzione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("livelloIstruzione")
  

  public String getLivelloIstruzione() {
    return livelloIstruzione;
  }
  public void setLivelloIstruzione(String livelloIstruzione) {
    this.livelloIstruzione = livelloIstruzione;
  }

  /**
   **/
  public AllCustomerInfo attivitaPrevalenteAltro(String attivitaPrevalenteAltro) {
    this.attivitaPrevalenteAltro = attivitaPrevalenteAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attivitaPrevalenteAltro")
  

  public String getAttivitaPrevalenteAltro() {
    return attivitaPrevalenteAltro;
  }
  public void setAttivitaPrevalenteAltro(String attivitaPrevalenteAltro) {
    this.attivitaPrevalenteAltro = attivitaPrevalenteAltro;
  }

  /**
   **/
  public AllCustomerInfo attivitaPrecedentePensionamento(String attivitaPrecedentePensionamento) {
    this.attivitaPrecedentePensionamento = attivitaPrecedentePensionamento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attivitaPrecedentePensionamento")
  

  public String getAttivitaPrecedentePensionamento() {
    return attivitaPrecedentePensionamento;
  }
  public void setAttivitaPrecedentePensionamento(String attivitaPrecedentePensionamento) {
    this.attivitaPrecedentePensionamento = attivitaPrecedentePensionamento;
  }

  /**
   **/
  public AllCustomerInfo descAttivitaPrecedentePensionamento(String descAttivitaPrecedentePensionamento) {
    this.descAttivitaPrecedentePensionamento = descAttivitaPrecedentePensionamento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descAttivitaPrecedentePensionamento")
  

  public String getDescAttivitaPrecedentePensionamento() {
    return descAttivitaPrecedentePensionamento;
  }
  public void setDescAttivitaPrecedentePensionamento(String descAttivitaPrecedentePensionamento) {
    this.descAttivitaPrecedentePensionamento = descAttivitaPrecedentePensionamento;
  }

  /**
   **/
  public AllCustomerInfo attivitaPrecedentePensionamentoAltro(String attivitaPrecedentePensionamentoAltro) {
    this.attivitaPrecedentePensionamentoAltro = attivitaPrecedentePensionamentoAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("attivitaPrecedentePensionamentoAltro")
  

  public String getAttivitaPrecedentePensionamentoAltro() {
    return attivitaPrecedentePensionamentoAltro;
  }
  public void setAttivitaPrecedentePensionamentoAltro(String attivitaPrecedentePensionamentoAltro) {
    this.attivitaPrecedentePensionamentoAltro = attivitaPrecedentePensionamentoAltro;
  }

  /**
   **/
  public AllCustomerInfo statoAttivitaPrevalente(String statoAttivitaPrevalente) {
    this.statoAttivitaPrevalente = statoAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("statoAttivitaPrevalente")
  

  public String getStatoAttivitaPrevalente() {
    return statoAttivitaPrevalente;
  }
  public void setStatoAttivitaPrevalente(String statoAttivitaPrevalente) {
    this.statoAttivitaPrevalente = statoAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo ambitoAttivita(String ambitoAttivita) {
    this.ambitoAttivita = ambitoAttivita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ambitoAttivita")
  

  public String getAmbitoAttivita() {
    return ambitoAttivita;
  }
  public void setAmbitoAttivita(String ambitoAttivita) {
    this.ambitoAttivita = ambitoAttivita;
  }

  /**
   **/
  public AllCustomerInfo descAmbitoAttivita(String descAmbitoAttivita) {
    this.descAmbitoAttivita = descAmbitoAttivita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descAmbitoAttivita")
  

  public String getDescAmbitoAttivita() {
    return descAmbitoAttivita;
  }
  public void setDescAmbitoAttivita(String descAmbitoAttivita) {
    this.descAmbitoAttivita = descAmbitoAttivita;
  }

  /**
   **/
  public AllCustomerInfo altraCaricaPubblicaNonPep(String altraCaricaPubblicaNonPep) {
    this.altraCaricaPubblicaNonPep = altraCaricaPubblicaNonPep;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("altraCaricaPubblicaNonPep")
  

  public String getAltraCaricaPubblicaNonPep() {
    return altraCaricaPubblicaNonPep;
  }
  public void setAltraCaricaPubblicaNonPep(String altraCaricaPubblicaNonPep) {
    this.altraCaricaPubblicaNonPep = altraCaricaPubblicaNonPep;
  }

  /**
   **/
  public AllCustomerInfo flgRelazPaesiAltoRischio(Boolean flgRelazPaesiAltoRischio) {
    this.flgRelazPaesiAltoRischio = flgRelazPaesiAltoRischio;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("flgRelazPaesiAltoRischio")
  

  public Boolean isFlgRelazPaesiAltoRischio() {
    return flgRelazPaesiAltoRischio;
  }
  public void setFlgRelazPaesiAltoRischio(Boolean flgRelazPaesiAltoRischio) {
    this.flgRelazPaesiAltoRischio = flgRelazPaesiAltoRischio;
  }

  /**
   **/
  public AllCustomerInfo dataRif(Date dataRif) {
    this.dataRif = dataRif;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRif")
  

  public Date getDataRif() {
    return dataRif;
  }
  public void setDataRif(Date dataRif) {
    this.dataRif = dataRif;
  }

  /**
   **/
  public AllCustomerInfo customerOtherInfo(CustomerOtherInfo customerOtherInfo) {
    this.customerOtherInfo = customerOtherInfo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("customerOtherInfo")
  

  public CustomerOtherInfo getCustomerOtherInfo() {
    return customerOtherInfo;
  }
  public void setCustomerOtherInfo(CustomerOtherInfo customerOtherInfo) {
    this.customerOtherInfo = customerOtherInfo;
  }

  /**
   **/
  public AllCustomerInfo desCittadinanza2(String desCittadinanza2) {
    this.desCittadinanza2 = desCittadinanza2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desCittadinanza2")
  

  public String getDesCittadinanza2() {
    return desCittadinanza2;
  }
  public void setDesCittadinanza2(String desCittadinanza2) {
    this.desCittadinanza2 = desCittadinanza2;
  }

  /**
   **/
  public AllCustomerInfo desCittadinanza3(String desCittadinanza3) {
    this.desCittadinanza3 = desCittadinanza3;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desCittadinanza3")
  

  public String getDesCittadinanza3() {
    return desCittadinanza3;
  }
  public void setDesCittadinanza3(String desCittadinanza3) {
    this.desCittadinanza3 = desCittadinanza3;
  }

  /**
   **/
  public AllCustomerInfo desStatoDomicilio(String desStatoDomicilio) {
    this.desStatoDomicilio = desStatoDomicilio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStatoDomicilio")
  

  public String getDesStatoDomicilio() {
    return desStatoDomicilio;
  }
  public void setDesStatoDomicilio(String desStatoDomicilio) {
    this.desStatoDomicilio = desStatoDomicilio;
  }

  /**
   **/
  public AllCustomerInfo desStatoAttivitaPrevalente(String desStatoAttivitaPrevalente) {
    this.desStatoAttivitaPrevalente = desStatoAttivitaPrevalente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desStatoAttivitaPrevalente")
  

  public String getDesStatoAttivitaPrevalente() {
    return desStatoAttivitaPrevalente;
  }
  public void setDesStatoAttivitaPrevalente(String desStatoAttivitaPrevalente) {
    this.desStatoAttivitaPrevalente = desStatoAttivitaPrevalente;
  }

  /**
   **/
  public AllCustomerInfo desNazioneRilascio(String desNazioneRilascio) {
    this.desNazioneRilascio = desNazioneRilascio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("desNazioneRilascio")
  

  public String getDesNazioneRilascio() {
    return desNazioneRilascio;
  }
  public void setDesNazioneRilascio(String desNazioneRilascio) {
    this.desNazioneRilascio = desNazioneRilascio;
  }

  /**
   **/
  public AllCustomerInfo tipoPensionamento(String tipoPensionamento) {
    this.tipoPensionamento = tipoPensionamento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoPensionamento")
  

  public String getTipoPensionamento() {
    return tipoPensionamento;
  }
  public void setTipoPensionamento(String tipoPensionamento) {
    this.tipoPensionamento = tipoPensionamento;
  }

  /**
   **/
  public AllCustomerInfo dataVerificaRafforzata(Date dataVerificaRafforzata) {
    this.dataVerificaRafforzata = dataVerificaRafforzata;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataVerificaRafforzata")
  

  public Date getDataVerificaRafforzata() {
    return dataVerificaRafforzata;
  }
  public void setDataVerificaRafforzata(Date dataVerificaRafforzata) {
    this.dataVerificaRafforzata = dataVerificaRafforzata;
  }

  /**
   **/
  public AllCustomerInfo dataVerificaRafforzataPrecedente(Date dataVerificaRafforzataPrecedente) {
    this.dataVerificaRafforzataPrecedente = dataVerificaRafforzataPrecedente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataVerificaRafforzataPrecedente")
  

  public Date getDataVerificaRafforzataPrecedente() {
    return dataVerificaRafforzataPrecedente;
  }
  public void setDataVerificaRafforzataPrecedente(Date dataVerificaRafforzataPrecedente) {
    this.dataVerificaRafforzataPrecedente = dataVerificaRafforzataPrecedente;
  }

  /**
   **/
  public AllCustomerInfo profiloRischio(String profiloRischio) {
    this.profiloRischio = profiloRischio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("profiloRischio")
  

  public String getProfiloRischio() {
    return profiloRischio;
  }
  public void setProfiloRischio(String profiloRischio) {
    this.profiloRischio = profiloRischio;
  }

  /**
   **/
  public AllCustomerInfo paeseBlackList1(String paeseBlackList1) {
    this.paeseBlackList1 = paeseBlackList1;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("paeseBlackList1")
  

  public String getPaeseBlackList1() {
    return paeseBlackList1;
  }
  public void setPaeseBlackList1(String paeseBlackList1) {
    this.paeseBlackList1 = paeseBlackList1;
  }

  /**
   **/
  public AllCustomerInfo paeseBlackList2(String paeseBlackList2) {
    this.paeseBlackList2 = paeseBlackList2;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("paeseBlackList2")
  

  public String getPaeseBlackList2() {
    return paeseBlackList2;
  }
  public void setPaeseBlackList2(String paeseBlackList2) {
    this.paeseBlackList2 = paeseBlackList2;
  }

  /**
   **/
  public AllCustomerInfo paeseBlackList3(String paeseBlackList3) {
    this.paeseBlackList3 = paeseBlackList3;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("paeseBlackList3")
  

  public String getPaeseBlackList3() {
    return paeseBlackList3;
  }
  public void setPaeseBlackList3(String paeseBlackList3) {
    this.paeseBlackList3 = paeseBlackList3;
  }

  /**
   **/
  public AllCustomerInfo motivoRelazione(String motivoRelazione) {
    this.motivoRelazione = motivoRelazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("motivoRelazione")
  

  public String getMotivoRelazione() {
    return motivoRelazione;
  }
  public void setMotivoRelazione(String motivoRelazione) {
    this.motivoRelazione = motivoRelazione;
  }

  /**
   **/
  public AllCustomerInfo flgFiduciarioProprioTerzi(FlgFiduciarioProprioTerziEnum flgFiduciarioProprioTerzi) {
    this.flgFiduciarioProprioTerzi = flgFiduciarioProprioTerzi;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flgFiduciarioProprioTerzi")
  

  public FlgFiduciarioProprioTerziEnum getFlgFiduciarioProprioTerzi() {
    return flgFiduciarioProprioTerzi;
  }
  public void setFlgFiduciarioProprioTerzi(FlgFiduciarioProprioTerziEnum flgFiduciarioProprioTerzi) {
    this.flgFiduciarioProprioTerzi = flgFiduciarioProprioTerzi;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AllCustomerInfo allCustomerInfo = (AllCustomerInfo) o;
    return Objects.equals(nazioneRilascio, allCustomerInfo.nazioneRilascio) &&
        Objects.equals(provRilascio, allCustomerInfo.provRilascio) &&
        Objects.equals(localRilascio, allCustomerInfo.localRilascio) &&
        Objects.equals(pressoRep, allCustomerInfo.pressoRep) &&
        Objects.equals(statoRep, allCustomerInfo.statoRep) &&
        Objects.equals(proviRep, allCustomerInfo.proviRep) &&
        Objects.equals(localRep, allCustomerInfo.localRep) &&
        Objects.equals(capRep, allCustomerInfo.capRep) &&
        Objects.equals(desUrbanaIndRep, allCustomerInfo.desUrbanaIndRep) &&
        Objects.equals(desStradaIndRep, allCustomerInfo.desStradaIndRep) &&
        Objects.equals(numCivicoRep, allCustomerInfo.numCivicoRep) &&
        Objects.equals(telefRep, allCustomerInfo.telefRep) &&
        Objects.equals(telefono, allCustomerInfo.telefono) &&
        Objects.equals(attivitaProfessio, allCustomerInfo.attivitaProfessio) &&
        Objects.equals(gruppo, allCustomerInfo.gruppo) &&
        Objects.equals(sottoGruppo, allCustomerInfo.sottoGruppo) &&
        Objects.equals(tipoDocumentoAR, allCustomerInfo.tipoDocumentoAR) &&
        Objects.equals(descTipoDocumento, allCustomerInfo.descTipoDocumento) &&
        Objects.equals(numDocumentoAR, allCustomerInfo.numDocumentoAR) &&
        Objects.equals(enteRilascio, allCustomerInfo.enteRilascio) &&
        Objects.equals(dataRilascio, allCustomerInfo.dataRilascio) &&
        Objects.equals(cittadinanza, allCustomerInfo.cittadinanza) &&
        Objects.equals(desCittadinanza, allCustomerInfo.desCittadinanza) &&
        Objects.equals(titoloStudio, allCustomerInfo.titoloStudio) &&
        Objects.equals(flagTasseEstero, allCustomerInfo.flagTasseEstero) &&
        Objects.equals(nazTasseEstero, allCustomerInfo.nazTasseEstero) &&
        Objects.equals(desNazTasseEstero, allCustomerInfo.desNazTasseEstero) &&
        Objects.equals(tin, allCustomerInfo.tin) &&
        Objects.equals(usStatus, allCustomerInfo.usStatus) &&
        Objects.equals(cab, allCustomerInfo.cab) &&
        Objects.equals(marketing, allCustomerInfo.marketing) &&
        Objects.equals(ricercheMercato, allCustomerInfo.ricercheMercato) &&
        Objects.equals(comunicazioniSoggettiTerzi, allCustomerInfo.comunicazioniSoggettiTerzi) &&
        Objects.equals(easyClientCode, allCustomerInfo.easyClientCode) &&
        Objects.equals(easyClientCodePrincipale, allCustomerInfo.easyClientCodePrincipale) &&
        Objects.equals(numCellulare, allCustomerInfo.numCellulare) &&
        Objects.equals(cittadinanza2, allCustomerInfo.cittadinanza2) &&
        Objects.equals(cittadinanza3, allCustomerInfo.cittadinanza3) &&
        Objects.equals(dataScadenzaAR, allCustomerInfo.dataScadenzaAR) &&
        Objects.equals(desSottogruppoAR, allCustomerInfo.desSottogruppoAR) &&
        Objects.equals(desGruppoAR, allCustomerInfo.desGruppoAR) &&
        Objects.equals(codEnteRilascioAR, allCustomerInfo.codEnteRilascioAR) &&
        Objects.equals(desNazioneRilascioAR, allCustomerInfo.desNazioneRilascioAR) &&
        Objects.equals(desProvinciaRilascioAR, allCustomerInfo.desProvinciaRilascioAR) &&
        Objects.equals(flLuogoRilascioEsteroAR, allCustomerInfo.flLuogoRilascioEsteroAR) &&
        Objects.equals(flDocumentiAllegatiAR, allCustomerInfo.flDocumentiAllegatiAR) &&
        Objects.equals(desDocumentiAllegatiAR, allCustomerInfo.desDocumentiAllegatiAR) &&
        Objects.equals(desNazioneRep, allCustomerInfo.desNazioneRep) &&
        Objects.equals(desProvinciaRep, allCustomerInfo.desProvinciaRep) &&
        Objects.equals(indirizzoRep, allCustomerInfo.indirizzoRep) &&
        Objects.equals(email, allCustomerInfo.email) &&
        Objects.equals(emailRep, allCustomerInfo.emailRep) &&
        Objects.equals(emailCertificata, allCustomerInfo.emailCertificata) &&
        Objects.equals(condizioneProfessionale, allCustomerInfo.condizioneProfessionale) &&
        Objects.equals(statoCivile, allCustomerInfo.statoCivile) &&
        Objects.equals(entePubblico, allCustomerInfo.entePubblico) &&
        Objects.equals(flCommerciale, allCustomerInfo.flCommerciale) &&
        Objects.equals(societaQuotata, allCustomerInfo.societaQuotata) &&
        Objects.equals(tipoSocieta, allCustomerInfo.tipoSocieta) &&
        Objects.equals(descTipoSocieta, allCustomerInfo.descTipoSocieta) &&
        Objects.equals(dataPrimaIscrizione, allCustomerInfo.dataPrimaIscrizione) &&
        Objects.equals(greenCard, allCustomerInfo.greenCard) &&
        Objects.equals(flForzaturaUSStatus, allCustomerInfo.flForzaturaUSStatus) &&
        Objects.equals(motivoForzaturaUsStatus, allCustomerInfo.motivoForzaturaUsStatus) &&
        Objects.equals(flTitolareEffettivo, allCustomerInfo.flTitolareEffettivo) &&
        Objects.equals(provAttivitaPrevalente, allCustomerInfo.provAttivitaPrevalente) &&
        Objects.equals(descProvAttivitaPrevalente, allCustomerInfo.descProvAttivitaPrevalente) &&
        Objects.equals(luogoAttivitaPrevalente, allCustomerInfo.luogoAttivitaPrevalente) &&
        Objects.equals(capAttivitaPrevalente, allCustomerInfo.capAttivitaPrevalente) &&
        Objects.equals(statoCliente, allCustomerInfo.statoCliente) &&
        Objects.equals(tipoCliente, allCustomerInfo.tipoCliente) &&
        Objects.equals(ultProgStorico, allCustomerInfo.ultProgStorico) &&
        Objects.equals(flagOrigineFondi, allCustomerInfo.flagOrigineFondi) &&
        Objects.equals(sedeAttivitaPrevalente, allCustomerInfo.sedeAttivitaPrevalente) &&
        Objects.equals(attivitaPrevalente, allCustomerInfo.attivitaPrevalente) &&
        Objects.equals(descAttivitaPrevalente, allCustomerInfo.descAttivitaPrevalente) &&
        Objects.equals(codRegione, allCustomerInfo.codRegione) &&
        Objects.equals(descRegione, allCustomerInfo.descRegione) &&
        Objects.equals(flEntitaNonFinanziaria, allCustomerInfo.flEntitaNonFinanziaria) &&
        Objects.equals(flagConsComunicazioniElettroniche, allCustomerInfo.flagConsComunicazioniElettroniche) &&
        Objects.equals(dataConsComunicazioniElettroniche, allCustomerInfo.dataConsComunicazioniElettroniche) &&
        Objects.equals(dataRevocaConsComunicazioniElettroniche, allCustomerInfo.dataRevocaConsComunicazioniElettroniche) &&
        Objects.equals(dataUpdMailComunicazioniElettroniche, allCustomerInfo.dataUpdMailComunicazioniElettroniche) &&
        Objects.equals(flBeneficiariDisabili, allCustomerInfo.flBeneficiariDisabili) &&
        Objects.equals(dataAttivazBeneficiariDisabili, allCustomerInfo.dataAttivazBeneficiariDisabili) &&
        Objects.equals(ateco, allCustomerInfo.ateco) &&
        Objects.equals(dataUltimaAdeguataVerifica, allCustomerInfo.dataUltimaAdeguataVerifica) &&
        Objects.equals(fondoPensione, allCustomerInfo.fondoPensione) &&
        Objects.equals(fiduciaria, allCustomerInfo.fiduciaria) &&
        Objects.equals(intermediarioFinanziario, allCustomerInfo.intermediarioFinanziario) &&
        Objects.equals(flPaesiBlackListAR, allCustomerInfo.flPaesiBlackListAR) &&
        Objects.equals(flgDomicilio, allCustomerInfo.flgDomicilio) &&
        Objects.equals(statoDomicilio, allCustomerInfo.statoDomicilio) &&
        Objects.equals(provDomicilio, allCustomerInfo.provDomicilio) &&
        Objects.equals(luogoDomicilio, allCustomerInfo.luogoDomicilio) &&
        Objects.equals(toponimoDomicilio, allCustomerInfo.toponimoDomicilio) &&
        Objects.equals(descrizioneDomicilio, allCustomerInfo.descrizioneDomicilio) &&
        Objects.equals(numCivicoDomicilio, allCustomerInfo.numCivicoDomicilio) &&
        Objects.equals(capDomicilio, allCustomerInfo.capDomicilio) &&
        Objects.equals(flgCorrispondenza, allCustomerInfo.flgCorrispondenza) &&
        Objects.equals(flgCorrispondenzaIntestataSoggetto, allCustomerInfo.flgCorrispondenzaIntestataSoggetto) &&
        Objects.equals(autorita, allCustomerInfo.autorita) &&
        Objects.equals(albo, allCustomerInfo.albo) &&
        Objects.equals(numeroIscrizioneAlbo, allCustomerInfo.numeroIscrizioneAlbo) &&
        Objects.equals(registroIscrizione, allCustomerInfo.registroIscrizione) &&
        Objects.equals(numeroIscrizioneRegistro, allCustomerInfo.numeroIscrizioneRegistro) &&
        Objects.equals(dataIscrizione, allCustomerInfo.dataIscrizione) &&
        Objects.equals(societaAzionariatoFiduciario, allCustomerInfo.societaAzionariatoFiduciario) &&
        Objects.equals(classeBeneficiari, allCustomerInfo.classeBeneficiari) &&
        Objects.equals(livelloIstruzione, allCustomerInfo.livelloIstruzione) &&
        Objects.equals(attivitaPrevalenteAltro, allCustomerInfo.attivitaPrevalenteAltro) &&
        Objects.equals(attivitaPrecedentePensionamento, allCustomerInfo.attivitaPrecedentePensionamento) &&
        Objects.equals(descAttivitaPrecedentePensionamento, allCustomerInfo.descAttivitaPrecedentePensionamento) &&
        Objects.equals(attivitaPrecedentePensionamentoAltro, allCustomerInfo.attivitaPrecedentePensionamentoAltro) &&
        Objects.equals(statoAttivitaPrevalente, allCustomerInfo.statoAttivitaPrevalente) &&
        Objects.equals(ambitoAttivita, allCustomerInfo.ambitoAttivita) &&
        Objects.equals(descAmbitoAttivita, allCustomerInfo.descAmbitoAttivita) &&
        Objects.equals(altraCaricaPubblicaNonPep, allCustomerInfo.altraCaricaPubblicaNonPep) &&
        Objects.equals(flgRelazPaesiAltoRischio, allCustomerInfo.flgRelazPaesiAltoRischio) &&
        Objects.equals(dataRif, allCustomerInfo.dataRif) &&
        Objects.equals(customerOtherInfo, allCustomerInfo.customerOtherInfo) &&
        Objects.equals(desCittadinanza2, allCustomerInfo.desCittadinanza2) &&
        Objects.equals(desCittadinanza3, allCustomerInfo.desCittadinanza3) &&
        Objects.equals(desStatoDomicilio, allCustomerInfo.desStatoDomicilio) &&
        Objects.equals(desStatoAttivitaPrevalente, allCustomerInfo.desStatoAttivitaPrevalente) &&
        Objects.equals(desNazioneRilascio, allCustomerInfo.desNazioneRilascio) &&
        Objects.equals(tipoPensionamento, allCustomerInfo.tipoPensionamento) &&
        Objects.equals(dataVerificaRafforzata, allCustomerInfo.dataVerificaRafforzata) &&
        Objects.equals(dataVerificaRafforzataPrecedente, allCustomerInfo.dataVerificaRafforzataPrecedente) &&
        Objects.equals(profiloRischio, allCustomerInfo.profiloRischio) &&
        Objects.equals(paeseBlackList1, allCustomerInfo.paeseBlackList1) &&
        Objects.equals(paeseBlackList2, allCustomerInfo.paeseBlackList2) &&
        Objects.equals(paeseBlackList3, allCustomerInfo.paeseBlackList3) &&
        Objects.equals(motivoRelazione, allCustomerInfo.motivoRelazione) &&
        Objects.equals(flgFiduciarioProprioTerzi, allCustomerInfo.flgFiduciarioProprioTerzi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nazioneRilascio, provRilascio, localRilascio, pressoRep, statoRep, proviRep, localRep, capRep, desUrbanaIndRep, desStradaIndRep, numCivicoRep, telefRep, telefono, attivitaProfessio, gruppo, sottoGruppo, tipoDocumentoAR, descTipoDocumento, numDocumentoAR, enteRilascio, dataRilascio, cittadinanza, desCittadinanza, titoloStudio, flagTasseEstero, nazTasseEstero, desNazTasseEstero, tin, usStatus, cab, marketing, ricercheMercato, comunicazioniSoggettiTerzi, easyClientCode, easyClientCodePrincipale, numCellulare, cittadinanza2, cittadinanza3, dataScadenzaAR, desSottogruppoAR, desGruppoAR, codEnteRilascioAR, desNazioneRilascioAR, desProvinciaRilascioAR, flLuogoRilascioEsteroAR, flDocumentiAllegatiAR, desDocumentiAllegatiAR, desNazioneRep, desProvinciaRep, indirizzoRep, email, emailRep, emailCertificata, condizioneProfessionale, statoCivile, entePubblico, flCommerciale, societaQuotata, tipoSocieta, descTipoSocieta, dataPrimaIscrizione, greenCard, flForzaturaUSStatus, motivoForzaturaUsStatus, flTitolareEffettivo, provAttivitaPrevalente, descProvAttivitaPrevalente, luogoAttivitaPrevalente, capAttivitaPrevalente, statoCliente, tipoCliente, ultProgStorico, flagOrigineFondi, sedeAttivitaPrevalente, attivitaPrevalente, descAttivitaPrevalente, codRegione, descRegione, flEntitaNonFinanziaria, flagConsComunicazioniElettroniche, dataConsComunicazioniElettroniche, dataRevocaConsComunicazioniElettroniche, dataUpdMailComunicazioniElettroniche, flBeneficiariDisabili, dataAttivazBeneficiariDisabili, ateco, dataUltimaAdeguataVerifica, fondoPensione, fiduciaria, intermediarioFinanziario, flPaesiBlackListAR, flgDomicilio, statoDomicilio, provDomicilio, luogoDomicilio, toponimoDomicilio, descrizioneDomicilio, numCivicoDomicilio, capDomicilio, flgCorrispondenza, flgCorrispondenzaIntestataSoggetto, autorita, albo, numeroIscrizioneAlbo, registroIscrizione, numeroIscrizioneRegistro, dataIscrizione, societaAzionariatoFiduciario, classeBeneficiari, livelloIstruzione, attivitaPrevalenteAltro, attivitaPrecedentePensionamento, descAttivitaPrecedentePensionamento, attivitaPrecedentePensionamentoAltro, statoAttivitaPrevalente, ambitoAttivita, descAmbitoAttivita, altraCaricaPubblicaNonPep, flgRelazPaesiAltoRischio, dataRif, customerOtherInfo, desCittadinanza2, desCittadinanza3, desStatoDomicilio, desStatoAttivitaPrevalente, desNazioneRilascio, tipoPensionamento, dataVerificaRafforzata, dataVerificaRafforzataPrecedente, profiloRischio, paeseBlackList1, paeseBlackList2, paeseBlackList3, motivoRelazione, flgFiduciarioProprioTerzi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AllCustomerInfo {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    nazioneRilascio: ").append(toIndentedString(nazioneRilascio)).append("\n");
    sb.append("    provRilascio: ").append(toIndentedString(provRilascio)).append("\n");
    sb.append("    localRilascio: ").append(toIndentedString(localRilascio)).append("\n");
    sb.append("    pressoRep: ").append(toIndentedString(pressoRep)).append("\n");
    sb.append("    statoRep: ").append(toIndentedString(statoRep)).append("\n");
    sb.append("    proviRep: ").append(toIndentedString(proviRep)).append("\n");
    sb.append("    localRep: ").append(toIndentedString(localRep)).append("\n");
    sb.append("    capRep: ").append(toIndentedString(capRep)).append("\n");
    sb.append("    desUrbanaIndRep: ").append(toIndentedString(desUrbanaIndRep)).append("\n");
    sb.append("    desStradaIndRep: ").append(toIndentedString(desStradaIndRep)).append("\n");
    sb.append("    numCivicoRep: ").append(toIndentedString(numCivicoRep)).append("\n");
    sb.append("    telefRep: ").append(toIndentedString(telefRep)).append("\n");
    sb.append("    telefono: ").append(toIndentedString(telefono)).append("\n");
    sb.append("    attivitaProfessio: ").append(toIndentedString(attivitaProfessio)).append("\n");
    sb.append("    gruppo: ").append(toIndentedString(gruppo)).append("\n");
    sb.append("    sottoGruppo: ").append(toIndentedString(sottoGruppo)).append("\n");
    sb.append("    tipoDocumentoAR: ").append(toIndentedString(tipoDocumentoAR)).append("\n");
    sb.append("    descTipoDocumento: ").append(toIndentedString(descTipoDocumento)).append("\n");
    sb.append("    numDocumentoAR: ").append(toIndentedString(numDocumentoAR)).append("\n");
    sb.append("    enteRilascio: ").append(toIndentedString(enteRilascio)).append("\n");
    sb.append("    dataRilascio: ").append(toIndentedString(dataRilascio)).append("\n");
    sb.append("    cittadinanza: ").append(toIndentedString(cittadinanza)).append("\n");
    sb.append("    desCittadinanza: ").append(toIndentedString(desCittadinanza)).append("\n");
    sb.append("    titoloStudio: ").append(toIndentedString(titoloStudio)).append("\n");
    sb.append("    flagTasseEstero: ").append(toIndentedString(flagTasseEstero)).append("\n");
    sb.append("    nazTasseEstero: ").append(toIndentedString(nazTasseEstero)).append("\n");
    sb.append("    desNazTasseEstero: ").append(toIndentedString(desNazTasseEstero)).append("\n");
    sb.append("    tin: ").append(toIndentedString(tin)).append("\n");
    sb.append("    usStatus: ").append(toIndentedString(usStatus)).append("\n");
    sb.append("    cab: ").append(toIndentedString(cab)).append("\n");
    sb.append("    marketing: ").append(toIndentedString(marketing)).append("\n");
    sb.append("    ricercheMercato: ").append(toIndentedString(ricercheMercato)).append("\n");
    sb.append("    comunicazioniSoggettiTerzi: ").append(toIndentedString(comunicazioniSoggettiTerzi)).append("\n");
    sb.append("    easyClientCode: ").append(toIndentedString(easyClientCode)).append("\n");
    sb.append("    easyClientCodePrincipale: ").append(toIndentedString(easyClientCodePrincipale)).append("\n");
    sb.append("    numCellulare: ").append(toIndentedString(numCellulare)).append("\n");
    sb.append("    cittadinanza2: ").append(toIndentedString(cittadinanza2)).append("\n");
    sb.append("    cittadinanza3: ").append(toIndentedString(cittadinanza3)).append("\n");
    sb.append("    dataScadenzaAR: ").append(toIndentedString(dataScadenzaAR)).append("\n");
    sb.append("    desSottogruppoAR: ").append(toIndentedString(desSottogruppoAR)).append("\n");
    sb.append("    desGruppoAR: ").append(toIndentedString(desGruppoAR)).append("\n");
    sb.append("    codEnteRilascioAR: ").append(toIndentedString(codEnteRilascioAR)).append("\n");
    sb.append("    desNazioneRilascioAR: ").append(toIndentedString(desNazioneRilascioAR)).append("\n");
    sb.append("    desProvinciaRilascioAR: ").append(toIndentedString(desProvinciaRilascioAR)).append("\n");
    sb.append("    flLuogoRilascioEsteroAR: ").append(toIndentedString(flLuogoRilascioEsteroAR)).append("\n");
    sb.append("    flDocumentiAllegatiAR: ").append(toIndentedString(flDocumentiAllegatiAR)).append("\n");
    sb.append("    desDocumentiAllegatiAR: ").append(toIndentedString(desDocumentiAllegatiAR)).append("\n");
    sb.append("    desNazioneRep: ").append(toIndentedString(desNazioneRep)).append("\n");
    sb.append("    desProvinciaRep: ").append(toIndentedString(desProvinciaRep)).append("\n");
    sb.append("    indirizzoRep: ").append(toIndentedString(indirizzoRep)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    emailRep: ").append(toIndentedString(emailRep)).append("\n");
    sb.append("    emailCertificata: ").append(toIndentedString(emailCertificata)).append("\n");
    sb.append("    condizioneProfessionale: ").append(toIndentedString(condizioneProfessionale)).append("\n");
    sb.append("    statoCivile: ").append(toIndentedString(statoCivile)).append("\n");
    sb.append("    entePubblico: ").append(toIndentedString(entePubblico)).append("\n");
    sb.append("    flCommerciale: ").append(toIndentedString(flCommerciale)).append("\n");
    sb.append("    societaQuotata: ").append(toIndentedString(societaQuotata)).append("\n");
    sb.append("    tipoSocieta: ").append(toIndentedString(tipoSocieta)).append("\n");
    sb.append("    descTipoSocieta: ").append(toIndentedString(descTipoSocieta)).append("\n");
    sb.append("    dataPrimaIscrizione: ").append(toIndentedString(dataPrimaIscrizione)).append("\n");
    sb.append("    greenCard: ").append(toIndentedString(greenCard)).append("\n");
    sb.append("    flForzaturaUSStatus: ").append(toIndentedString(flForzaturaUSStatus)).append("\n");
    sb.append("    motivoForzaturaUsStatus: ").append(toIndentedString(motivoForzaturaUsStatus)).append("\n");
    sb.append("    flTitolareEffettivo: ").append(toIndentedString(flTitolareEffettivo)).append("\n");
    sb.append("    provAttivitaPrevalente: ").append(toIndentedString(provAttivitaPrevalente)).append("\n");
    sb.append("    descProvAttivitaPrevalente: ").append(toIndentedString(descProvAttivitaPrevalente)).append("\n");
    sb.append("    luogoAttivitaPrevalente: ").append(toIndentedString(luogoAttivitaPrevalente)).append("\n");
    sb.append("    capAttivitaPrevalente: ").append(toIndentedString(capAttivitaPrevalente)).append("\n");
    sb.append("    statoCliente: ").append(toIndentedString(statoCliente)).append("\n");
    sb.append("    tipoCliente: ").append(toIndentedString(tipoCliente)).append("\n");
    sb.append("    ultProgStorico: ").append(toIndentedString(ultProgStorico)).append("\n");
    sb.append("    flagOrigineFondi: ").append(toIndentedString(flagOrigineFondi)).append("\n");
    sb.append("    sedeAttivitaPrevalente: ").append(toIndentedString(sedeAttivitaPrevalente)).append("\n");
    sb.append("    attivitaPrevalente: ").append(toIndentedString(attivitaPrevalente)).append("\n");
    sb.append("    descAttivitaPrevalente: ").append(toIndentedString(descAttivitaPrevalente)).append("\n");
    sb.append("    codRegione: ").append(toIndentedString(codRegione)).append("\n");
    sb.append("    descRegione: ").append(toIndentedString(descRegione)).append("\n");
    sb.append("    flEntitaNonFinanziaria: ").append(toIndentedString(flEntitaNonFinanziaria)).append("\n");
    sb.append("    flagConsComunicazioniElettroniche: ").append(toIndentedString(flagConsComunicazioniElettroniche)).append("\n");
    sb.append("    dataConsComunicazioniElettroniche: ").append(toIndentedString(dataConsComunicazioniElettroniche)).append("\n");
    sb.append("    dataRevocaConsComunicazioniElettroniche: ").append(toIndentedString(dataRevocaConsComunicazioniElettroniche)).append("\n");
    sb.append("    dataUpdMailComunicazioniElettroniche: ").append(toIndentedString(dataUpdMailComunicazioniElettroniche)).append("\n");
    sb.append("    flBeneficiariDisabili: ").append(toIndentedString(flBeneficiariDisabili)).append("\n");
    sb.append("    dataAttivazBeneficiariDisabili: ").append(toIndentedString(dataAttivazBeneficiariDisabili)).append("\n");
    sb.append("    ateco: ").append(toIndentedString(ateco)).append("\n");
    sb.append("    dataUltimaAdeguataVerifica: ").append(toIndentedString(dataUltimaAdeguataVerifica)).append("\n");
    sb.append("    fondoPensione: ").append(toIndentedString(fondoPensione)).append("\n");
    sb.append("    fiduciaria: ").append(toIndentedString(fiduciaria)).append("\n");
    sb.append("    intermediarioFinanziario: ").append(toIndentedString(intermediarioFinanziario)).append("\n");
    sb.append("    flPaesiBlackListAR: ").append(toIndentedString(flPaesiBlackListAR)).append("\n");
    sb.append("    flgDomicilio: ").append(toIndentedString(flgDomicilio)).append("\n");
    sb.append("    statoDomicilio: ").append(toIndentedString(statoDomicilio)).append("\n");
    sb.append("    provDomicilio: ").append(toIndentedString(provDomicilio)).append("\n");
    sb.append("    luogoDomicilio: ").append(toIndentedString(luogoDomicilio)).append("\n");
    sb.append("    toponimoDomicilio: ").append(toIndentedString(toponimoDomicilio)).append("\n");
    sb.append("    descrizioneDomicilio: ").append(toIndentedString(descrizioneDomicilio)).append("\n");
    sb.append("    numCivicoDomicilio: ").append(toIndentedString(numCivicoDomicilio)).append("\n");
    sb.append("    capDomicilio: ").append(toIndentedString(capDomicilio)).append("\n");
    sb.append("    flgCorrispondenza: ").append(toIndentedString(flgCorrispondenza)).append("\n");
    sb.append("    flgCorrispondenzaIntestataSoggetto: ").append(toIndentedString(flgCorrispondenzaIntestataSoggetto)).append("\n");
    sb.append("    autorita: ").append(toIndentedString(autorita)).append("\n");
    sb.append("    albo: ").append(toIndentedString(albo)).append("\n");
    sb.append("    numeroIscrizioneAlbo: ").append(toIndentedString(numeroIscrizioneAlbo)).append("\n");
    sb.append("    registroIscrizione: ").append(toIndentedString(registroIscrizione)).append("\n");
    sb.append("    numeroIscrizioneRegistro: ").append(toIndentedString(numeroIscrizioneRegistro)).append("\n");
    sb.append("    dataIscrizione: ").append(toIndentedString(dataIscrizione)).append("\n");
    sb.append("    societaAzionariatoFiduciario: ").append(toIndentedString(societaAzionariatoFiduciario)).append("\n");
    sb.append("    classeBeneficiari: ").append(toIndentedString(classeBeneficiari)).append("\n");
    sb.append("    livelloIstruzione: ").append(toIndentedString(livelloIstruzione)).append("\n");
    sb.append("    attivitaPrevalenteAltro: ").append(toIndentedString(attivitaPrevalenteAltro)).append("\n");
    sb.append("    attivitaPrecedentePensionamento: ").append(toIndentedString(attivitaPrecedentePensionamento)).append("\n");
    sb.append("    descAttivitaPrecedentePensionamento: ").append(toIndentedString(descAttivitaPrecedentePensionamento)).append("\n");
    sb.append("    attivitaPrecedentePensionamentoAltro: ").append(toIndentedString(attivitaPrecedentePensionamentoAltro)).append("\n");
    sb.append("    statoAttivitaPrevalente: ").append(toIndentedString(statoAttivitaPrevalente)).append("\n");
    sb.append("    ambitoAttivita: ").append(toIndentedString(ambitoAttivita)).append("\n");
    sb.append("    descAmbitoAttivita: ").append(toIndentedString(descAmbitoAttivita)).append("\n");
    sb.append("    altraCaricaPubblicaNonPep: ").append(toIndentedString(altraCaricaPubblicaNonPep)).append("\n");
    sb.append("    flgRelazPaesiAltoRischio: ").append(toIndentedString(flgRelazPaesiAltoRischio)).append("\n");
    sb.append("    dataRif: ").append(toIndentedString(dataRif)).append("\n");
    sb.append("    customerOtherInfo: ").append(toIndentedString(customerOtherInfo)).append("\n");
    sb.append("    desCittadinanza2: ").append(toIndentedString(desCittadinanza2)).append("\n");
    sb.append("    desCittadinanza3: ").append(toIndentedString(desCittadinanza3)).append("\n");
    sb.append("    desStatoDomicilio: ").append(toIndentedString(desStatoDomicilio)).append("\n");
    sb.append("    desStatoAttivitaPrevalente: ").append(toIndentedString(desStatoAttivitaPrevalente)).append("\n");
    sb.append("    desNazioneRilascio: ").append(toIndentedString(desNazioneRilascio)).append("\n");
    sb.append("    tipoPensionamento: ").append(toIndentedString(tipoPensionamento)).append("\n");
    sb.append("    dataVerificaRafforzata: ").append(toIndentedString(dataVerificaRafforzata)).append("\n");
    sb.append("    dataVerificaRafforzataPrecedente: ").append(toIndentedString(dataVerificaRafforzataPrecedente)).append("\n");
    sb.append("    profiloRischio: ").append(toIndentedString(profiloRischio)).append("\n");
    sb.append("    paeseBlackList1: ").append(toIndentedString(paeseBlackList1)).append("\n");
    sb.append("    paeseBlackList2: ").append(toIndentedString(paeseBlackList2)).append("\n");
    sb.append("    paeseBlackList3: ").append(toIndentedString(paeseBlackList3)).append("\n");
    sb.append("    motivoRelazione: ").append(toIndentedString(motivoRelazione)).append("\n");
    sb.append("    flgFiduciarioProprioTerzi: ").append(toIndentedString(flgFiduciarioProprioTerzi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
