package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class StoricoOpzioniContrattualiResponse   {

  private @Valid List<OpzioneContrattuale> storicoOpzioniContrattualiList = new ArrayList<OpzioneContrattuale>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public StoricoOpzioniContrattualiResponse storicoOpzioniContrattualiList(List<OpzioneContrattuale> storicoOpzioniContrattualiList) {
    this.storicoOpzioniContrattualiList = storicoOpzioniContrattualiList;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("storicoOpzioniContrattualiList")

  public List<OpzioneContrattuale> getStoricoOpzioniContrattualiList() {
    return storicoOpzioniContrattualiList;
  }
  public void setStoricoOpzioniContrattualiList(List<OpzioneContrattuale> storicoOpzioniContrattualiList) {
    this.storicoOpzioniContrattualiList = storicoOpzioniContrattualiList;
  }

  /**
   **/
  public StoricoOpzioniContrattualiResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StoricoOpzioniContrattualiResponse storicoOpzioniContrattualiResponse = (StoricoOpzioniContrattualiResponse) o;
    return Objects.equals(storicoOpzioniContrattualiList, storicoOpzioniContrattualiResponse.storicoOpzioniContrattualiList) &&
        Objects.equals(errori, storicoOpzioniContrattualiResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(storicoOpzioniContrattualiList, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StoricoOpzioniContrattualiResponse {\n");
    
    sb.append("    storicoOpzioniContrattualiList: ").append(toIndentedString(storicoOpzioniContrattualiList)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
