package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiPepRequest   {

  private @Valid Long codiceCliente = null;

  private @Valid DatiPep datiPep = null;

  private @Valid String username = null;

  /**
   * Codice Cliente NAW.
   **/
  public DatiPepRequest codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")
  @NotNull

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public DatiPepRequest datiPep(DatiPep datiPep) {
    this.datiPep = datiPep;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("datiPep")
  @NotNull

  public DatiPep getDatiPep() {
    return datiPep;
  }
  public void setDatiPep(DatiPep datiPep) {
    this.datiPep = datiPep;
  }

  /**
   **/
  public DatiPepRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiPepRequest datiPepRequest = (DatiPepRequest) o;
    return Objects.equals(codiceCliente, datiPepRequest.codiceCliente) &&
        Objects.equals(datiPep, datiPepRequest.datiPep) &&
        Objects.equals(username, datiPepRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, datiPep, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiPepRequest {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    datiPep: ").append(toIndentedString(datiPep)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
