package it.sistinf.rest.cobol.model.customer.aml;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CheckListRequestInfo   {

  private @Valid List<CheckListDataInfo> checkListData = new ArrayList<CheckListDataInfo>();

  private @Valid List<PepCrimeCustomerInfo> customers = new ArrayList<PepCrimeCustomerInfo>();

  private @Valid String flgAlarm = null;

  private @Valid String struttura = null;

  /**
   **/
  public CheckListRequestInfo checkListData(List<CheckListDataInfo> checkListData) {
    this.checkListData = checkListData;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("checkListData")
  @NotNull

  public List<CheckListDataInfo> getCheckListData() {
    return checkListData;
  }
  public void setCheckListData(List<CheckListDataInfo> checkListData) {
    this.checkListData = checkListData;
  }

  /**
   **/
  public CheckListRequestInfo customers(List<PepCrimeCustomerInfo> customers) {
    this.customers = customers;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("customers")
  @NotNull

  public List<PepCrimeCustomerInfo> getCustomers() {
    return customers;
  }
  public void setCustomers(List<PepCrimeCustomerInfo> customers) {
    this.customers = customers;
  }

  /**
   * Valorizzare con S
   **/
  public CheckListRequestInfo flgAlarm(String flgAlarm) {
    this.flgAlarm = flgAlarm;
    return this;
  }

  
  @ApiModelProperty(example = "S", required = true, value = "Valorizzare con S")
  @JsonProperty("flgAlarm")
  @NotNull

  public String getFlgAlarm() {
    return flgAlarm;
  }
  public void setFlgAlarm(String flgAlarm) {
    this.flgAlarm = flgAlarm;
  }

  /**
   * Livello 1 della rete di vendita
   **/
  public CheckListRequestInfo struttura(String struttura) {
    this.struttura = struttura;
    return this;
  }

  
  @ApiModelProperty(example = "0123", required = true, value = "Livello 1 della rete di vendita")
  @JsonProperty("struttura")
  @NotNull

  public String getStruttura() {
    return struttura;
  }
  public void setStruttura(String struttura) {
    this.struttura = struttura;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckListRequestInfo checkListRequestInfo = (CheckListRequestInfo) o;
    return Objects.equals(checkListData, checkListRequestInfo.checkListData) &&
        Objects.equals(customers, checkListRequestInfo.customers) &&
        Objects.equals(flgAlarm, checkListRequestInfo.flgAlarm) &&
        Objects.equals(struttura, checkListRequestInfo.struttura);
  }

  @Override
  public int hashCode() {
    return Objects.hash(checkListData, customers, flgAlarm, struttura);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckListRequestInfo {\n");
    
    sb.append("    checkListData: ").append(toIndentedString(checkListData)).append("\n");
    sb.append("    customers: ").append(toIndentedString(customers)).append("\n");
    sb.append("    flgAlarm: ").append(toIndentedString(flgAlarm)).append("\n");
    sb.append("    struttura: ").append(toIndentedString(struttura)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
