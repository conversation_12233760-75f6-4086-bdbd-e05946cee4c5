package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class VerificaRafforzataRequest   {

  private @Valid Long codiceCliente = null;

  private @Valid Date dataVerifica = null;

  private @Valid String user = null;

  /**
   * Codice Cliente NAW.
   **/
  public VerificaRafforzataRequest codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "Codice Cliente NAW.")
  @JsonProperty("codiceCliente")
  @NotNull

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public VerificaRafforzataRequest dataVerifica(Date dataVerifica) {
    this.dataVerifica = dataVerifica;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("dataVerifica")
  @NotNull

  public Date getDataVerifica() {
    return dataVerifica;
  }
  public void setDataVerifica(Date dataVerifica) {
    this.dataVerifica = dataVerifica;
  }

  /**
   **/
  public VerificaRafforzataRequest user(String user) {
    this.user = user;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("user")
  @NotNull
 @Size(min=1,max=8)
  public String getUser() {
    return user;
  }
  public void setUser(String user) {
    this.user = user;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VerificaRafforzataRequest verificaRafforzataRequest = (VerificaRafforzataRequest) o;
    return Objects.equals(codiceCliente, verificaRafforzataRequest.codiceCliente) &&
        Objects.equals(dataVerifica, verificaRafforzataRequest.dataVerifica) &&
        Objects.equals(user, verificaRafforzataRequest.user);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, dataVerifica, user);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VerificaRafforzataRequest {\n");
    
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    dataVerifica: ").append(toIndentedString(dataVerifica)).append("\n");
    sb.append("    user: ").append(toIndentedString(user)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
