package it.sistinf.rest.cobol.model.compagnia;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CompagniaRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid Integer compagnia = null;

  /**
   **/
  public CompagniaRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public CompagniaRequest compagnia(Integer compagnia) {
    this.compagnia = compagnia;
    return this;
  }

  
  @ApiModelProperty(example = "143", required = true, value = "")
  @JsonProperty("compagnia")
  @NotNull

  public Integer getCompagnia() {
    return compagnia;
  }
  public void setCompagnia(Integer compagnia) {
    this.compagnia = compagnia;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CompagniaRequest compagniaRequest = (CompagniaRequest) o;
    return Objects.equals(headerCobolSrv, compagniaRequest.headerCobolSrv) &&
        Objects.equals(compagnia, compagniaRequest.compagnia);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, compagnia);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CompagniaRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    compagnia: ").append(toIndentedString(compagnia)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
