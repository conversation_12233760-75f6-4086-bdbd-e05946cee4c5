package it.sistinf.rest.cobol.model.api;

import java.util.Objects;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ModelApiResponse   {

  private @Valid Integer code = null;

  private @Valid String type = null;

  private @Valid String message = null;

  /**
   **/
  public ModelApiResponse code(Integer code) {
    this.code = code;
    return this;
  }
  
  @ApiModelProperty(value = "")
  @JsonProperty("code")

  public Integer getCode() {
    return code;
  }
  public void setCode(Integer code) {
    this.code = code;
  }

  /**
   **/
  public ModelApiResponse type(String type) {
    this.type = type;
    return this;
  }
  
  @ApiModelProperty(value = "")
  @JsonProperty("type")

  public String getType() {
    return type;
  }
  public void setType(String type) {
    this.type = type;
  }

  /**
   **/
  public ModelApiResponse message(String message) {
    this.message = message;
    return this;
  }
  
  @ApiModelProperty(value = "")
  @JsonProperty("message")

  public String getMessage() {
    return message;
  }
  public void setMessage(String message) {
    this.message = message;
  }

  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModelApiResponse modelApiResponse = (ModelApiResponse) o;
    return Objects.equals(code, modelApiResponse.code) &&
        Objects.equals(type, modelApiResponse.type) &&
        Objects.equals(message, modelApiResponse.message);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, type, message);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModelApiResponse {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
