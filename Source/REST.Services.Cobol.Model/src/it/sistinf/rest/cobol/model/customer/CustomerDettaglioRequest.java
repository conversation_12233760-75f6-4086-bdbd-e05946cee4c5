package it.sistinf.rest.cobol.model.customer;
import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.user.ReteVendita;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class CustomerDettaglioRequest   {

  private @Valid String username = null;

  private @Valid Integer codSoc = null;

  private @Valid ReteVendita reteVendita = null;

  private @Valid CustomerRicercaDataBase customerRicerca = null;

  /**
   **/
  public CustomerDettaglioRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }

  /**
   **/
  public CustomerDettaglioRequest codSoc(Integer codSoc) {
    this.codSoc = codSoc;
    return this;
  }

  
  @ApiModelProperty(example = "143", required = true, value = "")
  @JsonProperty("codSoc")
  @NotNull

  public Integer getCodSoc() {
    return codSoc;
  }
  public void setCodSoc(Integer codSoc) {
    this.codSoc = codSoc;
  }

  /**
   **/
  public CustomerDettaglioRequest reteVendita(ReteVendita reteVendita) {
    this.reteVendita = reteVendita;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("reteVendita")
  @NotNull

  public ReteVendita getReteVendita() {
    return reteVendita;
  }
  public void setReteVendita(ReteVendita reteVendita) {
    this.reteVendita = reteVendita;
  }

  /**
   **/
  public CustomerDettaglioRequest customerRicerca(CustomerRicercaDataBase customerRicerca) {
    this.customerRicerca = customerRicerca;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("customerRicerca")
  @NotNull

  public CustomerRicercaDataBase getCustomerRicerca() {
    return customerRicerca;
  }
  public void setCustomerRicerca(CustomerRicercaDataBase customerRicerca) {
    this.customerRicerca = customerRicerca;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerDettaglioRequest customerDettaglioRequest = (CustomerDettaglioRequest) o;
    return Objects.equals(username, customerDettaglioRequest.username) &&
        Objects.equals(codSoc, customerDettaglioRequest.codSoc) &&
        Objects.equals(reteVendita, customerDettaglioRequest.reteVendita) &&
        Objects.equals(customerRicerca, customerDettaglioRequest.customerRicerca);
  }

  @Override
  public int hashCode() {
    return Objects.hash(username, codSoc, reteVendita, customerRicerca);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerDettaglioRequest {\n");
    
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    codSoc: ").append(toIndentedString(codSoc)).append("\n");
    sb.append("    reteVendita: ").append(toIndentedString(reteVendita)).append("\n");
    sb.append("    customerRicerca: ").append(toIndentedString(customerRicerca)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
