package it.sistinf.rest.cobol.model.dominio;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class ElencoSAERequest   {

public enum TipoPersonaEnum {

    F(String.valueOf("F")), G(String.valueOf("G"));


    private String value;

    TipoPersonaEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static TipoPersonaEnum fromValue(String v) {
        for (TipoPersonaEnum b : TipoPersonaEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid TipoPersonaEnum tipoPersona = null;

  private @Valid String codiceAttPrev = null;

  private @Valid String statoAttPrev = null;

  /**
   **/
  public ElencoSAERequest tipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("tipoPersona")
  @NotNull

  public TipoPersonaEnum getTipoPersona() {
    return tipoPersona;
  }
  public void setTipoPersona(TipoPersonaEnum tipoPersona) {
    this.tipoPersona = tipoPersona;
  }

  /**
   **/
  public ElencoSAERequest codiceAttPrev(String codiceAttPrev) {
    this.codiceAttPrev = codiceAttPrev;
    return this;
  }

  
  @ApiModelProperty(example = "16", required = true, value = "")
  @JsonProperty("codiceAttPrev")
  @NotNull

  public String getCodiceAttPrev() {
    return codiceAttPrev;
  }
  public void setCodiceAttPrev(String codiceAttPrev) {
    this.codiceAttPrev = codiceAttPrev;
  }

  /**
   **/
  public ElencoSAERequest statoAttPrev(String statoAttPrev) {
    this.statoAttPrev = statoAttPrev;
    return this;
  }

  
  @ApiModelProperty(example = "086", required = true, value = "")
  @JsonProperty("statoAttPrev")
  @NotNull

  public String getStatoAttPrev() {
    return statoAttPrev;
  }
  public void setStatoAttPrev(String statoAttPrev) {
    this.statoAttPrev = statoAttPrev;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ElencoSAERequest elencoSAERequest = (ElencoSAERequest) o;
    return Objects.equals(tipoPersona, elencoSAERequest.tipoPersona) &&
        Objects.equals(codiceAttPrev, elencoSAERequest.codiceAttPrev) &&
        Objects.equals(statoAttPrev, elencoSAERequest.statoAttPrev);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tipoPersona, codiceAttPrev, statoAttPrev);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ElencoSAERequest {\n");
    
    sb.append("    tipoPersona: ").append(toIndentedString(tipoPersona)).append("\n");
    sb.append("    codiceAttPrev: ").append(toIndentedString(codiceAttPrev)).append("\n");
    sb.append("    statoAttPrev: ").append(toIndentedString(statoAttPrev)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
