package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.EsitoResponse;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class TitolariEffettiviInfo extends EsitoResponse  {

  private @Valid String codiceCustomerTitEff = null;

  private @Valid String nominativoTitEff = null;

  private @Valid String luogoDiNascitaTitEff = null;

  private @Valid Date dataDiNascitaTitEff = null;

  private @Valid String relazioneContraenteTitEff = null;

  /**
   **/
  public TitolariEffettiviInfo codiceCustomerTitEff(String codiceCustomerTitEff) {
    this.codiceCustomerTitEff = codiceCustomerTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceCustomerTitEff")
  @NotNull

  public String getCodiceCustomerTitEff() {
    return codiceCustomerTitEff;
  }
  public void setCodiceCustomerTitEff(String codiceCustomerTitEff) {
    this.codiceCustomerTitEff = codiceCustomerTitEff;
  }

  /**
   **/
  public TitolariEffettiviInfo nominativoTitEff(String nominativoTitEff) {
    this.nominativoTitEff = nominativoTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nominativoTitEff")
  @NotNull

  public String getNominativoTitEff() {
    return nominativoTitEff;
  }
  public void setNominativoTitEff(String nominativoTitEff) {
    this.nominativoTitEff = nominativoTitEff;
  }

  /**
   **/
  public TitolariEffettiviInfo luogoDiNascitaTitEff(String luogoDiNascitaTitEff) {
    this.luogoDiNascitaTitEff = luogoDiNascitaTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("luogoDiNascitaTitEff")
  @NotNull

  public String getLuogoDiNascitaTitEff() {
    return luogoDiNascitaTitEff;
  }
  public void setLuogoDiNascitaTitEff(String luogoDiNascitaTitEff) {
    this.luogoDiNascitaTitEff = luogoDiNascitaTitEff;
  }

  /**
   **/
  public TitolariEffettiviInfo dataDiNascitaTitEff(Date dataDiNascitaTitEff) {
    this.dataDiNascitaTitEff = dataDiNascitaTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataDiNascitaTitEff")
  @NotNull

  public Date getDataDiNascitaTitEff() {
    return dataDiNascitaTitEff;
  }
  public void setDataDiNascitaTitEff(Date dataDiNascitaTitEff) {
    this.dataDiNascitaTitEff = dataDiNascitaTitEff;
  }

  /**
   **/
  public TitolariEffettiviInfo relazioneContraenteTitEff(String relazioneContraenteTitEff) {
    this.relazioneContraenteTitEff = relazioneContraenteTitEff;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneContraenteTitEff")
  @NotNull

  public String getRelazioneContraenteTitEff() {
    return relazioneContraenteTitEff;
  }
  public void setRelazioneContraenteTitEff(String relazioneContraenteTitEff) {
    this.relazioneContraenteTitEff = relazioneContraenteTitEff;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TitolariEffettiviInfo titolariEffettiviInfo = (TitolariEffettiviInfo) o;
    return Objects.equals(codiceCustomerTitEff, titolariEffettiviInfo.codiceCustomerTitEff) &&
        Objects.equals(nominativoTitEff, titolariEffettiviInfo.nominativoTitEff) &&
        Objects.equals(luogoDiNascitaTitEff, titolariEffettiviInfo.luogoDiNascitaTitEff) &&
        Objects.equals(dataDiNascitaTitEff, titolariEffettiviInfo.dataDiNascitaTitEff) &&
        Objects.equals(relazioneContraenteTitEff, titolariEffettiviInfo.relazioneContraenteTitEff);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCustomerTitEff, nominativoTitEff, luogoDiNascitaTitEff, dataDiNascitaTitEff, relazioneContraenteTitEff);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TitolariEffettiviInfo {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    codiceCustomerTitEff: ").append(toIndentedString(codiceCustomerTitEff)).append("\n");
    sb.append("    nominativoTitEff: ").append(toIndentedString(nominativoTitEff)).append("\n");
    sb.append("    luogoDiNascitaTitEff: ").append(toIndentedString(luogoDiNascitaTitEff)).append("\n");
    sb.append("    dataDiNascitaTitEff: ").append(toIndentedString(dataDiNascitaTitEff)).append("\n");
    sb.append("    relazioneContraenteTitEff: ").append(toIndentedString(relazioneContraenteTitEff)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
