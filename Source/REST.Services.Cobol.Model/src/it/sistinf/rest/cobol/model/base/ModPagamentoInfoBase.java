package it.sistinf.rest.cobol.model.base;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ModPagamentoInfoBase   {

  private @Valid String modPagamento = null;

  private @Valid String codiceIbanPrimaRata = null;

  private @Valid String intestatarioPrimaRata = null;

  private @Valid Boolean contoCointestato = null;

  private @Valid String nomeCointestatario = null;

  private @Valid String relContrCointest = null;

  private @Valid String relContrCointestAltro = null;

  private @Valid String swift = null;

  private @Valid String paese = null;

  private @Valid String motivoContoEstero = null;

  private @Valid String numSottoRub = null;

  /**
   **/
  public ModPagamentoInfoBase modPagamento(String modPagamento) {
    this.modPagamento = modPagamento;
    return this;
  }

  
  @ApiModelProperty(example = "1", value = "")
  @JsonProperty("modPagamento")

  public String getModPagamento() {
    return modPagamento;
  }
  public void setModPagamento(String modPagamento) {
    this.modPagamento = modPagamento;
  }

  /**
   **/
  public ModPagamentoInfoBase codiceIbanPrimaRata(String codiceIbanPrimaRata) {
    this.codiceIbanPrimaRata = codiceIbanPrimaRata;
    return this;
  }

  
  @ApiModelProperty(example = "CROCE ROSSA INTERNAZIONALE : ***************************", value = "")
  @JsonProperty("codiceIbanPrimaRata")

  public String getCodiceIbanPrimaRata() {
    return codiceIbanPrimaRata;
  }
  public void setCodiceIbanPrimaRata(String codiceIbanPrimaRata) {
    this.codiceIbanPrimaRata = codiceIbanPrimaRata;
  }

  /**
   **/
  public ModPagamentoInfoBase intestatarioPrimaRata(String intestatarioPrimaRata) {
    this.intestatarioPrimaRata = intestatarioPrimaRata;
    return this;
  }

  
  @ApiModelProperty(example = "ROSSI ANTONIO", value = "")
  @JsonProperty("intestatarioPrimaRata")

  public String getIntestatarioPrimaRata() {
    return intestatarioPrimaRata;
  }
  public void setIntestatarioPrimaRata(String intestatarioPrimaRata) {
    this.intestatarioPrimaRata = intestatarioPrimaRata;
  }

  /**
   **/
  public ModPagamentoInfoBase contoCointestato(Boolean contoCointestato) {
    this.contoCointestato = contoCointestato;
    return this;
  }

  
  @ApiModelProperty(example = "false", value = "")
  @JsonProperty("contoCointestato")

  public Boolean isContoCointestato() {
    return contoCointestato;
  }
  public void setContoCointestato(Boolean contoCointestato) {
    this.contoCointestato = contoCointestato;
  }

  /**
   **/
  public ModPagamentoInfoBase nomeCointestatario(String nomeCointestatario) {
    this.nomeCointestatario = nomeCointestatario;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nomeCointestatario")

  public String getNomeCointestatario() {
    return nomeCointestatario;
  }
  public void setNomeCointestatario(String nomeCointestatario) {
    this.nomeCointestatario = nomeCointestatario;
  }

  /**
   **/
  public ModPagamentoInfoBase relContrCointest(String relContrCointest) {
    this.relContrCointest = relContrCointest;
    return this;
  }

  
  @ApiModelProperty(example = "01", value = "")
  @JsonProperty("relContrCointest")

  public String getRelContrCointest() {
    return relContrCointest;
  }
  public void setRelContrCointest(String relContrCointest) {
    this.relContrCointest = relContrCointest;
  }

  /**
   **/
  public ModPagamentoInfoBase relContrCointestAltro(String relContrCointestAltro) {
    this.relContrCointestAltro = relContrCointestAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relContrCointestAltro")

  public String getRelContrCointestAltro() {
    return relContrCointestAltro;
  }
  public void setRelContrCointestAltro(String relContrCointestAltro) {
    this.relContrCointestAltro = relContrCointestAltro;
  }

  /**
   **/
  public ModPagamentoInfoBase swift(String swift) {
    this.swift = swift;
    return this;
  }

  
  @ApiModelProperty(example = "UNCRITMMRME", value = "")
  @JsonProperty("swift")

  public String getSwift() {
    return swift;
  }
  public void setSwift(String swift) {
    this.swift = swift;
  }

  /**
   **/
  public ModPagamentoInfoBase paese(String paese) {
    this.paese = paese;
    return this;
  }

  
  @ApiModelProperty(example = "086", value = "")
  @JsonProperty("paese")

  public String getPaese() {
    return paese;
  }
  public void setPaese(String paese) {
    this.paese = paese;
  }

  /**
   **/
  public ModPagamentoInfoBase motivoContoEstero(String motivoContoEstero) {
    this.motivoContoEstero = motivoContoEstero;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("motivoContoEstero")

  public String getMotivoContoEstero() {
    return motivoContoEstero;
  }
  public void setMotivoContoEstero(String motivoContoEstero) {
    this.motivoContoEstero = motivoContoEstero;
  }

  /**
   **/
  public ModPagamentoInfoBase numSottoRub(String numSottoRub) {
    this.numSottoRub = numSottoRub;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numSottoRub")

  public String getNumSottoRub() {
    return numSottoRub;
  }
  public void setNumSottoRub(String numSottoRub) {
    this.numSottoRub = numSottoRub;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModPagamentoInfoBase modPagamentoInfoBase = (ModPagamentoInfoBase) o;
    return Objects.equals(modPagamento, modPagamentoInfoBase.modPagamento) &&
        Objects.equals(codiceIbanPrimaRata, modPagamentoInfoBase.codiceIbanPrimaRata) &&
        Objects.equals(intestatarioPrimaRata, modPagamentoInfoBase.intestatarioPrimaRata) &&
        Objects.equals(contoCointestato, modPagamentoInfoBase.contoCointestato) &&
        Objects.equals(nomeCointestatario, modPagamentoInfoBase.nomeCointestatario) &&
        Objects.equals(relContrCointest, modPagamentoInfoBase.relContrCointest) &&
        Objects.equals(relContrCointestAltro, modPagamentoInfoBase.relContrCointestAltro) &&
        Objects.equals(swift, modPagamentoInfoBase.swift) &&
        Objects.equals(paese, modPagamentoInfoBase.paese) &&
        Objects.equals(motivoContoEstero, modPagamentoInfoBase.motivoContoEstero) &&
        Objects.equals(numSottoRub, modPagamentoInfoBase.numSottoRub);
  }

  @Override
  public int hashCode() {
    return Objects.hash(modPagamento, codiceIbanPrimaRata, intestatarioPrimaRata, contoCointestato, nomeCointestatario, relContrCointest, relContrCointestAltro, swift, paese, motivoContoEstero, numSottoRub);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModPagamentoInfoBase {\n");
    
    sb.append("    modPagamento: ").append(toIndentedString(modPagamento)).append("\n");
    sb.append("    codiceIbanPrimaRata: ").append(toIndentedString(codiceIbanPrimaRata)).append("\n");
    sb.append("    intestatarioPrimaRata: ").append(toIndentedString(intestatarioPrimaRata)).append("\n");
    sb.append("    contoCointestato: ").append(toIndentedString(contoCointestato)).append("\n");
    sb.append("    nomeCointestatario: ").append(toIndentedString(nomeCointestatario)).append("\n");
    sb.append("    relContrCointest: ").append(toIndentedString(relContrCointest)).append("\n");
    sb.append("    relContrCointestAltro: ").append(toIndentedString(relContrCointestAltro)).append("\n");
    sb.append("    swift: ").append(toIndentedString(swift)).append("\n");
    sb.append("    paese: ").append(toIndentedString(paese)).append("\n");
    sb.append("    motivoContoEstero: ").append(toIndentedString(motivoContoEstero)).append("\n");
    sb.append("    numSottoRub: ").append(toIndentedString(numSottoRub)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
