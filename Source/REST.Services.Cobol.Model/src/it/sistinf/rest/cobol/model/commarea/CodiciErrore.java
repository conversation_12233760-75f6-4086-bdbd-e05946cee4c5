package it.sistinf.rest.cobol.model.commarea;

import javax.validation.constraints.Size;
import javax.validation.Valid;


import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;


public class CodiciErrore   {

  private @Valid String codiceErrore = "0000";

  /**
   **/
  public CodiciErrore codiceErrore(String codiceErrore) {
    this.codiceErrore = codiceErrore;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceErrore")
 @Size(min=4,max=4)
  public String getCodiceErrore() {
    return codiceErrore;
  }
  public void setCodiceErrore(String codiceErrore) {
    this.codiceErrore = codiceErrore;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CodiciErrore codiciErrore = (CodiciErrore) o;
    return Objects.equals(codiceErrore, codiciErrore.codiceErrore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceErrore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CodiciErrore {\n");
    
    sb.append("    codiceErrore: ").append(toIndentedString(codiceErrore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
