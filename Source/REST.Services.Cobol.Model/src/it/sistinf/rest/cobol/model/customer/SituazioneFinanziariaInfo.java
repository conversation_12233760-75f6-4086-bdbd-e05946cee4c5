package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class SituazioneFinanziariaInfo   {

  private @Valid String fonteReddito = null;

  private @Valid String fonteRedditoAltro = null;

  private @Valid String redditoAnnuo = null;

  private @Valid String fatturatoAnnuo = null;

  private @Valid String partFinanzIncLiq = null;

  /**
   **/
  public SituazioneFinanziariaInfo fonteReddito(String fonteReddito) {
    this.fonteReddito = fonteReddito;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("fonteReddito")

  public String getFonteReddito() {
    return fonteReddito;
  }
  public void setFonteReddito(String fonteReddito) {
    this.fonteReddito = fonteReddito;
  }

  /**
   **/
  public SituazioneFinanziariaInfo fonteRedditoAltro(String fonteRedditoAltro) {
    this.fonteRedditoAltro = fonteRedditoAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("fonteRedditoAltro")

  public String getFonteRedditoAltro() {
    return fonteRedditoAltro;
  }
  public void setFonteRedditoAltro(String fonteRedditoAltro) {
    this.fonteRedditoAltro = fonteRedditoAltro;
  }

  /**
   **/
  public SituazioneFinanziariaInfo redditoAnnuo(String redditoAnnuo) {
    this.redditoAnnuo = redditoAnnuo;
    return this;
  }

  
  @ApiModelProperty(example = "1", value = "")
  @JsonProperty("redditoAnnuo")

  public String getRedditoAnnuo() {
    return redditoAnnuo;
  }
  public void setRedditoAnnuo(String redditoAnnuo) {
    this.redditoAnnuo = redditoAnnuo;
  }

  /**
   **/
  public SituazioneFinanziariaInfo fatturatoAnnuo(String fatturatoAnnuo) {
    this.fatturatoAnnuo = fatturatoAnnuo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("fatturatoAnnuo")

  public String getFatturatoAnnuo() {
    return fatturatoAnnuo;
  }
  public void setFatturatoAnnuo(String fatturatoAnnuo) {
    this.fatturatoAnnuo = fatturatoAnnuo;
  }

  /**
   **/
  public SituazioneFinanziariaInfo partFinanzIncLiq(String partFinanzIncLiq) {
    this.partFinanzIncLiq = partFinanzIncLiq;
    return this;
  }

  
  @ApiModelProperty(example = "2", required = true, value = "")
  @JsonProperty("partFinanzIncLiq")
  @NotNull

  public String getPartFinanzIncLiq() {
    return partFinanzIncLiq;
  }
  public void setPartFinanzIncLiq(String partFinanzIncLiq) {
    this.partFinanzIncLiq = partFinanzIncLiq;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SituazioneFinanziariaInfo situazioneFinanziariaInfo = (SituazioneFinanziariaInfo) o;
    return Objects.equals(fonteReddito, situazioneFinanziariaInfo.fonteReddito) &&
        Objects.equals(fonteRedditoAltro, situazioneFinanziariaInfo.fonteRedditoAltro) &&
        Objects.equals(redditoAnnuo, situazioneFinanziariaInfo.redditoAnnuo) &&
        Objects.equals(fatturatoAnnuo, situazioneFinanziariaInfo.fatturatoAnnuo) &&
        Objects.equals(partFinanzIncLiq, situazioneFinanziariaInfo.partFinanzIncLiq);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fonteReddito, fonteRedditoAltro, redditoAnnuo, fatturatoAnnuo, partFinanzIncLiq);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SituazioneFinanziariaInfo {\n");
    
    sb.append("    fonteReddito: ").append(toIndentedString(fonteReddito)).append("\n");
    sb.append("    fonteRedditoAltro: ").append(toIndentedString(fonteRedditoAltro)).append("\n");
    sb.append("    redditoAnnuo: ").append(toIndentedString(redditoAnnuo)).append("\n");
    sb.append("    fatturatoAnnuo: ").append(toIndentedString(fatturatoAnnuo)).append("\n");
    sb.append("    partFinanzIncLiq: ").append(toIndentedString(partFinanzIncLiq)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
