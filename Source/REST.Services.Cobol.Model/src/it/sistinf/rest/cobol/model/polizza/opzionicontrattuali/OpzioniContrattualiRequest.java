package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class OpzioniContrattualiRequest   {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid String utente = null;

  /**
   **/
  public OpzioniContrattualiRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public OpzioniContrattualiRequest utente(String utente) {
    this.utente = utente;
    return this;
  }

  
  @ApiModelProperty(example = "C1234567", value = "")
  @JsonProperty("utente")

  public String getUtente() {
    return utente;
  }
  public void setUtente(String utente) {
    this.utente = utente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OpzioniContrattualiRequest opzioniContrattualiRequest = (OpzioniContrattualiRequest) o;
    return Objects.equals(polizzaInfo, opzioniContrattualiRequest.polizzaInfo) &&
        Objects.equals(utente, opzioniContrattualiRequest.utente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, utente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OpzioniContrattualiRequest {\n");
    
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    utente: ").append(toIndentedString(utente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}