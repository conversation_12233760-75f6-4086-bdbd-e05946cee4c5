package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class StoricoOpzioniContrattualiRequest   {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid String utente = null;

  /**
   **/
  public StoricoOpzioniContrattualiRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public StoricoOpzioniContrattualiRequest utente(String utente) {
    this.utente = utente;
    return this;
  }

  
  @ApiModelProperty(example = "C1234567", required = true, value = "")
  @JsonProperty("utente")
  @NotNull

  public String getUtente() {
    return utente;
  }
  public void setUtente(String utente) {
    this.utente = utente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StoricoOpzioniContrattualiRequest storicoOpzioniContrattualiRequest = (StoricoOpzioniContrattualiRequest) o;
    return Objects.equals(polizzaInfo, storicoOpzioniContrattualiRequest.polizzaInfo) &&
        Objects.equals(utente, storicoOpzioniContrattualiRequest.utente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, utente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StoricoOpzioniContrattualiRequest {\n");
    
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    utente: ").append(toIndentedString(utente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
