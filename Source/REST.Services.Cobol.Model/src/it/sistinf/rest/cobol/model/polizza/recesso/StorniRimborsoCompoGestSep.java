package it.sistinf.rest.cobol.model.polizza.recesso;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class StorniRimborsoCompoGestSep   {

  private @Valid String fondoRival = null;

  private @Valid String descrFondoRival = null;

  private @Valid Date dataEffetto = null;

  private @Valid BigDecimal prestazione = null;

  private @Valid BigDecimal rendimento = null;

  private @Valid BigDecimal controvalore = null;

  private @Valid List<Integer> elencoNumPosiz = new ArrayList<Integer>();

  private @Valid BigDecimal frazioneAnno = null;

  private @Valid BigDecimal rendimentoLordo = null;

  private @Valid BigDecimal rendimentoTrattenuto = null;

  private @Valid BigDecimal rendimentoNetto = null;

  private @Valid BigDecimal tassoUtilizzato = null;

  private @Valid BigDecimal prestazioneLorda = null;

  /**
   **/
  public StorniRimborsoCompoGestSep fondoRival(String fondoRival) {
    this.fondoRival = fondoRival;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("fondoRival")

  public String getFondoRival() {
    return fondoRival;
  }
  public void setFondoRival(String fondoRival) {
    this.fondoRival = fondoRival;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep descrFondoRival(String descrFondoRival) {
    this.descrFondoRival = descrFondoRival;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("descrFondoRival")

  public String getDescrFondoRival() {
    return descrFondoRival;
  }
  public void setDescrFondoRival(String descrFondoRival) {
    this.descrFondoRival = descrFondoRival;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep dataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataEffetto")

  public Date getDataEffetto() {
    return dataEffetto;
  }
  public void setDataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep prestazione(BigDecimal prestazione) {
    this.prestazione = prestazione;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("prestazione")

  public BigDecimal getPrestazione() {
    return prestazione;
  }
  public void setPrestazione(BigDecimal prestazione) {
    this.prestazione = prestazione;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep rendimento(BigDecimal rendimento) {
    this.rendimento = rendimento;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("rendimento")

  public BigDecimal getRendimento() {
    return rendimento;
  }
  public void setRendimento(BigDecimal rendimento) {
    this.rendimento = rendimento;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep controvalore(BigDecimal controvalore) {
    this.controvalore = controvalore;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("controvalore")

  public BigDecimal getControvalore() {
    return controvalore;
  }
  public void setControvalore(BigDecimal controvalore) {
    this.controvalore = controvalore;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep elencoNumPosiz(List<Integer> elencoNumPosiz) {
    this.elencoNumPosiz = elencoNumPosiz;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoNumPosiz")

  public List<Integer> getElencoNumPosiz() {
    return elencoNumPosiz;
  }
  public void setElencoNumPosiz(List<Integer> elencoNumPosiz) {
    this.elencoNumPosiz = elencoNumPosiz;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep frazioneAnno(BigDecimal frazioneAnno) {
    this.frazioneAnno = frazioneAnno;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("frazioneAnno")

  public BigDecimal getFrazioneAnno() {
    return frazioneAnno;
  }
  public void setFrazioneAnno(BigDecimal frazioneAnno) {
    this.frazioneAnno = frazioneAnno;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep rendimentoLordo(BigDecimal rendimentoLordo) {
    this.rendimentoLordo = rendimentoLordo;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("rendimentoLordo")

  public BigDecimal getRendimentoLordo() {
    return rendimentoLordo;
  }
  public void setRendimentoLordo(BigDecimal rendimentoLordo) {
    this.rendimentoLordo = rendimentoLordo;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep rendimentoTrattenuto(BigDecimal rendimentoTrattenuto) {
    this.rendimentoTrattenuto = rendimentoTrattenuto;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("rendimentoTrattenuto")

  public BigDecimal getRendimentoTrattenuto() {
    return rendimentoTrattenuto;
  }
  public void setRendimentoTrattenuto(BigDecimal rendimentoTrattenuto) {
    this.rendimentoTrattenuto = rendimentoTrattenuto;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep rendimentoNetto(BigDecimal rendimentoNetto) {
    this.rendimentoNetto = rendimentoNetto;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("rendimentoNetto")

  public BigDecimal getRendimentoNetto() {
    return rendimentoNetto;
  }
  public void setRendimentoNetto(BigDecimal rendimentoNetto) {
    this.rendimentoNetto = rendimentoNetto;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep tassoUtilizzato(BigDecimal tassoUtilizzato) {
    this.tassoUtilizzato = tassoUtilizzato;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("tassoUtilizzato")

  public BigDecimal getTassoUtilizzato() {
    return tassoUtilizzato;
  }
  public void setTassoUtilizzato(BigDecimal tassoUtilizzato) {
    this.tassoUtilizzato = tassoUtilizzato;
  }

  /**
   **/
  public StorniRimborsoCompoGestSep prestazioneLorda(BigDecimal prestazioneLorda) {
    this.prestazioneLorda = prestazioneLorda;
    return this;
  }

  
  @ApiModelProperty(example = "100.01", value = "")
  @JsonProperty("prestazioneLorda")

  public BigDecimal getPrestazioneLorda() {
    return prestazioneLorda;
  }
  public void setPrestazioneLorda(BigDecimal prestazioneLorda) {
    this.prestazioneLorda = prestazioneLorda;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StorniRimborsoCompoGestSep storniRimborsoCompoGestSep = (StorniRimborsoCompoGestSep) o;
    return Objects.equals(fondoRival, storniRimborsoCompoGestSep.fondoRival) &&
        Objects.equals(descrFondoRival, storniRimborsoCompoGestSep.descrFondoRival) &&
        Objects.equals(dataEffetto, storniRimborsoCompoGestSep.dataEffetto) &&
        Objects.equals(prestazione, storniRimborsoCompoGestSep.prestazione) &&
        Objects.equals(rendimento, storniRimborsoCompoGestSep.rendimento) &&
        Objects.equals(controvalore, storniRimborsoCompoGestSep.controvalore) &&
        Objects.equals(elencoNumPosiz, storniRimborsoCompoGestSep.elencoNumPosiz) &&
        Objects.equals(frazioneAnno, storniRimborsoCompoGestSep.frazioneAnno) &&
        Objects.equals(rendimentoLordo, storniRimborsoCompoGestSep.rendimentoLordo) &&
        Objects.equals(rendimentoTrattenuto, storniRimborsoCompoGestSep.rendimentoTrattenuto) &&
        Objects.equals(rendimentoNetto, storniRimborsoCompoGestSep.rendimentoNetto) &&
        Objects.equals(tassoUtilizzato, storniRimborsoCompoGestSep.tassoUtilizzato) &&
        Objects.equals(prestazioneLorda, storniRimborsoCompoGestSep.prestazioneLorda);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fondoRival, descrFondoRival, dataEffetto, prestazione, rendimento, controvalore, elencoNumPosiz, frazioneAnno, rendimentoLordo, rendimentoTrattenuto, rendimentoNetto, tassoUtilizzato, prestazioneLorda);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StorniRimborsoCompoGestSep {\n");
    
    sb.append("    fondoRival: ").append(toIndentedString(fondoRival)).append("\n");
    sb.append("    descrFondoRival: ").append(toIndentedString(descrFondoRival)).append("\n");
    sb.append("    dataEffetto: ").append(toIndentedString(dataEffetto)).append("\n");
    sb.append("    prestazione: ").append(toIndentedString(prestazione)).append("\n");
    sb.append("    rendimento: ").append(toIndentedString(rendimento)).append("\n");
    sb.append("    controvalore: ").append(toIndentedString(controvalore)).append("\n");
    sb.append("    elencoNumPosiz: ").append(toIndentedString(elencoNumPosiz)).append("\n");
    sb.append("    frazioneAnno: ").append(toIndentedString(frazioneAnno)).append("\n");
    sb.append("    rendimentoLordo: ").append(toIndentedString(rendimentoLordo)).append("\n");
    sb.append("    rendimentoTrattenuto: ").append(toIndentedString(rendimentoTrattenuto)).append("\n");
    sb.append("    rendimentoNetto: ").append(toIndentedString(rendimentoNetto)).append("\n");
    sb.append("    tassoUtilizzato: ").append(toIndentedString(tassoUtilizzato)).append("\n");
    sb.append("    prestazioneLorda: ").append(toIndentedString(prestazioneLorda)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
