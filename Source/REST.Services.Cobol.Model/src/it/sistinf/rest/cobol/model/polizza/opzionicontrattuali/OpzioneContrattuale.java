package it.sistinf.rest.cobol.model.polizza.opzionicontrattuali;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;


public class OpzioneContrattuale   {

  private @Valid String evento = null;

  private @Valid String durata = null;

public enum PeriodicitaEnum {

    EMPTY(String.valueOf("")), ANNUALE(String.valueOf("ANNUALE")), SEMESTRALE(String.valueOf("SEMESTRALE")), QUADRIMESTRALE(String.valueOf("QUADRIMESTRALE")), TRIMESTRALE(String.valueOf("TRIMESTRALE")), BIMENSILE(String.valueOf("BIMENSILE")), MENSILE(String.valueOf("MENSILE"));


    private String value;

    PeriodicitaEnum (String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    @Override
    @JsonValue
    public String toString() {
        return String.valueOf(value);
    }

    @JsonCreator
    public static PeriodicitaEnum fromValue(String v) {
        for (PeriodicitaEnum b : PeriodicitaEnum.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        return null;
    }
}
  private @Valid PeriodicitaEnum periodicita = null;

  private @Valid BigDecimal importo = null;

  private @Valid Date dataEffetto = null;

  private @Valid Date dataAdesione = null;

  private @Valid BigDecimal obiettivo = null;

  /**
   **/
  public OpzioneContrattuale evento(String evento) {
    this.evento = evento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("evento")

  public String getEvento() {
    return evento;
  }
  public void setEvento(String evento) {
    this.evento = evento;
  }

  /**
   **/
  public OpzioneContrattuale durata(String durata) {
    this.durata = durata;
    return this;
  }

  
  @ApiModelProperty(example = "05", value = "")
  @JsonProperty("durata")

  public String getDurata() {
    return durata;
  }
  public void setDurata(String durata) {
    this.durata = durata;
  }

  /**
   **/
  public OpzioneContrattuale periodicita(PeriodicitaEnum periodicita) {
    this.periodicita = periodicita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("periodicita")

  public PeriodicitaEnum getPeriodicita() {
    return periodicita;
  }
  public void setPeriodicita(PeriodicitaEnum periodicita) {
    this.periodicita = periodicita;
  }

  /**
   **/
  public OpzioneContrattuale importo(BigDecimal importo) {
    this.importo = importo;
    return this;
  }

  
  @ApiModelProperty(example = "3.01", value = "")
  @JsonProperty("importo")

  public BigDecimal getImporto() {
    return importo;
  }
  public void setImporto(BigDecimal importo) {
    this.importo = importo;
  }

  /**
   **/
  public OpzioneContrattuale dataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataEffetto")

  public Date getDataEffetto() {
    return dataEffetto;
  }
  public void setDataEffetto(Date dataEffetto) {
    this.dataEffetto = dataEffetto;
  }

  /**
   **/
  public OpzioneContrattuale dataAdesione(Date dataAdesione) {
    this.dataAdesione = dataAdesione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataAdesione")

  public Date getDataAdesione() {
    return dataAdesione;
  }
  public void setDataAdesione(Date dataAdesione) {
    this.dataAdesione = dataAdesione;
  }

  /**
   **/
  public OpzioneContrattuale obiettivo(BigDecimal obiettivo) {
    this.obiettivo = obiettivo;
    return this;
  }

  
  @ApiModelProperty(example = "5.001", value = "")
  @JsonProperty("obiettivo")

  public BigDecimal getObiettivo() {
    return obiettivo;
  }
  public void setObiettivo(BigDecimal obiettivo) {
    this.obiettivo = obiettivo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OpzioneContrattuale opzioneContrattuale = (OpzioneContrattuale) o;
    return Objects.equals(evento, opzioneContrattuale.evento) &&
        Objects.equals(durata, opzioneContrattuale.durata) &&
        Objects.equals(periodicita, opzioneContrattuale.periodicita) &&
        Objects.equals(importo, opzioneContrattuale.importo) &&
        Objects.equals(dataEffetto, opzioneContrattuale.dataEffetto) &&
        Objects.equals(dataAdesione, opzioneContrattuale.dataAdesione) &&
        Objects.equals(obiettivo, opzioneContrattuale.obiettivo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(evento, durata, periodicita, importo, dataEffetto, dataAdesione, obiettivo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OpzioneContrattuale {\n");
    
    sb.append("    evento: ").append(toIndentedString(evento)).append("\n");
    sb.append("    durata: ").append(toIndentedString(durata)).append("\n");
    sb.append("    periodicita: ").append(toIndentedString(periodicita)).append("\n");
    sb.append("    importo: ").append(toIndentedString(importo)).append("\n");
    sb.append("    dataEffetto: ").append(toIndentedString(dataEffetto)).append("\n");
    sb.append("    dataAdesione: ").append(toIndentedString(dataAdesione)).append("\n");
    sb.append("    obiettivo: ").append(toIndentedString(obiettivo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
