package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ElencoPercipientiRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid String funzione = null;

  private @Valid String idPrenotazione = null;

  private @Valid String tipoVincolo = null;

  private @Valid String flgVincolo = null;

  private @Valid String richiedente = null;

  private @Valid BigDecimal importoVincolo = null;

  private @Valid Date dataRicezioneDoc = null;

  private @Valid String tipoProdottoSpeciale = null;

  private @Valid String penaleTFM = null;

  private @Valid String tipoUscita = null;

  private @Valid String flgManleva = null;

  private @Valid String flgInps = null;

  private @Valid BigDecimal importoInps = null;

  private @Valid BigDecimal percInps = null;

  private @Valid BigDecimal percIrpef = null;

  private @Valid BigDecimal imponibileIrpef = null;

  private @Valid BigDecimal importoIrpef = null;

  private @Valid BigDecimal importoRichiesto = null;

  private @Valid Date dataPrimaIscrizione = null;

  private @Valid String tipoIscritto = null;

  private @Valid Integer aliquitaTfr = null;

  private @Valid String flagArt11 = null;

  private @Valid String sottoFunzionePrenotaz = null;

  private @Valid String flgOpzione = null;

  private @Valid String tipoLiquid = null;

  private @Valid Integer posizioneUT = null;

  /**
   **/
  public ElencoPercipientiRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public ElencoPercipientiRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public ElencoPercipientiRequest funzione(String funzione) {
    this.funzione = funzione;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("funzione")
  @NotNull

  public String getFunzione() {
    return funzione;
  }
  public void setFunzione(String funzione) {
    this.funzione = funzione;
  }

  /**
   **/
  public ElencoPercipientiRequest idPrenotazione(String idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("idPrenotazione")
  

  public String getIdPrenotazione() {
    return idPrenotazione;
  }
  public void setIdPrenotazione(String idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
  }

  /**
   **/
  public ElencoPercipientiRequest tipoVincolo(String tipoVincolo) {
    this.tipoVincolo = tipoVincolo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoVincolo")
  

  public String getTipoVincolo() {
    return tipoVincolo;
  }
  public void setTipoVincolo(String tipoVincolo) {
    this.tipoVincolo = tipoVincolo;
  }

  /**
   **/
  public ElencoPercipientiRequest flgVincolo(String flgVincolo) {
    this.flgVincolo = flgVincolo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flgVincolo")
  

  public String getFlgVincolo() {
    return flgVincolo;
  }
  public void setFlgVincolo(String flgVincolo) {
    this.flgVincolo = flgVincolo;
  }

  /**
   **/
  public ElencoPercipientiRequest richiedente(String richiedente) {
    this.richiedente = richiedente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("richiedente")
  

  public String getRichiedente() {
    return richiedente;
  }
  public void setRichiedente(String richiedente) {
    this.richiedente = richiedente;
  }

  /**
   **/
  public ElencoPercipientiRequest importoVincolo(BigDecimal importoVincolo) {
    this.importoVincolo = importoVincolo;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoVincolo")
  

  public BigDecimal getImportoVincolo() {
    return importoVincolo;
  }
  public void setImportoVincolo(BigDecimal importoVincolo) {
    this.importoVincolo = importoVincolo;
  }

  /**
   **/
  public ElencoPercipientiRequest dataRicezioneDoc(Date dataRicezioneDoc) {
    this.dataRicezioneDoc = dataRicezioneDoc;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataRicezioneDoc")
  

  public Date getDataRicezioneDoc() {
    return dataRicezioneDoc;
  }
  public void setDataRicezioneDoc(Date dataRicezioneDoc) {
    this.dataRicezioneDoc = dataRicezioneDoc;
  }

  /**
   **/
  public ElencoPercipientiRequest tipoProdottoSpeciale(String tipoProdottoSpeciale) {
    this.tipoProdottoSpeciale = tipoProdottoSpeciale;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoProdottoSpeciale")
  

  public String getTipoProdottoSpeciale() {
    return tipoProdottoSpeciale;
  }
  public void setTipoProdottoSpeciale(String tipoProdottoSpeciale) {
    this.tipoProdottoSpeciale = tipoProdottoSpeciale;
  }

  /**
   **/
  public ElencoPercipientiRequest penaleTFM(String penaleTFM) {
    this.penaleTFM = penaleTFM;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("penaleTFM")
  

  public String getPenaleTFM() {
    return penaleTFM;
  }
  public void setPenaleTFM(String penaleTFM) {
    this.penaleTFM = penaleTFM;
  }

  /**
   **/
  public ElencoPercipientiRequest tipoUscita(String tipoUscita) {
    this.tipoUscita = tipoUscita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoUscita")
  

  public String getTipoUscita() {
    return tipoUscita;
  }
  public void setTipoUscita(String tipoUscita) {
    this.tipoUscita = tipoUscita;
  }

  /**
   **/
  public ElencoPercipientiRequest flgManleva(String flgManleva) {
    this.flgManleva = flgManleva;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flgManleva")
  

  public String getFlgManleva() {
    return flgManleva;
  }
  public void setFlgManleva(String flgManleva) {
    this.flgManleva = flgManleva;
  }

  /**
   **/
  public ElencoPercipientiRequest flgInps(String flgInps) {
    this.flgInps = flgInps;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flgInps")
  

  public String getFlgInps() {
    return flgInps;
  }
  public void setFlgInps(String flgInps) {
    this.flgInps = flgInps;
  }

  /**
   **/
  public ElencoPercipientiRequest importoInps(BigDecimal importoInps) {
    this.importoInps = importoInps;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoInps")
  

  public BigDecimal getImportoInps() {
    return importoInps;
  }
  public void setImportoInps(BigDecimal importoInps) {
    this.importoInps = importoInps;
  }

  /**
   **/
  public ElencoPercipientiRequest percInps(BigDecimal percInps) {
    this.percInps = percInps;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("percInps")
  

  public BigDecimal getPercInps() {
    return percInps;
  }
  public void setPercInps(BigDecimal percInps) {
    this.percInps = percInps;
  }

  /**
   **/
  public ElencoPercipientiRequest percIrpef(BigDecimal percIrpef) {
    this.percIrpef = percIrpef;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("percIrpef")
  

  public BigDecimal getPercIrpef() {
    return percIrpef;
  }
  public void setPercIrpef(BigDecimal percIrpef) {
    this.percIrpef = percIrpef;
  }

  /**
   **/
  public ElencoPercipientiRequest imponibileIrpef(BigDecimal imponibileIrpef) {
    this.imponibileIrpef = imponibileIrpef;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("imponibileIrpef")
  

  public BigDecimal getImponibileIrpef() {
    return imponibileIrpef;
  }
  public void setImponibileIrpef(BigDecimal imponibileIrpef) {
    this.imponibileIrpef = imponibileIrpef;
  }

  /**
   **/
  public ElencoPercipientiRequest importoIrpef(BigDecimal importoIrpef) {
    this.importoIrpef = importoIrpef;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoIrpef")
  

  public BigDecimal getImportoIrpef() {
    return importoIrpef;
  }
  public void setImportoIrpef(BigDecimal importoIrpef) {
    this.importoIrpef = importoIrpef;
  }

  /**
   **/
  public ElencoPercipientiRequest importoRichiesto(BigDecimal importoRichiesto) {
    this.importoRichiesto = importoRichiesto;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoRichiesto")
  

  public BigDecimal getImportoRichiesto() {
    return importoRichiesto;
  }
  public void setImportoRichiesto(BigDecimal importoRichiesto) {
    this.importoRichiesto = importoRichiesto;
  }

  /**
   **/
  public ElencoPercipientiRequest dataPrimaIscrizione(Date dataPrimaIscrizione) {
    this.dataPrimaIscrizione = dataPrimaIscrizione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataPrimaIscrizione")
  

  public Date getDataPrimaIscrizione() {
    return dataPrimaIscrizione;
  }
  public void setDataPrimaIscrizione(Date dataPrimaIscrizione) {
    this.dataPrimaIscrizione = dataPrimaIscrizione;
  }

  /**
   **/
  public ElencoPercipientiRequest tipoIscritto(String tipoIscritto) {
    this.tipoIscritto = tipoIscritto;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoIscritto")
  

  public String getTipoIscritto() {
    return tipoIscritto;
  }
  public void setTipoIscritto(String tipoIscritto) {
    this.tipoIscritto = tipoIscritto;
  }

  /**
   **/
  public ElencoPercipientiRequest aliquitaTfr(Integer aliquitaTfr) {
    this.aliquitaTfr = aliquitaTfr;
    return this;
  }

  
  @ApiModelProperty(example = "143", value = "")
  @JsonProperty("aliquitaTfr")
  

  public Integer getAliquitaTfr() {
    return aliquitaTfr;
  }
  public void setAliquitaTfr(Integer aliquitaTfr) {
    this.aliquitaTfr = aliquitaTfr;
  }

  /**
   **/
  public ElencoPercipientiRequest flagArt11(String flagArt11) {
    this.flagArt11 = flagArt11;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagArt11")
  

  public String getFlagArt11() {
    return flagArt11;
  }
  public void setFlagArt11(String flagArt11) {
    this.flagArt11 = flagArt11;
  }

  /**
   **/
  public ElencoPercipientiRequest sottoFunzionePrenotaz(String sottoFunzionePrenotaz) {
    this.sottoFunzionePrenotaz = sottoFunzionePrenotaz;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("sottoFunzionePrenotaz")
  

  public String getSottoFunzionePrenotaz() {
    return sottoFunzionePrenotaz;
  }
  public void setSottoFunzionePrenotaz(String sottoFunzionePrenotaz) {
    this.sottoFunzionePrenotaz = sottoFunzionePrenotaz;
  }

  /**
   **/
  public ElencoPercipientiRequest flgOpzione(String flgOpzione) {
    this.flgOpzione = flgOpzione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flgOpzione")
  

  public String getFlgOpzione() {
    return flgOpzione;
  }
  public void setFlgOpzione(String flgOpzione) {
    this.flgOpzione = flgOpzione;
  }

  /**
   **/
  public ElencoPercipientiRequest tipoLiquid(String tipoLiquid) {
    this.tipoLiquid = tipoLiquid;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoLiquid")
  

  public String getTipoLiquid() {
    return tipoLiquid;
  }
  public void setTipoLiquid(String tipoLiquid) {
    this.tipoLiquid = tipoLiquid;
  }

  /**
   **/
  public ElencoPercipientiRequest posizioneUT(Integer posizioneUT) {
    this.posizioneUT = posizioneUT;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("posizioneUT")
  

  public Integer getPosizioneUT() {
    return posizioneUT;
  }
  public void setPosizioneUT(Integer posizioneUT) {
    this.posizioneUT = posizioneUT;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ElencoPercipientiRequest elencoPercipientiRequest = (ElencoPercipientiRequest) o;
    return Objects.equals(headerCobolSrv, elencoPercipientiRequest.headerCobolSrv) &&
        Objects.equals(polizzaInfo, elencoPercipientiRequest.polizzaInfo) &&
        Objects.equals(funzione, elencoPercipientiRequest.funzione) &&
        Objects.equals(idPrenotazione, elencoPercipientiRequest.idPrenotazione) &&
        Objects.equals(tipoVincolo, elencoPercipientiRequest.tipoVincolo) &&
        Objects.equals(flgVincolo, elencoPercipientiRequest.flgVincolo) &&
        Objects.equals(richiedente, elencoPercipientiRequest.richiedente) &&
        Objects.equals(importoVincolo, elencoPercipientiRequest.importoVincolo) &&
        Objects.equals(dataRicezioneDoc, elencoPercipientiRequest.dataRicezioneDoc) &&
        Objects.equals(tipoProdottoSpeciale, elencoPercipientiRequest.tipoProdottoSpeciale) &&
        Objects.equals(penaleTFM, elencoPercipientiRequest.penaleTFM) &&
        Objects.equals(tipoUscita, elencoPercipientiRequest.tipoUscita) &&
        Objects.equals(flgManleva, elencoPercipientiRequest.flgManleva) &&
        Objects.equals(flgInps, elencoPercipientiRequest.flgInps) &&
        Objects.equals(importoInps, elencoPercipientiRequest.importoInps) &&
        Objects.equals(percInps, elencoPercipientiRequest.percInps) &&
        Objects.equals(percIrpef, elencoPercipientiRequest.percIrpef) &&
        Objects.equals(imponibileIrpef, elencoPercipientiRequest.imponibileIrpef) &&
        Objects.equals(importoIrpef, elencoPercipientiRequest.importoIrpef) &&
        Objects.equals(importoRichiesto, elencoPercipientiRequest.importoRichiesto) &&
        Objects.equals(dataPrimaIscrizione, elencoPercipientiRequest.dataPrimaIscrizione) &&
        Objects.equals(tipoIscritto, elencoPercipientiRequest.tipoIscritto) &&
        Objects.equals(aliquitaTfr, elencoPercipientiRequest.aliquitaTfr) &&
        Objects.equals(flagArt11, elencoPercipientiRequest.flagArt11) &&
        Objects.equals(sottoFunzionePrenotaz, elencoPercipientiRequest.sottoFunzionePrenotaz) &&
        Objects.equals(flgOpzione, elencoPercipientiRequest.flgOpzione) &&
        Objects.equals(tipoLiquid, elencoPercipientiRequest.tipoLiquid) &&
        Objects.equals(posizioneUT, elencoPercipientiRequest.posizioneUT);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, polizzaInfo, funzione, idPrenotazione, tipoVincolo, flgVincolo, richiedente, importoVincolo, dataRicezioneDoc, tipoProdottoSpeciale, penaleTFM, tipoUscita, flgManleva, flgInps, importoInps, percInps, percIrpef, imponibileIrpef, importoIrpef, importoRichiesto, dataPrimaIscrizione, tipoIscritto, aliquitaTfr, flagArt11, sottoFunzionePrenotaz, flgOpzione, tipoLiquid, posizioneUT);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ElencoPercipientiRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    funzione: ").append(toIndentedString(funzione)).append("\n");
    sb.append("    idPrenotazione: ").append(toIndentedString(idPrenotazione)).append("\n");
    sb.append("    tipoVincolo: ").append(toIndentedString(tipoVincolo)).append("\n");
    sb.append("    flgVincolo: ").append(toIndentedString(flgVincolo)).append("\n");
    sb.append("    richiedente: ").append(toIndentedString(richiedente)).append("\n");
    sb.append("    importoVincolo: ").append(toIndentedString(importoVincolo)).append("\n");
    sb.append("    dataRicezioneDoc: ").append(toIndentedString(dataRicezioneDoc)).append("\n");
    sb.append("    tipoProdottoSpeciale: ").append(toIndentedString(tipoProdottoSpeciale)).append("\n");
    sb.append("    penaleTFM: ").append(toIndentedString(penaleTFM)).append("\n");
    sb.append("    tipoUscita: ").append(toIndentedString(tipoUscita)).append("\n");
    sb.append("    flgManleva: ").append(toIndentedString(flgManleva)).append("\n");
    sb.append("    flgInps: ").append(toIndentedString(flgInps)).append("\n");
    sb.append("    importoInps: ").append(toIndentedString(importoInps)).append("\n");
    sb.append("    percInps: ").append(toIndentedString(percInps)).append("\n");
    sb.append("    percIrpef: ").append(toIndentedString(percIrpef)).append("\n");
    sb.append("    imponibileIrpef: ").append(toIndentedString(imponibileIrpef)).append("\n");
    sb.append("    importoIrpef: ").append(toIndentedString(importoIrpef)).append("\n");
    sb.append("    importoRichiesto: ").append(toIndentedString(importoRichiesto)).append("\n");
    sb.append("    dataPrimaIscrizione: ").append(toIndentedString(dataPrimaIscrizione)).append("\n");
    sb.append("    tipoIscritto: ").append(toIndentedString(tipoIscritto)).append("\n");
    sb.append("    aliquitaTfr: ").append(toIndentedString(aliquitaTfr)).append("\n");
    sb.append("    flagArt11: ").append(toIndentedString(flagArt11)).append("\n");
    sb.append("    sottoFunzionePrenotaz: ").append(toIndentedString(sottoFunzionePrenotaz)).append("\n");
    sb.append("    flgOpzione: ").append(toIndentedString(flgOpzione)).append("\n");
    sb.append("    tipoLiquid: ").append(toIndentedString(tipoLiquid)).append("\n");
    sb.append("    posizioneUT: ").append(toIndentedString(posizioneUT)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
