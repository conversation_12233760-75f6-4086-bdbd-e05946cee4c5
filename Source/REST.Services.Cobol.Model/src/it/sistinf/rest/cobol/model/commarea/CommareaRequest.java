package it.sistinf.rest.cobol.model.commarea;

import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import javax.validation.constraints.NotNull;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CommareaRequest   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  /**
   **/
  public CommareaRequest headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CommareaRequest commareaRequest = (CommareaRequest) o;
    return Objects.equals(headerCobolSrv, commareaRequest.headerCobolSrv);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CommareaRequest {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
