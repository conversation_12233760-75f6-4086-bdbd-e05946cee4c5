package it.sistinf.rest.cobol.model.compagnia;

import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CompagniaResponse   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid Integer compagnia = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public CompagniaResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public CompagniaResponse compagnia(Integer compagnia) {
    this.compagnia = compagnia;
    return this;
  }

  
  @ApiModelProperty(example = "143", value = "")
  @JsonProperty("compagnia")

  public Integer getCompagnia() {
    return compagnia;
  }
  public void setCompagnia(Integer compagnia) {
    this.compagnia = compagnia;
  }

  /**
   **/
  public CompagniaResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CompagniaResponse compagniaResponse = (CompagniaResponse) o;
    return Objects.equals(headerCobolSrv, compagniaResponse.headerCobolSrv) &&
        Objects.equals(compagnia, compagniaResponse.compagnia) &&
        Objects.equals(errori, compagniaResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, compagnia, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CompagniaResponse {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    compagnia: ").append(toIndentedString(compagnia)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
