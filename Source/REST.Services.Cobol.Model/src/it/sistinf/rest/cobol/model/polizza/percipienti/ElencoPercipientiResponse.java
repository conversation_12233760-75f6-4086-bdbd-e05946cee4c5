package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.EsitoResponse;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.customer.AllCustomerInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class ElencoPercipientiResponse extends EsitoResponse  {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid BigDecimal quotaPerc = null;

  private @Valid BigDecimal quotaImp = null;

  private @Valid String modPag = null;

  private @Valid String cfConiuge = null;

  private @Valid String coniugeACarico = null;

  private @Valid String flFipPip = null;

  private @Valid String relazioneBeneficiarioContraente = null;

  private @Valid String relazioneBeneficiarioContraenteAltro = null;

  private @Valid DatiSoggettoTerzo datiSoggettoTerzo = null;

  private @Valid DatiBeneficiarioPG datiBeneficiarioPG = null;

  private @Valid DatiPagamento datiPagamento = null;

  private @Valid Reimpiego datiReimpiego = null;

  private @Valid ValoreScadenza datiValoreScadenza = null;

  private @Valid List<PercipienteInfo> elencoPercipienti = new ArrayList<PercipienteInfo>();

  private @Valid List<AllCustomerInfo> elencoPercipienteSoggettoTerzo = new ArrayList<AllCustomerInfo>();

  private @Valid List<AllCustomerInfo> elencoEsecutorePolizza = new ArrayList<AllCustomerInfo>();

  private @Valid List<AllCustomerInfo> elencoRappLegaleRiscattoPolizza = new ArrayList<AllCustomerInfo>();

  /**
   **/
  public ElencoPercipientiResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public ElencoPercipientiResponse quotaPerc(BigDecimal quotaPerc) {
    this.quotaPerc = quotaPerc;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("quotaPerc")
  @NotNull

  public BigDecimal getQuotaPerc() {
    return quotaPerc;
  }
  public void setQuotaPerc(BigDecimal quotaPerc) {
    this.quotaPerc = quotaPerc;
  }

  /**
   **/
  public ElencoPercipientiResponse quotaImp(BigDecimal quotaImp) {
    this.quotaImp = quotaImp;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("quotaImp")
  @NotNull

  public BigDecimal getQuotaImp() {
    return quotaImp;
  }
  public void setQuotaImp(BigDecimal quotaImp) {
    this.quotaImp = quotaImp;
  }

  /**
   **/
  public ElencoPercipientiResponse modPag(String modPag) {
    this.modPag = modPag;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("modPag")
  @NotNull

  public String getModPag() {
    return modPag;
  }
  public void setModPag(String modPag) {
    this.modPag = modPag;
  }

  /**
   **/
  public ElencoPercipientiResponse cfConiuge(String cfConiuge) {
    this.cfConiuge = cfConiuge;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("cfConiuge")
  @NotNull

  public String getCfConiuge() {
    return cfConiuge;
  }
  public void setCfConiuge(String cfConiuge) {
    this.cfConiuge = cfConiuge;
  }

  /**
   **/
  public ElencoPercipientiResponse coniugeACarico(String coniugeACarico) {
    this.coniugeACarico = coniugeACarico;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("coniugeACarico")
  @NotNull

  public String getConiugeACarico() {
    return coniugeACarico;
  }
  public void setConiugeACarico(String coniugeACarico) {
    this.coniugeACarico = coniugeACarico;
  }

  /**
   **/
  public ElencoPercipientiResponse flFipPip(String flFipPip) {
    this.flFipPip = flFipPip;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flFipPip")
  @NotNull

  public String getFlFipPip() {
    return flFipPip;
  }
  public void setFlFipPip(String flFipPip) {
    this.flFipPip = flFipPip;
  }

  /**
   **/
  public ElencoPercipientiResponse relazioneBeneficiarioContraente(String relazioneBeneficiarioContraente) {
    this.relazioneBeneficiarioContraente = relazioneBeneficiarioContraente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioContraente")
  @NotNull

  public String getRelazioneBeneficiarioContraente() {
    return relazioneBeneficiarioContraente;
  }
  public void setRelazioneBeneficiarioContraente(String relazioneBeneficiarioContraente) {
    this.relazioneBeneficiarioContraente = relazioneBeneficiarioContraente;
  }

  /**
   **/
  public ElencoPercipientiResponse relazioneBeneficiarioContraenteAltro(String relazioneBeneficiarioContraenteAltro) {
    this.relazioneBeneficiarioContraenteAltro = relazioneBeneficiarioContraenteAltro;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("relazioneBeneficiarioContraenteAltro")
  @NotNull

  public String getRelazioneBeneficiarioContraenteAltro() {
    return relazioneBeneficiarioContraenteAltro;
  }
  public void setRelazioneBeneficiarioContraenteAltro(String relazioneBeneficiarioContraenteAltro) {
    this.relazioneBeneficiarioContraenteAltro = relazioneBeneficiarioContraenteAltro;
  }

  /**
   **/
  public ElencoPercipientiResponse datiSoggettoTerzo(DatiSoggettoTerzo datiSoggettoTerzo) {
    this.datiSoggettoTerzo = datiSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiSoggettoTerzo")
  @NotNull

  public DatiSoggettoTerzo getDatiSoggettoTerzo() {
    return datiSoggettoTerzo;
  }
  public void setDatiSoggettoTerzo(DatiSoggettoTerzo datiSoggettoTerzo) {
    this.datiSoggettoTerzo = datiSoggettoTerzo;
  }

  /**
   **/
  public ElencoPercipientiResponse datiBeneficiarioPG(DatiBeneficiarioPG datiBeneficiarioPG) {
    this.datiBeneficiarioPG = datiBeneficiarioPG;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiBeneficiarioPG")
  @NotNull

  public DatiBeneficiarioPG getDatiBeneficiarioPG() {
    return datiBeneficiarioPG;
  }
  public void setDatiBeneficiarioPG(DatiBeneficiarioPG datiBeneficiarioPG) {
    this.datiBeneficiarioPG = datiBeneficiarioPG;
  }

  /**
   **/
  public ElencoPercipientiResponse datiPagamento(DatiPagamento datiPagamento) {
    this.datiPagamento = datiPagamento;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiPagamento")
  @NotNull

  public DatiPagamento getDatiPagamento() {
    return datiPagamento;
  }
  public void setDatiPagamento(DatiPagamento datiPagamento) {
    this.datiPagamento = datiPagamento;
  }

  /**
   **/
  public ElencoPercipientiResponse datiReimpiego(Reimpiego datiReimpiego) {
    this.datiReimpiego = datiReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiReimpiego")
  @NotNull

  public Reimpiego getDatiReimpiego() {
    return datiReimpiego;
  }
  public void setDatiReimpiego(Reimpiego datiReimpiego) {
    this.datiReimpiego = datiReimpiego;
  }

  /**
   **/
  public ElencoPercipientiResponse datiValoreScadenza(ValoreScadenza datiValoreScadenza) {
    this.datiValoreScadenza = datiValoreScadenza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiValoreScadenza")
  @NotNull

  public ValoreScadenza getDatiValoreScadenza() {
    return datiValoreScadenza;
  }
  public void setDatiValoreScadenza(ValoreScadenza datiValoreScadenza) {
    this.datiValoreScadenza = datiValoreScadenza;
  }

  /**
   **/
  public ElencoPercipientiResponse elencoPercipienti(List<PercipienteInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipienti")
  @NotNull

  public List<PercipienteInfo> getElencoPercipienti() {
    return elencoPercipienti;
  }
  public void setElencoPercipienti(List<PercipienteInfo> elencoPercipienti) {
    this.elencoPercipienti = elencoPercipienti;
  }

  /**
   **/
  public ElencoPercipientiResponse elencoPercipienteSoggettoTerzo(List<AllCustomerInfo> elencoPercipienteSoggettoTerzo) {
    this.elencoPercipienteSoggettoTerzo = elencoPercipienteSoggettoTerzo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoPercipienteSoggettoTerzo")
  @NotNull

  public List<AllCustomerInfo> getElencoPercipienteSoggettoTerzo() {
    return elencoPercipienteSoggettoTerzo;
  }
  public void setElencoPercipienteSoggettoTerzo(List<AllCustomerInfo> elencoPercipienteSoggettoTerzo) {
    this.elencoPercipienteSoggettoTerzo = elencoPercipienteSoggettoTerzo;
  }

  /**
   **/
  public ElencoPercipientiResponse elencoEsecutorePolizza(List<AllCustomerInfo> elencoEsecutorePolizza) {
    this.elencoEsecutorePolizza = elencoEsecutorePolizza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoEsecutorePolizza")
  @NotNull

  public List<AllCustomerInfo> getElencoEsecutorePolizza() {
    return elencoEsecutorePolizza;
  }
  public void setElencoEsecutorePolizza(List<AllCustomerInfo> elencoEsecutorePolizza) {
    this.elencoEsecutorePolizza = elencoEsecutorePolizza;
  }

  /**
   **/
  public ElencoPercipientiResponse elencoRappLegaleRiscattoPolizza(List<AllCustomerInfo> elencoRappLegaleRiscattoPolizza) {
    this.elencoRappLegaleRiscattoPolizza = elencoRappLegaleRiscattoPolizza;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elencoRappLegaleRiscattoPolizza")
  @NotNull

  public List<AllCustomerInfo> getElencoRappLegaleRiscattoPolizza() {
    return elencoRappLegaleRiscattoPolizza;
  }
  public void setElencoRappLegaleRiscattoPolizza(List<AllCustomerInfo> elencoRappLegaleRiscattoPolizza) {
    this.elencoRappLegaleRiscattoPolizza = elencoRappLegaleRiscattoPolizza;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ElencoPercipientiResponse elencoPercipientiResponse = (ElencoPercipientiResponse) o;
    return Objects.equals(headerCobolSrv, elencoPercipientiResponse.headerCobolSrv) &&
        Objects.equals(quotaPerc, elencoPercipientiResponse.quotaPerc) &&
        Objects.equals(quotaImp, elencoPercipientiResponse.quotaImp) &&
        Objects.equals(modPag, elencoPercipientiResponse.modPag) &&
        Objects.equals(cfConiuge, elencoPercipientiResponse.cfConiuge) &&
        Objects.equals(coniugeACarico, elencoPercipientiResponse.coniugeACarico) &&
        Objects.equals(flFipPip, elencoPercipientiResponse.flFipPip) &&
        Objects.equals(relazioneBeneficiarioContraente, elencoPercipientiResponse.relazioneBeneficiarioContraente) &&
        Objects.equals(relazioneBeneficiarioContraenteAltro, elencoPercipientiResponse.relazioneBeneficiarioContraenteAltro) &&
        Objects.equals(datiSoggettoTerzo, elencoPercipientiResponse.datiSoggettoTerzo) &&
        Objects.equals(datiBeneficiarioPG, elencoPercipientiResponse.datiBeneficiarioPG) &&
        Objects.equals(datiPagamento, elencoPercipientiResponse.datiPagamento) &&
        Objects.equals(datiReimpiego, elencoPercipientiResponse.datiReimpiego) &&
        Objects.equals(datiValoreScadenza, elencoPercipientiResponse.datiValoreScadenza) &&
        Objects.equals(elencoPercipienti, elencoPercipientiResponse.elencoPercipienti) &&
        Objects.equals(elencoPercipienteSoggettoTerzo, elencoPercipientiResponse.elencoPercipienteSoggettoTerzo) &&
        Objects.equals(elencoEsecutorePolizza, elencoPercipientiResponse.elencoEsecutorePolizza) &&
        Objects.equals(elencoRappLegaleRiscattoPolizza, elencoPercipientiResponse.elencoRappLegaleRiscattoPolizza);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, quotaPerc, quotaImp, modPag, cfConiuge, coniugeACarico, flFipPip, relazioneBeneficiarioContraente, relazioneBeneficiarioContraenteAltro, datiSoggettoTerzo, datiBeneficiarioPG, datiPagamento, datiReimpiego, datiValoreScadenza, elencoPercipienti, elencoPercipienteSoggettoTerzo, elencoEsecutorePolizza, elencoRappLegaleRiscattoPolizza);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ElencoPercipientiResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    quotaPerc: ").append(toIndentedString(quotaPerc)).append("\n");
    sb.append("    quotaImp: ").append(toIndentedString(quotaImp)).append("\n");
    sb.append("    modPag: ").append(toIndentedString(modPag)).append("\n");
    sb.append("    cfConiuge: ").append(toIndentedString(cfConiuge)).append("\n");
    sb.append("    coniugeACarico: ").append(toIndentedString(coniugeACarico)).append("\n");
    sb.append("    flFipPip: ").append(toIndentedString(flFipPip)).append("\n");
    sb.append("    relazioneBeneficiarioContraente: ").append(toIndentedString(relazioneBeneficiarioContraente)).append("\n");
    sb.append("    relazioneBeneficiarioContraenteAltro: ").append(toIndentedString(relazioneBeneficiarioContraenteAltro)).append("\n");
    sb.append("    datiSoggettoTerzo: ").append(toIndentedString(datiSoggettoTerzo)).append("\n");
    sb.append("    datiBeneficiarioPG: ").append(toIndentedString(datiBeneficiarioPG)).append("\n");
    sb.append("    datiPagamento: ").append(toIndentedString(datiPagamento)).append("\n");
    sb.append("    datiReimpiego: ").append(toIndentedString(datiReimpiego)).append("\n");
    sb.append("    datiValoreScadenza: ").append(toIndentedString(datiValoreScadenza)).append("\n");
    sb.append("    elencoPercipienti: ").append(toIndentedString(elencoPercipienti)).append("\n");
    sb.append("    elencoPercipienteSoggettoTerzo: ").append(toIndentedString(elencoPercipienteSoggettoTerzo)).append("\n");
    sb.append("    elencoEsecutorePolizza: ").append(toIndentedString(elencoEsecutorePolizza)).append("\n");
    sb.append("    elencoRappLegaleRiscattoPolizza: ").append(toIndentedString(elencoRappLegaleRiscattoPolizza)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
