package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class RapportoReimpiego   {

  private @Valid String flagSelRapportoReimpiego = null;

  private @Valid String numCategoriaRapportoReimpiego = null;

  private @Valid String agenziaRapportoReimpiego = null;

  private @Valid String numeroCollettivaReimpiego = null;

  private @Valid String numeroRapportoReimpiego = null;

  private @Valid String convenzioneRapportoReimpiego = null;

  private @Valid BigDecimal importoRapportoReimpiego = null;

  /**
   **/
  public RapportoReimpiego flagSelRapportoReimpiego(String flagSelRapportoReimpiego) {
    this.flagSelRapportoReimpiego = flagSelRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("flagSelRapportoReimpiego")
  @NotNull

  public String getFlagSelRapportoReimpiego() {
    return flagSelRapportoReimpiego;
  }
  public void setFlagSelRapportoReimpiego(String flagSelRapportoReimpiego) {
    this.flagSelRapportoReimpiego = flagSelRapportoReimpiego;
  }

  /**
   **/
  public RapportoReimpiego numCategoriaRapportoReimpiego(String numCategoriaRapportoReimpiego) {
    this.numCategoriaRapportoReimpiego = numCategoriaRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numCategoriaRapportoReimpiego")
  @NotNull

  public String getNumCategoriaRapportoReimpiego() {
    return numCategoriaRapportoReimpiego;
  }
  public void setNumCategoriaRapportoReimpiego(String numCategoriaRapportoReimpiego) {
    this.numCategoriaRapportoReimpiego = numCategoriaRapportoReimpiego;
  }

  /**
   **/
  public RapportoReimpiego agenziaRapportoReimpiego(String agenziaRapportoReimpiego) {
    this.agenziaRapportoReimpiego = agenziaRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("agenziaRapportoReimpiego")
  @NotNull

  public String getAgenziaRapportoReimpiego() {
    return agenziaRapportoReimpiego;
  }
  public void setAgenziaRapportoReimpiego(String agenziaRapportoReimpiego) {
    this.agenziaRapportoReimpiego = agenziaRapportoReimpiego;
  }

  /**
   **/
  public RapportoReimpiego numeroCollettivaReimpiego(String numeroCollettivaReimpiego) {
    this.numeroCollettivaReimpiego = numeroCollettivaReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numeroCollettivaReimpiego")
  @NotNull

  public String getNumeroCollettivaReimpiego() {
    return numeroCollettivaReimpiego;
  }
  public void setNumeroCollettivaReimpiego(String numeroCollettivaReimpiego) {
    this.numeroCollettivaReimpiego = numeroCollettivaReimpiego;
  }

  /**
   **/
  public RapportoReimpiego numeroRapportoReimpiego(String numeroRapportoReimpiego) {
    this.numeroRapportoReimpiego = numeroRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numeroRapportoReimpiego")
  @NotNull

  public String getNumeroRapportoReimpiego() {
    return numeroRapportoReimpiego;
  }
  public void setNumeroRapportoReimpiego(String numeroRapportoReimpiego) {
    this.numeroRapportoReimpiego = numeroRapportoReimpiego;
  }

  /**
   **/
  public RapportoReimpiego convenzioneRapportoReimpiego(String convenzioneRapportoReimpiego) {
    this.convenzioneRapportoReimpiego = convenzioneRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("convenzioneRapportoReimpiego")
  @NotNull

  public String getConvenzioneRapportoReimpiego() {
    return convenzioneRapportoReimpiego;
  }
  public void setConvenzioneRapportoReimpiego(String convenzioneRapportoReimpiego) {
    this.convenzioneRapportoReimpiego = convenzioneRapportoReimpiego;
  }

  /**
   **/
  public RapportoReimpiego importoRapportoReimpiego(BigDecimal importoRapportoReimpiego) {
    this.importoRapportoReimpiego = importoRapportoReimpiego;
    return this;
  }

  
  @ApiModelProperty(example = "0.01", value = "")
  @JsonProperty("importoRapportoReimpiego")
  @NotNull

  public BigDecimal getImportoRapportoReimpiego() {
    return importoRapportoReimpiego;
  }
  public void setImportoRapportoReimpiego(BigDecimal importoRapportoReimpiego) {
    this.importoRapportoReimpiego = importoRapportoReimpiego;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RapportoReimpiego rapportoReimpiego = (RapportoReimpiego) o;
    return Objects.equals(flagSelRapportoReimpiego, rapportoReimpiego.flagSelRapportoReimpiego) &&
        Objects.equals(numCategoriaRapportoReimpiego, rapportoReimpiego.numCategoriaRapportoReimpiego) &&
        Objects.equals(agenziaRapportoReimpiego, rapportoReimpiego.agenziaRapportoReimpiego) &&
        Objects.equals(numeroCollettivaReimpiego, rapportoReimpiego.numeroCollettivaReimpiego) &&
        Objects.equals(numeroRapportoReimpiego, rapportoReimpiego.numeroRapportoReimpiego) &&
        Objects.equals(convenzioneRapportoReimpiego, rapportoReimpiego.convenzioneRapportoReimpiego) &&
        Objects.equals(importoRapportoReimpiego, rapportoReimpiego.importoRapportoReimpiego);
  }

  @Override
  public int hashCode() {
    return Objects.hash(flagSelRapportoReimpiego, numCategoriaRapportoReimpiego, agenziaRapportoReimpiego, numeroCollettivaReimpiego, numeroRapportoReimpiego, convenzioneRapportoReimpiego, importoRapportoReimpiego);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RapportoReimpiego {\n");
    
    sb.append("    flagSelRapportoReimpiego: ").append(toIndentedString(flagSelRapportoReimpiego)).append("\n");
    sb.append("    numCategoriaRapportoReimpiego: ").append(toIndentedString(numCategoriaRapportoReimpiego)).append("\n");
    sb.append("    agenziaRapportoReimpiego: ").append(toIndentedString(agenziaRapportoReimpiego)).append("\n");
    sb.append("    numeroCollettivaReimpiego: ").append(toIndentedString(numeroCollettivaReimpiego)).append("\n");
    sb.append("    numeroRapportoReimpiego: ").append(toIndentedString(numeroRapportoReimpiego)).append("\n");
    sb.append("    convenzioneRapportoReimpiego: ").append(toIndentedString(convenzioneRapportoReimpiego)).append("\n");
    sb.append("    importoRapportoReimpiego: ").append(toIndentedString(importoRapportoReimpiego)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
