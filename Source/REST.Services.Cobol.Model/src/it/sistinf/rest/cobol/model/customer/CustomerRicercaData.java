package it.sistinf.rest.cobol.model.customer;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class CustomerRicercaData extends CustomerRicercaDataBase  {

  private @Valid String nominativo = null;

  private @Valid String nome = null;

  private @Valid String citta = null;

  private @Valid Date dataNascita = null;

  /**
   **/
  public CustomerRicercaData nominativo(String nominativo) {
    this.nominativo = nominativo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nominativo")
 @Size(min=1,max=100)
  public String getNominativo() {
    return nominativo;
  }
  public void setNominativo(String nominativo) {
    this.nominativo = nominativo;
  }

  /**
   **/
  public CustomerRicercaData nome(String nome) {
    this.nome = nome;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("nome")
 @Size(min=1,max=30)
  public String getNome() {
    return nome;
  }
  public void setNome(String nome) {
    this.nome = nome;
  }

  /**
   **/
  public CustomerRicercaData citta(String citta) {
    this.citta = citta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("citta")
 @Size(min=1,max=35)
  public String getCitta() {
    return citta;
  }
  public void setCitta(String citta) {
    this.citta = citta;
  }

  /**
   **/
  public CustomerRicercaData dataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("dataNascita")

  public Date getDataNascita() {
    return dataNascita;
  }
  public void setDataNascita(Date dataNascita) {
    this.dataNascita = dataNascita;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerRicercaData customerRicercaData = (CustomerRicercaData) o;
    return Objects.equals(nominativo, customerRicercaData.nominativo) &&
        Objects.equals(nome, customerRicercaData.nome) &&
        Objects.equals(citta, customerRicercaData.citta) &&
        Objects.equals(dataNascita, customerRicercaData.dataNascita);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nominativo, nome, citta, dataNascita);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerRicercaData {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    nominativo: ").append(toIndentedString(nominativo)).append("\n");
    sb.append("    nome: ").append(toIndentedString(nome)).append("\n");
    sb.append("    citta: ").append(toIndentedString(citta)).append("\n");
    sb.append("    dataNascita: ").append(toIndentedString(dataNascita)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
