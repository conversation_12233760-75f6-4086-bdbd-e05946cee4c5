package it.sistinf.rest.cobol.model.dominio;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DominioBaseRequest   {

  private @Valid List<String> codice = new ArrayList<String>();

  /**
   **/
  public DominioBaseRequest codice(List<String> codice) {
    this.codice = codice;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codice")

  public List<String> getCodice() {
    return codice;
  }
  public void setCodice(List<String> codice) {
    this.codice = codice;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DominioBaseRequest dominioBaseRequest = (DominioBaseRequest) o;
    return Objects.equals(codice, dominioBaseRequest.codice);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codice);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DominioBaseRequest {\n");
    
    sb.append("    codice: ").append(toIndentedString(codice)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
