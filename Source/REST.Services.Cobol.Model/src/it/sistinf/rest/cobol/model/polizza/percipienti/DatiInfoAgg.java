package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DatiInfoAgg   {

  private @Valid String tipoRendita = null;

  private @Valid Long numAnniRenditaCerta = null;

  private @Valid BigDecimal percReversibilita = null;

  private @Valid String tipoLiquid = null;

  private @Valid BigDecimal percRenParziale = null;

  /**
   **/
  public DatiInfoAgg tipoRendita(String tipoRendita) {
    this.tipoRendita = tipoRendita;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoRendita")
  @NotNull

  public String getTipoRendita() {
    return tipoRendita;
  }
  public void setTipoRendita(String tipoRendita) {
    this.tipoRendita = tipoRendita;
  }

  /**
   **/
  public DatiInfoAgg numAnniRenditaCerta(Long numAnniRenditaCerta) {
    this.numAnniRenditaCerta = numAnniRenditaCerta;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("numAnniRenditaCerta")
  @NotNull

  public Long getNumAnniRenditaCerta() {
    return numAnniRenditaCerta;
  }
  public void setNumAnniRenditaCerta(Long numAnniRenditaCerta) {
    this.numAnniRenditaCerta = numAnniRenditaCerta;
  }

  /**
   **/
  public DatiInfoAgg percReversibilita(BigDecimal percReversibilita) {
    this.percReversibilita = percReversibilita;
    return this;
  }

  
  @ApiModelProperty(example = "200000000.01", value = "")
  @JsonProperty("percReversibilita")
  @NotNull

  public BigDecimal getPercReversibilita() {
    return percReversibilita;
  }
  public void setPercReversibilita(BigDecimal percReversibilita) {
    this.percReversibilita = percReversibilita;
  }

  /**
   **/
  public DatiInfoAgg tipoLiquid(String tipoLiquid) {
    this.tipoLiquid = tipoLiquid;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("tipoLiquid")
  @NotNull

  public String getTipoLiquid() {
    return tipoLiquid;
  }
  public void setTipoLiquid(String tipoLiquid) {
    this.tipoLiquid = tipoLiquid;
  }

  /**
   **/
  public DatiInfoAgg percRenParziale(BigDecimal percRenParziale) {
    this.percRenParziale = percRenParziale;
    return this;
  }

  
  @ApiModelProperty(example = "200000000.01", value = "")
  @JsonProperty("percRenParziale")
  @NotNull

  public BigDecimal getPercRenParziale() {
    return percRenParziale;
  }
  public void setPercRenParziale(BigDecimal percRenParziale) {
    this.percRenParziale = percRenParziale;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatiInfoAgg datiInfoAgg = (DatiInfoAgg) o;
    return Objects.equals(tipoRendita, datiInfoAgg.tipoRendita) &&
        Objects.equals(numAnniRenditaCerta, datiInfoAgg.numAnniRenditaCerta) &&
        Objects.equals(percReversibilita, datiInfoAgg.percReversibilita) &&
        Objects.equals(tipoLiquid, datiInfoAgg.tipoLiquid) &&
        Objects.equals(percRenParziale, datiInfoAgg.percRenParziale);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tipoRendita, numAnniRenditaCerta, percReversibilita, tipoLiquid, percRenParziale);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatiInfoAgg {\n");
    
    sb.append("    tipoRendita: ").append(toIndentedString(tipoRendita)).append("\n");
    sb.append("    numAnniRenditaCerta: ").append(toIndentedString(numAnniRenditaCerta)).append("\n");
    sb.append("    percReversibilita: ").append(toIndentedString(percReversibilita)).append("\n");
    sb.append("    tipoLiquid: ").append(toIndentedString(tipoLiquid)).append("\n");
    sb.append("    percRenParziale: ").append(toIndentedString(percRenParziale)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
