package it.sistinf.rest.cobol.model.base;

import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class WorkflowResponse extends EsitoResponse  {

  private @Valid Long idPrenotazione = null;

  /**
   **/
  public WorkflowResponse idPrenotazione(Long idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("idPrenotazione")

  public Long getIdPrenotazione() {
    return idPrenotazione;
  }
  public void setIdPrenotazione(Long idPrenotazione) {
    this.idPrenotazione = idPrenotazione;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WorkflowResponse workflowResponse = (WorkflowResponse) o;
    return Objects.equals(idPrenotazione, workflowResponse.idPrenotazione);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPrenotazione);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WorkflowResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    idPrenotazione: ").append(toIndentedString(idPrenotazione)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
