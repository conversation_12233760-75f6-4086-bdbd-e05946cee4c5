package it.sistinf.rest.cobol.model.polizza.anagrafica;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class SalvaSoggettoTerzoResponse   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public SalvaSoggettoTerzoResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")
  @NotNull

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public SalvaSoggettoTerzoResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")
  @NotNull

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SalvaSoggettoTerzoResponse salvaSoggettoTerzoResponse = (SalvaSoggettoTerzoResponse) o;
    return Objects.equals(headerCobolSrv, salvaSoggettoTerzoResponse.headerCobolSrv) &&
        Objects.equals(errori, salvaSoggettoTerzoResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SalvaSoggettoTerzoResponse {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
