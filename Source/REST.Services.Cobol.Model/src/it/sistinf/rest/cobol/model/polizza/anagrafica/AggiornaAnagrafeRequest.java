package it.sistinf.rest.cobol.model.polizza.anagrafica;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;
import it.sistinf.rest.cobol.model.proposta.common.AnagraficaRapportoCustomer;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;


public class AggiornaAnagrafeRequest   {

  private @Valid PolizzaInfo polizzaInfo = null;

  private @Valid AnagraficaRapportoCustomer anagraficaRapportoCustomer = null;

  private @Valid String codProdotto = null;

  private @Valid String username = null;

  /**
   **/
  public AggiornaAnagrafeRequest polizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("polizzaInfo")
  @NotNull

  public PolizzaInfo getPolizzaInfo() {
    return polizzaInfo;
  }
  public void setPolizzaInfo(PolizzaInfo polizzaInfo) {
    this.polizzaInfo = polizzaInfo;
  }

  /**
   **/
  public AggiornaAnagrafeRequest anagraficaRapportoCustomer(AnagraficaRapportoCustomer anagraficaRapportoCustomer) {
    this.anagraficaRapportoCustomer = anagraficaRapportoCustomer;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("anagraficaRapportoCustomer")
  @NotNull

  public AnagraficaRapportoCustomer getAnagraficaRapportoCustomer() {
    return anagraficaRapportoCustomer;
  }
  public void setAnagraficaRapportoCustomer(AnagraficaRapportoCustomer anagraficaRapportoCustomer) {
    this.anagraficaRapportoCustomer = anagraficaRapportoCustomer;
  }

  /**
   **/
  public AggiornaAnagrafeRequest codProdotto(String codProdotto) {
    this.codProdotto = codProdotto;
    return this;
  }

  
  @ApiModelProperty(example = "M101", required = true, value = "")
  @JsonProperty("codProdotto")
  @NotNull
 @Size(max=10)
  public String getCodProdotto() {
    return codProdotto;
  }
  public void setCodProdotto(String codProdotto) {
    this.codProdotto = codProdotto;
  }

  /**
   **/
  public AggiornaAnagrafeRequest username(String username) {
    this.username = username;
    return this;
  }

  
  @ApiModelProperty(required = true, value = "")
  @JsonProperty("username")
  @NotNull
 @Size(min=1,max=8)
  public String getUsername() {
    return username;
  }
  public void setUsername(String username) {
    this.username = username;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AggiornaAnagrafeRequest aggiornaAnagrafeRequest = (AggiornaAnagrafeRequest) o;
    return Objects.equals(polizzaInfo, aggiornaAnagrafeRequest.polizzaInfo) &&
        Objects.equals(anagraficaRapportoCustomer, aggiornaAnagrafeRequest.anagraficaRapportoCustomer) &&
        Objects.equals(codProdotto, aggiornaAnagrafeRequest.codProdotto) &&
        Objects.equals(username, aggiornaAnagrafeRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizzaInfo, anagraficaRapportoCustomer, codProdotto, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AggiornaAnagrafeRequest {\n");
    
    sb.append("    polizzaInfo: ").append(toIndentedString(polizzaInfo)).append("\n");
    sb.append("    anagraficaRapportoCustomer: ").append(toIndentedString(anagraficaRapportoCustomer)).append("\n");
    sb.append("    codProdotto: ").append(toIndentedString(codProdotto)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
