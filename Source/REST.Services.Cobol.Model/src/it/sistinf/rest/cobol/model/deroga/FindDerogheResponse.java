package it.sistinf.rest.cobol.model.deroga;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.dominio.ElementoDominio;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class FindDerogheResponse   {

  private @Valid List<ElementoDominio> elementiDominioDeroga = new ArrayList<ElementoDominio>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public FindDerogheResponse elementiDominioDeroga(List<ElementoDominio> elementiDominioDeroga) {
    this.elementiDominioDeroga = elementiDominioDeroga;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elementiDominioDeroga")

  public List<ElementoDominio> getElementiDominioDeroga() {
    return elementiDominioDeroga;
  }
  public void setElementiDominioDeroga(List<ElementoDominio> elementiDominioDeroga) {
    this.elementiDominioDeroga = elementiDominioDeroga;
  }

  /**
   **/
  public FindDerogheResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FindDerogheResponse findDerogheResponse = (FindDerogheResponse) o;
    return Objects.equals(elementiDominioDeroga, findDerogheResponse.elementiDominioDeroga) &&
        Objects.equals(errori, findDerogheResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(elementiDominioDeroga, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FindDerogheResponse {\n");
    
    sb.append("    elementiDominioDeroga: ").append(toIndentedString(elementiDominioDeroga)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
