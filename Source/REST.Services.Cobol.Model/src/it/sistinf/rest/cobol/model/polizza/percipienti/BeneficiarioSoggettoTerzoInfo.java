package it.sistinf.rest.cobol.model.polizza.percipienti;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.EsitoResponse;
import it.sistinf.rest.cobol.model.proposta.common.DatiValutazioneIntermediario;

import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;


public class BeneficiarioSoggettoTerzoInfo extends EsitoResponse  {

  private @Valid Long codiceCliente = null;

  private @Valid String ruolo = null;

  private @Valid DatiValutazioneIntermediario datiValutazioneIntermediario = null;

  /**
   **/
  public BeneficiarioSoggettoTerzoInfo codiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("codiceCliente")
  

  public Long getCodiceCliente() {
    return codiceCliente;
  }
  public void setCodiceCliente(Long codiceCliente) {
    this.codiceCliente = codiceCliente;
  }

  /**
   **/
  public BeneficiarioSoggettoTerzoInfo ruolo(String ruolo) {
    this.ruolo = ruolo;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("ruolo")
  

  public String getRuolo() {
    return ruolo;
  }
  public void setRuolo(String ruolo) {
    this.ruolo = ruolo;
  }

  /**
   **/
  public BeneficiarioSoggettoTerzoInfo datiValutazioneIntermediario(DatiValutazioneIntermediario datiValutazioneIntermediario) {
    this.datiValutazioneIntermediario = datiValutazioneIntermediario;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("datiValutazioneIntermediario")
  

  public DatiValutazioneIntermediario getDatiValutazioneIntermediario() {
    return datiValutazioneIntermediario;
  }
  public void setDatiValutazioneIntermediario(DatiValutazioneIntermediario datiValutazioneIntermediario) {
    this.datiValutazioneIntermediario = datiValutazioneIntermediario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BeneficiarioSoggettoTerzoInfo beneficiarioSoggettoTerzoInfo = (BeneficiarioSoggettoTerzoInfo) o;
    return Objects.equals(codiceCliente, beneficiarioSoggettoTerzoInfo.codiceCliente) &&
        Objects.equals(ruolo, beneficiarioSoggettoTerzoInfo.ruolo) &&
        Objects.equals(datiValutazioneIntermediario, beneficiarioSoggettoTerzoInfo.datiValutazioneIntermediario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codiceCliente, ruolo, datiValutazioneIntermediario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BeneficiarioSoggettoTerzoInfo {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    codiceCliente: ").append(toIndentedString(codiceCliente)).append("\n");
    sb.append("    ruolo: ").append(toIndentedString(ruolo)).append("\n");
    sb.append("    datiValutazioneIntermediario: ").append(toIndentedString(datiValutazioneIntermediario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
