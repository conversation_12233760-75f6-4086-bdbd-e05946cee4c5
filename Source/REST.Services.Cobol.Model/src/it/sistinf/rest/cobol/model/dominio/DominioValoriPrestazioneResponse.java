package it.sistinf.rest.cobol.model.dominio;

import io.swagger.annotations.ApiModelProperty;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import com.fasterxml.jackson.annotation.JsonProperty;


public class DominioValoriPrestazioneResponse   {

  private @Valid HeaderCobolSrv headerCobolSrv = null;

  private @Valid String categoria = null;

  private @Valid String codiceTariffa = null;

  private @Valid List<ElementoDominio> elementiDominio = new ArrayList<ElementoDominio>();

  private @Valid List<Errore> errori = new ArrayList<Errore>();

  /**
   **/
  public DominioValoriPrestazioneResponse headerCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("headerCobolSrv")

  public HeaderCobolSrv getHeaderCobolSrv() {
    return headerCobolSrv;
  }
  public void setHeaderCobolSrv(HeaderCobolSrv headerCobolSrv) {
    this.headerCobolSrv = headerCobolSrv;
  }

  /**
   **/
  public DominioValoriPrestazioneResponse categoria(String categoria) {
    this.categoria = categoria;
    return this;
  }

  
  @ApiModelProperty(example = "I", value = "")
  @JsonProperty("categoria")

  public String getCategoria() {
    return categoria;
  }
  public void setCategoria(String categoria) {
    this.categoria = categoria;
  }

  /**
   **/
  public DominioValoriPrestazioneResponse codiceTariffa(String codiceTariffa) {
    this.codiceTariffa = codiceTariffa;
    return this;
  }

  
  @ApiModelProperty(example = "U101", value = "")
  @JsonProperty("codiceTariffa")

  public String getCodiceTariffa() {
    return codiceTariffa;
  }
  public void setCodiceTariffa(String codiceTariffa) {
    this.codiceTariffa = codiceTariffa;
  }

  /**
   **/
  public DominioValoriPrestazioneResponse elementiDominio(List<ElementoDominio> elementiDominio) {
    this.elementiDominio = elementiDominio;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("elementiDominio")

  public List<ElementoDominio> getElementiDominio() {
    return elementiDominio;
  }
  public void setElementiDominio(List<ElementoDominio> elementiDominio) {
    this.elementiDominio = elementiDominio;
  }

  /**
   **/
  public DominioValoriPrestazioneResponse errori(List<Errore> errori) {
    this.errori = errori;
    return this;
  }

  
  @ApiModelProperty(value = "")
  @JsonProperty("errori")

  public List<Errore> getErrori() {
    return errori;
  }
  public void setErrori(List<Errore> errori) {
    this.errori = errori;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DominioValoriPrestazioneResponse dominioValoriPrestazioneResponse = (DominioValoriPrestazioneResponse) o;
    return Objects.equals(headerCobolSrv, dominioValoriPrestazioneResponse.headerCobolSrv) &&
        Objects.equals(categoria, dominioValoriPrestazioneResponse.categoria) &&
        Objects.equals(codiceTariffa, dominioValoriPrestazioneResponse.codiceTariffa) &&
        Objects.equals(elementiDominio, dominioValoriPrestazioneResponse.elementiDominio) &&
        Objects.equals(errori, dominioValoriPrestazioneResponse.errori);
  }

  @Override
  public int hashCode() {
    return Objects.hash(headerCobolSrv, categoria, codiceTariffa, elementiDominio, errori);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DominioValoriPrestazioneResponse {\n");
    
    sb.append("    headerCobolSrv: ").append(toIndentedString(headerCobolSrv)).append("\n");
    sb.append("    categoria: ").append(toIndentedString(categoria)).append("\n");
    sb.append("    codiceTariffa: ").append(toIndentedString(codiceTariffa)).append("\n");
    sb.append("    elementiDominio: ").append(toIndentedString(elementiDominio)).append("\n");
    sb.append("    errori: ").append(toIndentedString(errori)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
