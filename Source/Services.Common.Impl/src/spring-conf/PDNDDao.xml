<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
   		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
   		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd" default-lazy-init="true">

	<bean id="PDNDDao" class="it.sistinf.albedoweb.services.anagrafica.pdnd.service.persistency.PDNDDao" init-method="init" >    
		<property name="sqlMapClient">
			<ref bean="blueLifeSqlMapConfig" />
		</property>
		<property name="blueLifeLogger" ref="BlueLifeLogger" />
	</bean> 
 
</beans>