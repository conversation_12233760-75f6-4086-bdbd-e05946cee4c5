package it.sistinf.rest.model.polizza.utility;

import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ComponenteGarantitaDTO;
import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.ElencoFondiRiscattoDTO;
import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoDTO;
import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.FondiRiscattoErr;
import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoRequestDTO;
import it.sistinf.albedoweb.backoffice.storni.riscatto.dto.RiscattoResponseDTO;
import it.sistinf.albedoweb.backoffice.storni.scadenza.dto.ScadenzaRequestDTO;
import it.sistinf.albedoweb.common.reflection.ReflectionUtils;
import it.sistinf.albedoweb.common.util.DateUtils;
import it.sistinf.albedoweb.common.util.GenericUtils;
import it.sistinf.albedoweb.common.util.WsdlMapUtils;
import it.sistinf.albedoweb.customer.util.CustomerHelperProxy;
import it.sistinf.albedoweb.fondi.dto.FondiRivalutazioneEULRequestDTO;
import it.sistinf.albedoweb.fondi.dto.FondoDTO;
import it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioRequestDTO;
import it.sistinf.albedoweb.polizza.dto.PolizzaDettaglioResponseDTO;
import it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaDettaglioRequestDTO;
import it.sistinf.albedoweb.proposta.anagrafica.dto.AnagraficaDettaglioResponseDTO;
import it.sistinf.albedoweb.proposta.anagrafica.dto.DatiAnagraficheDTO;
import it.sistinf.albedoweb.proposta.datiidentificativi.dto.PropostaDatiIdentificativiRequestDTO;
import it.sistinf.albedoweb.proposta.dto.PropostaAttivazioneRequestDTO;
import it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoRequestDTO;
import it.sistinf.albedoweb.proposta.dto.PropostaRiepilogoResponseDTO;
import it.sistinf.albedoweb.proposta.unitatecniche.dto.DatiTecniciUTRequestDTO;
import it.sistinf.albedoweb.proposta.unitatecniche.dto.DatiTecniciUTResponseDTO;
import it.sistinf.albedoweb.proposta.unitatecniche.dto.ElencoFondoDTO;
import it.sistinf.albedoweb.services.abilitazione.utente.messages.SelectUtenteInfoResponse;
import it.sistinf.albedoweb.services.abilitazione.utente.types.UtenteInfo;
import it.sistinf.albedoweb.services.abilitazione.utente.types.UtenteReteVenditaInfo;
import it.sistinf.albedoweb.services.anagrafica.anagrafica.util.AnagraficaUtils;
import it.sistinf.albedoweb.services.anagrafica.customer.core.validators.ValidatorKeyEntry;
import it.sistinf.albedoweb.services.anagrafica.customer.messages.ControlloBloccoProfiloRischioRequest;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerDetailsInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerDettaglioInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerInteractionInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerOtherInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerUtenteLoginInfo;
import it.sistinf.albedoweb.services.anagrafica.customer.types.CustomerWithoutPolizza;
import it.sistinf.albedoweb.services.common.anagrafe.messages.SelectModPagamentoRequest;
import it.sistinf.albedoweb.services.common.anagrafe.messages.SelectModPagamentoResponse;
import it.sistinf.albedoweb.services.common.anagrafe.messages.SelectValutazioneIntermediarioRequest;
import it.sistinf.albedoweb.services.common.anagrafe.types.AltreInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.AnagraficaByPolizza;
import it.sistinf.albedoweb.services.common.anagrafe.types.AnagraficaRapportoCustomerInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.AnagraficheDiRapportoInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.CoordinateBancarieInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.DatiGeneraliInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.FigureAnagraficheRelazionateInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.ModPagamentoInfo;
import it.sistinf.albedoweb.services.common.anagrafe.types.ValutazioneIntermediarioInfo;
import it.sistinf.albedoweb.services.common.constants.SrvConstants;
import it.sistinf.albedoweb.services.common.types.CodeDescription;
import it.sistinf.albedoweb.services.common.types.WsdlMap;
import it.sistinf.albedoweb.services.continuous_monitoring.CmMonitorFromProxy;
import it.sistinf.albedoweb.services.continuous_monitoring.RuoloXFunzione;
import it.sistinf.albedoweb.services.deroghe.types.Dbtabd01;
import it.sistinf.albedoweb.services.deroghe.types.Dbtabd07;
import it.sistinf.albedoweb.services.domini.naw.messages.DominiResponse;
import it.sistinf.albedoweb.services.domini.naw.service.DominiInterface;
import it.sistinf.albedoweb.services.domini.naw.types.ElementoDominioInfo;
import it.sistinf.albedoweb.services.elis.commontype.types.PolicyKeyInfo;
import it.sistinf.albedoweb.services.elis.commontype.types.UserCredential;
import it.sistinf.albedoweb.services.elis.syncropolicy.messages.PrintRequest;
import it.sistinf.albedoweb.services.elis.syncropolicy.messages.PrintResponse;
import it.sistinf.albedoweb.services.elis.syncropolicy.types.TipoStampa;
import it.sistinf.albedoweb.services.polizza.commontypes.types.PolizzaInfoSimple;
import it.sistinf.albedoweb.services.polizza.commontypes.types.PolizzaInfoSimpleEstesa;
import it.sistinf.albedoweb.services.polizza.opzionicontrattuali.messages.ElencoAttivazioniDisattivazioniOpzioniContrattualiRequest;
import it.sistinf.albedoweb.services.polizza.opzionicontrattuali.messages.EstrazionePremioDisponibileTotaleRequest;
import it.sistinf.albedoweb.services.polizza.opzionicontrattuali.types.OpzioneContrattualeEventoInfo;
import it.sistinf.albedoweb.services.polizza.polizza.helper.PolizzaHelper;
import it.sistinf.albedoweb.services.polizza.polizza.messages.AnagraficaRapportoUpdateRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.DatiPrestazionePolizzaRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.DatiPrestazionePolizzaResponse;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectDettaglioQuietanzaRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectInquiryQuietanzeRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectNoteRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectNoteResponse;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizza59Request;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizza59Response;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizza822Request;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizzaEstesaInfoAggiuntiveRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizzaEstesaRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizzaEstesaResponse;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizzaRequest;
import it.sistinf.albedoweb.services.polizza.polizza.messages.SelectPolizzaResponse;
import it.sistinf.albedoweb.services.polizza.polizza.types.AnagraficaRapportoInfo;
import it.sistinf.albedoweb.services.polizza.polizza.types.FondoRivalutazioneInfo;
import it.sistinf.albedoweb.services.polizza.polizza.types.FondoUnitLinked;
import it.sistinf.albedoweb.services.polizza.polizza.types.LineaInvestimentoInfo;
import it.sistinf.albedoweb.services.polizza.polizza.types.OpzioniContrattualiInfo;
import it.sistinf.albedoweb.services.polizza.polizza.types.PianoSmobilizzoInfo;
import it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfoEstesa;
import it.sistinf.albedoweb.services.polizza.posizione.messages.SelectPosizioneIfrsRequest;
import it.sistinf.albedoweb.services.polizza.posizione.messages.SelectPosizioneIfrsResponse;
import it.sistinf.albedoweb.services.polizza.posizione.types.PosizionePolizzaIfrsInfo;
import it.sistinf.albedoweb.services.polizza.posizione.types.QuietanzaTitoloInfo;
import it.sistinf.albedoweb.services.polizza.storni.messages.CaricaParametriRecessoRequest;
import it.sistinf.albedoweb.services.polizza.storni.messages.CaricaStorniCommonRequest;
import it.sistinf.albedoweb.services.polizza.storni.messages.CaricaStorniCommonResponse;
import it.sistinf.albedoweb.services.polizza.storni.messages.ControllaParametriRecessoRequest;
import it.sistinf.albedoweb.services.polizza.storni.messages.ControllaParametriRecessoResponse;
import it.sistinf.albedoweb.services.polizza.storni.messages.ControllaSingolaFiguraResponse;
import it.sistinf.albedoweb.services.polizza.storni.messages.MonitoringAnagrafichePolizzaRequest;
import it.sistinf.albedoweb.services.polizza.storni.messages.SalvaRecessoRequest;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniBase;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniCommonInfo;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniDatiPagamento;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniFigureInfo;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniInfo;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoCompoGestSepInfo;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoCompoUnitInfo;
import it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoInfo;
import it.sistinf.albedoweb.services.portafoglio.rapporto.messages.InsertBlocchiWorkflowRequest;
import it.sistinf.albedoweb.services.portafoglio.rapporto.types.BlocchiWorkflowInfo;
import it.sistinf.albedoweb.services.portafoglio.rapporto.types.OrigineFondiInfo;
import it.sistinf.albedoweb.services.prenotazione.documentazione.types.DatiDocumentazioneInfo;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.InsertPrenotazPostVenditaRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.SelectOpzioneContrattualeRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.SelectOpzioneContrattualeResponse;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.SelectPrenotazPostVenditaRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.SelectVincoloPegnoRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.SelectVincoloPegnoResponse;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.UpdateOpzioniContrattualiRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.messages.UpdatePrenotazPostVenditaRequest;
import it.sistinf.albedoweb.services.prenotazione.postvendita.types.PrenotazionePostVenditaInfo;
import it.sistinf.albedoweb.services.prenotazione.postvendita.types.VincoloPegnoInfo;
import it.sistinf.albedoweb.services.prodotto.prodotto.messages.SelectAttiPortafoglioPerProdottoRequest;
import it.sistinf.albedoweb.services.prodotto.prodotto.messages.SelectAttiPortafoglioPerProdottoResponse;
import it.sistinf.albedoweb.services.prodotto.prodotto.messages.SelectTipoProdottoDettaglioGenericoResponse;
import it.sistinf.albedoweb.services.prodotto.prodotto.types.DominioProdotto;
import it.sistinf.albedoweb.services.prodotto.prodotto.types.ProdottoTipoDettaglio;
import it.sistinf.albedoweb.services.proposta.proposta.messages.ControllaVersamentoAggiuntivoRequest;
import it.sistinf.albedoweb.services.proposta.proposta.messages.SalvaVersamentoAggiuntivoRequest;
import it.sistinf.albedoweb.services.proposta.proposta.types.PropostaDatiRiepilogoInfo;
import it.sistinf.albedoweb.services.struttura.rete.messages.SelectLivello1Request;
import it.sistinf.albedoweb.services.struttura.rete.messages.SelectLivello1Response;
import it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteInterface;
import it.sistinf.albedoweb.services.struttura.rete.types.Livello1Info;
import it.sistinf.albedoweb.services.tabella.tracciato.messages.SelectRaggruppamentoUnitLinkRequest;
import it.sistinf.albedoweb.services.tabella.tracciato.messages.SelectRaggruppamentoUnitLinkResponse;
import it.sistinf.albedoweb.services.tabella.tracciato.types.RaggruppamentoUnitLink410Info;
import it.sistinf.albedoweb.services.tabella.tracciato.types.Vttab190Info;
import it.sistinf.albedoweb.unitaTecniche.dto.UTPosizioneDettaglioDTO;
import it.sistinf.albedoweb.workflow.enumeration.PrenotazioneTransitionEnum;
import it.sistinf.albedoweb.workflow.enumeration.ProcessTypeEnumeration;
import it.sistinf.albedoweb.workflow.mgr.exception.handlers.WorkflowMgrErrorCodes;
import it.sistinf.albedoweb.workflow.mgr.model.ExecutionContext;
import it.sistinf.common.ApplicationException;
import it.sistinf.common.InfrastructureException;
import it.sistinf.common.albedo.dto.RapportoDTO;
import it.sistinf.common.albedo.dto.SingolaSessioneAlbedoWebDTO;
import it.sistinf.common.albedo.login.dto.DisimpegnoPolizzaRequestDTO;
import it.sistinf.common.albedo.login.dto.LoginRequestDTO;
import it.sistinf.common.albedo.login.dto.LoginResponseDTO;
import it.sistinf.common.albedo.rapporti.dto.RapportiBaseRequestDTO;
import it.sistinf.common.dto.AreaErroreDTO;
import it.sistinf.common.dto.InteractionDTO;
import it.sistinf.common.utility.Utils;
import it.sistinf.rest.cobol.model.base.Errore;
import it.sistinf.rest.cobol.model.base.ErroreCampo;
import it.sistinf.rest.cobol.model.base.EsitoCobolResponse;
import it.sistinf.rest.cobol.model.base.EsitoResponse;
import it.sistinf.rest.cobol.model.base.HeaderCobolSrv;
import it.sistinf.rest.cobol.model.base.WorkflowCobolResponse;
import it.sistinf.rest.cobol.model.base.WorkflowResponse;
import it.sistinf.rest.cobol.model.customer.AllCustomerInfo;
import it.sistinf.rest.cobol.model.customer.FiguraAnagraficaRelazionataInfo;
import it.sistinf.rest.cobol.model.dominio.ElementoDominio;
import it.sistinf.rest.cobol.model.dominio.ElementoDominioCorrelato;
import it.sistinf.rest.cobol.model.polizza.CMLRequest;
import it.sistinf.rest.cobol.model.polizza.CMLResponse;
import it.sistinf.rest.cobol.model.polizza.ContinuousMonitoringRequest;
import it.sistinf.rest.cobol.model.polizza.ContinuousMonitoringResponse;
import it.sistinf.rest.cobol.model.polizza.DatiIdentificativiVA;
import it.sistinf.rest.cobol.model.polizza.DatiInputControllaVAEmesso;
import it.sistinf.rest.cobol.model.polizza.DatiPrincipaliVA;
import it.sistinf.rest.cobol.model.polizza.DatiProgrammatiVA;
import it.sistinf.rest.cobol.model.polizza.DatiSelezioneAttivitaVA;
import it.sistinf.rest.cobol.model.polizza.DatiTecniciVA;
import it.sistinf.rest.cobol.model.polizza.DatiVideoVA;
import it.sistinf.rest.cobol.model.polizza.DatiVideoVAEmesso;
import it.sistinf.rest.cobol.model.polizza.DatiWorkflowVA;
import it.sistinf.rest.cobol.model.polizza.DisimpegnaPolizzaRequest;
import it.sistinf.rest.cobol.model.polizza.DisimpegnaPolizzaResponse;
import it.sistinf.rest.cobol.model.polizza.DurataPagPremiVA;
import it.sistinf.rest.cobol.model.polizza.DurataVA;
import it.sistinf.rest.cobol.model.polizza.FondiVA;
import it.sistinf.rest.cobol.model.polizza.FondoVA;
import it.sistinf.rest.cobol.model.polizza.FunzioniPolizzaRequest;
import it.sistinf.rest.cobol.model.polizza.FunzioniPolizzaResponse;
import it.sistinf.rest.cobol.model.polizza.GrigliaCompatibilitaRequest;
import it.sistinf.rest.cobol.model.polizza.GrigliaCompatibilitaResponse;
import it.sistinf.rest.cobol.model.polizza.InitPropostaRequest;
import it.sistinf.rest.cobol.model.polizza.InitPropostaResponse;
import it.sistinf.rest.cobol.model.polizza.InizializzaRequest;
import it.sistinf.rest.cobol.model.polizza.InizializzaResponse;
import it.sistinf.rest.cobol.model.polizza.InizializzaVAEmessoRequest;
import it.sistinf.rest.cobol.model.polizza.InizializzaVAEmessoResponse;
import it.sistinf.rest.cobol.model.polizza.OpzioniFacoltaVA;
import it.sistinf.rest.cobol.model.polizza.SezioniPremiVA;
import it.sistinf.rest.cobol.model.polizza.anagrafica.AggiornaAnagrafeRequest;
import it.sistinf.rest.cobol.model.polizza.anagrafica.AggiornaAnagrafeResponse;
import it.sistinf.rest.cobol.model.polizza.anagrafica.DatiCustomer;
import it.sistinf.rest.cobol.model.polizza.anagrafica.SalvaSoggettoTerzoRequest;
import it.sistinf.rest.cobol.model.polizza.anagrafica.SalvaSoggettoTerzoResponse;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.BeneficiarioInfo;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.ControllaOpzContrattualiRequest;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.ControllaOpzContrattualiResponse;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.InoltraOpzContrattualiRequest;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.OpzioneContrattuale;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.OpzioneContrattuale.PeriodicitaEnum;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.OpzioniContrattualiRequest;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.OpzioniContrattualiResponse;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.SalvaOpzContrattualiRequest;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.SalvaOpzContrattualiResponse;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.StoricoOpzioniContrattualiRequest;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.StoricoOpzioniContrattualiResponse;
import it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo;
import it.sistinf.rest.cobol.model.polizza.percipienti.ControllaDatiRecessoPerInserimentoRequest;
import it.sistinf.rest.cobol.model.polizza.percipienti.ControllaDatiRecessoPerInserimentoResponse;
import it.sistinf.rest.cobol.model.polizza.percipienti.DatiPagamento;
import it.sistinf.rest.cobol.model.polizza.percipienti.ElencoPercipientiRequest;
import it.sistinf.rest.cobol.model.polizza.percipienti.ElencoPercipientiResponse;
import it.sistinf.rest.cobol.model.polizza.percipienti.InserisciPercipientiRequest;
import it.sistinf.rest.cobol.model.polizza.percipienti.InserisciPercipientiResponse;
import it.sistinf.rest.cobol.model.polizza.recesso.ControllaRecessoRequest;
import it.sistinf.rest.cobol.model.polizza.recesso.ControllaRecessoResponse;
import it.sistinf.rest.cobol.model.polizza.recesso.DatiSoggettoTerzoInfo;
import it.sistinf.rest.cobol.model.polizza.recesso.InizializzaRecessoRequest;
import it.sistinf.rest.cobol.model.polizza.recesso.InizializzaRecessoResponse;
import it.sistinf.rest.cobol.model.polizza.recesso.RecessoDatiParametri;
import it.sistinf.rest.cobol.model.polizza.recesso.RecessoDatiParametri.TipoOperazioneEnum;
import it.sistinf.rest.cobol.model.polizza.recesso.SalvaPrenotazioneRecessoRequest;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniCommon;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniFigure;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniRimborso;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniRimborsoCompoGestSep;
import it.sistinf.rest.cobol.model.polizza.recesso.StorniRimborsoCompoUnit;
import it.sistinf.rest.cobol.model.polizza.riscatto.DatiInputSalvaPrenotazioneRiscatto;
import it.sistinf.rest.cobol.model.polizza.riscatto.InoltraPrenotazRiscattoRequest;
import it.sistinf.rest.cobol.model.polizza.riscatto.InoltraPrenotazRiscattoResponse;
import it.sistinf.rest.cobol.model.polizza.riscatto.SalvaPrenotazRiscattoRequest;
import it.sistinf.rest.cobol.model.polizza.riscatto.SalvaPrenotazRiscattoResponse;
import it.sistinf.rest.cobol.model.polizza.scadenza.DatiInputSalvaPrenotazioneScadenza;
import it.sistinf.rest.cobol.model.polizza.scadenza.InoltraPrenotazioneScadenzaRequest;
import it.sistinf.rest.cobol.model.polizza.scadenza.InoltraPrenotazioneScadenzaResponse;
import it.sistinf.rest.cobol.model.polizza.scadenza.SalvaPrenotazioneScadenzaRequest;
import it.sistinf.rest.cobol.model.polizza.scadenza.SalvaPrenotazioneScadenzaResponse;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.ControllaSalvaVAEmessoRequest;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.ControllaVAEmessoRequest;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.ControllaVAEmessoResponse;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.DatiInputSalvaPrenotazioneVA;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.InoltraPrenotazVAEmessoRequest;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.InoltraPrenotazVAEmessoResponse;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.SalvaPrenotazVAEmessoRequest;
import it.sistinf.rest.cobol.model.polizza.vaEmesso.SalvaPrenotazVAEmessoResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.ComponenteGarantita;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DatiPrestazionePolizza;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DatiTecniciPolizza;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioPolizzaRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioPolizzaResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioQuietanzaInfo;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioQuietanzaInfo.TipoPremioEnum;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioQuietanzaRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.DettaglioQuietanzaResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.FondoRiscatto;
import it.sistinf.rest.cobol.model.portafoglio.polizza.FondoULPolizza;
import it.sistinf.rest.cobol.model.portafoglio.polizza.IFSR17Info;
import it.sistinf.rest.cobol.model.portafoglio.polizza.LineaInvestimentoPolizza;
import it.sistinf.rest.cobol.model.portafoglio.polizza.NotaInfo;
import it.sistinf.rest.cobol.model.portafoglio.polizza.NotePolizzaRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.NotePolizzaResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaSimpleEstesa;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PosizioneSinteticaRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.PosizioneSinteticaResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.Quietanza;
import it.sistinf.rest.cobol.model.portafoglio.polizza.QuietanzaInfo;
import it.sistinf.rest.cobol.model.portafoglio.polizza.QuietanzeRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.QuietanzeResponse;
import it.sistinf.rest.cobol.model.portafoglio.polizza.VincoloPegnoRequest;
import it.sistinf.rest.cobol.model.portafoglio.polizza.VincoloPegnoResponse;
import it.sistinf.rest.cobol.model.proposta.common.AnagraficaRapportoCustomer;
import it.sistinf.rest.cobol.model.proposta.common.AnagraficheDiRapporto;
import it.sistinf.rest.cobol.model.proposta.common.DatiAnagraficaByPolizza;
import it.sistinf.rest.cobol.model.proposta.common.DatiValutazioneIntermediario;
import it.sistinf.rest.cobol.model.proposta.common.PrimaRata;
import it.sistinf.rest.cobol.model.proposta.common.PropostaInfo;
import it.sistinf.rest.cobol.model.proposta.common.RapportoInfo;
import it.sistinf.rest.cobol.model.proposta.datiidentificativi.FondoUL;
import it.sistinf.rest.cobol.model.user.LoginRequest;
import it.sistinf.rest.cobol.model.user.ReteVendita;
import it.sistinf.rest.cobol.model.user.UtenteInfoResponse;
import it.sistinf.rest.common.constant.RestSrvConstants;
import it.sistinf.rest.model.anagrafica.utility.AnagraficaUtility;
import it.sistinf.rest.model.common.utility.CommonUtility;
import it.sistinf.rest.model.common.utility.InteractionUtility;
import it.sistinf.rest.model.common.utility.ServiceUtility;
import it.sistinf.rest.model.customer.utility.CustomerUtility;
import it.sistinf.rest.model.proposta.utility.PropostaUtility;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import zurich.workflow.mgr.extensions.context.PrenotazioneExecutionContext;
import zurich.workflow.mgr.extensions.dao.PrenotazioneTokenFindDTO;
import zurich.workflow.mgr.extensions.tokens.PrenotazioneToken;

public class PolizzaUtility {

	public static FunzioniPolizzaResponse valorizzafunzioniPolizzaResponseViolation(List<String> validatorViolations) {
		FunzioniPolizzaResponse funzioniPolizzaResponse = new FunzioniPolizzaResponse();
		funzioniPolizzaResponse.setErrori(ServiceUtility.valorizzaErrori(validatorViolations));
		return funzioniPolizzaResponse;
	}
	
	public static GrigliaCompatibilitaResponse valorizzaGrigliaCompatibilitaResponseViolation(List<String> validatorViolations) {
		GrigliaCompatibilitaResponse grigliaCompatibilitaResponse = new GrigliaCompatibilitaResponse();
		grigliaCompatibilitaResponse.setErrori(ServiceUtility.valorizzaErrori(validatorViolations));
		return grigliaCompatibilitaResponse;
	}
	
	public static SelectPolizzaEstesaRequest valorizzaSelectPolizzaEstesaRequest(PolizzaInfo polizzaInfo) {
		SelectPolizzaEstesaRequest selectPolizzaEstesaRequest = new SelectPolizzaEstesaRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaInfo);
		parametri.put(SrvConstants.PROVENIENZA_STAMPA, SrvConstants.PROVENIENZA_STAMPA);
		parametri.put(SrvConstants.ANAGRAFICHE_RAPPORTO, SrvConstants.ANAGRAFICHE_RAPPORTO);
		parametri.put(SrvConstants.FONDI, SrvConstants.FONDI); //per VTTABRRF e VTTABFUL Fondi predefiniti o liberi
		parametri.put(SrvConstants.SCOPO_NATURA_RAPPORTO, SrvConstants.SCOPO_NATURA_RAPPORTO); //per SCOPO NATURA RAPPORTO
		selectPolizzaEstesaRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return selectPolizzaEstesaRequest;
	}
	
	public static SelectPolizzaEstesaRequest valorizzaSelectAnagrafichePolizza(PolizzaInfo polizzaInfo) {
		SelectPolizzaEstesaRequest selectPolizzaEstesaRequest = new SelectPolizzaEstesaRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaInfo);
		parametri.put(SrvConstants.ANAGRAFICHE_RAPPORTO, SrvConstants.ANAGRAFICHE_RAPPORTO);
		selectPolizzaEstesaRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return selectPolizzaEstesaRequest;
	}	

	public static SelectPolizzaRequest valorizzaSelectPolizzaRequest(PolizzaInfo polizzaInfo, UtenteInfoResponse utenteInfoResponse) {
		SelectPolizzaRequest selectPolizzaRequest = new SelectPolizzaRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaInfo);
		parametri.put(SrvConstants.ANAGRAFICHE_RAPPORTO, SrvConstants.SI);
		selectPolizzaRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		CustomerDettaglioInfo cdi = valorizzaCustomerDettaglioInfo(utenteInfoResponse);
		selectPolizzaRequest.setCustomerRicercaDettaglio(cdi);
		return selectPolizzaRequest;
	}
	
	public static SelectPolizzaRequest valorizzaSelectPolizzaRequest(PolizzaInfo polizzaInfo) {
		SelectPolizzaRequest selectPolizzaRequest = new SelectPolizzaRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaInfo);
		selectPolizzaRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return selectPolizzaRequest;
	}
	public static SelectPolizzaEstesaInfoAggiuntiveRequest selectPolizzaInfoEstesa(PolizzaInfo polizzaInfo) {
		SelectPolizzaEstesaInfoAggiuntiveRequest polizzaRequest = new SelectPolizzaEstesaInfoAggiuntiveRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaInfo);
		parametri.put(SrvConstants.PROGR, "0");
		parametri.put(SrvConstants.STAMPA_APPENDICI_OPZIONI_CONTRATTUALI, SrvConstants.STAMPA_APPENDICI_OPZIONI_CONTRATTUALI);
		polizzaRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return polizzaRequest;
	}
	
	public static Map<String, String> creaMapChiavePolizza(PolizzaInfo polizzaInfo) {
		Map<String, String> parametri = new HashMap<String, String>();
		parametri.put(SrvConstants.CODSOC, polizzaInfo.getCodSocieta().toString());
		parametri.put(SrvConstants.CATEGORIA, GenericUtils.stringToStringNoNull(polizzaInfo.getNumeroCategoria()));
		parametri.put(SrvConstants.AGENZIA, GenericUtils.stringToStringNoNull(polizzaInfo.getCodAgenzia()));
		parametri.put(SrvConstants.NUM_COLL, polizzaInfo.getNumeroCollettiva().toString());
		parametri.put(SrvConstants.NUM_POLIZZA, polizzaInfo.getNumeroPolizza().toString());
		return parametri;
	}
	
	private static Map<String, String> creaMapChiavePolizza(Quietanza quietanza) {
		Map<String, String> parametri = new HashMap<String, String>();
		parametri.put(SrvConstants.CODSOC, quietanza.getCodSocieta().toString());
		parametri.put(SrvConstants.CATEGORIA, GenericUtils.stringToStringNoNull(quietanza.getNumeroCategoria()));
		parametri.put(SrvConstants.AGENZIA, GenericUtils.stringToStringNoNull(quietanza.getCodAgenzia()));
		parametri.put(SrvConstants.NUM_COLL, quietanza.getNumeroCollettiva().toString());
		parametri.put(SrvConstants.NUM_POLIZZA, quietanza.getNumeroPolizza().toString());
		return parametri;
	}

	public static DettaglioPolizzaResponse valorizzaDettaglioPolizzaResponse(SelectPolizzaEstesaResponse polizzaResponse, SelectRaggruppamentoUnitLinkResponse rulResponse, SelectPosizioneIfrsResponse ifrsResponse, DatiPrestazionePolizzaResponse prestazioneResponse, SelectModPagamentoResponse modPagamentoResponse, String descrStato, String descrVariazione, String ulFondo) {
		DettaglioPolizzaResponse dettaglioPolizzaResponse = new DettaglioPolizzaResponse();
		dettaglioPolizzaResponse.setAnagraficaRapporto(valorizzaAnagraficheRapporto(polizzaResponse));
		dettaglioPolizzaResponse.setLineaInvestimentoPolizza(valorizzaLineaInvestimento(rulResponse, ifrsResponse, ulFondo));
		dettaglioPolizzaResponse.setDatiPrestazionePolizza(valorizzaDatiPrestazione(prestazioneResponse));
		dettaglioPolizzaResponse.setDatiTecniciPolizza(valorizzaDatiTecnici(polizzaResponse, descrStato, descrVariazione));
		dettaglioPolizzaResponse.setOrigineFondiInfoList(PropostaUtility.valorizzaOrigineFondiInfoList(polizzaResponse.getPolizzeInfoSelectEstesa().get(0).getDatiOrigineFondi()));
		dettaglioPolizzaResponse.setScopoNaturaRapportoInfo(PropostaUtility.valorizzaScopoNaturaRapportoInfo(polizzaResponse.getPolizzeInfoSelectEstesa().get(0).getDatiScopoNaturaRapporto()));		
		dettaglioPolizzaResponse.setModPagamentoInfoList(PropostaUtility.valorizzaModalitaPagamentoInfo(modPagamentoResponse.getModPagamentoInfoSelect()));
		dettaglioPolizzaResponse.setPropostaInfo(valorizzaPropostaInfo(polizzaResponse));
		return dettaglioPolizzaResponse;
	}

	private static PropostaInfo valorizzaPropostaInfo(SelectPolizzaEstesaResponse polizzaResponse) {
		PropostaInfo chiaveProposta = new PropostaInfo();
		PolizzaInfoEstesa polizza = polizzaResponse.getPolizzeInfoSelectEstesa().get(0);
	    it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo = polizza.getPolizza();
		chiaveProposta.setCodSocieta(polizza.getCodSocieta());
		chiaveProposta.setNumeroCategoria(GenericUtils.stringToString(polizza.getNumeroCategoria()));
		chiaveProposta.setCodAgenzia(GenericUtils.stringToStringNoNull(polizzaInfo.getAgenziaProp()));
		chiaveProposta.setNumeroPropostaCollettiva(polizza.getNumeroCollettiva());
		chiaveProposta.setNumeroProposta(polizza.getNumeroProposta());
		return chiaveProposta;
	}

	private static DatiTecniciPolizza valorizzaDatiTecnici(SelectPolizzaEstesaResponse polizzaResponse, String descrStato, String descrVariazione) {
		DatiTecniciPolizza datiTecnici = new DatiTecniciPolizza();
		PolizzaInfoEstesa polizza = polizzaResponse.getPolizzeInfoSelectEstesa().get(0);
		datiTecnici.setConvenzione(GenericUtils.stringToStringNoNull(polizza.getConvenzione()));
		datiTecnici.setDeroga(GenericUtils.stringToStringNoNull(polizza.getCodDeroga()));
		datiTecnici.setClassePremio(GenericUtils.stringToString(polizza.getClassePremio()));
		it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo pInfo = polizza.getPolizza();
		datiTecnici.setDecorrenza(DateUtils.longToDate(pInfo.getDataDecorrenza()));
		datiTecnici.setEmissione(DateUtils.longToDate(pInfo.getDataEmissione()));
		datiTecnici.setScadenza(DateUtils.longToDate(pInfo.getDataScadenza()));
		datiTecnici.setDurata(GenericUtils.doubleToBigDecimal(pInfo.getDurata()));
		datiTecnici.setStato(GenericUtils.stringToStringNoNull(pInfo.getStato()));
		datiTecnici.setDescStato(descrStato);
		datiTecnici.setCausale(GenericUtils.stringToStringNoNull(pInfo.getCausale()));
		if(!"".equals(descrVariazione)) {
			datiTecnici.setDescVariazione(descrVariazione);
			datiTecnici.setCodiceVariazione(GenericUtils.stringToStringNoNull(polizza.getPolizza().getLiberoA4()));
		}
		datiTecnici.setTipoProdotto(polizza.getTipologiaProdotto());
		return datiTecnici;
	}

	private static DatiPrestazionePolizza valorizzaDatiPrestazione(DatiPrestazionePolizzaResponse prestazioneResponse) {
		DatiPrestazionePolizza datiPrestazione = new DatiPrestazionePolizza();
		FondoRivalutazioneInfo fondoRival = null;
		if (prestazioneResponse.getDatiPrestazioneGS() != null) {
			fondoRival = prestazioneResponse.getDatiPrestazioneGS().getFondoRivalutazione();
			datiPrestazione.setCodice(fondoRival.getFondoRivalutazione());
			datiPrestazione.setDescrizione(GenericUtils.stringToString(fondoRival.getDescrFondoRival()));
			datiPrestazione.setRivalutazione(new Date(fondoRival.getDataRivalutazione()));
			datiPrestazione.setPrestazione(fondoRival.getValorePrestazioneGS());
			datiPrestazione.setPercentualeAllocRival(prestazioneResponse.getDatiPrestazioneGS().getPercAllocazioneGS());
		}
		if (prestazioneResponse.getDatiPrestazioneUL() != null) {
			datiPrestazione.setElencoFondi(valorizzaElencoFondi(prestazioneResponse.getDatiPrestazioneUL().getListaFondiUL()));
		}		
		return datiPrestazione;
	}

	private static List<FondoULPolizza> valorizzaElencoFondi(List<FondoUnitLinked> listaFondiULNaw) {
		List<FondoULPolizza> listaFondiULRest = new ArrayList<FondoULPolizza>();
		for(FondoUnitLinked fondoNaw : listaFondiULNaw) {
			FondoULPolizza fondoRest = valorizzaFondo(fondoNaw);
			listaFondiULRest.add(fondoRest);
		}
		return listaFondiULRest;
	}

	private static FondoULPolizza valorizzaFondo(FondoUnitLinked fondoNaw) {
		LineaInvestimentoInfo lineaInvestimento = fondoNaw.getLineaInvestimento();
		FondoULPolizza fondoRest = new FondoULPolizza();		
		fondoRest.setCodice(Integer.valueOf(GenericUtils.stringToString(lineaInvestimento.getCodiceLineaInv())));
		fondoRest.setDescrizione(GenericUtils.stringToString(lineaInvestimento.getDescrLineaInv()));
		fondoRest.setValoreQuota(lineaInvestimento.getValoreQuote());
		fondoRest.setNumQuote(lineaInvestimento.getNumeroQuote());
		fondoRest.setControvalore(lineaInvestimento.getValorePrestazioneUL());
		fondoRest.setPercInizio(fondoNaw.getPercInvestimentoUL());
		fondoRest.setPercAttuale(fondoNaw.getPercControvaloreUL());
		long dataInvestimento = lineaInvestimento.getDataValQuote();
		if(it.sistinf.albedoweb.common.util.DateUtils.DATE_01_01_0001_AS_LONG != dataInvestimento) {
			fondoRest.setDataInvestimento(DateUtils.longToDate(lineaInvestimento.getDataValQuote()));
		}
		fondoRest.setTipologiaFondo(lineaInvestimento.getTipologiaFondo());
		return fondoRest;
	}
	
	private static LineaInvestimentoPolizza valorizzaLineaInvestimento(SelectRaggruppamentoUnitLinkResponse rulResponse, SelectPosizioneIfrsResponse ifrsResponse, String ulFondo) {
		LineaInvestimentoPolizza lineaInvestimento = new LineaInvestimentoPolizza();
		if(rulResponse != null) {
			RaggruppamentoUnitLink410Info linInv = rulResponse.getRaggruUnitLink410Info().get(0);
			lineaInvestimento.setCodiceLineaInvestimento(GenericUtils.stringToString(ulFondo));
			lineaInvestimento.setLineaInvestimento(GenericUtils.stringToString(linInv.getDesRaggrUL()));
			lineaInvestimento.setTipoLineaInvestimento(GenericUtils.stringToStringNoNull(linInv.getPredefinito()));
		}
//TODO: Premi
		lineaInvestimento.setListaIFSR17(valorizzaListaIFSR(ifrsResponse));
		return lineaInvestimento;
	}

	private static List<IFSR17Info> valorizzaListaIFSR(SelectPosizioneIfrsResponse ifrsResponse) {
		List<IFSR17Info> listaIFSRRest = new ArrayList<IFSR17Info>();
		List<PosizionePolizzaIfrsInfo> listaIFSRNaw = ifrsResponse.getPosizioneIfrsInfoSelect();
		for(PosizionePolizzaIfrsInfo ifsrNaw : listaIFSRNaw) {
			IFSR17Info ifsrRest = new IFSR17Info();
			ifsrRest.setCodiceUT(GenericUtils.stringToString(ifsrNaw.getCodiceUt()));
			ifsrRest.setDescrizioneUT(GenericUtils.stringToString(ifsrNaw.getDescrizioneUt()));
			String portfolio = GenericUtils.stringToString(ifsrNaw.getPortfolioGrouping())+" "+GenericUtils.stringToString(ifsrNaw.getDescPortfolioGrouping());
			ifsrRest.setPortfolioGrouping(portfolio);
			String profitability = GenericUtils.stringToString(ifsrNaw.getProfitabilityGrouping())+" "+GenericUtils.stringToString(ifsrNaw.getDescProfitabilityGrouping());
			ifsrRest.setProfitabilityGrouping(profitability);
			ifsrRest.setUnderwritingYear(ifsrNaw.getUnderwritingYear());
			listaIFSRRest.add(ifsrRest);
		}
		return listaIFSRRest;
	}

	private static AnagraficheDiRapporto valorizzaAnagraficheRapporto(SelectPolizzaEstesaResponse polizzaResponse) {
		AnagraficheDiRapporto anagraficheDiRapporto = new AnagraficheDiRapporto();
		if((polizzaResponse != null) && (!polizzaResponse.getPolizzeInfoSelectEstesa().isEmpty())) {
			List<AnagraficaRapportoCustomer> anagraficaPrincipaleRapporto = new ArrayList<AnagraficaRapportoCustomer>();
			List<AnagraficaRapportoCustomerInfo> anagraficaPrincipale = polizzaResponse.getPolizzeInfoSelectEstesa().get(0).getAnagraficaPrincipaleRapporto();
			for(AnagraficaRapportoCustomerInfo anagraficaPrincipaleInfoResponse : anagraficaPrincipale) {
				AnagraficaRapportoCustomer anagraficaRapportoCustomer = new AnagraficaRapportoCustomer();
//				CustomerInfo customerInfo = PropostaUtility.valorizzaCustomerInfoForAnagraficheDiRapporto(anagraficaPrincipaleInfoResponse);
//				anagraficaRapportoCustomer.setCustomerInfo(customerInfo);
				AllCustomerInfo allCustomerInfo = CustomerUtility.valorizzaAllCustomerInfo(anagraficaPrincipaleInfoResponse);
				anagraficaRapportoCustomer.setAllCustomerInfo(allCustomerInfo);
				DatiAnagraficaByPolizza anagraficaByPolizza = PropostaUtility.valorizzaAnagraficaByPolizza(anagraficaPrincipaleInfoResponse.getAnagraficaByPolizza(), allCustomerInfo.getCodiceCliente().intValue());
				anagraficaRapportoCustomer.setAnagraficaByPolizza(anagraficaByPolizza);
				anagraficaPrincipaleRapporto.add(anagraficaRapportoCustomer);
				PropostaUtility.gestioneAnagraficeRelazionate(anagraficaPrincipaleRapporto, anagraficaPrincipaleInfoResponse, true);
			}
			anagraficheDiRapporto.getAnagraficaPrincipaleRapporto().addAll(anagraficaPrincipaleRapporto);
		}
		return anagraficheDiRapporto;
	}

	public static SelectRaggruppamentoUnitLinkRequest valorizzaSelectRaggruppamentoUnitLinkRequest(PolizzaInfoEstesa polizza) {
		SelectRaggruppamentoUnitLinkRequest rulRequest = new SelectRaggruppamentoUnitLinkRequest();
		Vttab190Info vttab190Info = new Vttab190Info();
		vttab190Info.setCodSocieta(polizza.getCodSocieta());
		vttab190Info.setCategoria(SrvConstants.SPACE);
		vttab190Info.setChiave1(GenericUtils.stringToStringNoNull(polizza.getUlFondo()));
		rulRequest.setVttab190(vttab190Info);
		return rulRequest;
	}

	public static SelectPosizioneIfrsRequest valorizzaSelectPosizioneIfrsRequest(DettaglioPolizzaRequest polizzaRequest) {
		SelectPosizioneIfrsRequest ifrsRequest = new SelectPosizioneIfrsRequest();
		Map<String, String> parametri = creaMapChiavePolizza(polizzaRequest.getPolizzaInfo());
		ifrsRequest.setPosizioneIfrsInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return ifrsRequest;
	}

	public static DatiPrestazionePolizzaRequest valorizzaPrestazioneRequest(it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo) {
		DatiPrestazionePolizzaRequest prestazioneRequest = new DatiPrestazionePolizzaRequest();
		prestazioneRequest.setPolizzaInfoSimple(polizzaInfo.getPolizzaInfoSimple());
		prestazioneRequest.setPolizzaInfo(polizzaInfo);
		return prestazioneRequest;
	}

	public static Dbtabd01 valorizzaPerDescDeroga(String codDeroga) {
		Dbtabd01 criteria = new Dbtabd01();
		criteria.setCodsoc(SrvConstants.SOCIETA_GENERICA_INT);
		criteria.setCdDeroga(GenericUtils.stringToString(codDeroga));
		criteria.setSegregazioneDecodificata("");
		criteria.setIsActive(SrvConstants.SI);
		return criteria;
	}

	public static String trovaDescDeroga(List<Dbtabd01> listaDeroghe, String codDeroga) {
		String descDeroga = "";
		for (Dbtabd01 deroga : listaDeroghe) {
			if(GenericUtils.stringToStringNoNull(deroga.getCdDeroga()).equals(codDeroga)) {
				descDeroga = GenericUtils.stringToStringNoNull(deroga.getDeDerogaBrv());
				break;
			}
		}
		return descDeroga;
	}

	public static Dbtabd07 valorizzaPerDescConvenzione(String codConvenzione) {
		Dbtabd07 criteria = new Dbtabd07();
		criteria.setCodsoc(SrvConstants.SOCIETA_GENERICA_INT);
		criteria.setCdConve(GenericUtils.stringToString(codConvenzione));
		criteria.setSegregazioneDecodificata("");
		criteria.setIsActive(SrvConstants.SI);
		return criteria;
	}

	public static String trovaDescConvenzione(List<Dbtabd07> listaConvenzioni, String codConvenzione) {
		String descConv = "";
		for (Dbtabd07 convenzione : listaConvenzioni) {
			if(GenericUtils.stringToStringNoNull(convenzione.getCdConve()).equals(codConvenzione)) {
				descConv = GenericUtils.stringToStringNoNull(convenzione.getDeConveBrv());
				break;
			}
		}
		return descConv;
	}

	public static SelectInquiryQuietanzeRequest valorizzaSelectInquiryQuietanzeRequest(QuietanzeRequest quietanzeRequest) {
		SelectInquiryQuietanzeRequest siqRequest = new SelectInquiryQuietanzeRequest();
		PolizzaInfo polizza = quietanzeRequest.getPolizzaInfo();
		Map<String,String> aMap = creaMapChiavePolizza(polizza);
		String collettiva = GenericUtils.isCategoriaCollettiva(GenericUtils.stringToString(polizza.getNumeroCategoria())) ? SrvConstants.SI : SrvConstants.NO;
		aMap.put(SrvConstants.CATEGORIA_COLLETTIVA, collettiva);
		aMap.put(SrvConstants.FENDAC, SrvConstants.NO);
		aMap.put(SrvConstants.INQUIRY_QUIETANZE, SrvConstants.SI);
		siqRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(aMap));
		return siqRequest;
	}

	public static List<QuietanzaTitoloInfo> evaluateQuietanzeRealiVirtuali(List<QuietanzaTitoloInfo> elencoQuietanze) {		
		for (Iterator<QuietanzaTitoloInfo> iterator = elencoQuietanze.iterator(); iterator.hasNext();) {
			QuietanzaTitoloInfo quietanzaTitoloInfo = iterator.next();
			if(SrvConstants.TIPO_QUIETANZA_VIRTUALE.equals(quietanzaTitoloInfo.getCategoria())) {
				quietanzaTitoloInfo.setImpIncasso(new Double(0)); // 
			}			
		}
		return elencoQuietanze;
	}

	public static QuietanzeResponse valorizzaQuietanzeResponse(List<QuietanzaTitoloInfo> elencoQuietanze) {
		QuietanzeResponse quietanzeResponse = new QuietanzeResponse();
		List<QuietanzaInfo> listaQuietanze = new ArrayList<QuietanzaInfo>();
		for(QuietanzaTitoloInfo qtInfo : elencoQuietanze) {
			QuietanzaInfo qInfo = valorizzaQuietanzaInfo(qtInfo);
			listaQuietanze.add(qInfo);
		}
		quietanzeResponse.setListaQuietanze(listaQuietanze);
		return quietanzeResponse;
	}

	private static QuietanzaInfo valorizzaQuietanzaInfo(QuietanzaTitoloInfo qtInfo) {
		QuietanzaInfo qInfo = new QuietanzaInfo();
		qInfo.setScadenza(new Date(qtInfo.getDataScadQuiet()));
		qInfo.setPremioRata(BigDecimal.valueOf(qtInfo.getPremioRata()));
		qInfo.setIncassato(BigDecimal.valueOf(qtInfo.getImpIncasso()));
		qInfo.setTipo(GenericUtils.stringToStringNoNull(qtInfo.getCategoria()));
		qInfo.setGiorniRitardoPagamento(GenericUtils.stringToInteger(qtInfo.getDifferenzaGG()));
		qInfo.setStornata(GenericUtils.stringToBoolean(qtInfo.getFlStornataS()));
		qInfo.setPosizione(qtInfo.getPosizioneInfoSimple().getNumeroPosizione());
		qInfo.setDupkey(qtInfo.getProgressivo());
		qInfo.setAppendice(qtInfo.getAppendice().intValue());
		return qInfo;
	}

	public static SelectNoteRequest valorizzaSelectNoteRequest(NotePolizzaRequest noteRequest, SelectUtenteInfoResponse userResponse) {
		SelectNoteRequest selectNoteRequest = new SelectNoteRequest();
		PolizzaInfo polizza = noteRequest.getPolizzaInfo();
		Map<String,String> aMap = creaMapChiavePolizza(polizza);
		List<UtenteInfo> listaUtenti = userResponse.getUtenteInfo();
		if(!listaUtenti.isEmpty()) {
			String direzione = listaUtenti.get(0).getUteDirezione();
			if (SrvConstants.SI.equals(direzione)){
				aMap.put("FLG_DIREZIONE", SrvConstants.SI);
			}
		}
		selectNoteRequest.setNoteMap(WsdlMapUtils.valorizzaWsdlMapFor(aMap));
		return selectNoteRequest;
	}

	public static NotePolizzaResponse valorizzaNoteResponse(SelectNoteResponse snResponse) {
		NotePolizzaResponse noteResponse = new NotePolizzaResponse();
		List<NotaInfo> listaNote = new ArrayList<NotaInfo>();
		for(it.sistinf.albedoweb.services.polizza.polizza.types.NotaInfo notaNaw : snResponse.getNote()) {
			NotaInfo notaRest = new NotaInfo();
			notaRest.setData(new Date(notaNaw.getDataModifica()));
			
			notaRest.setOggetto(GenericUtils.convertiCaratteriSpeciali(GenericUtils.stringToStringNoNull(notaNaw.getOggetto())));
			notaRest.setTesto(GenericUtils.convertiCaratteriSpeciali(GenericUtils.stringToStringNoNull(notaNaw.getTesto())));
			listaNote.add(notaRest);
		}
		noteResponse.setListaNote(listaNote);
		return noteResponse;
	}

	public static List<Errore> valorizzaErroriDettaglioPolizza() {
		List<Errore> errori = new ArrayList<Errore>();
		Errore errore = InteractionUtility.valorizzaErrore(RestSrvConstants.VALERR0001, RestSrvConstants.ERRORE_BLOCCANTE, "Polizza non trovata");
		errori.add(errore);
		return errori;
	}

	public static PrintRequest valorizzaPrintRequest(PosizioneSinteticaRequest request) {
		PrintRequest printRequest = new PrintRequest();
		UserCredential user = new UserCredential();
		user.setUsername(GenericUtils.stringToString(request.getUtente()));
		PolicyKeyInfo chiavePolizza = valorizzaChiavePolizza(request.getPolizzaInfo());
		printRequest.setTipoStampa(TipoStampa.SITUAZIONE_SINTETICA);
		printRequest.setCanale(SrvConstants.LDT);
		printRequest.setUser(user);
		printRequest.setPolicyKeyInfo(chiavePolizza);
		return printRequest;
	}

	private static PolicyKeyInfo valorizzaChiavePolizza(PolizzaInfo polizzaInfo) {
		PolicyKeyInfo chiavePolizza = new PolicyKeyInfo();
//		chiavePolizza.setPolicyCompany(GenericUtils.trascodificaSocieta(""+polizzaInfo.getCodSocieta()));
		chiavePolizza.setPolicyCompany(""+polizzaInfo.getCodSocieta());
		chiavePolizza.setPolicyAgency(polizzaInfo.getCodAgenzia());
//		chiavePolizza.setCategory(GenericUtils.getCategoriaFrom(GenericUtils.stringToString(polizzaInfo.getNumeroCategoria())));
		chiavePolizza.setCategory(GenericUtils.stringToString(polizzaInfo.getNumeroCategoria()));
		chiavePolizza.setGroupNum(""+polizzaInfo.getNumeroCollettiva());
		chiavePolizza.setPolicyNum(""+polizzaInfo.getNumeroPolizza());
		return chiavePolizza;
	}

	public static PosizioneSinteticaResponse valorizzaPosizioneSinteticaResponse(PrintResponse printResponse) {
		PosizioneSinteticaResponse posizioneSinteticaResponse = new PosizioneSinteticaResponse();
		posizioneSinteticaResponse.setNomeFile(GenericUtils.stringToString(printResponse.getNomeFile()));
		posizioneSinteticaResponse.setPdfData(printResponse.getPdfData());
		return posizioneSinteticaResponse;
	}

	public static SelectModPagamentoRequest valorizzaModPagamentoRequest(it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo) {
		SelectModPagamentoRequest modPagamentoRequest = new SelectModPagamentoRequest();
		Map<String,String> aMap = new HashMap<String, String>();
		aMap.put(SrvConstants.CODSOC, ""+polizzaInfo.getCodSocieta());
		aMap.put(SrvConstants.CATEGORIA, GenericUtils.stringToString(polizzaInfo.getNumeroCategoria()));
		aMap.put(SrvConstants.AGENZIA, GenericUtils.stringToStringNoNull(polizzaInfo.getCodAgenzia()));
		aMap.put(SrvConstants.NUM_COLL, ""+polizzaInfo.getNumeroCollettiva());
		aMap.put(SrvConstants.NUM_POLIZZA, ""+polizzaInfo.getNumeroPolizza());
		aMap.put(SrvConstants.FLG_PROP_POL, SrvConstants.TIPO_RAPP_RID_POLIZZA);
		modPagamentoRequest.setInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(aMap));
		return modPagamentoRequest;
	}

	public static SelectVincoloPegnoRequest valorizzaSelectVincoloPegnoRequest(VincoloPegnoRequest request) {
		SelectVincoloPegnoRequest vpRequest = new SelectVincoloPegnoRequest();
		vpRequest.setVincoloPegnoInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(creaMapChiavePolizza(request.getPolizzaInfo())));
		return vpRequest;
	}

	public static VincoloPegnoResponse valorizzaVincoloPegnoResponse(SelectVincoloPegnoResponse response) {
		VincoloPegnoResponse vpResponse = new VincoloPegnoResponse();
		if(response!=null && !response.getVincoliPegni().isEmpty()) {
			VincoloPegnoInfo vpInfo = response.getVincoliPegni().get(0);
			vpResponse.setVincoloPegnoInfo(valorizzaVincoloPegnoInfo(vpInfo));
		}
		return vpResponse;
	}

	private static it.sistinf.rest.cobol.model.portafoglio.polizza.VincoloPegnoInfo valorizzaVincoloPegnoInfo(VincoloPegnoInfo vpInfoNaw) {
		it.sistinf.rest.cobol.model.portafoglio.polizza.VincoloPegnoInfo vpInfoRest = new it.sistinf.rest.cobol.model.portafoglio.polizza.VincoloPegnoInfo();
		vpInfoRest.setDataRichiesta(DateUtils.longToDate(vpInfoNaw.getDataRichiesta()));
		vpInfoRest.setTipoVincolo(GenericUtils.stringToString(vpInfoNaw.getTipoVincolo()));
		vpInfoRest.setDataInizio(DateUtils.longToDate(vpInfoNaw.getDataInizio()));
		vpInfoRest.setDataFine(DateUtils.longToDate(vpInfoNaw.getDataFine()));
		vpInfoRest.setMotivazione(GenericUtils.stringToString(vpInfoNaw.getMotivoVincolo()));
		vpInfoRest.setMaxVincolabile(GenericUtils.doubleToBigDecimal(vpInfoNaw.getImportoMaxVincolabile()));
		vpInfoRest.setImportoVincolo(GenericUtils.doubleToBigDecimal(vpInfoNaw.getImportoVincolo()));
		return vpInfoRest;
	}

	public static SelectDettaglioQuietanzaRequest valorizzaSelectDettaglioQuietanzaRequest(DettaglioQuietanzaRequest request) {
		SelectDettaglioQuietanzaRequest dqRequest = new SelectDettaglioQuietanzaRequest();
		Quietanza quietanza = request.getQuietanza();
		Map<String,String> aMap = creaMapChiavePolizza(quietanza);
		aMap.put(SrvConstants.FENDAC, SrvConstants.NO);
		aMap.put(SrvConstants.SOLO_QUIETANZA, SrvConstants.SI);
		aMap.put(SrvConstants.NUM_POSIZIONE, ""+quietanza.getPosizione());
		aMap.put(SrvConstants.DUPKEY, ""+quietanza.getDupkey());
		aMap.put("DATA_SCAD_QUIET", ""+quietanza.getScadenza().getTime());
		dqRequest.setPolizzaInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(aMap));
		return dqRequest;
	}

	public static DettaglioQuietanzaResponse valorizzaDettaglioQuietanzaResponse(QuietanzaTitoloInfo qtInfo) throws Exception {
		DettaglioQuietanzaResponse dettaglioQuietanzaResponse = new DettaglioQuietanzaResponse();
		DettaglioQuietanzaInfo dqInfo = new DettaglioQuietanzaInfo();
		if(SrvConstants.TIPO_QUIETANZA_VIRTUALE.equals(qtInfo.getCategoria())) {
			qtInfo.setImpIncasso(new Double(0)); 
		}
		valorizzaDiffGGQuietanze(qtInfo);
//		QuietanzaInfo qInfo = valorizzaQuietanzaInfo(qtInfo);
		//GenericUtils.copyProperties(dqInfo, qInfo);
		//BeanUtils.copyProperties(dqInfo, qInfo);
		dqInfo.setScadenza(new Date(qtInfo.getDataScadQuiet()));
		dqInfo.setPremioRata(BigDecimal.valueOf(qtInfo.getPremioRata()));
		dqInfo.setIncassato(BigDecimal.valueOf(qtInfo.getImpIncasso()));
		dqInfo.setTipo(GenericUtils.stringToStringNoNull(qtInfo.getCategoria()));
		dqInfo.setGiorniRitardoPagamento(GenericUtils.stringToInteger(qtInfo.getDifferenzaGG()));
		dqInfo.setStornata(GenericUtils.stringToBoolean(qtInfo.getFlStornataS()));
		dqInfo.setPosizione(qtInfo.getPosizioneInfoSimple().getNumeroPosizione());
		dqInfo.setDupkey(qtInfo.getProgressivo());
//		dqInfo.setAppendice(qtInfo.getAppendice().intValue());
		dqInfo.setDataIncasso(new Date(qtInfo.getDataIncasso()));
		String tipoPremio = GenericUtils.stringToString(qtInfo.getPremioInfo().getFlTipoPremio());
		if(tipoPremio != null) {
			dqInfo.setTipoPremio(TipoPremioEnum.valueOf(tipoPremio));
		}
//		Map<String,String> mapTipoQuietanza = valorizzaMapTipoQuietanza();
		dqInfo.setTipoQuietanza(GenericUtils.stringToString(qtInfo.getTipoQuiet()));
		dqInfo.setFrazionamento(qtInfo.getPremioInfo().getFrazionamentoPremio().intValue());
		Map<String,String> mapDescTipoPremio = valorizzaMapDescTipoPremio();
		dqInfo.setDescTipoPremio(mapDescTipoPremio.get(tipoPremio));
		dettaglioQuietanzaResponse.setDettaglioQuietanzaInfo(dqInfo);
		return dettaglioQuietanzaResponse;
	}

//	private static Map<String, String> valorizzaMapTipoQuietanza() {
//		Map<String, String> aMap = new HashMap<String, String>();
//		aMap.put("0", "Trasferimento di riserva matematica");
//		aMap.put("1", "Quietanza di perfezionamento");
//		aMap.put("2", "Quietanza di primo anno");
//		aMap.put("3", "Quietanza di annualita successive");
//		aMap.put("4", "Interessi di mora");
//		aMap.put("5", "Interessi su prestiti");
//		aMap.put("6", "Pagamento garanzia accessoria UL");
//		aMap.put("7", "Storno incasso");
//		aMap.put("8", "Rimborso prem");
//		aMap.put("9", "Storno provvigionale");
//		return aMap;
//	}
	
	private static Map<String, String> valorizzaMapDescTipoPremio() {
		Map<String, String> aMap = new HashMap<String, String>();
		aMap.put("A", "Annuo");
		aMap.put("L", "Limitato");
		aMap.put("U", "Unico");
		return aMap;
	}
	
	private static void valorizzaDiffGGQuietanze(QuietanzaTitoloInfo unaQuietanza) {
		String aStrDiff = SrvConstants.ZERO;
		if (unaQuietanza.getDataIncasso() != it.sistinf.albedoweb.common.util.DateUtils.DATE_01_01_0001_AS_LONG) {
			int giorniTraDate = it.sistinf.albedoweb.common.util.DateUtils.calcolaDifferenzaDateInGiorni(unaQuietanza.getDataScadQuiet(), unaQuietanza.getDataIncasso());
			aStrDiff = (giorniTraDate > 0) ? ""+giorniTraDate : SrvConstants.ZERO;
		}
		unaQuietanza.setDifferenzaGG(aStrDiff);
	}

	public static FunzioniPolizzaResponse valorizzaViolation(FunzioniPolizzaRequest body, List<String> validatorViolations) {
		FunzioniPolizzaResponse polizzaResponse = new FunzioniPolizzaResponse();
		polizzaResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return polizzaResponse;
	}

	public static ControllaDatiRecessoPerInserimentoResponse valorizzaViolation(ControllaDatiRecessoPerInserimentoRequest body, List<String> validatorViolations) {
		ControllaDatiRecessoPerInserimentoResponse controllaDatiRecessoPerInserimento = new ControllaDatiRecessoPerInserimentoResponse();
		controllaDatiRecessoPerInserimento.setEsito(false);
		controllaDatiRecessoPerInserimento.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return controllaDatiRecessoPerInserimento;
	}

	public static GrigliaCompatibilitaResponse valorizzaViolation(GrigliaCompatibilitaRequest body, List<String> validatorViolations) {
		GrigliaCompatibilitaResponse grigliaCompatibilitaResponse = new GrigliaCompatibilitaResponse();
		grigliaCompatibilitaResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return grigliaCompatibilitaResponse;
	}
	
	public static ExecutionContext buildSimpleExecutionContext(PolizzaInfo polizzaInfo) {
		PrenotazioneToken pt = new PrenotazioneToken();
		pt.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
		pt.setNumCategoria(polizzaInfo.getNumeroCategoria());
		pt.setNumCollettiva(polizzaInfo.getNumeroCollettiva());
		pt.setCodiceSocieta(polizzaInfo.getCodSocieta());
		pt.setAgenzia(polizzaInfo.getCodAgenzia());
		ExecutionContext executionCtx = new ExecutionContext();
		executionCtx.setToken(pt);
		return executionCtx;
	}

	public static LoginRequest valorizzaLoginRequest(String username, int codSoc) {
		LoginRequest loginRequest = new LoginRequest();
		loginRequest.setHeaderCobolSrv(CommonUtility.valorizzaHeaderCobol(GenericUtils.stringToString(username), codSoc));
		return loginRequest;
	}
	
	public static SelectAttiPortafoglioPerProdottoRequest valorizzaSelectAttiPortafoglioRequest(FunzioniPolizzaRequest body, HeaderCobolSrv headerCobolSrv) {
		SelectAttiPortafoglioPerProdottoRequest selectAttiPortafoglioPerProdottoRequest = new SelectAttiPortafoglioPerProdottoRequest();
		Map<String, String> parametri = new HashMap<String, String>();
		parametri.put(SrvConstants.CODSOC, ""+body.getPolizzaInfo().getCodSocieta());
		parametri.put(SrvConstants.CATEGORIA, GenericUtils.stringToString(body.getPolizzaInfo().getNumeroCategoria()));
		InteractionUtility.addHeaderCobolSrvToWsdlMap(headerCobolSrv, parametri);
		selectAttiPortafoglioPerProdottoRequest.setElencoAttiPortafoglioPerProdottoInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametri));
		return selectAttiPortafoglioPerProdottoRequest;
	}
	
	public static FunzioniPolizzaResponse valorizzaAttiDiPortafoglioResponse(SelectAttiPortafoglioPerProdottoResponse selectArriPortPerProdotto,
			                                                                 HeaderCobolSrv headerCobolSrv, DominiResponse dominioFunzioniGriglia) {
		FunzioniPolizzaResponse funzioniPolizzaResponse = new FunzioniPolizzaResponse();
		List<ElementoDominio> elencoAttiPortafoglioPerProdotto = valorizzaElencoAttiPortafoglio(selectArriPortPerProdotto.getElencoAttiPortafoglioPerProdotto());
		funzioniPolizzaResponse.setElencoAttiPortafoglioPerProdotto(elencoAttiPortafoglioPerProdotto);
		List<ElementoDominioCorrelato> elencoAttiPortafoglioAbilitati = valorizzaElencoAttiPortafoglioAbilitati(selectArriPortPerProdotto.getElencoAttiPortafoglioAbilitati(), dominioFunzioniGriglia);
		funzioniPolizzaResponse.setElencoAttiPortafoglioAbilitati(elencoAttiPortafoglioAbilitati);
		funzioniPolizzaResponse.setHeaderCobolSrv(headerCobolSrv);
		return funzioniPolizzaResponse;
	}

	private static List<ElementoDominio> valorizzaElencoAttiPortafoglio(List<DominioProdotto> elencoAtti) {
		List<ElementoDominio> elencoAttiPortafoglio = new ArrayList<ElementoDominio>();
		for(DominioProdotto domProd : elencoAtti){
			ElementoDominio elementoDominio = new ElementoDominio();
			elementoDominio.setCodice(GenericUtils.stringToStringNoNull(domProd.getCodice()));
			elementoDominio.setDescrizione(GenericUtils.stringToStringNoNull(domProd.getDescrizione()));
			elencoAttiPortafoglio.add(elementoDominio);
		}
		return elencoAttiPortafoglio;
	}
	
//	private static List<ElementoDominioCorrelato> valorizzaElencoAttiPortafoglioAbilitati(List<DominioProdotto> elencoAttiPortafoglioAbilitati, DominiResponse dominioFunzioniGriglia) {
//		List<ElementoDominioCorrelato> elencoAttiPortafoglio = new ArrayList<ElementoDominioCorrelato>();
//		ElementoDominioInfo elementoDominioInfo = new ElementoDominioInfo();
//		for(DominioProdotto domProd : elencoAttiPortafoglioAbilitati){
//			ElementoDominioCorrelato elementoDominioCorrelato = new ElementoDominioCorrelato();
//			elementoDominioCorrelato.setCodice(GenericUtils.stringToStringNoNull(domProd.getCodice()));
//			elementoDominioCorrelato.setDescrizione(dominioFunzioniGriglia.getResponseInfo();
////			elementoDominioCorrelato.setCodiceCorrelato(GenericUtils.stringToStringNoNull(domProd.getCodiceCorrelato()));
//			elementoDominioCorrelato.setCodiceCorrelato(SrvConstants.SPACE);
//			elencoAttiPortafoglio.add(elementoDominioCorrelato);
//		}
//		return elencoAttiPortafoglio;
//	}
	
	private static List<ElementoDominioCorrelato> valorizzaElencoAttiPortafoglioAbilitati(List<DominioProdotto> elencoAttiPortafoglioAbilitati, DominiResponse dominioFunzioniGriglia) {
	    List<ElementoDominioCorrelato> elencoAttiPortafoglio = new ArrayList<ElementoDominioCorrelato>();
	    List<ElementoDominioInfo> listaDomini = dominioFunzioniGriglia.getResponseInfo();
	    for (DominioProdotto domProd : elencoAttiPortafoglioAbilitati) {
	        ElementoDominioCorrelato correlato = new ElementoDominioCorrelato();
	        String codice = GenericUtils.stringToStringNoNull(domProd.getCodice());
	        String azione = GenericUtils.stringToStringNoNull(domProd.getDescrizione());
	        correlato.setCodice(codice);
	        correlato.setDescrizione(azione);
	        boolean trovato = false;
	        Iterator<ElementoDominioInfo> iterDominio = listaDomini.iterator();
	        while (!trovato && iterDominio.hasNext()) {
	        	ElementoDominioInfo info = iterDominio.next();
	        	String azioneDominio = GenericUtils.stringToStringNoNull(info.getDescrizioneEstesa());
	        	if (azione.equals(azioneDominio)) {
	        		String funzione = GenericUtils.stringToStringNoNull(info.getDescrizione());
	        		correlato.setCodiceCorrelato(funzione);	
	        		trovato = true;
	        	}
	        }
	        elencoAttiPortafoglio.add(correlato);
	    }
	    return elencoAttiPortafoglio;
	}
	
	public static PolizzaDettaglioRequestDTO valorizzaPolizzaDettaglioRequestDTO(it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo, HeaderCobolSrv headerCobolSrv) {
		PolizzaDettaglioRequestDTO polizzaRequestDTO = new PolizzaDettaglioRequestDTO();
		InteractionDTO iDTO = InteractionUtility.valorizzaInteractionDTO(headerCobolSrv);
		polizzaRequestDTO.setInteractionDTO(iDTO);
		polizzaRequestDTO.setNumCategoria(polizzaInfo.getNumeroCategoria());
		polizzaRequestDTO.setCategoria(Utils.valorizzaCategoria(polizzaInfo.getNumeroCategoria()));
		polizzaRequestDTO.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		polizzaRequestDTO.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		polizzaRequestDTO.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		return polizzaRequestDTO;
	}

	public static ContinuousMonitoringResponse valorizzaViolation(ContinuousMonitoringRequest body, List<String> validatorViolations) {
		ContinuousMonitoringResponse cmResponse = new ContinuousMonitoringResponse();
		cmResponse.setEsito(false);
		cmResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return cmResponse;
	}
	
	public static boolean chiamataCM(PolizzaInfo chiavePolizza, SelectPolizzaResponse polizzaResponse, PolizzaDettaglioResponseDTO polizzaResponseDTO, StrutturaReteInterface strutturaRete) throws Exception {
		CmMonitorFromProxy cm = new CmMonitorFromProxy();
		AnagraficheDiRapportoInfo anagraficheDiRapportoInfo = polizzaResponse.getAnagraficaRapporto();
		it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo = polizzaResponse.getPolizzeInfoSelect().get(0);
		int codCompagnia = chiavePolizza.getCodSocieta();
		String livello1 = GenericUtils.stringToString(polizzaInfo.getCodAgenziaGest());
		String livello0 = GenericUtils.stringToString(getLivello1Info(livello1, strutturaRete).getCodiceGruppo());
		String codProdotto = GenericUtils.stringToString(polizzaInfo.getCodProdotto());
		SingolaSessioneAlbedoWebDTO sessioneDTO = valorizzaSessioneDTO(chiavePolizza);
//		boolean isAgenziaNonAdeguata = CustomerHelper.isAgenziaNonAdeguata(codCompagnia, livello1);
//		boolean isProdottoTcmDbk = CustomerHelper.impostaIndicatoreProdottoTcmDbk(codCompagnia, codProdotto, livello0, livello1);
//		boolean isProdottoTcmBpa = CustomerHelper.impostaIndicatoreProdottoTcmBpa(codCompagnia, codProdotto, livello0, livello1);
//		boolean isProdottoTcm = CustomerHelper.isProdottoTcm(codCompagnia, sessioneDTO, isProdottoTcmDbk, isProdottoTcmBpa);
//		boolean isPremioTcmSuperiore = CustomerHelper.impostaIndicatorePremioPolizzaTcmOltreLimiti(null, sessioneDTO, ""+codCompagnia, isProdottoTcmDbk, isProdottoTcm, anagraficheDiRapportoInfo.getAnagraficaPrincipaleRapporto(), polizzaResponseDTO, "");
//		boolean fonteRedditoVis = CustomerHelper.isAgenziaDbk(codCompagnia, livello1);
		boolean isAgenziaNonAdeguata = CustomerHelperProxy.isAgenziaNonAdeguata(codCompagnia, livello1);
		boolean isProdottoTcmDbk = CustomerHelperProxy.impostaIndicatoreProdottoTcmDbk(codCompagnia, codProdotto, livello0, livello1);
		boolean isProdottoTcmBpa = CustomerHelperProxy.impostaIndicatoreProdottoTcmBpa(codCompagnia, codProdotto, livello0, livello1);
		boolean isProdottoTcm = CustomerHelperProxy.isProdottoTcm(codCompagnia, sessioneDTO, isProdottoTcmDbk, isProdottoTcmBpa);
		boolean isPremioTcmSuperiore = CustomerHelperProxy.impostaIndicatorePremioPolizzaTcmOltreLimiti(null, sessioneDTO, ""+codCompagnia, isProdottoTcmDbk, isProdottoTcm, anagraficheDiRapportoInfo.getAnagraficaPrincipaleRapporto(), polizzaResponseDTO, "");
		boolean fonteRedditoVis = CustomerHelperProxy.isAgenziaDbk(codCompagnia, livello1);
		boolean isPip = SrvConstants.TIPO_PRODOTTO_PIP.equals(polizzaInfo.getClassificazioneProdotto()) || SrvConstants.TIPO_PRODOTTO_FIP.equals(polizzaInfo.getClassificazioneProdotto()) ? true : false;
		List<RuoloXFunzione> ruoliXFunzione = cm.inizializza(anagraficheDiRapportoInfo, true, isAgenziaNonAdeguata, isProdottoTcm, isPremioTcmSuperiore, isProdottoTcmDbk, fonteRedditoVis, "", isPip);
		List<String> codificaAzione = RuoloXFunzione.getCodificaAzione();
		Map<String, Boolean> abilitazioniCM = ReflectionUtils.fromListToMapAsBool(ruoliXFunzione, codificaAzione, "valida");
		for (String codifAzione : abilitazioniCM.keySet()) {
			Boolean isValida = abilitazioniCM.get(codifAzione);
			if(isValida==false) {
				return false;
			}
		}
		return true;
	}

	private static SingolaSessioneAlbedoWebDTO valorizzaSessioneDTO(PolizzaInfo chiavePolizza) {
		SingolaSessioneAlbedoWebDTO sessioneDTO = new SingolaSessioneAlbedoWebDTO();
		RapportoDTO rapporto = new RapportoDTO();
		rapporto.setNumCategoria(GenericUtils.stringToString(chiavePolizza.getNumeroCategoria()));
		rapporto.setTipoCategoria(GenericUtils.getCategoriaFrom(rapporto.getNumCategoria()));
		rapporto.setAgenziaPolizza(GenericUtils.stringToStringNoNull(chiavePolizza.getCodAgenzia()));
		rapporto.setNumeroPolizzaColl(""+chiavePolizza.getNumeroCollettiva());
		rapporto.setNumeroPolizza(""+chiavePolizza.getNumeroPolizza());
		sessioneDTO.setRapporto(rapporto);
		return sessioneDTO;
	}
	
	public static Livello1Info getLivello1Info(String livello1, StrutturaReteInterface strutturaRete) {
		SelectLivello1Response response = null;
		SelectLivello1Request request = new SelectLivello1Request();
		request.setLiv1InfoMap(creaMapPerLivello0(livello1));
		try {
			response = strutturaRete.selectLivello1(request);
		} catch (it.sistinf.albedoweb.services.struttura.rete.service.PolicyFault e) {
			e.printStackTrace();
		} catch (it.sistinf.albedoweb.services.struttura.rete.service.ServiceFault e) {
			e.printStackTrace();
		}
		return response.getLiv1Info().get(0);
	}
	
	private static WsdlMap creaMapPerLivello0(String livello1) {
		Map<String,String> aMap = new HashMap<String, String>();
		GenericUtils.inserisciCampoStringInMapAction(aMap, SrvConstants.SOCIETA_GENERICA, SrvConstants.RETE_CODSOC);
		GenericUtils.inserisciCampoStringInMapAction(aMap, livello1, SrvConstants.RETE_AGENZIA);
		return WsdlMapUtils.valorizzaWsdlMapFor(aMap);
	}

	public static MonitoringAnagrafichePolizzaRequest valorizzaMonitoringAnagrafichePolizzaRequest(PolizzaInfo chiavePolizza, SelectPolizzaResponse polizzaResponse, String tipoLiquidazione, UtenteInfoResponse utenteInfoResponse) throws Exception {
		MonitoringAnagrafichePolizzaRequest mapRequest = new MonitoringAnagrafichePolizzaRequest();
		//Mappa parametri
		Map<String,String> parametriMonitoring = creaMapChiavePolizza(chiavePolizza);
		parametriMonitoring.put(SrvConstants.TIPO_LIQUIDAZIONE, tipoLiquidazione);
		parametriMonitoring.put(SrvConstants.RECUPERO_POLIZZA, SrvConstants.NO); //TODO: da verificare
		parametriMonitoring.put(SrvConstants.CMP_EXT, SrvConstants.SPACE);
		mapRequest.setMonitoringInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(parametriMonitoring));
		it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo = polizzaResponse.getPolizzeInfoSelect().get(0);
		int codCompagnia = chiavePolizza.getCodSocieta();
		String livello1 = GenericUtils.stringToString(polizzaInfo.getCodAgenziaGest());
		boolean isAgenziaNonAdeguata = CustomerHelperProxy.isAgenziaNonAdeguata(codCompagnia, livello1);
		boolean isPip = SrvConstants.TIPO_PRODOTTO_PIP.equals(polizzaInfo.getClassificazioneProdotto()) || SrvConstants.TIPO_PRODOTTO_FIP.equals(polizzaInfo.getClassificazioneProdotto()) ? true : false;
		//CUSTOMER OTHER INFO
		CustomerOtherInfo customerOther = valorizzaCustomerOtherInfo(chiavePolizza, polizzaInfo, codCompagnia, isAgenziaNonAdeguata,isPip);
		mapRequest.setCustomerOtherInfo(customerOther);
		//CUSTOMER DETTAGLIO
		CustomerDettaglioInfo customerDettaglioInfo = valorizzaCustomerDettaglioInfo(utenteInfoResponse);
		customerDettaglioInfo.getCustomerInteraction().setUtilizzatore("LIQ");
		mapRequest.setCustomerDettaglioInfo(customerDettaglioInfo);
		return mapRequest;
	}

	public static CustomerOtherInfo valorizzaCustomerOtherInfo(
			PolizzaInfo chiavePolizza,
			it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo,
			int codCompagnia, boolean isAgenziaNonAdeguata, boolean isPip) {
		CustomerOtherInfo customerOther = new CustomerOtherInfo();
		customerOther.setCodSocieta(codCompagnia);
		customerOther.setCodProdotto(GenericUtils.stringToString(polizzaInfo.getCodProdotto()));
		customerOther.setNumCategoria(GenericUtils.stringToString(chiavePolizza.getNumeroCategoria()));
		customerOther.setFlControlloAmleto(true); //TODO: da verificare
		customerOther.setFlAgenziaNonAdeguata(isAgenziaNonAdeguata);
		customerOther.setFlPip(isPip);
		return customerOther;
	}

	public static CustomerDettaglioInfo getCustomerDettaglioInfo(UtenteInfoResponse utenteInfoResponse) {
		return valorizzaCustomerDettaglioInfo(utenteInfoResponse);
	}
	
	private static CustomerDettaglioInfo valorizzaCustomerDettaglioInfo(UtenteInfoResponse utenteInfoResponse) {
		CustomerDettaglioInfo cdi = new CustomerDettaglioInfo();
		CustomerDetailsInfo custDetailsInfo = new CustomerDetailsInfo();
		cdi.setCustomerDetailsInfo(custDetailsInfo);	
		cdi.setCustomerValidation(new ValidatorKeyEntry());
		CustomerInteractionInfo customerInteractionInfo = PropostaUtility.valorizzaCustomerInteractionInfo(utenteInfoResponse);
		cdi.setCustomerInteraction(customerInteractionInfo);
		return cdi;
	}

	public static CMLResponse valorizzaViolation(CMLRequest body, List<String> validatorViolations) {
		CMLResponse cmResponse = new CMLResponse();
		cmResponse.setEsito(false);
		cmResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return cmResponse;
	}

	public static SalvaOpzContrattualiResponse valorizzaViolation(SalvaOpzContrattualiRequest body, List<String> validatorViolations) {
		SalvaOpzContrattualiResponse salvaOpzContrattualiResponse = new SalvaOpzContrattualiResponse();
		salvaOpzContrattualiResponse.setEsito(false);
		salvaOpzContrattualiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return salvaOpzContrattualiResponse;
	}
	
//	public static SalvaOpzContrattualiResponse valorizzaErrori(SalvaOpzContrattualiRequest body, List<Errore> listErrori) {
//		SalvaOpzContrattualiResponse response = new SalvaOpzContrattualiResponse();
//		response.setEsito(false);
//		response.setErrori(listErrori);
//		return response;
//	}	
	
	public static ControllaOpzContrattualiResponse valorizzaViolation(ControllaOpzContrattualiRequest body, List<String> validatorViolations) {
		ControllaOpzContrattualiResponse controllaOpzContrattualiResponse = new ControllaOpzContrattualiResponse();
		controllaOpzContrattualiResponse.setEsito(false);
		controllaOpzContrattualiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return controllaOpzContrattualiResponse;
	}

	public static PrenotazioneTokenFindDTO valorizzaPrenotazioneTokenFindDTO(PolizzaInfo polizzaInfo) {
		PrenotazioneTokenFindDTO searchDTO = new PrenotazioneTokenFindDTO();
		PrenotazioneToken unaPrenotazione = new PrenotazioneToken();
		unaPrenotazione.setCodiceSocieta(polizzaInfo.getCodSocieta());
		unaPrenotazione.setAgenzia(polizzaInfo.getCodAgenzia().trim());
		unaPrenotazione.setNumCategoria(GenericUtils.stringToString(polizzaInfo.getNumeroCategoria()));
		unaPrenotazione.setNumCollettiva(polizzaInfo.getNumeroCollettiva());
		unaPrenotazione.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
		unaPrenotazione.setSegregazione(SrvConstants.SPACE);
		unaPrenotazione.setTipoRapporto(SrvConstants.TIPO_RAPPORTO_POLIZZA);
		unaPrenotazione.setProcessType(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI);
		searchDTO.setSearchParams(unaPrenotazione);
		return searchDTO;
	}

/*	
	public static ExecutionContext buildExecutionContextForSalvaOpzContrattuali(SalvaOpzContrattualiRequest body, Long pk, String azioneEseguita, String tipoPratica, String tipologiaPratica, String nomeContraente, String cognomeRagSocialeContraente, boolean isDirezione, int codCli, List<String> listaBlocchi) {
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForSalvaOpzContrattuali(body, pk, azioneEseguita, tipologiaPratica, codCli);
//		PrenotazioneToken tk = new PrenotazioneToken();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));
		interactionDTO.getUtenteInfo().setFlagDirezione(GenericUtils.booleanToString(isDirezione));
//		String tipologiaPratica = getTipoPrenotazioneFrom(body);
		PrenotazioneToken tk = initWorkFlowStartRequest(interactionDTO, polizzaInfo, tipoPratica, body.getReteDiVendita(), nomeContraente, cognomeRagSocialeContraente);		
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setToken(tk);	
		executionCtx.setProcessName(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI.name());
		executionCtx.setProcessId(tk.getPk());
		executionCtx.setProcessStep(PrenotazioneTransitionEnum.transition_to_inserita.name());
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionCtx.setInteractionDto(interactionDtoMap);
		InsertBlocchiWorkflowRequest insBlocchiWorkflowRequest = getInsertBlocchiWorkflowRequest(executionCtx, listaBlocchi, pk.intValue(), interactionDTO, polizzaInfo, body.getUtente());
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_BLOCCHI_INPUT, insBlocchiWorkflowRequest);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
*/	
	public static ExecutionContext buildExecutionContextForSalvaPrenotazioneOpzContrattuali(SalvaOpzContrattualiRequest body, Long pk, String azioneEseguita, String tipoPratica, String tipologiaPratica, String nomeContraente, String cognomeRagSocialeContraente, boolean isDirezione, int codCli, int codCustomer, List<String> listaBlocchi) {
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForSalvaOpzContrattuali(body, pk, azioneEseguita, tipologiaPratica, codCli, codCustomer);
//		PrenotazioneToken tk = new PrenotazioneToken();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));
		interactionDTO.getUtenteInfo().setFlagDirezione(GenericUtils.booleanToString(isDirezione));
//		String tipologiaPratica = getTipoPrenotazioneFrom(body);
		PrenotazioneToken tk = initWorkFlowStartRequest(interactionDTO, polizzaInfo, tipoPratica, body.getReteDiVendita(), nomeContraente, cognomeRagSocialeContraente);		
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setToken(tk);	
		executionCtx.setProcessName(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI.name());
		executionCtx.setProcessId(tk.getPk());
		executionCtx.setProcessStep(PrenotazioneTransitionEnum.transition_to_inserita.name());
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionCtx.setInteractionDto(interactionDtoMap);
		InsertBlocchiWorkflowRequest insBlocchiWorkflowRequest = getInsertBlocchiWorkflowRequest("OC", executionCtx, listaBlocchi, pk.intValue(), polizzaInfo, body.getUtente());
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_BLOCCHI_INPUT, insBlocchiWorkflowRequest);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
/*	
    public static ExecutionContext buildExecutionContextForRespingiOpzContrattuali(SalvaOpzContrattualiRequest body, Long pk, String azioneEseguita, String tipoPratica, String tipologiaPratica, String nomeContraente, String cognomeRagSocialeContraente, boolean isDirezione, int codCli, List<String> listaBlocchi) {
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForSalvaOpzContrattuali(body, pk, azioneEseguita, tipologiaPratica, codCli);
//		PrenotazioneToken tk = new PrenotazioneToken();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));
		interactionDTO.getUtenteInfo().setFlagDirezione(GenericUtils.booleanToString(isDirezione));
//		String tipologiaPratica = getTipoPrenotazioneFrom(body);
		PrenotazioneToken tk = initWorkFlowStartRequest(interactionDTO, polizzaInfo, tipoPratica, body.getReteDiVendita(), nomeContraente, cognomeRagSocialeContraente);		
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setToken(tk);	
		executionCtx.setProcessName(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI.name());
		executionCtx.setProcessId(tk.getPk());
		executionCtx.setProcessStep(PrenotazioneTransitionEnum.transition_to_inserita.name());
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionCtx.setInteractionDto(interactionDtoMap);
		InsertBlocchiWorkflowRequest insBlocchiWorkflowRequest = getInsertBlocchiWorkflowRequest("OC", executionCtx, listaBlocchi, pk.intValue(), polizzaInfo, body.getUtente());
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_BLOCCHI_INPUT, insBlocchiWorkflowRequest);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
*/
	
	public static InsertBlocchiWorkflowRequest getInsertBlocchiWorkflowRequest(String tipoPrenotazione, PrenotazioneExecutionContext executionCtx, List<String> blocchiWrk, int idPrenotazione, PolizzaInfo polizzaInfo, String utente) {

		InsertBlocchiWorkflowRequest insBlocchiWorkflowRequest = new InsertBlocchiWorkflowRequest();
		List<BlocchiWorkflowInfo> blocchiWorkflowList = getListaBlocchi(tipoPrenotazione, blocchiWrk, idPrenotazione, polizzaInfo, utente);
		insBlocchiWorkflowRequest.getBlocchiWorkflowInfo().addAll(blocchiWorkflowList);
		return insBlocchiWorkflowRequest;
	}

	public static List<BlocchiWorkflowInfo> getListaBlocchi(String tipoPrenotazione, List<String> blocchiWrk, int idPrenotazione, PolizzaInfo polizzaInfo, String utente) {
		List<BlocchiWorkflowInfo> blocchiWorkflowList = new ArrayList<BlocchiWorkflowInfo>();
		BlocchiWorkflowInfo blocchiWorkflowInfo = null;
		Long dataOdierna = (new Date()).getTime();
		for(String bloccoWrk : blocchiWrk){
			blocchiWorkflowInfo = new BlocchiWorkflowInfo();
			blocchiWorkflowInfo.setCodiceSocieta(polizzaInfo.getCodSocieta());
			blocchiWorkflowInfo.setCategoria(polizzaInfo.getNumeroCategoria());
			blocchiWorkflowInfo.setAgenziaPol(polizzaInfo.getCodAgenzia());
			blocchiWorkflowInfo.setNumColl(polizzaInfo.getNumeroCollettiva());
			blocchiWorkflowInfo.setNumPolizza(polizzaInfo.getNumeroPolizza());
			blocchiWorkflowInfo.setFlgPropostaPolizza(SrvConstants.TIPO_RAPP_RID_POLIZZA);
			blocchiWorkflowInfo.setIdPrenotazione(idPrenotazione);
			blocchiWorkflowInfo.setTipoPrenotazione(tipoPrenotazione);
			blocchiWorkflowInfo.setDataPrenotazione(dataOdierna);
			blocchiWorkflowInfo.setIdBlk(bloccoWrk);
			blocchiWorkflowInfo.setSoggApprov(SrvConstants.SPACE);
			blocchiWorkflowInfo.setDataApprov(DateUtils.DATE_01_01_0001_AS_LONG);
			blocchiWorkflowInfo.setUserCreaz(utente);
			blocchiWorkflowInfo.setUserVariaz(utente);
			blocchiWorkflowList.add(blocchiWorkflowInfo);			
		}
		return blocchiWorkflowList;
	}

	public static ExecutionContext buildExecutionContextForInoltraOpzContrattuali(InoltraOpzContrattualiRequest body, UtenteInfoResponse utenteInfoResponse, PrenotazionePostVenditaInfo psInfo, ValutazioneIntermediarioInfo valutazioneIntermediario, PrenotazioneToken tk) {
		boolean isDirezione = utenteInfoResponse.getUtente().isDirezione();
//		psInfo.setDataInvioDocumentazione(body.getDataOperazione().getTime()); //dataOperazione -> dataInvioDocumentazione
		psInfo.setDataInvioDocumentazione(new Date().getTime());
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForInoltraOpzContrattuali(psInfo, valutazioneIntermediario);
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));
		interactionDTO.getUtenteInfo().setFlagDirezione(GenericUtils.booleanToString(isDirezione));
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setToken(tk);	
		executionCtx.setProcessName(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI.name());
		executionCtx.setProcessId(tk.getPk());
		executionCtx.setProcessStep(PrenotazioneTransitionEnum.transition_to_inoltrata.name());
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionCtx.setInteractionDto(interactionDtoMap);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
	public static PrenotazioneExecutionContext buildExecutionContextForAppianAcquisitionFromInoltra(PrenotazioneToken token, String transactionName) {
		PrenotazioneExecutionContext executionCtx = buildExecutionContextForAppian(token, transactionName);
		Map<String, Serializable> javaServiceInputMap = new HashMap<String, Serializable>();
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
	public static PrenotazioneExecutionContext buildExecutionContextForAppianForReject(PrenotazioneToken token, String transactionName, PrenotazionePostVenditaInfo psInfo,
			                                                                           ValutazioneIntermediarioInfo valutazioneIntermediario) {
		PrenotazioneExecutionContext executionCtx = buildExecutionContextForAppian(token, transactionName);
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForTerminateOpzContrattuali(psInfo, valutazioneIntermediario, transactionName);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
	public static PrenotazioneExecutionContext buildExecutionContextForAppianForConclusa(PrenotazioneToken token, String transactionName, PrenotazionePostVenditaInfo psInfo,
            																			 ValutazioneIntermediarioInfo valutazioneIntermediario, InteractionDTO iDto) {
		PrenotazioneExecutionContext executionCtx = buildExecutionContextForAppian(token, transactionName);
//		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForTerminateOpzContrattuali(psInfo, valutazioneIntermediario, transactionName);
		Map<String, Serializable> javaServiceInputMap = buildExecutionContextForAppianForConclusa(psInfo, valutazioneIntermediario, transactionName, token.getTipologiaPratica(), iDto);
		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
	protected static PrenotazioneExecutionContext buildExecutionContextForAppian(PrenotazioneToken token, String transactionName) {
//TODO verificare se servono
//				boolean isDirezione = utenteInfoResponse.getUtente().isDirezione();
//				Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputMapForInoltraOpzContrattuali(psInfo, valutazioneIntermediario);
//				PolizzaInfo polizzaInfo = body.getPolizzaInfo();
//				InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));
//				interactionDTO.getUtenteInfo().setFlagDirezione(GenericUtils.booleanToString(isDirezione));
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setToken(token);	
		token.getProcessType().name();
		token.getProcessType2();
//				executionCtx.setProcessName(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI.name());
		executionCtx.setProcessName(token.getProcessType().name());
		executionCtx.setProcessId(token.getPk());
		executionCtx.setProcessStep(transactionName);
//TODO verificare se servono
//				Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
//				interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
//				executionCtx.setInteractionDto(interactionDtoMap);
//		executionCtx.setJavaServicesInput(javaServiceInputMap);
		return executionCtx;
	}
	
	private static Map<String, Serializable> buildJavaServiceInputMapForInoltraOpzContrattuali(PrenotazionePostVenditaInfo psInfo, ValutazioneIntermediarioInfo valutazioneIntermediario) {			
//		Map<String, Serializable> javaServiceInputMap = new HashMap<String, Serializable>();
//		UpdatePrenotazPostVenditaRequest req = new UpdatePrenotazPostVenditaRequest();
//		psInfo.setAzioneEseguita(PrenotazioneTransitionEnum.transition_to_inoltrata.name());
//		req.setPrenotazionePostVendita(psInfo);
//		req.setValutazioneIntermediarioInfo(valutazioneIntermediario);
//		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_INPUT, req);
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputBaseMap(psInfo, valutazioneIntermediario, PrenotazioneTransitionEnum.transition_to_inoltrata.name());			
		return javaServiceInputMap;
	}
	
	private static Map<String, Serializable> buildJavaServiceInputMapForTerminateOpzContrattuali(PrenotazionePostVenditaInfo psInfo, ValutazioneIntermediarioInfo valutazioneIntermediario, String transactionName) {
		Map<String, Serializable> javaServiceInputMap = buildJavaServiceInputBaseMap(psInfo, valutazioneIntermediario, transactionName);			
		return javaServiceInputMap;
	}	
	
	private static Map<String, Serializable> buildExecutionContextForAppianForConclusa(PrenotazionePostVenditaInfo psInfo, ValutazioneIntermediarioInfo valutazioneIntermediario, String transactionName, String tipologiaPratica, InteractionDTO iDto) {
		Map<String, Serializable> javaServiceInputMap = new HashMap<String, Serializable>();
		UpdateOpzioniContrattualiRequest req = new UpdateOpzioniContrattualiRequest();
		psInfo.setAzioneEseguita(transactionName);
		psInfo.setIntestatarioConto(GenericUtils.stringToString(psInfo.getIntestatarioConto()));
		req.setPrenotazionePostVendita(psInfo);
		CoordinateBancarieInfo coordinateBancarieInfo = setCoordinateBancarieInfo(psInfo, tipologiaPratica);
		req.setCoordinateBancarie(coordinateBancarieInfo);
		OpzioniContrattualiInfo opzioniContrattualiInfo = getOpzioniContrattualiInfoFrom(psInfo, tipologiaPratica);
		req.setOpzioniContrattualiPolizza(opzioniContrattualiInfo);
		req.setValutazioneIntermediarioInfo(valutazioneIntermediario);
		req.setWsdlMap(InteractionDTO.getInteractionWsdlMap(iDto));
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_INPUT, req);
//TODO MANCA LA SCRITTURA DELLA VTTAB513		
		return javaServiceInputMap;
	}

	private static Map<String, Serializable> buildJavaServiceInputBaseMap(PrenotazionePostVenditaInfo psInfo, ValutazioneIntermediarioInfo valutazioneIntermediario, String transizione) {
		Map<String, Serializable> javaServiceInputMap = new HashMap<String, Serializable>();
		UpdatePrenotazPostVenditaRequest req = new UpdatePrenotazPostVenditaRequest();
		psInfo.setAzioneEseguita(transizione);
		req.setPrenotazionePostVendita(psInfo);
		req.setValutazioneIntermediarioInfo(valutazioneIntermediario);
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_INPUT, req);		
		return javaServiceInputMap;
	}	

	protected static PrenotazioneToken initWorkFlowStartRequest(InteractionDTO interactionDTO, PolizzaInfo polizzaInfo, String tipologiaPratica, 
																ReteVendita reteDiVendita, String nomeContraente, String cognomeRagSocialeContraente) {
		PrenotazioneToken tk = new PrenotazioneToken();
//TODO (Prenotazione unaPrenotazione)
//		if (unaPrenotazione == null || unaPrenotazione.getPrenToken() == null) {
			tk.setPermanentKey(interactionDTO.getPermKey());
			tk.setCodiceSocieta(polizzaInfo.getCodSocieta());
			tk.setNumCategoria(GenericUtils.stringToStringNoNull(polizzaInfo.getNumeroCategoria()));
			tk.setAgenzia(polizzaInfo.getCodAgenzia().trim());	
			tk.setNumCollettiva(polizzaInfo.getNumeroCollettiva());
			tk.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
			tk.setNomeContraente(nomeContraente);
			tk.setCognomeRagSocialeContraente(cognomeRagSocialeContraente);
			if(reteDiVendita != null){
				tk.setOrgLevel0(GenericUtils.stringToStringNoNull(reteDiVendita.getGruppo())); 
				tk.setOrgLevel1(GenericUtils.stringToStringNoNull(reteDiVendita.getAgenzia())); 
				tk.setOrgLevel2(GenericUtils.stringToStringNoNull(reteDiVendita.getSubagenzia())); 
				tk.setOrgLevel3(GenericUtils.stringToStringNoNull(reteDiVendita.getCollocatore()));
			}			
			tk.setTipologiaPratica(tipologiaPratica);
			tk.setProcessType(ProcessTypeEnumeration.WORKFLOW_OPZIONI_CONTRATTUALI);
//		} else {
//			tk = unaPrenotazione.getPrenToken();
//		}	
		return tk;
	}
	
	private static Map<String, Serializable> buildJavaServiceInputMapForSalvaOpzContrattuali(SalvaOpzContrattualiRequest body, Long pk, String azioneEseguita, String tipoPrenotazione, int codCli, int codCustomer) {
		PolizzaInfo polizza = body.getPolizzaInfo();
		Map<String, Serializable> javaServiceInputMap = new HashMap<String, Serializable>();
		PrenotazionePostVenditaInfo psInfo = new PrenotazionePostVenditaInfo();	
		psInfo.setCodSoc(polizza.getCodSocieta());
		psInfo.setCategoria(polizza.getNumeroCategoria());
		psInfo.setAgenziaPolizza(polizza.getCodAgenzia());
		psInfo.setNumeroCollettiva(polizza.getNumeroCollettiva());
		psInfo.setNumeroPolizza(polizza.getNumeroPolizza());
		psInfo.setIdPrenotazione(pk.intValue());
		
//		String tipoPrenotazione = getTipoPrenotazioneFrom(body);
//		String tipoPratica = DominiFullJavaHelper.getCodiceTipologiaPratica(tipoPrenotazione);
		psInfo.setTipoPrenotazione(tipoPrenotazione);
		Long dataOperazione = (body.getDataOperazione() != null) ? body.getDataOperazione().getTime() : (new Date()).getTime();
		psInfo.setDataPrenotazione(dataOperazione);
		psInfo.setDataRichiesta(dataOperazione);
		psInfo.setDataInvioDocumentazione(DateUtils.DATE_01_01_0001_AS_LONG);//oppure current date
		psInfo.setDataRicezioneDocumentazione(DateUtils.DATE_01_01_0001_AS_LONG);
		
		psInfo.setUserUltimaVariazione(GenericUtils.stringToString(body.getUtente()));
		
		String codFacoltativo = getCodFacoltativoFrom(body);
		psInfo.setOpzioneFacoltativa(codFacoltativo);
		
		OpzioniContrattualiInfo opzioniContrattualiInfo = setOpzioniContrattualiInfo(codFacoltativo, body, dataOperazione);// setta anche datatAdesione e dataRevoca
		setOpzioniContrattualiPostVedintaInfo(opzioniContrattualiInfo, psInfo);
		
		if(SrvConstants.CEDOLA.equals(codFacoltativo)) {// RPP
			BeneficiarioInfo beneficiarioInfo = body.getRppInfo().getBeneficiarioInfo();
			psInfo.setIban(beneficiarioInfo.getCodiceIban());
			psInfo.setIntestatarioConto(beneficiarioInfo.getBeneficiario());
			psInfo.setModalitaPagamento(beneficiarioInfo.getModPagamento());
		}
//		psInfo.setBeneficiariIncompleti(daForm.isBeneficiariIncompleti());
		psInfo.setAzioneEseguita(azioneEseguita);
//		int codCustomer = GenericUtils.stringToInteger(body.getCodCustomer());
		psInfo.setCodiceCliente(codCustomer);
//		if (SrvConstants.ATTIVA_CEDOLA.equals(object))
		InsertPrenotazPostVenditaRequest req = new InsertPrenotazPostVenditaRequest();
		req.setPrenotazionePostVendita(psInfo);
		req.setValutazioneIntermediarioInfo(impostaValutazioneIntermediario(body.getDatiValutazioneIntermediario(), codCli, codCustomer, body.getUtente()));
		javaServiceInputMap.put(SrvConstants.JAVA_SERVICE_INPUT, req);			
		return javaServiceInputMap;
	}
	
	public static String getTipoPrenotazioneFrom(SalvaOpzContrattualiRequest body) {
	    // RPP
	    if (body.getRppInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum op = body.getRppInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_CEDOLA;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_CEDOLA;
	        }
	    }
	    // LifeCycle
	    if (body.getLifeCycleInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum op = body.getLifeCycleInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_LIFE_CYCLE;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_LIFE_CYCLE;
	        }
	    }
	    // TakeProfit
	    if (body.getTakeProfitInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum op = body.getTakeProfitInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_TAKE_PROFIT;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_TAKE_PROFIT;
	        }
	    }
	    // Nessun info valorizzata
	    return null;
	}
	
	 public static String getTipoOperazioneFrom(String tipo){
        if (tipo == null) {
            return null;
        }
        if((SrvConstants.ATTIVA_CEDOLA.equals(tipo)) || (SrvConstants.ATTIVA_LIFE_CYCLE.equals(tipo)) || (SrvConstants.ATTIVA_TAKE_PROFIT.equals(tipo))){
            return RppInfo.TipoOperazioneEnum.ATTIVAZIONE.name();
        }
        if((SrvConstants.DISATT_CEDOLA.equals(tipo)) || (SrvConstants.DISATT_LIFE_CYCLE.equals(tipo)) || (SrvConstants.DISATT_TAKE_PROFIT.equals(tipo))){
            return RppInfo.TipoOperazioneEnum.DISATTIVAZIONE.name();
        }
        return null;
    }
	
	private static String getCodFacoltativoFrom(SalvaOpzContrattualiRequest body) {
	    if (body.getRppInfo() != null) {
	        return SrvConstants.CEDOLA;// RPP
	    }
	    if (body.getTakeProfitInfo() != null) {
	        return SrvConstants.TAKEPROFIT;
	    }
	    if (body.getLifeCycleInfo() != null) {
	        return SrvConstants.LIFECYCLE;
	    }
	    // Nessun info valorizzata
	    return null;
	}
	
	public static String getCodFacoltativoFrom(ControllaOpzContrattualiRequest body) {
	    if (body.getRppInfo() != null) {
	        return SrvConstants.CEDOLA;// RPP
	    }
	    if (body.getTakeProfitInfo() != null) {
	        return SrvConstants.TAKEPROFIT;
	    }
	    if (body.getLifeCycleInfo() != null) {
	        return SrvConstants.LIFECYCLE;
	    }
	    // Nessun info valorizzata
	    return null;
	}
	
	public static String getTipoPrenotazioneFrom(ControllaOpzContrattualiRequest body) {
	    // RPP
	    if (body.getRppInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum op = body.getRppInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_CEDOLA;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.RppInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_CEDOLA;
	        }
	    }
	    // LifeCycle
	    if (body.getLifeCycleInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum op = body.getLifeCycleInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_LIFE_CYCLE;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.LifeCycleInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_LIFE_CYCLE;
	        }
	    }
	    // TakeProfit
	    if (body.getTakeProfitInfo() != null) {
	    	it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum op = body.getTakeProfitInfo().getTipoOperazione();
	        if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum.ATTIVAZIONE.equals(op)) {
	            return SrvConstants.ATTIVA_TAKE_PROFIT;
	        } else if (it.sistinf.rest.cobol.model.polizza.opzionicontrattuali.TakeProfitInfo.TipoOperazioneEnum.DISATTIVAZIONE.equals(op)) {
	            return SrvConstants.DISATT_TAKE_PROFIT;
	        }
	    }
	    // Nessun info valorizzata
	    return null;
	}

	private static OpzioniContrattualiInfo setOpzioniContrattualiInfo(String codFacoltativo, SalvaOpzContrattualiRequest body, Long dataOperazione) {
		//METODO RIPRESO DA SalvaOpzioniContrattualiAction.java
		OpzioniContrattualiInfo opzioniContrattualiInfo = new OpzioniContrattualiInfo();
		opzioniContrattualiInfo.setFlagOpzioneFacoltativa(codFacoltativo);
		if(codFacoltativo.equals(SrvConstants.CEDOLA)) {// RPP
			RppInfo rppInfo = body.getRppInfo();
			BigDecimal importo = rppInfo.getImporto();
			opzioniContrattualiInfo.setImportoPercentualeCedola((importo != null) ? new Double(importo.doubleValue()) : 0.00);
			String durata = GenericUtils.stringToString(rppInfo.getDurata());
			opzioniContrattualiInfo.setDurataCedola((durata != null) ? Integer.valueOf(durata) : null);
			String periodicita = GenericUtils.stringToString(rppInfo.getPeriodicita());
			opzioniContrattualiInfo.setPeriodCedola((periodicita != null) ? Short.valueOf(periodicita) : 0);
			opzioniContrattualiInfo.setObiettivoTakeProfit(0.00);
			String tipoOperazione = (rppInfo.getTipoOperazione() != null) ? rppInfo.getTipoOperazione().name() : null;
			if(SrvConstants.OP_ATTIVAZIONE.equals(tipoOperazione)) {
				opzioniContrattualiInfo.setDataAdesione(dataOperazione);
				opzioniContrattualiInfo.setDataRevoca(DateUtils.DATE_01_01_0001_AS_LONG);
//				opzioniContrattualiInfo.setDataRevoca(DateUtils.getDefaultDB().getTime());
			} else {//DISATTIVAZIONE
//				opzioniContrattualiInfo.setDataAdesione(DateUtils.getDefaultDB().getTime());
				opzioniContrattualiInfo.setDataAdesione(DateUtils.DATE_01_01_0001_AS_LONG);
				opzioniContrattualiInfo.setDataRevoca(dataOperazione);
			}
		} else if(codFacoltativo.equals(SrvConstants.TAKEPROFIT)) {
			TakeProfitInfo takeProfit = body.getTakeProfitInfo();
			opzioniContrattualiInfo.setImportoPercentualeCedola(0.00);
			opzioniContrattualiInfo.setPeriodCedola(new Short("0"));
			opzioniContrattualiInfo.setDurataCedola(0);
			Integer obiettivo = takeProfit.getObiettivo();
			opzioniContrattualiInfo.setObiettivoTakeProfit((obiettivo != null) ? Double.valueOf(""+obiettivo+".00") : 0.00);
			String tipoOperazione = (takeProfit.getTipoOperazione() != null) ? takeProfit.getTipoOperazione().name() : null;
			if(SrvConstants.OP_ATTIVAZIONE.equals(tipoOperazione)) {
				opzioniContrattualiInfo.setDataAdesione(dataOperazione);
				opzioniContrattualiInfo.setDataRevoca(DateUtils.DATE_01_01_0001_AS_LONG);
//				opzioniContrattualiInfo.setDataRevoca(DateUtils.getDefaultDB().getTime());
			} else {//DISATTIVAZIONE
//				opzioniContrattualiInfo.setDataAdesione(DateUtils.getDefaultDB().getTime());
				opzioniContrattualiInfo.setDataAdesione(DateUtils.DATE_01_01_0001_AS_LONG);				
				opzioniContrattualiInfo.setDataRevoca(dataOperazione);
			}
		} else if(codFacoltativo.equals(SrvConstants.LIFECYCLE)) {
			LifeCycleInfo lifeCycleInfo = body.getLifeCycleInfo();
			opzioniContrattualiInfo.setImportoPercentualeCedola(0.00);
			opzioniContrattualiInfo.setPeriodCedola(new Short("0"));
			opzioniContrattualiInfo.setDurataCedola(0);
			opzioniContrattualiInfo.setObiettivoTakeProfit(0.00);
			String tipoOperazione = (lifeCycleInfo.getTipoOperazione() != null) ? lifeCycleInfo.getTipoOperazione().name() : null;
			if(SrvConstants.OP_ATTIVAZIONE.equals(tipoOperazione)) {
				opzioniContrattualiInfo.setDataAdesione(dataOperazione);
				opzioniContrattualiInfo.setDataRevoca(DateUtils.DATE_01_01_0001_AS_LONG);
//				opzioniContrattualiInfo.setDataRevoca(DateUtils.getDefaultDB().getTime());
			} else {//DISATTIVAZIONE
//				opzioniContrattualiInfo.setDataAdesione(DateUtils.getDefaultDB().getTime());
				opzioniContrattualiInfo.setDataAdesione(DateUtils.DATE_01_01_0001_AS_LONG);	
				opzioniContrattualiInfo.setDataRevoca(dataOperazione);
			}
		}
		return opzioniContrattualiInfo;
	}
	
	private static void setOpzioniContrattualiPostVedintaInfo(OpzioniContrattualiInfo opzioniContrattualiInfo, PrenotazionePostVenditaInfo psInfo){
		PolizzaHelper.valorizzaPrenotazionePostVendita(opzioniContrattualiInfo, psInfo);
	}
	
	private static ValutazioneIntermediarioInfo impostaValutazioneIntermediario(DatiValutazioneIntermediario datiValutazioneIntermediarioInput, int codCli, int codCustomer, String utente) {
//		FiguraAnagraficaDTO contraente = sessioneDTO.getAnagrafica();
		ValutazioneIntermediarioInfo valutazioneIntermediarioInfo = new ValutazioneIntermediarioInfo();
		if (datiValutazioneIntermediarioInput != null) {
			valutazioneIntermediarioInfo.setCodCustomer(codCustomer);
			valutazioneIntermediarioInfo.setCodCli(codCli);
			valutazioneIntermediarioInfo.setRichDiretta(GenericUtils.booleanToString(datiValutazioneIntermediarioInput.isRichDiretta()));
			valutazioneIntermediarioInfo.setValutCliente(GenericUtils.stringToStringNoNull(datiValutazioneIntermediarioInput.getValutCliente()));
			valutazioneIntermediarioInfo.setAnniRapportoInterm(GenericUtils.stringToStringNoNull(datiValutazioneIntermediarioInput.getAnniRapportoInterm()));
			valutazioneIntermediarioInfo.setOpeCoerente(GenericUtils.booleanToString(datiValutazioneIntermediarioInput.isOpeCoerente()));
			valutazioneIntermediarioInfo.setProfRischioInterm(GenericUtils.stringToStringNoNull(datiValutazioneIntermediarioInput.getProfRischioInterm()));
			Date dataOperazione = datiValutazioneIntermediarioInput.getDataOperazione();
			valutazioneIntermediarioInfo.setDataOperazione((dataOperazione != null) ? dataOperazione.getTime() : new Date().getTime());
			valutazioneIntermediarioInfo.setProfRischioKyc(SrvConstants.SPACE);
			valutazioneIntermediarioInfo.setUserCreaz(GenericUtils.stringToStringNoNull(utente));
			valutazioneIntermediarioInfo.setUserVariaz(GenericUtils.stringToStringNoNull(utente));
		}
		return valutazioneIntermediarioInfo;
	}
	
	public static OpzioniContrattualiResponse valorizzaViolation(OpzioniContrattualiRequest body, List<String> validatorViolations) {
		OpzioniContrattualiResponse opzioniContrattualiResponse = new OpzioniContrattualiResponse();
		opzioniContrattualiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return opzioniContrattualiResponse;
	}

	public static ElencoPercipientiResponse valorizzaViolation(ElencoPercipientiRequest body, List<String> validatorViolations) {
		ElencoPercipientiResponse elencoPercipientiResponse = new ElencoPercipientiResponse();
		elencoPercipientiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return elencoPercipientiResponse;
	}
	
	public static InserisciPercipientiResponse valorizzaViolation(InserisciPercipientiRequest body, List<String> validatorViolations) {
		InserisciPercipientiResponse inserisciPercipientiResponse = new InserisciPercipientiResponse();
		inserisciPercipientiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return inserisciPercipientiResponse;
	}
	
	public static SelectPolizza822Request valorizzaSelectPolizza822Request(OpzioniContrattualiRequest body) {
		SelectPolizza822Request request = new SelectPolizza822Request();
		WsdlMap wsdlMap = WsdlMapUtils.valorizzaWsdlMapFor(creaMapChiavePolizza(body.getPolizzaInfo()));
		WsdlMapUtils.addItemFor(wsdlMap, SrvConstants.PROGR, SrvConstants.ZERO);
		request.setPolizzaInfoMap(wsdlMap);
		return request;
	}
	
	public static OpzioniContrattualiResponse valorizzaOpzioniContrattualiResponse(OpzioniContrattualiInfo opzInfo, SelectPolizza59Response response59) {
		OpzioniContrattualiResponse opzioniContrattualiResponse = new OpzioniContrattualiResponse();
		String opzFlagOpzFacoltativa  = opzInfo.getFlagOpzioneFacoltativa().trim();
		if (opzFlagOpzFacoltativa.equals(SrvConstants.CEDOLA)) { // RppInfo
			RppInfo rppInfo = valorizzaRppInfoFrom(opzInfo, response59);
			opzioniContrattualiResponse.setRppInfo(rppInfo);
		} else if (opzFlagOpzFacoltativa.equals(SrvConstants.TAKEPROFIT)) {
			TakeProfitInfo takeProfitInfo = valorizzaTakeProfitInfoFrom(opzInfo);
			opzioniContrattualiResponse.setTakeProfitInfo(takeProfitInfo);
		} else if (opzFlagOpzFacoltativa.equals(SrvConstants.LIFECYCLE)) {
			LifeCycleInfo lifeCycleInfo = valorizzaLifeCycleInfoFrom(opzInfo);
			opzioniContrattualiResponse.setLifeCycleInfo(lifeCycleInfo);
		}
		if (!opzFlagOpzFacoltativa.equals(SrvConstants.SPACE)) {
			Date aDateAdesione = DateUtils.longToDate(opzInfo.getDataAdesione());
			opzioniContrattualiResponse.setDataAdesione(aDateAdesione);
			opzioniContrattualiResponse.setDataEffetto(aDateAdesione);
		}
		return opzioniContrattualiResponse;
	}
	
	private static RppInfo valorizzaRppInfoFrom(OpzioniContrattualiInfo opzInfo, SelectPolizza59Response response59) {
		PianoSmobilizzoInfo pianoSmobilizzoInfo = null;
		BeneficiarioInfo beneficiarioInfo = null;
		if ((response59 != null) && (!response59.getPianoSmobilizzoInfo().isEmpty())) {
			pianoSmobilizzoInfo = response59.getPianoSmobilizzoInfo().get(0);
			beneficiarioInfo = new BeneficiarioInfo();
			beneficiarioInfo.setCodiceIban(AnagraficaUtils.getIban(pianoSmobilizzoInfo));
			beneficiarioInfo.setBeneficiario(GenericUtils.stringToStringNoNull(pianoSmobilizzoInfo.getIntestatarioCC()));
			beneficiarioInfo.setModPagamento(GenericUtils.stringToStringNoNull(pianoSmobilizzoInfo.getModalPagamento()));
		}
		RppInfo rppInfo = new RppInfo();
		rppInfo.setBeneficiarioInfo(beneficiarioInfo);
		rppInfo.setDurata(GenericUtils.integerToString(opzInfo.getDurataCedola()));
		rppInfo.setImporto(GenericUtils.doubleToBigDecimal(opzInfo.getImportoPercentualeCedola()));
		rppInfo.setPeriodicita(GenericUtils.shortToStringNoNull(opzInfo.getPeriodCedola()));
		return rppInfo;
	}	
	
	private static LifeCycleInfo valorizzaLifeCycleInfoFrom(OpzioniContrattualiInfo opzInfo) {
		LifeCycleInfo lifeCycleInfo = new LifeCycleInfo();
//		lifeCycleInfo.setTipoOperazione(null);
		return lifeCycleInfo;
	}
	
	private static TakeProfitInfo valorizzaTakeProfitInfoFrom(OpzioniContrattualiInfo opzInfo) {
		TakeProfitInfo takeProfitInfo = new TakeProfitInfo();
		takeProfitInfo.setObiettivo(opzInfo.getObiettivoTakeProfit().intValue());
//		takeProfitInfo.setTipoOperazione(null);
		return takeProfitInfo;
	}

	private static DatiValutazioneIntermediario valorizzaDatiValutazioneIntermendiarioFrom(SelectOpzioneContrattualeResponse selectOpzioneContrattualeResponse) {
		ValutazioneIntermediarioInfo valutazioneIntermediarioInfo = selectOpzioneContrattualeResponse.getValutazioneIntermediarioInfo();
		DatiValutazioneIntermediario datiValutazioneIntermediario = new DatiValutazioneIntermediario();
		datiValutazioneIntermediario.setAnniRapportoInterm(GenericUtils.stringToStringNoNull(valutazioneIntermediarioInfo.getAnniRapportoInterm()));
		datiValutazioneIntermediario.setDataOperazione(DateUtils.longToDate(valutazioneIntermediarioInfo.getDataOperazione()));
		datiValutazioneIntermediario.setOpeCoerente(GenericUtils.stringToBoolean(valutazioneIntermediarioInfo.getOpeCoerente()));
		datiValutazioneIntermediario.setProfRischioInterm(GenericUtils.stringToStringNoNull(valutazioneIntermediarioInfo.getProfRischioInterm()));
		datiValutazioneIntermediario.setRichDiretta(GenericUtils.stringToBoolean(valutazioneIntermediarioInfo.getRichDiretta()));
		datiValutazioneIntermediario.setValutCliente(GenericUtils.stringToStringNoNull(valutazioneIntermediarioInfo.getValutCliente()));
		return datiValutazioneIntermediario;
	}

	public static SelectOpzioneContrattualeRequest valorizzaSelectOpzioneContrattualeRequest(OpzioniContrattualiRequest body, String opzContrattuale) {
		SelectOpzioneContrattualeRequest request = new SelectOpzioneContrattualeRequest();
		WsdlMap wsdlMap = WsdlMapUtils.valorizzaWsdlMapFor(creaMapChiavePolizza(body.getPolizzaInfo()));
		String tipoOperazione = null;
		if (opzContrattuale.equals(SrvConstants.CEDOLA)) {
			tipoOperazione = SrvConstants.ATTIVA_CEDOLA;
		} else if (opzContrattuale.equals(SrvConstants.TAKEPROFIT)) {
			tipoOperazione = SrvConstants.ATTIVA_TAKE_PROFIT;			
		} else if (opzContrattuale.equals(SrvConstants.LIFECYCLE)) {
			tipoOperazione = SrvConstants.ATTIVA_LIFE_CYCLE;			
		}
		WsdlMapUtils.addItemFor(wsdlMap, SrvConstants.TIPO_OPERAZIONE, tipoOperazione);
		request.setOpzioneContrattualeInfoMap(wsdlMap);
		return request;
	}
	
	public static InizializzaResponse valorizzaViolation(InizializzaRequest body, List<String> validatorViolations) {
		InizializzaResponse inizializzaResponse = new InizializzaResponse();
		inizializzaResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return inizializzaResponse;
	}

	public static PolizzaDettaglioRequestDTO valorizzaElencoUTRequest(it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo, PolizzaDettaglioResponseDTO polizzaResponseDTO) {
		PolizzaDettaglioRequestDTO elencoUTRequest = new PolizzaDettaglioRequestDTO();
		elencoUTRequest.setInteractionDTO(polizzaResponseDTO.getInteractionDTO());
		elencoUTRequest.setNumCategoria(polizzaInfo.getNumeroCategoria());
		elencoUTRequest.setCategoria(Utils.valorizzaCategoria(polizzaInfo.getNumeroCategoria()));
		elencoUTRequest.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		elencoUTRequest.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		elencoUTRequest.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		elencoUTRequest.setDataCreazione(GenericUtils.stringToStringNoNull(polizzaResponseDTO.getDataCreazione()));
		elencoUTRequest.setFlTipoTariffa(GenericUtils.stringToStringNoNull(polizzaResponseDTO.getFlTipoTariffa()));
		return elencoUTRequest;
	}

	public static FondiRivalutazioneEULRequestDTO valorizzaFondiRivalutazioneEULRequestDTO(it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo, InteractionDTO interactionDTO) {
		FondiRivalutazioneEULRequestDTO fondiRivalutazioneEULRequestDTO = new FondiRivalutazioneEULRequestDTO();
		fondiRivalutazioneEULRequestDTO.setInteractionDTO(interactionDTO);
		fondiRivalutazioneEULRequestDTO.setNumCategoria(polizzaInfo.getNumeroCategoria());
		fondiRivalutazioneEULRequestDTO.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		fondiRivalutazioneEULRequestDTO.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		fondiRivalutazioneEULRequestDTO.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		return fondiRivalutazioneEULRequestDTO;
	}
	
	public static InizializzaResponse valorizzaException(InizializzaRequest body, Exception e) {
		InizializzaResponse inizializzaResponse = valorizzaCommon(body);
		inizializzaResponse.setErrori(InteractionUtility.valorizzaException(e));
		return inizializzaResponse;
	}
	
	public static InizializzaRecessoResponse valorizzaException(InizializzaRecessoRequest body, Exception e) {
		InizializzaRecessoResponse inizializzaResponse = new InizializzaRecessoResponse();
		inizializzaResponse.setErrori(InteractionUtility.valorizzaException(e));
		return inizializzaResponse;
	}

	private static InizializzaResponse valorizzaCommon(InizializzaRequest body) {
		InizializzaResponse inizializzaResponse = new InizializzaResponse();
		inizializzaResponse.setHeaderCobolSrv(CommonUtility.valorizzaHeaderCobol(GenericUtils.stringToString(body.getUtente()), body.getPolizzaInfo().getCodSocieta()));
		inizializzaResponse.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return inizializzaResponse;
	}
	
	public static SelectPolizza59Request valorizzaSelectPolizza059Request(OpzioniContrattualiRequest body) {
		SelectPolizza59Request request = new SelectPolizza59Request();
		WsdlMap wsdlMap = WsdlMapUtils.valorizzaWsdlMapFor(creaMapChiavePolizza(body.getPolizzaInfo()));
		WsdlMapUtils.addItemFor(wsdlMap, SrvConstants.DATA_CHIUSURA, ""+DateUtils.DATE_01_01_0001_AS_LONG);
		request.setPolizzaInfoMap(wsdlMap);
		return request;
	}

	public static InitPropostaResponse valorizzaViolation(InitPropostaRequest body, List<String> validatorViolations) {
		InitPropostaResponse inizializzaResponse = new InitPropostaResponse();
		inizializzaResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return inizializzaResponse;
	}

	public static InitPropostaResponse valorizzaException(InitPropostaRequest body, Exception e) {
		InitPropostaResponse initPropostaResponse = valorizzaCommon(body);
		initPropostaResponse.setErrori(InteractionUtility.valorizzaException(e));
		return initPropostaResponse;
	}

	private static InitPropostaResponse valorizzaCommon(InitPropostaRequest body) {
		InitPropostaResponse initPropostaResponse = new InitPropostaResponse();
		initPropostaResponse.setHeaderCobolSrv(body.getHeaderCobolSrv());
		initPropostaResponse.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return initPropostaResponse;
	}
	
	public static RapportiBaseRequestDTO valorizzaRapportiBaseRequestDTO(InteractionDTO interactionDTO, PropostaInfo propostaInfo) {
		RapportiBaseRequestDTO rapportiBaseRequestDTO = new RapportiBaseRequestDTO();
		rapportiBaseRequestDTO.setInteractionDTO(interactionDTO);
		rapportiBaseRequestDTO.setNumCategoria(propostaInfo.getNumeroCategoria());
//		rapportiBaseRequestDTO.setCategoria(GenericUtils.getCategoriaFrom(propostaInfo.getNumeroCategoria()));
		rapportiBaseRequestDTO.setCategoria(Utils.valorizzaCategoria(propostaInfo.getNumeroCategoria()));
		rapportiBaseRequestDTO.setAgenziaProposta(propostaInfo.getCodAgenzia());
		rapportiBaseRequestDTO.setNumeroColl(""+propostaInfo.getNumeroPropostaCollettiva());
		rapportiBaseRequestDTO.setNumeroProposta(""+propostaInfo.getNumeroProposta());
		rapportiBaseRequestDTO.setFlDaVersamentoAggiuntivo(SrvConstants.NO);
		return rapportiBaseRequestDTO;
	}

	public static StoricoOpzioniContrattualiResponse valorizzaViolation(StoricoOpzioniContrattualiRequest body, List<String> validatorViolations) {
		StoricoOpzioniContrattualiResponse storicoOpzioniContrattualiResponse = new StoricoOpzioniContrattualiResponse();
		storicoOpzioniContrattualiResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return storicoOpzioniContrattualiResponse;
	}

	public static ElencoAttivazioniDisattivazioniOpzioniContrattualiRequest valorizzaElencoAttivazioniDisattivazioniOpzioniContrattualiRequest(StoricoOpzioniContrattualiRequest body) {
		ElencoAttivazioniDisattivazioniOpzioniContrattualiRequest eAttDisOpzContrattRequest = new ElencoAttivazioniDisattivazioniOpzioniContrattualiRequest();
		WsdlMap wsdlMap = WsdlMapUtils.valorizzaWsdlMapFor(creaMapChiavePolizza(body.getPolizzaInfo()));
		eAttDisOpzContrattRequest.setInfoMap(wsdlMap);
		return eAttDisOpzContrattRequest;
	}

//	public static List<OpzioneContrattualeEventoInfo> valorizzaOpzioniContrattualiEventoList(List<OpzioneContrattualeEventoInfo> opzioniContrattualiEventoList) {
//		List<OpzioneContrattualeEventoInfo> listaOpzioniContrattualiInfo = new ArrayList<OpzioneContrattualeEventoInfo>();
//		for (OpzioneContrattualeEventoInfo opzContrattualeEvento : opzioniContrattualiEventoList) {
//			OpzioneContrattualeEventoInfo opzContrattualeRest = valorizzaOpzioneContrattualeEventoInfo(opzContrattualeEvento);
//			listaOpzioniContrattualiInfo.add(opzContrattualeRest);
//		}
//		return listaOpzioniContrattualiInfo;
//	}
	
//	private static OpzioneContrattualeEventoInfo valorizzaOpzioneContrattualeEventoInfo(OpzioneContrattualeEventoInfo opzContrattualeEvento) {
//		OpzioneContrattualeEventoInfo opzioneContrattualeEventoInfo = new OpzioneContrattualeEventoInfo();
//		opzioneContrattualeEventoInfo.setCodiceEvento(GenericUtils.stringToString(opzContrattualeEvento.getCodiceEvento()));
//		opzioneContrattualeEventoInfo.setDataEffetto(opzContrattualeEvento.getDataEffetto());
//		opzioneContrattualeEventoInfo.setDataAdesione(opzContrattualeEvento.getDataAdesione());
//		opzioneContrattualeEventoInfo.setImportoPercCedola(opzContrattualeEvento.getImportoPercCedola());
//		opzioneContrattualeEventoInfo.setDurataCedola(opzContrattualeEvento.getDurataCedola());
//		opzioneContrattualeEventoInfo.setPeriodCedola(opzContrattualeEvento.getPeriodCedola());
//		opzioneContrattualeEventoInfo.setOpzioneFacoltativa(opzContrattualeEvento.getOpzioneFacoltativa());
//		if(opzContrattualeEvento.getOpzioneFacoltativa().equals(SrvConstants.TAKEPROFIT)) {
//			opzioneContrattualeEventoInfo.setObiettivoTakeProfit(opzContrattualeEvento.getObiettivoTakeProfit());
//		}
//		return opzioneContrattualeEventoInfo;
//	}

	public static StoricoOpzioniContrattualiResponse valorizzaStoricoOpzioniContrattualiResponse(List<OpzioneContrattualeEventoInfo> elencoOpzioniContrattuali) {
		StoricoOpzioniContrattualiResponse storicoOpzioniContrattualiResponse = new StoricoOpzioniContrattualiResponse();	
		List<OpzioneContrattuale> storicoOpzioniContrattualiList = new ArrayList<OpzioneContrattuale>();
		for (OpzioneContrattualeEventoInfo opzContrattualeEvento : elencoOpzioniContrattuali){
			OpzioneContrattuale opzContrattuale = valorizzaOpzioneContrattuale(opzContrattualeEvento);
			storicoOpzioniContrattualiList.add(opzContrattuale);
		}
		storicoOpzioniContrattualiResponse.setStoricoOpzioniContrattualiList(storicoOpzioniContrattualiList);
		return storicoOpzioniContrattualiResponse;
	}

	private static OpzioneContrattuale valorizzaOpzioneContrattuale(OpzioneContrattualeEventoInfo opzContrattualeEvento) {
		String opzFacoltativa = GenericUtils.stringToString(opzContrattualeEvento.getOpzioneFacoltativa());
		OpzioneContrattuale opzioneContrattuale = new OpzioneContrattuale();
		opzioneContrattuale.setEvento(opzContrattualeEvento.getCodiceEvento());
		opzioneContrattuale.setDataEffetto(DateUtils.longToDate(opzContrattualeEvento.getDataEffetto()));
		opzioneContrattuale.setDataAdesione(DateUtils.longToDate(opzContrattualeEvento.getDataAdesione()));
		// Dati da popolare solo in caso di RppInfo
		if (SrvConstants.CEDOLA.equals(opzFacoltativa)) {
			opzioneContrattuale.setImporto(GenericUtils.doubleToBigDecimal(opzContrattualeEvento.getImportoPercCedola()));
			opzioneContrattuale.setDurata(""+opzContrattualeEvento.getDurataCedola());
			Map<String, String> mapPeriodicita = getPeriodicita();
			String valore = GenericUtils.stringToString(mapPeriodicita.get(""+opzContrattualeEvento.getPeriodCedola()));
			if (valore != null) {
				opzioneContrattuale.setPeriodicita(PeriodicitaEnum.valueOf(valore));
			}
		}
		// Dati da popolare solo in caso di TakeProfitInfo
		if (SrvConstants.TAKEPROFIT.equals(opzFacoltativa)) {
			opzioneContrattuale.setObiettivo(BigDecimal.valueOf(opzContrattualeEvento.getObiettivoTakeProfit()));
		}
		return opzioneContrattuale;
	}
	
	private static Map<String, String> getPeriodicita() {
		Map<String, String> map = new HashMap<String, String>();
		map.put("0", "");
		map.put("1", "ANNUALE");
		map.put("2", "SEMESTRALE");
		map.put("3", "QUADRIMESTRALE");
		map.put("4", "TRIMESTRALE");
		map.put("6", "BIMESTRALE");
		map.put("12", "MENSILE");
		return map;
	}
	
	public static EstrazionePremioDisponibileTotaleRequest getEstrazionePremioDisponibileTotaleRequest(PolizzaInfo polizza) {
		EstrazionePremioDisponibileTotaleRequest req = new EstrazionePremioDisponibileTotaleRequest();
		Map<String, String> map = creaMapChiavePolizza(polizza);
		WsdlMap wsMap = WsdlMapUtils.valorizzaWsdlMapFor(map);
		req.setEstrazionePremioDisponibileTotaleInfoMap(wsMap);
		return req;
	}

	public static ControllaOpzContrattualiResponse valorizzaErrori(List<ErroreCampo> listCdErr) {
		ControllaOpzContrattualiResponse response = new ControllaOpzContrattualiResponse();
		response.setEsito(false);
		List<Errore> errori = new ArrayList<Errore>();
		Errore errore = CommonUtility.valorizzaErroreCommon("P_ITVAL0134E", RestSrvConstants.ERRORE_BLOCCANTE, "");
		errore.setErroriCampo(listCdErr);
		errori.add(errore);
		response.setErrori(errori);
		return response;
	}
	
	public static PropostaRiepilogoRequestDTO valorizzaListaUTRequest(HeaderCobolSrv headerCobolSrv) {
		PropostaRiepilogoRequestDTO listaUTRequest = new PropostaRiepilogoRequestDTO();
		listaUTRequest.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(headerCobolSrv));
		listaUTRequest.setFromRiepilogo(SrvConstants.NO);
		listaUTRequest.setFlVersamentoAgg(SrvConstants.SI);
		listaUTRequest.setFlTipoVersamentoAgg("E");
		listaUTRequest.setFlTipoWorkflow("A");
		listaUTRequest.setIdPrenotazione(SrvConstants.ZERO);
		listaUTRequest.setTipoPrenotazione(SrvConstants.SPACE);
		listaUTRequest.setFlSoloLettura(SrvConstants.NO);
		listaUTRequest.setFlagSenzaCalcoli(SrvConstants.NO);
		return listaUTRequest;
	}

	public static InizializzaVAEmessoResponse valorizzaException(InizializzaVAEmessoRequest body, Exception e) {
		InizializzaVAEmessoResponse initVAEmessoResponse = valorizzaCommon(body);
		initVAEmessoResponse.setErrori(InteractionUtility.valorizzaException(e));
		return initVAEmessoResponse;
	}

	private static InizializzaVAEmessoResponse valorizzaCommon(InizializzaVAEmessoRequest body) {
		InizializzaVAEmessoResponse initVAEmessoResponse = new InizializzaVAEmessoResponse();
		initVAEmessoResponse.setHeaderCobolSrv(body.getHeaderCobolSrv());
		initVAEmessoResponse.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return initVAEmessoResponse;
	}

	public static PropostaDatiIdentificativiRequestDTO valorizzaCaricaFondiRequest(InteractionDTO interactionDTO, String codLineaInvestimento) {
		PropostaDatiIdentificativiRequestDTO fondiRequest = new PropostaDatiIdentificativiRequestDTO();
		fondiRequest.setInteractionDTO(interactionDTO);
		fondiRequest.setCodLineaInvestimento(GenericUtils.stringToStringNoNull(codLineaInvestimento));
		fondiRequest.setFlagRipartenzaFondiUl(SrvConstants.NO);
		fondiRequest.setRipUlFondo(SrvConstants.SPACE);
		return fondiRequest;
	}

	public static InizializzaVAEmessoResponse valorizzaViolation(InizializzaVAEmessoRequest body, List<String> validatorViolations) {
		InizializzaVAEmessoResponse inizializzaVAEmessoResponse = new InizializzaVAEmessoResponse();
		inizializzaVAEmessoResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return inizializzaVAEmessoResponse;
	}

	public static PropostaRiepilogoRequestDTO valorizzaListaUTPipRequest(HeaderCobolSrv headerCobolSrv) {
		PropostaRiepilogoRequestDTO listaUTRequest = new PropostaRiepilogoRequestDTO();
		listaUTRequest.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(headerCobolSrv));
		listaUTRequest.setFromRiepilogo(SrvConstants.NO);
		listaUTRequest.setFlVersamentoAgg(SrvConstants.SI);
		listaUTRequest.setFlTipoVersamentoAgg("E");
		listaUTRequest.setFlTipoWorkflow("A");
		listaUTRequest.setIdPrenotazione(SrvConstants.ZERO);
		listaUTRequest.setTipoPrenotazione(SrvConstants.SPACE);
		listaUTRequest.setFlSoloLettura(SrvConstants.NO);
		listaUTRequest.setFlagSenzaCalcoli(SrvConstants.NO);
		listaUTRequest.setDataDecorrenza(SrvConstants.SPACE);
		return listaUTRequest;
	}
	
//	public static WorkflowResponse valorizzaWrkViolation(Long idPrenotazione, List<String> validatorViolations) {
//		WorkflowResponse workflowResponse = new WorkflowResponse();
//		workflowResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
//		workflowResponse.setEsito(false);
//		workflowResponse.setIdPrenotazione(idPrenotazione);
//		return workflowResponse;
//	}
	
	public static <T extends WorkflowResponse> T valorizzaWrkViolation(Class<T> responseClass, Long idPrenotazione, List<String> validatorViolations) {
		try {
			T response = responseClass.getDeclaredConstructor().newInstance();
			response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
			response.setEsito(false);
			response.setIdPrenotazione(idPrenotazione);
			return response;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
//	public static WorkflowResponse valorizzaWrkErrori(Long idPrenotazione, List<Errore> errori) {
//		WorkflowResponse workflowResponse = new WorkflowResponse();
//		workflowResponse.setErrori(errori);
//		workflowResponse.setEsito(false);
//		workflowResponse.setIdPrenotazione(idPrenotazione);
//		return workflowResponse;
//	}
	
	public static <T extends WorkflowResponse> T valorizzaWrkErrori(Class<T> responseClass, Long idPrenotazione, List<Errore> errori) throws Exception{
		T response = responseClass.getDeclaredConstructor().newInstance();
		response.setErrori(errori);
		response.setEsito(false);
		response.setIdPrenotazione(idPrenotazione);
		return response;
	}
	
	public static SelectPrenotazPostVenditaRequest valorizzaSelectPrenotazPostVendita(Long idPrenotazione) {
		SelectPrenotazPostVenditaRequest req = new SelectPrenotazPostVenditaRequest();				
		WsdlMap wsMap = new WsdlMap();
		Map<String, String> mapValori = new HashMap<String, String>();
		mapValori.put(SrvConstants.ID_PRENOTAZIONE, idPrenotazione.toString());
		wsMap = WsdlMapUtils.valorizzaWsdlMapFor(mapValori); 
		req.setPrenotazPostVenditaInfoMap(wsMap);
		return req;
	}	
	
	public static SelectValutazioneIntermediarioRequest valorizzaSelectValutazioneIntermediario(PolizzaInfo polizzaInfo, Long idPrenotazione) {
		SelectValutazioneIntermediarioRequest selectValutazioneIntermediarioRequest = new SelectValutazioneIntermediarioRequest();
		Map<String, String> map = PolizzaUtility.creaMapChiavePolizza(polizzaInfo);
		map.put(SrvConstants.FLAG_PROP_POL, SrvConstants.DUE);
		map.put(SrvConstants.ID_PRENOTAZIONE, idPrenotazione.toString());
		selectValutazioneIntermediarioRequest.setInfoMap(WsdlMapUtils.valorizzaWsdlMapFor(map));
		return selectValutazioneIntermediarioRequest;
	}	
	
	public static PropostaRiepilogoRequestDTO valorizzaListaUTTradizRequest(HeaderCobolSrv headerCobolSrv) {
		PropostaRiepilogoRequestDTO listaUTRequest = new PropostaRiepilogoRequestDTO();
		listaUTRequest.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(headerCobolSrv));
		listaUTRequest.setFromRiepilogo(SrvConstants.NO);
		listaUTRequest.setFlVersamentoAgg(SrvConstants.SI);
		listaUTRequest.setIdPrenotazione(SrvConstants.ZERO);
		listaUTRequest.setTipoPrenotazione(SrvConstants.SPACE);
		listaUTRequest.setDataDecorrenza(SrvConstants.SPACE);
		return listaUTRequest;
	}

	public static ControlloBloccoProfiloRischioRequest valorizzaControlloBloccoProfiloRischioRequest(String codiceCliente, String profiloRischio) {
		ControlloBloccoProfiloRischioRequest request = new ControlloBloccoProfiloRischioRequest();
		WsdlMap wsdlMap = new WsdlMap();
		WsdlMapUtils.addItemFor(wsdlMap, SrvConstants.CODICE_CLIENTE, codiceCliente);
		WsdlMapUtils.addItemFor(wsdlMap, SrvConstants.PROF_DI_RISCHIO, profiloRischio);	
		request.setInfoMap(wsdlMap);
		return request;
	}

	public static DisimpegnaPolizzaResponse valorizzaViolation(DisimpegnaPolizzaRequest body,List<String> validatorViolations) {
		DisimpegnaPolizzaResponse disimpegnaPolizzaResponse = new DisimpegnaPolizzaResponse();
		disimpegnaPolizzaResponse.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return disimpegnaPolizzaResponse;
	}

	public static DisimpegnoPolizzaRequestDTO valorizzaDisimpegnoPolizzaRequestDTO(DisimpegnaPolizzaRequest body) {
		DisimpegnoPolizzaRequestDTO disimpegnoPolizzaRequestDTO = new DisimpegnoPolizzaRequestDTO();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		disimpegnoPolizzaRequestDTO.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
		disimpegnoPolizzaRequestDTO.setCategoria(Utils.valorizzaCategoria(polizzaInfo.getNumeroCategoria()));
		disimpegnoPolizzaRequestDTO.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		disimpegnoPolizzaRequestDTO.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		disimpegnoPolizzaRequestDTO.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		disimpegnoPolizzaRequestDTO.setPosizione(SrvConstants.ZERO);
		disimpegnoPolizzaRequestDTO.setLivello(SrvConstants.ZERO);
		return disimpegnoPolizzaRequestDTO;
	}

	public static LoginRequestDTO valorizzaExitRequest(InteractionDTO interactionDTO) {
		LoginRequestDTO loginRequestDTO = new LoginRequestDTO();
		loginRequestDTO.setInteractionDTO(interactionDTO);
		loginRequestDTO.setEsito("  ");
		return loginRequestDTO;
	}

	public static DisimpegnaPolizzaResponse valorizzaDisimpegnaPolizzaResponse(LoginResponseDTO loginResponseDTO) {
		DisimpegnaPolizzaResponse disimpegnaPolizzaResponse = new DisimpegnaPolizzaResponse();
		disimpegnaPolizzaResponse.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(loginResponseDTO.getInteractionDTO()));
		return disimpegnaPolizzaResponse;
	}
	
	public static PolizzaInfo valorizzaPolizzaInfoFrom(RapportoInfo rapporto) {
		PolizzaInfo polizzaRest = new PolizzaInfo();
		polizzaRest.setCodSocieta(rapporto.getCodSocieta());
		polizzaRest.setCodAgenzia(rapporto.getCodAgenzia());
		polizzaRest.setNumeroCollettiva(rapporto.getNumeroCollettiva());
		polizzaRest.setNumeroCollettiva(rapporto.getNumeroCollettiva());
		polizzaRest.setNumeroPolizza(rapporto.getNumeroRapporto());
		return polizzaRest;
	}
	
	public static void handleWorkflowException(EsitoResponse esitoResponse, String functionName, String errorCode, String errorMessage) {
		esitoResponse.setEsito(false);		
		if (WorkflowMgrErrorCodes.INVALID_PROCESS_NAME.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di nome processo non valido.", null));
		} else if (WorkflowMgrErrorCodes.INVALID_PROCESS_STEP.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di azione di processo non valida.", null));
		} else if (WorkflowMgrErrorCodes.INVALID_PROCESS_TOKEN.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di token di processo non valido.", null));
		} else if (WorkflowMgrErrorCodes.INVALID_PROCESS_TRANSITION.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di transizione di stato non valida.", null));
		} else if (WorkflowMgrErrorCodes.INVALID_PROCESS_INPUT_PARAMETERS.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di parametri di input di processo non validi.", null));
		} else if (WorkflowMgrErrorCodes.PROCESS_VALIDATION_EXCEPTION.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di workflow in corso.", null));
		} else if (WorkflowMgrErrorCodes.RELATION_VALIDATION_EXCEPTION.equals(errorCode)) {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" non attivabile a causa di parametri di input di rapporto non validi.", null));
		} else if (WorkflowMgrErrorCodes.PROCESS_TRANSITION_ALREADY_DONE.equals(errorCode)) {
			esitoResponse.setEsito(true);
//			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, "Funzione "+functionName+" elaborata in precedenza.", null));
			esitoResponse.setErrori(CommonUtility.valorizzaErrore(errorCode, SrvConstants.ERR_INFORMATIVO, errorMessage));
		} else {
			esitoResponse.setErrori(ServiceUtility.valorizzaErrori(errorCode, errorMessage, null));
		}
	}	
	
	/**
	 * creaDecodificaErrori
	 * @param riscattoResponse RiscattoNewFiscResponseDTO
	 * @return MappingFromCampo2DecodificaErrore
	 */
	public static List<ErroreCampo> creaDecodificaErrori(RiscattoResponseDTO riscattoResponse) {

		List<ErroreCampo> erroriCampo = new ArrayList<ErroreCampo>();

		if (riscattoResponse == null) {
			return erroriCampo;
		}
			
		String importoRichiestoCdErr = riscattoResponse.getImportoRichiestoCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "importoRichiesto", importoRichiestoCdErr);
		
		int numElementi = riscattoResponse.getElencoFondiRiscattoErr().size();
		for (int i = 0; i < numElementi; i++) {
			FondiRiscattoErr erroriFondiRiscatto = (FondiRiscattoErr) riscattoResponse.getElencoFondiRiscattoErr().get(i);
			String suffisso = new Integer(i).toString();
			String valoreRiscattoCdErr = erroriFondiRiscatto.getValoreRiscattoCdErr();
			CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "listaValoriRiscatto["+suffisso+"].valoreRiscatto", valoreRiscattoCdErr);
			String valoreMinQuotaCdErr = erroriFondiRiscatto.getValoreMinQuotaCdErr();
			CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "listaValoriRiscatto["+suffisso+"].valoreRiscatto", valoreMinQuotaCdErr);
		}

		String dataConsDocCdErr = riscattoResponse.getDataConsDocCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "dataConsDoc", dataConsDocCdErr);
		
		String valoreDaCdErr = riscattoResponse.getValoreDaCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "valoreDa", valoreDaCdErr);
		
		String dataPrimaIscrizioneCdErr = riscattoResponse.getDataPrimaIscrizioneCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "dataPrimaIscrizione", dataPrimaIscrizioneCdErr);
		
		String tipoIscrittoCdErr = riscattoResponse.getTipoIscrittoCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "tipoIscritto", tipoIscrittoCdErr);
		
		String aliquitaTfrCdErr = riscattoResponse.getAliquitaTfrCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "aliquitaTfr", aliquitaTfrCdErr);
		
		String flagArt11CdErr = riscattoResponse.getFlagArt11CdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "flagArt11", flagArt11CdErr);
		
		String dataRichDocClienteCdErr = riscattoResponse.getDataRichDocClienteCdErr();
		CommonUtility.valorizzaErroreCampoCobol(erroriCampo, "dataRichDocCliente", dataRichDocClienteCdErr);

		return erroriCampo;
	}
	
	public static DatiInputSalvaPrenotazioneVA valorizzaDatiInputSalvaPrenotazioneVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiInputSalvaPrenotazioneVA datiSalvaPrenotazione = new DatiInputSalvaPrenotazioneVA();
		datiSalvaPrenotazione.setVersamentoAggiuntivo(GenericUtils.stringToBoolean(listaUTResponse.getFlVersamentoAgg()));
		datiSalvaPrenotazione.setTipoVersAggiuntivo(GenericUtils.stringToString(listaUTResponse.getFlTipoVersamentoAgg()));
		datiSalvaPrenotazione.setDataRichiestaCliente(CommonUtility.stringToDate(listaUTResponse.getDataRichiestaCliente()));
		datiSalvaPrenotazione.setNumUTPresenti(listaUTResponse.getNumUT());
		List<String> posizioni = new ArrayList<String>();
		for(int i=0; i<listaUTResponse.getNumUT(); i++) {
			posizioni.add(CommonUtility.completaCampo(listaUTResponse.getListaUTRiepilogo().get(i).getPosizione(),4,'0',SrvConstants.LEFT));
		}
		return datiSalvaPrenotazione;
	}

	public static PropostaAttivazioneRequestDTO valorizzaSalvaPrenotazRequest(SalvaPrenotazVAEmessoRequest body, Boolean direzione, Long idPrenotazione) {
		PropostaAttivazioneRequestDTO paRequest = new PropostaAttivazioneRequestDTO();
		paRequest.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
		DatiInputSalvaPrenotazioneVA datiInput = body.getDatiInput();
		PolizzaInfo chiavePolizza = body.getPolizzaInfo();
		paRequest.setFlVersamentoAgg(GenericUtils.booleanToString(datiInput.isVersamentoAggiuntivo()));
		paRequest.setFlTipoVersamentoAgg(GenericUtils.stringToString(datiInput.getTipoVersAggiuntivo()));
		paRequest.setFlOnline(SrvConstants.SI);
		paRequest.setCodCliSogg3(SrvConstants.SPACE); 
		paRequest.setIdPrenotazione(GenericUtils.longToStringNoNull(idPrenotazione));
		paRequest.setTipoPrenotazione(SrvConstants.TIPO_PRATICA_VERSAMENTO_EMESSO_NO_TRANCHING);
		if(SrvConstants.TRANCHING_MINUSCOLO.equals(datiInput.getTipologiaProdotto())) {
			paRequest.setTipoPrenotazione(SrvConstants.TIPO_PRATICA_VERSAMENTO_EMESSO);
		}
		paRequest.setSottoFunzionePrenotaz(SrvConstants.SPACE);
		paRequest.setDataRichiestaCliente(CommonUtility.dateToString(datiInput.getDataRichiestaCliente()));
		paRequest.setDataPervenimentoCartaceo(CommonUtility.dateToString(datiInput.getDataPervenimentoCartaceo()));
		paRequest.setDataIncasso(CommonUtility.dateToString(datiInput.getDataIncasso()));
		paRequest.setLetteraSollecito(GenericUtils.booleanToString(datiInput.isLetteraSollecito()));
		paRequest.setFlQuietanza(SrvConstants.SI);
		paRequest.setFlStampaQuietanza(SrvConstants.NO);
		paRequest.setFlStampaPolizza(SrvConstants.NO);
		paRequest.setFlStampaAltriDoc(SrvConstants.NO);
		paRequest.setBloccoAttivazione(SrvConstants.SPACE);
		paRequest.setDescBloccoAttiv(SrvConstants.SPACE);
		paRequest.setDataEmissione(CommonUtility.dateToString(datiInput.getDataRichiestaCliente()));
		paRequest.setNumCategoria(chiavePolizza.getNumeroCategoria());
		paRequest.setCategoria(Utils.valorizzaCategoria(chiavePolizza.getNumeroCategoria()));
		paRequest.setAgenziaPolizza(chiavePolizza.getCodAgenzia());
		paRequest.setNumeroColl(""+chiavePolizza.getNumeroCollettiva());
		paRequest.setNumPreventivo(SrvConstants.ZERO);
		paRequest.setNumeroPolizza(""+chiavePolizza.getNumeroPolizza());
		paRequest.setNumeroProposta(""+datiInput.getNumProposta());
		paRequest.setStato(SrvConstants.SPACE);
		paRequest.setIndiceUt(SrvConstants.ZERO);
		paRequest.setNumUTPresenti(""+datiInput.getNumUTPresenti());
		ArrayList<UTPosizioneDettaglioDTO> listaPosizioni = new ArrayList<UTPosizioneDettaglioDTO>();
		for(String posizione : datiInput.getPosizioni()) {
			UTPosizioneDettaglioDTO posizioneDettaglio = new UTPosizioneDettaglioDTO();
			posizioneDettaglio.setNumeroProposta(""+datiInput.getNumProposta());
			posizioneDettaglio.setPosizione(posizione);
			posizioneDettaglio.setFlAttSs("A");
			listaPosizioni.add(posizioneDettaglio);
		}
		paRequest.setListaUTAttivazione(listaPosizioni);
		paRequest.setPremioVita(SrvConstants.SPACE);
		paRequest.setSconti(SrvConstants.SPACE);
		paRequest.setComplementariInfort(SrvConstants.SPACE);
		paRequest.setInvaliditaPerm(SrvConstants.SPACE);
		paRequest.setAltreComplementari(SrvConstants.SPACE);
		paRequest.setTotalePremioAnnuo(SrvConstants.SPACE);
		paRequest.setInteressiFrazionamento(SrvConstants.SPACE);
		paRequest.setPremioFrazionato(SrvConstants.SPACE);
		paRequest.setDiritti(SrvConstants.SPACE);
		paRequest.setSpeseMediche(SrvConstants.SPACE);
		paRequest.setImposteFirma(SrvConstants.SPACE);
		paRequest.setPremioAllaFirma(SrvConstants.SPACE);
		paRequest.setOttico(SrvConstants.SPACE);
		paRequest.setIncasso(SrvConstants.SPACE);
		paRequest.setLivelloZero(datiInput.getLivelloZero());
		paRequest.setFlDirezione(GenericUtils.booleanToString(direzione));
		paRequest.setFlChiamataServizio(SrvConstants.UNO);
		paRequest.setFlIncassoZurich(GenericUtils.booleanToString(datiInput.isIncassoZurich()));
		paRequest.setModPagamento(datiInput.getModPagamento());
		paRequest.setIban(datiInput.getIban());
//		paRequest.setdatac
		return paRequest;
	}

//	public static SalvaPrenotazVAEmessoResponse valorizzaSalvaPrenotazResponse(InteractionDTO interactionDTO, Long idPrenotazione) {
//		SalvaPrenotazVAEmessoResponse response = new SalvaPrenotazVAEmessoResponse();
//		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
//		response.setIdPrenotazione(idPrenotazione);
//		return response;
//	}

//	public static WorkflowCobolResponse valorizzaWorkflowCobolResponse(InteractionDTO interactionDTO, Long idPrenotazione) {
//		WorkflowCobolResponse response = new WorkflowCobolResponse();
//		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
//		response.setIdPrenotazione(idPrenotazione);
//		response.setEsito(true);
//		return response;
//	}
	
	public static SalvaPrenotazVAEmessoResponse valorizzaException(SalvaPrenotazVAEmessoRequest body, Exception e) {
		SalvaPrenotazVAEmessoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}

	private static SalvaPrenotazVAEmessoResponse valorizzaCommon(SalvaPrenotazVAEmessoRequest body) {
		SalvaPrenotazVAEmessoResponse response = new SalvaPrenotazVAEmessoResponse();
		response.setHeaderCobolSrv(body.getHeaderCobolSrv());
		response.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return response;
	}

	public static SalvaPrenotazVAEmessoResponse valorizzaViolation(SalvaPrenotazVAEmessoRequest body, List<String> validatorViolations) {
		SalvaPrenotazVAEmessoResponse response = new SalvaPrenotazVAEmessoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}

	public static AnagraficaDettaglioRequestDTO valorizzaSalvaSoggettoTerzoRequest(SalvaSoggettoTerzoRequest body) {
		AnagraficaDettaglioRequestDTO request = new AnagraficaDettaglioRequestDTO();
		request.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
		request.setFlOnline(SrvConstants.SI);
		request.setFlPresenzaFigure(SrvConstants.SI);
		request.setNumElementiTrovati(1);
		request.setFlAltri(false);
		DatiAnagraficheDTO figuraAnag = new DatiAnagraficheDTO();
		DatiCustomer datiCustomer = body.getDatiCustomer();
		figuraAnag.setTipoRuolo("E");
		figuraAnag.setProgrRuolo(SrvConstants.UNO);
		figuraAnag.setCodiceCliente(GenericUtils.integerToStringNoNull(datiCustomer.getCodiceCliente()));
		figuraAnag.setCognome(GenericUtils.stringToStringNoNull(datiCustomer.getCognome()));
		figuraAnag.setNome(GenericUtils.stringToStringNoNull(datiCustomer.getNome()));
		figuraAnag.setCodiceFiscale(GenericUtils.stringToStringNoNull(datiCustomer.getCodiceFiscale()));
		figuraAnag.setDataDiNascita(CommonUtility.dateToString(datiCustomer.getDataDiNascita()));
		figuraAnag.setCodProvinciaDiNascita(GenericUtils.stringToStringNoNull(datiCustomer.getCodProvinciaDiNascita()));
		figuraAnag.setLuogoDiNascita(GenericUtils.stringToStringNoNull(datiCustomer.getLuogoDiNascita()));
		figuraAnag.setCodSesso(GenericUtils.stringToStringNoNull(datiCustomer.getCodSesso().toString()));
		figuraAnag.setCodiceProfessione(GenericUtils.stringToStringNoNull(datiCustomer.getCodiceProfessione()));
		figuraAnag.setTipoPersona(GenericUtils.stringToStringNoNull(datiCustomer.getTipoPersona().toString()));
		figuraAnag.setCittadinanza1(GenericUtils.stringToStringNoNull(datiCustomer.getCittadinanza1()));
		figuraAnag.setCittadinanza2(GenericUtils.stringToStringNoNull(datiCustomer.getCittadinanza2()));
		figuraAnag.setCittadinanza3(GenericUtils.stringToStringNoNull(datiCustomer.getCittadinanza3()));
		figuraAnag.setCodProvinciaDiResidenza(GenericUtils.stringToStringNoNull(datiCustomer.getCodProvinciaDiResidenza()));
		figuraAnag.setComDiResidenza(GenericUtils.stringToStringNoNull(datiCustomer.getComDiResidenza()));
		figuraAnag.setIndirizzoDiResidenza(GenericUtils.stringToStringNoNull(datiCustomer.getComDiResidenza()));
		figuraAnag.setCapResidenza(GenericUtils.stringToStringNoNull(datiCustomer.getCapResidenza()));
		figuraAnag.setCodStato(GenericUtils.stringToStringNoNull(datiCustomer.getCodStato()));
		figuraAnag.setCodTipoDocumento(GenericUtils.stringToStringNoNull(datiCustomer.getCodTipoDocumento()));
		figuraAnag.setNumeroDocumento(GenericUtils.stringToStringNoNull(datiCustomer.getNumeroDocumento()));
		figuraAnag.setLuogoRilascio(GenericUtils.stringToStringNoNull(datiCustomer.getLuogoRilascio()));
		figuraAnag.setDataRilascio(CommonUtility.dateToString(datiCustomer.getDataRilascio()));
		figuraAnag.setEnteRilascio(GenericUtils.stringToStringNoNull(datiCustomer.getEnteRilascio()));
		figuraAnag.setCab(GenericUtils.integerToStringNoNull(datiCustomer.getCab()));
		figuraAnag.setGruppoAR(GenericUtils.stringToStringNoNull(datiCustomer.getGruppoAR()));
		figuraAnag.setSottogruppoAR(GenericUtils.stringToStringNoNull(datiCustomer.getSottogruppoAR()));
		figuraAnag.setAtecoAR(GenericUtils.stringToStringNoNull(datiCustomer.getAtecoAR()));
		figuraAnag.setCodStatoRilascio(GenericUtils.stringToStringNoNull(datiCustomer.getCodStatoRilascio()));
		figuraAnag.setCodProvinciaRilascio(GenericUtils.stringToStringNoNull(datiCustomer.getCodProvinciaRilascio()));
		figuraAnag.setDataScadenza(CommonUtility.dateToString(datiCustomer.getDataScadenza()));
		figuraAnag.setPoliticamenteEsposta(GenericUtils.stringToStringNoNull(datiCustomer.getPoliticamenteEsposta()));
		figuraAnag.setTasseEstero(GenericUtils.stringToStringNoNull(datiCustomer.getTasseEstero()));
		figuraAnag.setTasseEsteroCodStato(GenericUtils.stringToStringNoNull(datiCustomer.getTasseEsteroCodStato()));
		figuraAnag.setUsStatus(GenericUtils.stringToStringNoNull(datiCustomer.getUsStatus()));
		figuraAnag.setTaxIdNumber(GenericUtils.stringToStringNoNull(datiCustomer.getTaxIdNumber()));
		figuraAnag.setOrigineFondi(GenericUtils.stringToStringNoNull(datiCustomer.getOrigineFondi()));
		figuraAnag.setOrigineFondiAltro(GenericUtils.stringToStringNoNull(datiCustomer.getOrigineFondiAltro()));
		figuraAnag.setTipoRelazione(GenericUtils.stringToStringNoNull(datiCustomer.getTipoRelazione()));
		figuraAnag.setFlDelega(GenericUtils.booleanToString(datiCustomer.isFlDelega()));
		figuraAnag.setAttPrevalenteCod(GenericUtils.stringToStringNoNull(datiCustomer.getAttPrevalenteCod()));
		figuraAnag.setTipoRappresentanza(GenericUtils.stringToStringNoNull(datiCustomer.getTipoRappresentanza()));
		figuraAnag.setAltroTipoRappresentanza(GenericUtils.stringToStringNoNull(datiCustomer.getAltroTipoRappresentanza()));
		figuraAnag.setAltroRelazione(GenericUtils.stringToStringNoNull(datiCustomer.getAltroRelazione()));
		ArrayList<DatiAnagraficheDTO> figuraAnagrafica = new ArrayList<DatiAnagraficheDTO>();
		figuraAnagrafica.add(figuraAnag);
		request.setFiguraAnagrafica(figuraAnagrafica);
		return request;
	}

	public static SalvaSoggettoTerzoResponse valorizzaSalvaSoggettoTerzoRequest(AnagraficaDettaglioResponseDTO adResponse) {
		SalvaSoggettoTerzoResponse response = new SalvaSoggettoTerzoResponse();
		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(adResponse.getInteractionDTO()));
		return response;
	}

	public static SalvaSoggettoTerzoResponse valorizzaException(SalvaSoggettoTerzoRequest body, Exception e) {
		SalvaSoggettoTerzoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}

	private static SalvaSoggettoTerzoResponse valorizzaCommon(SalvaSoggettoTerzoRequest body) {
		SalvaSoggettoTerzoResponse response = new SalvaSoggettoTerzoResponse();
		response.setHeaderCobolSrv(body.getHeaderCobolSrv());
		response.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return response;
	}
	
	public static void valorizzaContraenteCdErrFromSoggettoTerzo(List<ErroreCampo> listaDatiFigura, DatiAnagraficheDTO soggettoTerzo) {
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tipoRuolo", soggettoTerzo.getTipoRuolo());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceCliente", soggettoTerzo.getCodiceCliente());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "cognome", soggettoTerzo.getCognome());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "nome", soggettoTerzo.getNome());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceFiscale", soggettoTerzo.getCodiceFiscale());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataDiNascita", soggettoTerzo.getDataDiNascita());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codProvinciaDiNascita", soggettoTerzo.getCodProvinciaDiNascita());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "luogoDiNascita", soggettoTerzo.getLuogoDiNascita());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codSesso", soggettoTerzo.getCodSesso());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceProfessione", soggettoTerzo.getCodiceProfessione());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tipoPersona", soggettoTerzo.getTipoPersona());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "cittadinanza1", soggettoTerzo.getCittadinanza1());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "cittadinanza2", soggettoTerzo.getCittadinanza2());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "cittadinanza3", soggettoTerzo.getCittadinanza3());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codProvinciaDiResidenza", soggettoTerzo.getCodProvinciaDiResidenza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "comDiResidenza", soggettoTerzo.getComDiResidenza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "indirizzoDiResidenza", soggettoTerzo.getIndirizzoDiResidenza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "capResidenza", soggettoTerzo.getCapResidenza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codStato", soggettoTerzo.getCodStato());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codTipoDocumento", soggettoTerzo.getCodTipoDocumento());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "numeroDocumento", soggettoTerzo.getNumeroDocumento());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "luogoRilascio", soggettoTerzo.getLuogoRilascio());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataRilascio", soggettoTerzo.getDataRilascio());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "enteRilascio", soggettoTerzo.getEnteRilascio());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "cab", soggettoTerzo.getCab());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "gruppoAR", soggettoTerzo.getGruppoAR());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sottogruppoAR", soggettoTerzo.getSottogruppoAR());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "atecoAR", soggettoTerzo.getAtecoAR());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "politicamenteEsposta", soggettoTerzo.getPoliticamenteEsposta());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tasseEstero", soggettoTerzo.getTasseEstero());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tasseEsteroCodStato", soggettoTerzo.getTasseEsteroCodStato());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "usStatus", soggettoTerzo.getUsStatus());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "taxIdNumber", soggettoTerzo.getTaxIdNumber());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "attPrevalenteCod", soggettoTerzo.getAttPrevalenteCod());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "origineFondi", soggettoTerzo.getOrigineFondi());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "origineFondiAltro", soggettoTerzo.getOrigineFondiAltro());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codStatoRilascio", soggettoTerzo.getCodStatoRilascio());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codProvinciaRilascio", soggettoTerzo.getCodProvinciaRilascio());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataScadenza", soggettoTerzo.getDataScadenza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tipoRelazione", soggettoTerzo.getTipoRelazione());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flDelega", soggettoTerzo.getFlDelega());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tipoRappresentanza", soggettoTerzo.getTipoRappresentanza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "altroTipoRappresentanza", soggettoTerzo.getAltroTipoRappresentanza());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "altroRelazione", soggettoTerzo.getAltroRelazione());
	}

	public static SalvaSoggettoTerzoResponse valorizzaViolation(SalvaSoggettoTerzoRequest body, List<String> validatorViolations) {
		SalvaSoggettoTerzoResponse response = new SalvaSoggettoTerzoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}
	

	public static ControllaVAEmessoResponse valorizzaViolation(ControllaVAEmessoRequest body, List<String> validatorViolations) {
		ControllaVAEmessoResponse response = new ControllaVAEmessoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}

	public static ControllaVAEmessoResponse valorizzaControllaVAEmessoResponse(DatiTecniciUTResponseDTO datiTecniciResponse, PropostaRiepilogoResponseDTO propostaRiepilogoResponse) {
//		datiTecniciResponse -> VWLSE560
//		propostaRiepilogoResponse -> VWLSE459
		ControllaVAEmessoResponse response = new ControllaVAEmessoResponse();
		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(propostaRiepilogoResponse.getInteractionDTO()));
		response.setPercAllocazioneUL(GenericUtils.stringToBigDecimal(datiTecniciResponse.getPercentualeI(), SrvConstants.ZERO));
		response.setPercAllocazioneGS(GenericUtils.stringToBigDecimal(datiTecniciResponse.getPercentualeII(), SrvConstants.ZERO));
		response.setCodiceConvenzione(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodiceConvenzione()));
		response.setCodiceDeroga(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodiceDeroga()));
		response.setPreRataCompl(GenericUtils.stringToStringNoNull(datiTecniciResponse.getPreRataCompl()));
		valorizzaPremioGestioneSeparataPremioUnitLinked(propostaRiepilogoResponse, response);
		//campi presi dal VWLSE559
		response.setCodLineaInvestimento(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodLineaInvestimento())); 
		response.setCodOpzFacoltativa(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodFacoltativo()));
		response.setCodImportoCedola(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodImportoCedola()));
		response.setCodDurataCedola(GenericUtils.stringToStringNoNull(datiTecniciResponse.getCodDurataCedola()));		
		return response;
	}

	public static ControllaVAEmessoResponse valorizzaException(ControllaVAEmessoRequest body, ApplicationException e) {
		ControllaVAEmessoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}

	private static ControllaVAEmessoResponse valorizzaCommon(ControllaVAEmessoRequest body) {
		ControllaVAEmessoResponse response = new ControllaVAEmessoResponse();
		response.setHeaderCobolSrv(body.getHeaderCobolSrv());
		response.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return response;
	}

	public static ControllaVAEmessoResponse valorizzaException(ControllaVAEmessoRequest body, InfrastructureException e) {
		ControllaVAEmessoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}

	public static ControllaVAEmessoResponse valorizzaException(ControllaVAEmessoRequest body, Exception e) {
		ControllaVAEmessoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}
	public static boolean isScenarioRpp(String tipologiaPratica) {
		return SrvConstants.ATTIVA_CEDOLA.equals(tipologiaPratica) || SrvConstants.DISATT_CEDOLA.equals(tipologiaPratica);
	}
	
	private static CoordinateBancarieInfo setCoordinateBancarieInfo(PrenotazionePostVenditaInfo psInfo, String tipologiaPratica){
		CoordinateBancarieInfo coordinateBancarieInfo = new CoordinateBancarieInfo();
		coordinateBancarieInfo.setCodSoc(0);
		coordinateBancarieInfo.setCodiceCliente(0);
		coordinateBancarieInfo.setIntestatarioConto("");
		coordinateBancarieInfo.setIbanNazione("");
		coordinateBancarieInfo.setIbanCNN(0.00);
		coordinateBancarieInfo.setIbanCIN("");
		coordinateBancarieInfo.setAbi("");
		coordinateBancarieInfo.setCab("");
		coordinateBancarieInfo.setContoCorr("");
		coordinateBancarieInfo.setModalitaPagamento("");
		if(SrvConstants.ATTIVA_CEDOLA.equals(tipologiaPratica)){
			Map<String, String> mappaIban = GenericUtils.scomponiIban(GenericUtils.stringToString(psInfo.getIban()));
			coordinateBancarieInfo.setIbanNazione(mappaIban.get(SrvConstants.IBAN_NAZIONE));
			coordinateBancarieInfo.setIbanCNN(mappaIban.get(SrvConstants.IBAN_CCN) != null ? Double.valueOf(mappaIban.get(SrvConstants.IBAN_CCN)) : 0.00);
			coordinateBancarieInfo.setIbanCIN(mappaIban.get(SrvConstants.IBAN_CIN));
			coordinateBancarieInfo.setAbi(mappaIban.get(SrvConstants.IBAN_ABI));
			coordinateBancarieInfo.setCab(mappaIban.get(SrvConstants.IBAN_CAB));
			coordinateBancarieInfo.setContoCorr(mappaIban.get(SrvConstants.IBAN_CONTO_C));
			coordinateBancarieInfo.setCodSoc(psInfo.getCodSoc());
			coordinateBancarieInfo.setIntestatarioConto(GenericUtils.stringToStringNoNull(psInfo.getIntestatarioConto()));
			coordinateBancarieInfo.setModalitaPagamento(GenericUtils.stringToStringNoNull(psInfo.getModalitaPagamento()));
		}
		return coordinateBancarieInfo;
	}
	
	private static OpzioniContrattualiInfo getOpzioniContrattualiInfoFrom(PrenotazionePostVenditaInfo psInfo, String tipologiaPratica) {
		OpzioniContrattualiInfo opzioniContrattualiInfo = new OpzioniContrattualiInfo();
		String codFacoltativo = GenericUtils.stringToStringNoNull(psInfo.getOpzioneFacoltativa());
		opzioniContrattualiInfo.setFlagOpzioneFacoltativa(codFacoltativo);
		String tipoOperazione = getTipoOperazioneFrom(tipologiaPratica);
		if(codFacoltativo.equals(SrvConstants.CEDOLA)) {// RPP
			opzioniContrattualiInfo.setImportoPercentualeCedola(psInfo.getImportoCedola());
			opzioniContrattualiInfo.setDurataCedola(psInfo.getDurataCedola());
			opzioniContrattualiInfo.setPeriodCedola(psInfo.getPeriodCedola());
			opzioniContrattualiInfo.setObiettivoTakeProfit(0.00);
		} else if(codFacoltativo.equals(SrvConstants.TAKEPROFIT)) {
			opzioniContrattualiInfo.setImportoPercentualeCedola(0.00);
			opzioniContrattualiInfo.setPeriodCedola(new Short("0"));
			opzioniContrattualiInfo.setDurataCedola(0);
			opzioniContrattualiInfo.setObiettivoTakeProfit(psInfo.getObiettivoTakeProfit());
		} else if(codFacoltativo.equals(SrvConstants.LIFECYCLE)) {
			opzioniContrattualiInfo.setImportoPercentualeCedola(0.00);
			opzioniContrattualiInfo.setPeriodCedola(new Short("0"));
			opzioniContrattualiInfo.setDurataCedola(0);
			opzioniContrattualiInfo.setObiettivoTakeProfit(0.00);
		}
		if(SrvConstants.OP_ATTIVAZIONE.equals(tipoOperazione)) {
			opzioniContrattualiInfo.setDataAdesione(psInfo.getDataPrenotazione());
			opzioniContrattualiInfo.setDataRevoca(DateUtils.DATE_01_01_0001_AS_LONG);
		} else {//DISATTIVAZIONE
			opzioniContrattualiInfo.setDataAdesione(DateUtils.DATE_01_01_0001_AS_LONG);	
			opzioniContrattualiInfo.setDataRevoca(psInfo.getDataPrenotazione());
		}
		return opzioniContrattualiInfo;
	}
	
	public static PrenotazioneExecutionContext initWorkFlowVAEmessoStartRequest(String permKey, PolizzaInfo chiavePolizza, UtenteInfoResponse utenteInfoResponse, String cognomeRagSocialeContraente, String nomeContraente) {
		PrenotazioneToken tk = new PrenotazioneToken();
		tk.setCognomeRagSocialeContraente(cognomeRagSocialeContraente.trim().length() > 40 ? cognomeRagSocialeContraente.trim().substring(0, 40) : cognomeRagSocialeContraente.trim());
		tk.setNomeContraente(nomeContraente.trim().length() > 30 ? nomeContraente.trim().substring(0, 30) : nomeContraente.trim());
		tk.setPermanentKey(permKey);
		tk.setCodiceSocieta(chiavePolizza.getCodSocieta());
		tk.setNumCategoria(chiavePolizza.getNumeroCategoria());
		tk.setAgenzia(chiavePolizza.getCodAgenzia());
		tk.setNumCollettiva(chiavePolizza.getNumeroCollettiva());
		tk.setNumeroPolizza(chiavePolizza.getNumeroPolizza());
		
		ReteVendita reteDiVendita = utenteInfoResponse.getReteDiVendita().get(0);
		if(reteDiVendita != null){
			tk.setOrgLevel0(GenericUtils.stringToStringNoNull(reteDiVendita.getGruppo())); 
			tk.setOrgLevel1(GenericUtils.stringToStringNoNull(reteDiVendita.getAgenzia())); 
			tk.setOrgLevel2(GenericUtils.stringToStringNoNull(reteDiVendita.getSubagenzia())); 
			tk.setOrgLevel3(GenericUtils.stringToStringNoNull(reteDiVendita.getCollocatore()));
		}	
		
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setPolizzaFK((tk.getNumeroPolizza().compareTo(new Integer(0)) == 0)
									? tk.getNumCollettiva().toString()
									: tk.getNumeroPolizza().toString());
		executionCtx.setToken(tk);	
		return executionCtx;
	}
	
	public static PrenotazioneExecutionContext initWorkFlowRiscattoStartRequest(String permKey, PolizzaInfo chiavePolizza, UtenteInfoResponse utenteInfoResponse, String cognomeRagSocialeContraente, String nomeContraente) {
		return initWorkFlowVAEmessoStartRequest(permKey, chiavePolizza, utenteInfoResponse, cognomeRagSocialeContraente, nomeContraente);
	}
	
	public static PrenotazioneExecutionContext initWorkFlowScadenzaStartRequest(String permKey, PolizzaInfo chiavePolizza, UtenteInfoResponse utenteInfoResponse, String cognomeRagSocialeContraente, String nomeContraente) {
		return initWorkFlowVAEmessoStartRequest(permKey, chiavePolizza, utenteInfoResponse, cognomeRagSocialeContraente, nomeContraente);
	}
	
	public static PrenotazioneExecutionContext initWorkFlowRiscattoInoltraRequest(HeaderCobolSrv headerCobolSrv, PrenotazioneToken tk) {
		return initWorkFlowVAEmessoInoltraRequest(headerCobolSrv, tk);
	}
	
	public static PrenotazioneExecutionContext initWorkFlowScadenzaInoltraRequest(HeaderCobolSrv headerCobolSrv, PrenotazioneToken tk) {
		return initWorkFlowVAEmessoInoltraRequest(headerCobolSrv, tk);
	}
	
	public static SalvaVersamentoAggiuntivoRequest gestioneSalvaVersamentoAggiuntivoRequestInserimento(SalvaPrenotazVAEmessoRequest body, int unCodCustomer) throws Exception{
		SalvaVersamentoAggiuntivoRequest salvaVersamentoAggiuntivoRequest = new SalvaVersamentoAggiuntivoRequest();
		salvaVersamentoAggiuntivoRequest.setTipoOperazione("S");
//		salvaVersamentoAggiuntivoRequest.setTipoOperazione("C".equals(requestDTO.getSottoFunzionePrenotaz()) ? "C" : "I");
		salvaVersamentoAggiuntivoRequest.setTipoOperazioneDB("S");
		
//		boolean isNuovoControlloAmleto = getSessionObject(request, SrvConstants.CONTROLLI_AMLETO)==null ? false : (boolean) getSessionObject(request, SrvConstants.CONTROLLI_AMLETO);

		//if (SrvConstants.NO.equals(versamentoAggiuntivoForm.getViPensiono())){
//		if (isNuovoControlloAmleto){
		String user = body.getUtente();
//		String codCustomer = body.getCodCustomer();
		String codCustomer = "" + unCodCustomer;
		List<OrigineFondiInfo> listaOrigFondi = new ArrayList<OrigineFondiInfo>();
		for(it.sistinf.rest.cobol.model.proposta.common.OrigineFondiInfo origineFondo : body.getOrigineFondiList()){
			OrigineFondiInfo origine = valorizzaOrigineFondo(body.getPolizzaInfo(), user, origineFondo, codCustomer);
			listaOrigFondi.add(origine);
		}
		salvaVersamentoAggiuntivoRequest.getOrigineFondiInfo().addAll(listaOrigFondi);
		salvaVersamentoAggiuntivoRequest.setModPagamentoInfo(valorizzaModPagamento(body.getPolizzaInfo(), user, body.getDatiModPagamento()));
		salvaVersamentoAggiuntivoRequest.setValutazioneIntermediarioInfo(valorizzaValutazioneInterme(body.getPolizzaInfo(), user, body.getDatiValutazioneIntermediario(), codCustomer));
		
//		if("".equals(GenericUtils.stringToStringNoNull(versamentoAggiuntivoForm.getDataApprovazione())) || "".equals(GenericUtils.stringToStringNoNull(versamentoAggiuntivoForm.getSoggettoApprovatore()))){
//			versamentoAggiuntivoForm.setBlocchiWorkflowListToInsert(new ArrayList<BlocchiWorkflowInfo>());
//		}else{
//			gestioneListaBlocchiToInsert(listMotiviCompleta, versamentoAggiuntivoForm, request, sessioneDTO, user);
//		}
//		salvaVersamentoAggiuntivoRequest.getBlocchiWorkflowInfo().addAll(versamentoAggiuntivoForm.getBlocchiWorkflowListToInsert());

		return salvaVersamentoAggiuntivoRequest;
		//valorizza soggetto terzo
//			if(versamentoAggiuntivoForm.getSoggettoTerzoVersamento()!=null){
//				versamentoAggiuntivoForm.getSoggettoTerzoVersamento().getDatiAltro().setUserCreaz(user);
//				versamentoAggiuntivoForm.getSoggettoTerzoVersamento().getDatiAltro().setUserVariaz("");
//				versamentoAggiuntivoForm.getSoggettoTerzoVersamento().getDatiGenerali().setCodSoc(getInteraction(request).getCodiceSocieta());
//				salvaVersamentoAggiuntivoRequest.setSoggettoTerzo(versamentoAggiuntivoForm.getSoggettoTerzoVersamento());
//			}
		}
//		else if(GenericUtils.stringToStringNoNull(requestDTO.getFlIncassoZurich()).equals(SrvConstants.SI)){
//			if (versamentoAggiuntivoForm.getModPagamentoInfo()!=null){
//				versamentoAggiuntivoForm.getModPagamentoInfo().setUserCreaz(user);
//				versamentoAggiuntivoForm.getModPagamentoInfo().setUserVariaz(user);
//				salvaVersamentoAggiuntivoRequest.setModPagamentoInfo(versamentoAggiuntivoForm.getModPagamentoInfo());
//			}
//		}

	private static ValutazioneIntermediarioInfo valorizzaValutazioneInterme(PolizzaInfo chiaveRapporto,String user, DatiValutazioneIntermediario datiValInter, String codCustomer) {
		ValutazioneIntermediarioInfo valutazioneIntermediarioInfo = new ValutazioneIntermediarioInfo();
		valutazioneIntermediarioInfo.setCodSoc(chiaveRapporto.getCodSocieta());
		valutazioneIntermediarioInfo.setAgenzia(GenericUtils.stringToString(chiaveRapporto.getCodAgenzia()));
		valutazioneIntermediarioInfo.setCategoria(GenericUtils.stringToString(chiaveRapporto.getNumeroCategoria()));
		valutazioneIntermediarioInfo.setNumColl(chiaveRapporto.getNumeroCollettiva());
		valutazioneIntermediarioInfo.setNumPolizza(chiaveRapporto.getNumeroPolizza());
		valutazioneIntermediarioInfo.setFlagPropPol(SrvConstants.TIPO_RAPP_RID_POLIZZA);
		valutazioneIntermediarioInfo.setDataOperazione(datiValInter.getDataOperazione().getTime());
//		valutazioneIntermediarioInfo.setTipoMov();
		Short dupkey = 0;
		valutazioneIntermediarioInfo.setDupkey(dupkey);
		valutazioneIntermediarioInfo.setCodCustomer(GenericUtils.stringToInteger(codCustomer));
		valutazioneIntermediarioInfo.setCodCli(new Integer(0));
		valutazioneIntermediarioInfo.setRichDiretta(GenericUtils.booleanToString(datiValInter.isRichDiretta()));
		valutazioneIntermediarioInfo.setValutCliente(GenericUtils.stringToString(datiValInter.getValutCliente()));
		valutazioneIntermediarioInfo.setAnniRapportoInterm(GenericUtils.stringToString(datiValInter.getAnniRapportoInterm()));
		valutazioneIntermediarioInfo.setProfRischioInterm(GenericUtils.stringToString(datiValInter.getProfRischioInterm()));
		valutazioneIntermediarioInfo.setProfRischioKyc(SrvConstants.SPACE);
		valutazioneIntermediarioInfo.setOpeCoerente(GenericUtils.booleanToString(datiValInter.isOpeCoerente()));
		valutazioneIntermediarioInfo.setDataCreaz(new Date().getTime());
		valutazioneIntermediarioInfo.setDataVariaz(new Date().getTime());
		valutazioneIntermediarioInfo.setUserCreaz(user);
		valutazioneIntermediarioInfo.setUserVariaz(user);
		return valutazioneIntermediarioInfo;
	}

	public static ModPagamentoInfo valorizzaModPagamento(PolizzaInfo chiaveRapporto, String username, it.sistinf.rest.cobol.model.proposta.common.ModPagamentoInfo modPagamentoInput) throws Exception {
		ModPagamentoInfo modPagamento = new ModPagamentoInfo();
		modPagamento.setCodSoc(chiaveRapporto.getCodSocieta());
		modPagamento.setCategoria(GenericUtils.stringToString(chiaveRapporto.getNumeroCategoria()));
		modPagamento.setAgenzia(GenericUtils.stringToString(chiaveRapporto.getCodAgenzia()));
		modPagamento.setNumColl(chiaveRapporto.getNumeroCollettiva());
		modPagamento.setNumPolizza(chiaveRapporto.getNumeroPolizza());
		modPagamento.setFlagPropPol(SrvConstants.TIPO_RAPP_RID_POLIZZA);
		modPagamento.setDataEffetto(modPagamentoInput.getDataEffetto() != null ? modPagamentoInput.getDataEffetto().getTime() : null);
		modPagamento.setCodOperazione(SrvConstants.CD_OPERAZIONE_PRIMA_RATA);
		Short dupkey = 0;
		modPagamento.setDupkey(dupkey);
		modPagamento.setPagatorePercipiente(GenericUtils.stringToStringNoNull(modPagamentoInput.getPagatore()));
		modPagamento.setCodCustPagPercipiente(GenericUtils.stringToIntegerWithValue(modPagamentoInput.getCodCustPagPercipiente(), 0));
		modPagamento.setModPagamento(GenericUtils.stringToStringNoNull(modPagamentoInput.getModPagamento()));
		modPagamento.setNumAssegno(GenericUtils.stringToStringNoNull(modPagamentoInput.getNumAssegno()));
		modPagamento.setIbanPaese(""); 
		modPagamento.setIbanCcn(""); 
		modPagamento.setIbanCin(""); 
		modPagamento.setIbanAbi(""); 
		modPagamento.setIbanCab(""); 
		modPagamento.setIbanContoCc(""); 
		String codiceIbanPrimaRata = GenericUtils.stringToStringNoNull(modPagamentoInput.getCodiceIbanPrimaRata());
		if(!codiceIbanPrimaRata.equals("")){
			if(codiceIbanPrimaRata.length() == 27){
				modPagamento.setIbanPaese(codiceIbanPrimaRata.length() > 1 ? codiceIbanPrimaRata.substring(0,2) : "");
				modPagamento.setIbanCcn(codiceIbanPrimaRata.length() > 3 ? codiceIbanPrimaRata.substring(2,4) : "");
				modPagamento.setIbanCin(codiceIbanPrimaRata.length() > 4 ? codiceIbanPrimaRata.substring(4,5) : "");
				modPagamento.setIbanAbi(codiceIbanPrimaRata.length() > 9 ? codiceIbanPrimaRata.substring(5,10) : "");
				modPagamento.setIbanCab(codiceIbanPrimaRata.length() > 14 ? codiceIbanPrimaRata.substring(10,15) : "");
				modPagamento.setIbanContoCc(codiceIbanPrimaRata.length() > 26 ? codiceIbanPrimaRata.substring(15,27) : "");
			} else {
				throw new Exception("IBAN NON CORRETTO");
			}
		} else {
			modPagamento.setIbanAbi(GenericUtils.stringToStringNoNull(modPagamentoInput.getCodBancaPrimaRata())); 
			modPagamento.setIbanCab(GenericUtils.stringToStringNoNull(modPagamentoInput.getCodFilialePrimaRata())); 
		}
		modPagamento.setIntestatario(GenericUtils.stringToStringNoNull(modPagamentoInput.getIntestatarioPrimaRata()));
		modPagamento.setFlagCointest(GenericUtils.booleanToString(modPagamentoInput.isContoCointestato()));
		modPagamento.setCointestatario(GenericUtils.stringToStringNoNull(modPagamentoInput.getNomeCointestatario()));
		modPagamento.setRelContrCointest(GenericUtils.stringToStringNoNull(modPagamentoInput.getRelContrCointest()));
		modPagamento.setRelContrCointestAltro(GenericUtils.stringToStringNoNull(modPagamentoInput.getRelContrCointestAltro()));
		modPagamento.setSwift(GenericUtils.stringToStringNoNull(modPagamentoInput.getSwift()));
		modPagamento.setPaese(GenericUtils.stringToStringNoNull(modPagamentoInput.getPaese()));
		modPagamento.setMotivo(GenericUtils.stringToStringNoNull(modPagamentoInput.getMotivoContoEstero()));
		modPagamento.setNumSottoRub(GenericUtils.stringToStringNoNull(modPagamentoInput.getNumSottoRub()));
		modPagamento.setIdPrenotazione(new Integer(0));
		modPagamento.setUserCreaz(username);
		modPagamento.setUserVariaz(username);
		modPagamento.setDataContoZurich(new Date().getTime());
		return modPagamento;
	}

	private static OrigineFondiInfo valorizzaOrigineFondo(PolizzaInfo chiaveRapporto, String username, it.sistinf.rest.cobol.model.proposta.common.OrigineFondiInfo origineFondoInput, String codCustomer) {
		OrigineFondiInfo origFondo = new OrigineFondiInfo();			
		origFondo.setCodSoc(chiaveRapporto.getCodSocieta());
		origFondo.setCategoria(GenericUtils.stringToString(chiaveRapporto.getNumeroCategoria()));
		origFondo.setAgenzia(GenericUtils.stringToString(chiaveRapporto.getCodAgenzia()));
		origFondo.setNumColl(chiaveRapporto.getNumeroCollettiva());
		origFondo.setNumPolizza(chiaveRapporto.getNumeroPolizza());
		origFondo.setFlagPropPol(SrvConstants.TIPO_RAPP_RID_POLIZZA);
		
		origFondo.setDataOperazione(origineFondoInput.getDataOperazione().getTime());
		origFondo.setTipoMov(Double.valueOf(SrvConstants.TIPO_EVENTO_EMISSIONE_ONLINE)); //TODO
		origFondo.setCodCustomer(GenericUtils.stringToInteger(codCustomer));
		origFondo.setOrigineFondi(GenericUtils.stringToStringNoNull(origineFondoInput.getOrigineFondi()));
		origFondo.setOrigineFondiAltro(GenericUtils.stringToStringNoNull(origineFondoInput.getOrigineFondiAltro()));
		origFondo.setCodCli(new Integer(0));
		origFondo.setTempo(GenericUtils.stringToStringNoNull(origineFondoInput.getIntervalloPagamentoFondi()));
		origFondo.setCodPaeseProvenienzaSomme(GenericUtils.stringToStringNoNull(origineFondoInput.getCodPaeseProvenienzaSomme()));
		origFondo.setIdPrenotazione(new Integer(0));
		origFondo.setDataCreaz(new Date().getTime());
		origFondo.setDataVariaz(new Date().getTime());
		origFondo.setUserCreaz(username);
		origFondo.setUserVariaz(username);
		return origFondo;
	}

	public static SalvaPrenotazVAEmessoResponse valorizzaException(SalvaPrenotazVAEmessoRequest body, String errMess) {
		SalvaPrenotazVAEmessoResponse response = valorizzaCommon(body);
		List<Errore> errori = new ArrayList<Errore>();
		Errore errore = InteractionUtility.valorizzaErrore(RestSrvConstants.INFERR0001, RestSrvConstants.ERRORE_BLOCCANTE, errMess);
		errori.add(errore); 
		return response;
	}
	
	public static DatiInputControllaVAEmesso valorizzaDatiInputControllaVAEmesso(PropostaRiepilogoResponseDTO listaUTResponse, DatiTecniciUTResponseDTO caricaUTResponse, String tipologiaProdotto) {
//		VWLSE559 -> listaUTResponse
//		VWLSE458 -> caricaUTResponse
		DatiInputControllaVAEmesso datiInputControllaVAEmesso = new DatiInputControllaVAEmesso();
		DatiVideoVA datiVideoVA = valorizzaDatiVideoVA(caricaUTResponse);
		DatiVideoVAEmesso datiVideoVAEmesso = valorizzaDatiVideoVAEmesso(listaUTResponse);
		if("tranching".equals(tipologiaProdotto)){
			DatiWorkflowVA datiWorkflowVA = valorizzaDatiWorkflowVA(listaUTResponse);
			datiInputControllaVAEmesso.setDatiWorkflowVA(datiWorkflowVA);
			DatiIdentificativiVA datiIdentificativiVA = valorizzaDatiIdentificativiVA(listaUTResponse);
			datiInputControllaVAEmesso.setDatiIdentificativiVA(datiIdentificativiVA);
		}
		DatiPrincipaliVA datiPrincipaliVA = valorizzaDatiPrincipaliVA(caricaUTResponse);
		DatiTecniciVA datiTecniciVA = valorizzaDatiTecniciVA(caricaUTResponse);
		SezioniPremiVA sezioniPremiVA = valorizzaSezioniPremiVA(caricaUTResponse);
		FondiVA fondiVA = valorizzaFondiVA(caricaUTResponse);
		datiInputControllaVAEmesso.setDatiVideoVA(datiVideoVA);
		datiInputControllaVAEmesso.setDatiVideoVAEmesso(datiVideoVAEmesso);
		datiInputControllaVAEmesso.setDatiPrincipaliVA(datiPrincipaliVA);
		datiInputControllaVAEmesso.setDatiTecniciVA(datiTecniciVA);
		datiInputControllaVAEmesso.setSezioniPremiVA(sezioniPremiVA);
		datiInputControllaVAEmesso.setFondiVA(fondiVA);
		datiInputControllaVAEmesso.setPosizioneUT(GenericUtils.stringToStringNoNull(caricaUTResponse.getPosizioneUT()));
		return datiInputControllaVAEmesso;
	}

	private static DatiVideoVA valorizzaDatiVideoVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		DatiVideoVA datiVideoVA = new DatiVideoVA();
		datiVideoVA.setNuovaUT(GenericUtils.stringToStringNoNull(caricaUTResponse.getNuovaUT()));
		datiVideoVA.setProdottoUnit(GenericUtils.stringToStringNoNull(caricaUTResponse.getProdottoUnit()));
		datiVideoVA.setFlPremio(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPremio()));
		datiVideoVA.setFlPremioNetto(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPremioNetto()));
		datiVideoVA.setFlPremioRata(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPremioRata()));
		datiVideoVA.setFlPremioIngresso(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPremioIngresso()));
		datiVideoVA.setFlResiduoPlafond(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlResiduoPlafond()));
		datiVideoVA.setFlPrestaCapRend(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPrestaCapRend()));
		datiVideoVA.setFlDecrescenza(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDecrescenza()));
		datiVideoVA.setFlDurataDecor(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurataDecor()));
		datiVideoVA.setFlFrazionamento(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlFrazionamento()));
		datiVideoVA.setFlDoublePrem(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDoublePrem()));
		datiVideoVA.setFlVisita(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlVisita()));
		datiVideoVA.setFlInizioRateaz(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlInizioRateaz()));
		datiVideoVA.setFlContrattoScudato(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlContrattoScudato()));
		datiVideoVA.setFlPremioLim(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPremioLim()));
		datiVideoVA.setFlRischiComuni(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlRischiComuni()));
		datiVideoVA.setFlVincolo(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlVincolo()));
		datiVideoVA.setFlPrestazione(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPrestazione()));
		datiVideoVA.setFlDurPagPremi(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurPagPremi()));
		datiVideoVA.setFlDurataAnni(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurataAnni()));
		datiVideoVA.setFlDurataMesi(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurataMesi()));
		datiVideoVA.setFlDurataAnniPremi(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurataAnniPremi()));
		datiVideoVA.setFlDurataMesiPremi(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDurataMesiPremi()));
		datiVideoVA.setFlPip(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlPip()));
		return datiVideoVA;
	}
	
	private static DatiVideoVAEmesso valorizzaDatiVideoVAEmesso(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiVideoVAEmesso datiVideoVAEmesso = new DatiVideoVAEmesso();
		datiVideoVAEmesso.setFlOpzioneFacoltativa(GenericUtils.stringToStringNoNull(listaUTResponse.getFlOpzioneFacoltativa()));
		datiVideoVAEmesso.setFlVAProgrammatico(GenericUtils.stringToStringNoNull(listaUTResponse.getFlVAProgrammatico()));
		datiVideoVAEmesso.setOpzioneGestionale(GenericUtils.stringToStringNoNull(listaUTResponse.getOpzioneGestionale()));
		datiVideoVAEmesso.setCodLineaInvestimento(GenericUtils.stringToStringNoNull(listaUTResponse.getCodLineaInvestimento()));
		datiVideoVAEmesso.setDataRichiestaCliente(GenericUtils.stringToStringNoNull(listaUTResponse.getDataRichiestaCliente()));
		datiVideoVAEmesso.setVolontaTrasferimento(GenericUtils.stringToStringNoNull(listaUTResponse.getVolontaTrasferimento()));//campo che serve nel giro 'altro' del controlla
		return datiVideoVAEmesso;
	}

	private static DatiWorkflowVA valorizzaDatiWorkflowVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiWorkflowVA datiWorkflowVA = new DatiWorkflowVA();
		datiWorkflowVA.setIdPrenotazione(GenericUtils.stringToStringNoNull(listaUTResponse.getIdPrenotazione()));
		datiWorkflowVA.setTipoPrenotazione(GenericUtils.stringToStringNoNull(listaUTResponse.getTipoPrenotazione()));
		return datiWorkflowVA;
	}
	
	private static DatiIdentificativiVA valorizzaDatiIdentificativiVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiIdentificativiVA datiIdentificativiVA = new DatiIdentificativiVA();
		datiIdentificativiVA.setFlProfiloLibero(GenericUtils.stringToBooleanNoNull(listaUTResponse.getFlProfiloLibero()));
		datiIdentificativiVA.setOpzioniFacoltaVA(valorizzaOpzioniFacoltaVA(listaUTResponse));
		datiIdentificativiVA.setDatiProgrammatiVA(valorizzaDatiProgrammatiVA(listaUTResponse));
		datiIdentificativiVA.setDatiSelezioneAttivitaVA(valorizzaDatiSelezioneAttivitaVA(listaUTResponse));
		datiIdentificativiVA.setGaranzieFondiListVA(valorizzaGaranzieFondiListVA(listaUTResponse));
		return datiIdentificativiVA;
	}

	private static OpzioniFacoltaVA valorizzaOpzioniFacoltaVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		OpzioniFacoltaVA opzioniFacoltaVA = new OpzioniFacoltaVA();
		opzioniFacoltaVA.setCodFacoltativo(GenericUtils.stringToStringNoNull(listaUTResponse.getCodFacoltativo()));
		opzioniFacoltaVA.setCodImportoCedola(GenericUtils.stringToStringNoNull(listaUTResponse.getCodImportoCedola()));
		opzioniFacoltaVA.setCodDurataCedola(GenericUtils.stringToStringNoNull(listaUTResponse.getCodDurataCedola()));
		return opzioniFacoltaVA;
	}
	
	private static DatiProgrammatiVA valorizzaDatiProgrammatiVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiProgrammatiVA datiProgrammatiVA = new DatiProgrammatiVA();
		datiProgrammatiVA.setPeriodicitaVA(GenericUtils.stringToStringNoNull(listaUTResponse.getPeriodicitaVA()));
		datiProgrammatiVA.setImportoVA(GenericUtils.stringToStringNoNull(listaUTResponse.getImportoVA()));
		return datiProgrammatiVA;
	}
	
	private static DatiSelezioneAttivitaVA valorizzaDatiSelezioneAttivitaVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiSelezioneAttivitaVA datiSelezioneAttivitaVA = new DatiSelezioneAttivitaVA();
		datiSelezioneAttivitaVA.setFlMultiramo(GenericUtils.stringToStringNoNull(listaUTResponse.getFlMultiramo()));
		datiSelezioneAttivitaVA.setNumUTMultiramo(""+listaUTResponse.getNumUTMultiramo());
		datiSelezioneAttivitaVA.setTotale(GenericUtils.stringToStringNoNull(listaUTResponse.getTotale()));
		return datiSelezioneAttivitaVA;
	}
	
	private static List<FondoVA> valorizzaGaranzieFondiListVA(PropostaRiepilogoResponseDTO listaUTResponse) {
		List<FondoVA> garanzieFondiListVA = new ArrayList<FondoVA>();
		FondoVA fondoVA = new FondoVA();
		fondoVA.setFlFondi(listaUTResponse.getFlFondiI());
		fondoVA.setCodiceUT(listaUTResponse.getCodiceUTI());
		fondoVA.setDescCodiceUT(listaUTResponse.getDescCodiceUTI());
		fondoVA.setMinimo(GenericUtils.stringToBigDecimal(listaUTResponse.getMinimoI()));
		fondoVA.setMassimo(GenericUtils.stringToBigDecimal(listaUTResponse.getMassimoI()));
		fondoVA.setPercentuale(GenericUtils.stringToBigDecimal(listaUTResponse.getPercentualeI()));
		fondoVA.setFlProtetto(listaUTResponse.getFlProtettoI());
		garanzieFondiListVA.add(fondoVA);
		
		fondoVA = new FondoVA();
		fondoVA.setFlFondi(listaUTResponse.getFlFondiII());
		fondoVA.setCodiceUT(listaUTResponse.getCodiceUTII());
		fondoVA.setDescCodiceUT(listaUTResponse.getDescCodiceUTII());
		fondoVA.setMinimo(GenericUtils.stringToBigDecimal(listaUTResponse.getMinimoII()));
		fondoVA.setMassimo(GenericUtils.stringToBigDecimal(listaUTResponse.getMassimoII()));
		fondoVA.setPercentuale(GenericUtils.stringToBigDecimal(listaUTResponse.getPercentualeII()));
		fondoVA.setFlProtetto(listaUTResponse.getFlProtettoII());
		garanzieFondiListVA.add(fondoVA);
		return garanzieFondiListVA;
	}
	
	private static DatiPrincipaliVA valorizzaDatiPrincipaliVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		DatiPrincipaliVA datiPrincipaliVA = new DatiPrincipaliVA();
		datiPrincipaliVA.setDataDecorrenzaPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getDataDecorrenzaPrinc()));
		datiPrincipaliVA.setDurataAnniPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataAnniPrinc()));
		datiPrincipaliVA.setDurataMesiPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataMesiPrinc()));
		datiPrincipaliVA.setDataScadenzaPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getDataScadenzaPrinc()));
		datiPrincipaliVA.setEtaAssicPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getEtaAssicPrinc()));
		datiPrincipaliVA.setEtaAssicSecPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getEtaAssicSecPrinc()));
		datiPrincipaliVA.setFrazionamentoPrinc(GenericUtils.stringToStringNoNull(caricaUTResponse.getFrazionamentoPrinc()));
		datiPrincipaliVA.setFrazPrimaRata(GenericUtils.stringToStringNoNull(caricaUTResponse.getFrazPrimaRata()));
		return datiPrincipaliVA;
	}
	
	private static DatiTecniciVA valorizzaDatiTecniciVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		DatiTecniciVA datiTecniciVA = new DatiTecniciVA();
		datiTecniciVA.setCodiceTariffa(GenericUtils.stringToStringNoNull(caricaUTResponse.getCodiceTariffa()));
		datiTecniciVA.setDescrizioneTariffa(GenericUtils.stringToStringNoNull(caricaUTResponse.getDescrizioneTariffa()));
		datiTecniciVA.setDataDecorrenza(GenericUtils.stringToStringNoNull(caricaUTResponse.getDataDecorrenza()));
		datiTecniciVA.setDurataVA(valorizzaDurataVA(caricaUTResponse));
		datiTecniciVA.setDurataPagPremiVA(valorizzaDurataPagPremiVA(caricaUTResponse));
		return datiTecniciVA;
	}

	private static DurataVA valorizzaDurataVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		DurataVA durataVA = new DurataVA();
		durataVA.setDurataAnni(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataAnni()));
		durataVA.setFiller(GenericUtils.stringToStringNoNull(caricaUTResponse.getFiller()));
		durataVA.setDurataMesi(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataMesi()));
		return durataVA;
	}
	
	private static DurataPagPremiVA valorizzaDurataPagPremiVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		DurataPagPremiVA durataPagPremiVA = new DurataPagPremiVA();
		durataPagPremiVA.setDurataPagPremiAnni(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataPagPremiAnni()));
		durataPagPremiVA.setFillerPagPremi(GenericUtils.stringToStringNoNull(caricaUTResponse.getFillerPagPremi()));
		durataPagPremiVA.setDurataPagPremiMesi(GenericUtils.stringToStringNoNull(caricaUTResponse.getDurataPagPremiMesi()));
		durataPagPremiVA.setDataScadenza(GenericUtils.stringToStringNoNull(caricaUTResponse.getDataScadenza()));
		durataPagPremiVA.setCodiceFrazionamento(GenericUtils.stringToStringNoNull(caricaUTResponse.getCodiceFrazionamento()));
		durataPagPremiVA.setMesiRateoIniziale(GenericUtils.stringToStringNoNull(caricaUTResponse.getMesiRateoIniziale()));
		durataPagPremiVA.setDataInvestimento(GenericUtils.stringToStringNoNull(caricaUTResponse.getDataInvestimento()));
		return durataPagPremiVA;
	}
	
	private static SezioniPremiVA valorizzaSezioniPremiVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		SezioniPremiVA sezioniPremiVA = new SezioniPremiVA();
		sezioniPremiVA.setDecrescenza(GenericUtils.stringToStringNoNull(caricaUTResponse.getDecrescenza()));
		sezioniPremiVA.setDescDecrescenza(GenericUtils.stringToStringNoNull(caricaUTResponse.getDescDecrescenza()));
		sezioniPremiVA.setPlafondIndex(GenericUtils.stringToStringNoNull(caricaUTResponse.getPlafondIndex()));
		sezioniPremiVA.setPlafond(GenericUtils.stringToStringNoNull(caricaUTResponse.getPlafond()));
		sezioniPremiVA.setPrevin(GenericUtils.stringToStringNoNull(caricaUTResponse.getPrevin()));
		sezioniPremiVA.setPrevinMin(GenericUtils.stringToStringNoNull(caricaUTResponse.getPrevinMin()));
		sezioniPremiVA.setRateoIni(GenericUtils.stringToStringNoNull(caricaUTResponse.getRateoIni()));
		sezioniPremiVA.setFlVisitaMedica(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlVisitaMedica()));
		sezioniPremiVA.setFlDoublePremium(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlDoublePremium()));
		sezioniPremiVA.setResiduoPlafond(GenericUtils.stringToStringNoNull(caricaUTResponse.getResiduoPlafond()));
		sezioniPremiVA.setTipoPremio(GenericUtils.stringToStringNoNull(caricaUTResponse.getTipoPremio()));
		sezioniPremiVA.setFlFumatore(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlFumatore()));
		sezioniPremiVA.setFlFumatoreSec(GenericUtils.stringToStringNoNull(caricaUTResponse.getFlFumatoreSec()));
		return sezioniPremiVA;
	}
	
	private static FondiVA valorizzaFondiVA(DatiTecniciUTResponseDTO caricaUTResponse) {
		FondiVA fondiVA = new FondiVA();
		ElencoFondoDTO elencoFondoDTO = caricaUTResponse.getElencoFondi();
		fondiVA.setFlAltri(GenericUtils.stringToStringNoNull(elencoFondoDTO.getFlAltri()));
		fondiVA.setPercFondoULTot(GenericUtils.stringToStringNoNull(elencoFondoDTO.getPercFondoULTot()));
		fondiVA.setListaFondi(valorizzaListaFondi(elencoFondoDTO.getFondi()));
		return fondiVA;
	}

	private static List<FondoUL> valorizzaListaFondi(ArrayList<FondoDTO> fondi) {
		List<FondoUL> fondiListUl = new ArrayList<FondoUL>();
		for(FondoDTO fondoDTO : fondi){
			FondoUL fondoUL = new FondoUL();
			fondoUL.setCodice(GenericUtils.stringToStringNoNull(fondoDTO.getCodice()));
			fondoUL.setDescrizione(GenericUtils.stringToStringNoNull(fondoDTO.getDescrizione()));
			fondoUL.setPercentuale(GenericUtils.stringToBigDecimal(GenericUtils.stringToStringNoNull(fondoDTO.getPercentuale())));
			fondiListUl.add(fondoUL);
		}
		return fondiListUl;
	}
	
	public static DatiInputControllaVAEmesso valorizzaDatiInputControllaVAEmessoViPensiono(PropostaRiepilogoResponseDTO listaUTResponse) {
		DatiInputControllaVAEmesso datiInputControllaVAEmesso = new DatiInputControllaVAEmesso();
		DatiVideoVAEmesso datiVideoVAEmesso = valorizzaDatiVideoVAEmesso(listaUTResponse);
		DatiWorkflowVA datiWorkflowVA = valorizzaDatiWorkflowVA(listaUTResponse);
		DatiIdentificativiVA datiIdentificativiVA = valorizzaDatiIdentificativiVA(listaUTResponse);
		datiInputControllaVAEmesso.setDatiVideoVAEmesso(datiVideoVAEmesso);
		datiInputControllaVAEmesso.setDatiWorkflowVA(datiWorkflowVA);
		datiInputControllaVAEmesso.setDatiIdentificativiVA(datiIdentificativiVA);
		return datiInputControllaVAEmesso;
	}

	public static Map<String, Boolean> getMappa(SelectTipoProdottoDettaglioGenericoResponse selectTipoProdottoDettaglioGenericoResponse) {
    	Map<String, Boolean> aMap = new HashMap<String, Boolean>();
		if(!selectTipoProdottoDettaglioGenericoResponse.getProdottiSelect().isEmpty()) {
			ProdottoTipoDettaglio prodottoTD = selectTipoProdottoDettaglioGenericoResponse.getProdottiSelect().get(0);
			aMap = getMappa(prodottoTD);
		}
		return aMap;
	}	
	
	public static Map<String, Boolean> getMappa(ProdottoTipoDettaglio prodottoTD) {
    	Map<String, Boolean> aMap = new HashMap<String, Boolean>();
		String tranching = GenericUtils.stringToString(prodottoTD.getTranching());
		boolean isTranching = SrvConstants.SI.equals(tranching);
		aMap.put("isTranching", isTranching);
		String tipoProdotto = GenericUtils.stringToString(prodottoTD.getMultigaranzia());
		String pip = GenericUtils.stringToString(prodottoTD.getVipensiono());
		boolean isViPensiono = SrvConstants.PIP.equals(pip) && "M".equals(tipoProdotto);
		aMap.put("isViPensiono", isViPensiono);
		boolean isPip = SrvConstants.PIP.equals(pip) && "U".equals(tipoProdotto);
		aMap.put("isPip", isPip);
		boolean isFondiUL = !SrvConstants.PIP.equals(pip) && ("M".equals(tipoProdotto) || "U".equals(tipoProdotto));
		aMap.put("isFondiUL", isFondiUL);
		return aMap;
	}		
	
	public static InoltraPrenotazVAEmessoResponse valorizzaException(InoltraPrenotazVAEmessoRequest body, String errMess) {
		InoltraPrenotazVAEmessoResponse response = valorizzaCommon(body);
		List<Errore> errori = new ArrayList<Errore>();
		Errore errore = InteractionUtility.valorizzaErrore(RestSrvConstants.INFERR0001, RestSrvConstants.ERRORE_BLOCCANTE, errMess);
		errori.add(errore); 
		return response;
	}
	
//	public static InoltraPrenotazVAEmessoResponse valorizzaInoltraPrenotazResponse(InteractionDTO interactionDTO) {
//		InoltraPrenotazVAEmessoResponse response = new InoltraPrenotazVAEmessoResponse();
//		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
//		return response;
//	}

//	public static WorkflowCobolResponse valorizzaWorkflowCobolResponse(InteractionDTO interactionDTO, AreaErroreDTO areaErroreDTO, boolean esito) {
//		WorkflowCobolResponse workflowCobolResponse = new WorkflowCobolResponse();
//		workflowCobolResponse.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
//		workflowCobolResponse.setErrori(InteractionUtility.valorizzaErrori(areaErroreDTO));
//		workflowCobolResponse.setEsito(esito);
//		return workflowCobolResponse;
//	}
	public static <T extends WorkflowCobolResponse> T valorizzaWorkflowCobolResponse(Class<T> responseClass, InteractionDTO interactionDTO, Long idPrenotazione) throws Exception {
        T response = responseClass.getDeclaredConstructor().newInstance();
        if(interactionDTO != null) {
        	response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
        }
        response.setIdPrenotazione(idPrenotazione);
        response.setEsito(true);
        return response;
	}
	
	public static <T extends WorkflowCobolResponse> T valorizzaWorkflowCobolResponse(Class<T> responseClass, InteractionDTO interactionDTO, AreaErroreDTO areaErroreDTO, boolean esito) throws Exception{
		T response = responseClass.getDeclaredConstructor().newInstance();
		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
		response.setErrori(InteractionUtility.valorizzaErrori(areaErroreDTO));
		response.setEsito(esito);
		return response;
	}
	
	public static InoltraPrenotazVAEmessoResponse valorizzaException(InoltraPrenotazVAEmessoRequest body, Exception e) {
		InoltraPrenotazVAEmessoResponse response = valorizzaCommon(body);
		response.setErrori(InteractionUtility.valorizzaException(e));
		return response;
	}

	private static InoltraPrenotazVAEmessoResponse valorizzaCommon(InoltraPrenotazVAEmessoRequest body) {
		InoltraPrenotazVAEmessoResponse response = new InoltraPrenotazVAEmessoResponse();
		response.setHeaderCobolSrv(body.getHeaderCobolSrv());
		response.getHeaderCobolSrv().setReturnCode(RestSrvConstants.ERRORE_BLOCCANTE);
		return response;
	}

	public static InoltraPrenotazVAEmessoResponse valorizzaViolation(InoltraPrenotazVAEmessoRequest body, List<String> validatorViolations) {
		InoltraPrenotazVAEmessoResponse response = new InoltraPrenotazVAEmessoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}

	public static PolizzaInfo valorizzaChiavePolizza(PrenotazioneToken token) {
		PolizzaInfo polizzaInfo = new PolizzaInfo();
		polizzaInfo.setCodSocieta(token.getCodiceSocieta());
		polizzaInfo.setNumeroCategoria(GenericUtils.stringToStringNoNull(token.getNumCategoria()));
		polizzaInfo.setCodAgenzia(GenericUtils.stringToStringNoNull(token.getAgenzia()));
		polizzaInfo.setNumeroCollettiva(token.getNumCollettiva());
		polizzaInfo.setNumeroPolizza(token.getNumeroPolizza());
		return polizzaInfo;
	}

	public static PrenotazioneExecutionContext initWorkFlowVAEmessoInoltraRequest(HeaderCobolSrv headerCobolSrv, PrenotazioneToken tk) {
		PrenotazioneExecutionContext executionCtx = new PrenotazioneExecutionContext();
		executionCtx.setPolizzaFK((tk.getNumeroPolizza().compareTo(new Integer(0)) == 0)
									? tk.getNumCollettiva().toString()
									: tk.getNumeroPolizza().toString());
		tk.setPermanentKey(headerCobolSrv.getPermKey());
		executionCtx.setToken(tk);	
		return executionCtx;
	}
	
	public static DatiTecniciUTRequestDTO valorizzaControllaVAEmessoTranchingRequest(ControllaVAEmessoRequest body, String esisteModPagamento) {
		DatiTecniciUTRequestDTO datiTecniciUTRequestDTO = new DatiTecniciUTRequestDTO();
		datiTecniciUTRequestDTO.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
//		datiTecniciUTRequestDTO.setFlNavigazione(SrvConstants.SPACE);
		valorizzaCampiDatiVideoVA(body, datiTecniciUTRequestDTO);
		datiTecniciUTRequestDTO.setFlTipoOperazione(SrvConstants.EMESSO);//E
		datiTecniciUTRequestDTO.setFlVersamentoAgg(SrvConstants.SI);
		valorizzaCampiDatiVideoVAEmesso(body, datiTecniciUTRequestDTO);
	
		valorizzaDatiWorkflowVARequest(datiTecniciUTRequestDTO, SrvConstants.TIPO_PRATICA_VERSAMENTO_EMESSO);

//		datiTecniciUTRequestDTO.setDataPervenimentoCartac(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setDataIncasso(SrvConstants.SPACE);
		DatiIdentificativiVA datiIdentificativiVA = body.getDatiIdentificativiVA();
		
		valorizzaOpzioniFacoltaVA(datiTecniciUTRequestDTO, datiIdentificativiVA);
		valorizzaDatiProgrammatiVA(datiTecniciUTRequestDTO, datiIdentificativiVA);
		valorizzaDatiSelezioneAttivitaVA(datiTecniciUTRequestDTO, datiIdentificativiVA);
		valorizzaPrimaSecondaUT(datiTecniciUTRequestDTO, datiIdentificativiVA);
		/*
		datiTecniciUTRequestDTO.setFlFondiIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceUTIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDescCodiceUTIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMinimoIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMassimoIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPercentualeIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlProtettoIII(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlFondiIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceUTIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDescCodiceUTIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMinimoIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMassimoIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPercentualeIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlProtettoIV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlFondiV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceUTV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDescCodiceUTV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMinimoV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setMassimoV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPercentualeV(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlProtettoV(SrvConstants.SPACE);
		 */
		
		datiTecniciUTRequestDTO.setCodPagatorePrimaRata(GenericUtils.stringToStringNoNull(body.getCodPagatorePrimaRata()));//dato input tranching
		datiTecniciUTRequestDTO.setCgRegola(GenericUtils.stringToStringNoNull(body.getCgRegola()));
		datiTecniciUTRequestDTO.setCgProgrMod("000000000");
//		datiTecniciUTRequestDTO.setCgModello(SrvConstants.SPACE);
		DatiPrincipaliVA datiPrincipaliVA = body.getDatiPrincipaliVA();
		valorizzaDatiPrincipaliVA(datiPrincipaliVA, datiTecniciUTRequestDTO);
		
		DatiTecniciVA datiTecniciVA = body.getDatiTecniciVA();
		valorizzaDatiTecniciVA(datiTecniciUTRequestDTO, datiTecniciVA);
		
		SezioniPremiVA sezioniPremiVA = body.getSezioniPremiVA();
		valorizzaSezionePremiVa(datiTecniciUTRequestDTO, sezioniPremiVA);
		
//		datiTecniciUTRequestDTO.setContributoIscritto(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setContributoAzienda(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTfr(SrvConstants.SPACE);

		FondiVA fondiVA = body.getFondiVA();
		valorizzaElencoFondiVA(datiTecniciUTRequestDTO, fondiVA);
		
//		datiTecniciUTRequestDTO.setFlInvaliditaPermanente(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezInvaliditaPermanente(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplInv(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezInvaliditaPermanenteTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteTasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezInv(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteRendita(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteInvaliditaPermanenteRendita(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementareInfortuni(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementareInfortuni(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementareInfortuniTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniTasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniCapitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementareInfortuniCapitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplMlg(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare1Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplLtc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare2Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplIna(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare3Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplAlc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare4Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneInfCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSportCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setAltroCalc(SrvConstants.SPACE);
		
		datiTecniciUTRequestDTO.setAnnSanitario("00");
		datiTecniciUTRequestDTO.setAnnSanitario2("00");
		datiTecniciUTRequestDTO.setAnnSanitario3("00");
		datiTecniciUTRequestDTO.setAnnProfessione("00");
		datiTecniciUTRequestDTO.setAnnProfessioneInf("00");
		datiTecniciUTRequestDTO.setAnnSport("00");
		datiTecniciUTRequestDTO.setAnnAltro("00");
		
/*		datiTecniciUTRequestDTO.setFlCessioneCIRT(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSopramortalita(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni2(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni3(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setProfessioneIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setProfessioneInfIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSportIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setAltroIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoPolizza(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoQuotaAgente(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoQuietanza(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoSpeseMediche(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagAnni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagVir(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagMesi(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlTrasferimenti(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDifferimentoRata(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setInizioRateazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCoassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesCoassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setRiassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesRiassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setModPagamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesModPagamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceIban(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setNuovoFrazionamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setAnnoFrazionamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPercAbbatProv(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setQuotaAssicurato(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat1(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat2(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat3(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat4(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPremioLim(SrvConstants.SPACE);*/
		
		datiTecniciUTRequestDTO.setPosizioneUT(GenericUtils.stringToStringNoNull(body.getPosizioneUT()));
//		datiTecniciUTRequestDTO.setStatoUT(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setOrigineFondi("P");
//		datiTecniciUTRequestDTO.setOrigineFondiAltro(SrvConstants.SPACE);
		
		PrimaRata modalitaPagamentoVA = body.getModalitaPagamentoVA();
		valorizzaDatiModPagamentoPrimaRata(datiTecniciUTRequestDTO, modalitaPagamentoVA);//dato input tranching
		datiTecniciUTRequestDTO.setEsisteModPagamento(esisteModPagamento); //recuperato tramite dominio
		datiTecniciUTRequestDTO.setFlSelVAMultiplo(GenericUtils.stringToStringNoNull(body.getFlSelVAMultiplo()));//dato input tranching
		datiTecniciUTRequestDTO.setNumVAMultiplo(GenericUtils.stringToStringNoNull(body.getNumVAMultiplo()));//dato input tranching
		return datiTecniciUTRequestDTO;
	}
	
	private static void valorizzaDatiWorkflowVARequest(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, String tipoPrenotazione ) {

		datiTecniciUTRequestDTO.setIdPrenotazione("0");
		datiTecniciUTRequestDTO.setTipoPrenotazione(tipoPrenotazione);
		datiTecniciUTRequestDTO.setDataPervenimentoCartaceo(SrvConstants.DATAINIZIO);
		datiTecniciUTRequestDTO.setDataIncasso(SrvConstants.DATAINIZIO);
	}	

	private static void valorizzaCampiDatiVideoVA(ControllaVAEmessoRequest body, DatiTecniciUTRequestDTO datiTecniciUTRequestDTO) {
		DatiVideoVA datiVideoVAInput = body.getDatiVideoVA();
		datiTecniciUTRequestDTO.setNuovaUT(GenericUtils.stringToStringNoNull(datiVideoVAInput.getNuovaUT()));
		datiTecniciUTRequestDTO.setProdottoUnit(GenericUtils.stringToStringNoNull(datiVideoVAInput.getProdottoUnit()));
		datiTecniciUTRequestDTO.setFlPremio(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPremio()));
		datiTecniciUTRequestDTO.setFlPremioNetto(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPremioNetto()));
		datiTecniciUTRequestDTO.setFlPremioRata(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPremioRata()));
		datiTecniciUTRequestDTO.setFlPremioIngresso(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPremioIngresso()));
		datiTecniciUTRequestDTO.setFlResiduoPlafond(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlResiduoPlafond()));
		datiTecniciUTRequestDTO.setFlPrestaCapRend(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPrestaCapRend()));
		datiTecniciUTRequestDTO.setFlDecrescenza(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDecrescenza()));
		datiTecniciUTRequestDTO.setFlDurataDecor(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurataDecor()));
		datiTecniciUTRequestDTO.setFlFrazionamento(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlFrazionamento()));
		datiTecniciUTRequestDTO.setFlDoublePrem(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDoublePrem()));
		datiTecniciUTRequestDTO.setFlVisita(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlVisita()));
		datiTecniciUTRequestDTO.setFlInizioRateaz(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlInizioRateaz()));
		datiTecniciUTRequestDTO.setFlContrattoScudato(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlContrattoScudato()));
		datiTecniciUTRequestDTO.setFlPremioLim(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPremioLim()));
		datiTecniciUTRequestDTO.setFlRischiComuni(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlRischiComuni()));
		datiTecniciUTRequestDTO.setFlVincolo(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlVincolo()));
		datiTecniciUTRequestDTO.setFlPrestazione(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPrestazione()));
		datiTecniciUTRequestDTO.setFlDurPagPremi(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurPagPremi()));
		datiTecniciUTRequestDTO.setFlDurataAnni(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurataAnni()));
		datiTecniciUTRequestDTO.setFlDurataMesi(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurataMesi()));
		datiTecniciUTRequestDTO.setFlDurataAnniPremi(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurataAnniPremi()));
		datiTecniciUTRequestDTO.setFlDurataMesiPremi(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlDurataMesiPremi()));
		datiTecniciUTRequestDTO.setFlDaQuestionario(SrvConstants.NO);
		datiTecniciUTRequestDTO.setFlPip(GenericUtils.stringToStringNoNull(datiVideoVAInput.getFlPip()));
	}
	
	private static void valorizzaCampiDatiVideoVAEmesso(ControllaVAEmessoRequest body, DatiTecniciUTRequestDTO datiTecniciUTRequestDTO) {
		DatiVideoVAEmesso datiVideoVAEmesso = body.getDatiVideoVAEmesso();
		datiTecniciUTRequestDTO.setFlOpzioneFacoltativa(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getFlOpzioneFacoltativa()));
		datiTecniciUTRequestDTO.setFlVAProgrammatico(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getFlVAProgrammatico()));
		datiTecniciUTRequestDTO.setOpzioneGestionale(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getOpzioneGestionale()));
		datiTecniciUTRequestDTO.setCodLineaInvestimento(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getCodLineaInvestimento()));
		datiTecniciUTRequestDTO.setCodiceConvenzione(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getCodiceConvenzione()));//dato input tranching e altro
		datiTecniciUTRequestDTO.setCodiceDeroga(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getCodiceDeroga()));//dato input tranching e altro
		datiTecniciUTRequestDTO.setDataRichiestaCliente(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getDataRichiestaCliente()));
		datiTecniciUTRequestDTO.setVolontaTrasferimento(GenericUtils.stringToStringNoNull(datiVideoVAEmesso.getVolontaTrasferimento()));
	}
	
	private static void valorizzaOpzioniFacoltaVA(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, DatiIdentificativiVA datiIdentificativiVA) {
		OpzioniFacoltaVA opzioniFacoltaVA = datiIdentificativiVA.getOpzioniFacoltaVA();
		datiTecniciUTRequestDTO.setCodFacoltativo(GenericUtils.stringToStringNoNull(opzioniFacoltaVA.getCodFacoltativo()));
		datiTecniciUTRequestDTO.setCodImportoCedola(GenericUtils.stringToStringNoNull(opzioniFacoltaVA.getCodImportoCedola()));
		datiTecniciUTRequestDTO.setCodDurataCedola(GenericUtils.stringToStringNoNull(opzioniFacoltaVA.getCodDurataCedola()));
		datiTecniciUTRequestDTO.setFlEsclusioneCedola(GenericUtils.stringToStringNoNull(opzioniFacoltaVA.getFlEsclusioneCedola()));//dato input tranching
	}
	
	private static void valorizzaDatiProgrammatiVA(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, DatiIdentificativiVA datiIdentificativiVA) {
		DatiProgrammatiVA datiProgrammatiVA = datiIdentificativiVA.getDatiProgrammatiVA();
		datiTecniciUTRequestDTO.setPeriodicitaVA(GenericUtils.stringToStringNoNull(datiProgrammatiVA.getPeriodicitaVA()));
		datiTecniciUTRequestDTO.setImportoVA(GenericUtils.stringToStringNoNull(datiProgrammatiVA.getImportoVA()));
		datiTecniciUTRequestDTO.setFlSelVAProgrammatico(GenericUtils.stringToStringNoNull(datiProgrammatiVA.getFlSelVAProgrammatico()));//dato input tranching
	}
	
	private static void valorizzaDatiSelezioneAttivitaVA(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, DatiIdentificativiVA datiIdentificativiVA) {
		DatiSelezioneAttivitaVA datiSelezioneAttivitaVA = datiIdentificativiVA.getDatiSelezioneAttivitaVA();
		datiTecniciUTRequestDTO.setFlMultiramo(GenericUtils.stringToStringNoNull(datiSelezioneAttivitaVA.getFlMultiramo()));
		datiTecniciUTRequestDTO.setNumUTMultiramo(GenericUtils.stringToInt(datiSelezioneAttivitaVA.getNumUTMultiramo()));
		datiTecniciUTRequestDTO.setTotale(GenericUtils.stringToStringNoNull(datiSelezioneAttivitaVA.getTotale()));
	}
	
	private static void valorizzaPrimaSecondaUT(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, DatiIdentificativiVA datiIdentificativiVA) {
		List<FondoVA> garanzieFondiListVA = datiIdentificativiVA.getGaranzieFondiListVA();
		if(garanzieFondiListVA.size() >= 2){//garanzie gestite dalle opz gestionali. Attualmente max 2 (es codiceUTI )
			FondoVA fondoVA = garanzieFondiListVA.get(0);
			datiTecniciUTRequestDTO.setFlFondiI(GenericUtils.stringToStringNoNull(fondoVA.getFlFondi()));
		    datiTecniciUTRequestDTO.setCodiceUTI(GenericUtils.stringToStringNoNull(fondoVA.getCodiceUT()));
		    datiTecniciUTRequestDTO.setDescCodiceUTI(GenericUtils.stringToStringNoNull(fondoVA.getDescCodiceUT()));
//		    datiTecniciUTRequestDTO.setMinimoI(GenericUtils.bigDecimalToStringNoNull(fondoVA.getMinimo()));
//		    datiTecniciUTRequestDTO.setMassimoI(GenericUtils.bigDecimalToStringNoNull(fondoVA.getMassimo()));
//		    datiTecniciUTRequestDTO.setPercentualeI(GenericUtils.bigDecimalToStringNoNull(fondoVA.getPercentuale()));//dato input tranching		    
		    datiTecniciUTRequestDTO.setMinimoI(GenericUtils.bigDecimalToString(fondoVA.getMinimo(), 3, 3, ","));
		    datiTecniciUTRequestDTO.setMassimoI(GenericUtils.bigDecimalToString(fondoVA.getMassimo(), 3, 3, ","));
		    BigDecimal pct = fondoVA.getPercentuale();
		    datiTecniciUTRequestDTO.setPercentualeI((pct != null) ? GenericUtils.bigDecimalToString(pct, 3, 3, ",") : "");//dato input tranching
		    datiTecniciUTRequestDTO.setFlProtettoI(GenericUtils.stringToStringNoNull(fondoVA.getFlProtetto()));

		    fondoVA = garanzieFondiListVA.get(1);
		    datiTecniciUTRequestDTO.setFlFondiII(GenericUtils.stringToStringNoNull(fondoVA.getFlFondi()));
		    datiTecniciUTRequestDTO.setCodiceUTII(GenericUtils.stringToStringNoNull(fondoVA.getCodiceUT()));
		    datiTecniciUTRequestDTO.setDescCodiceUTII(GenericUtils.stringToStringNoNull(fondoVA.getDescCodiceUT()));
//		    datiTecniciUTRequestDTO.setMinimoII(GenericUtils.bigDecimalToStringNoNull(fondoVA.getMinimo()));
//		    datiTecniciUTRequestDTO.setMassimoII(GenericUtils.bigDecimalToStringNoNull(fondoVA.getMassimo()));
//		    datiTecniciUTRequestDTO.setPercentualeII(GenericUtils.bigDecimalToStringNoNull(fondoVA.getPercentuale()));//dato input tranching
		    datiTecniciUTRequestDTO.setMinimoII(GenericUtils.bigDecimalToString(fondoVA.getMinimo(), 3, 3, ","));
		    datiTecniciUTRequestDTO.setMassimoII(GenericUtils.bigDecimalToString(fondoVA.getMassimo(), 3, 3, ","));
		    pct = fondoVA.getPercentuale();
		    datiTecniciUTRequestDTO.setPercentualeII((pct != null) ? GenericUtils.bigDecimalToString(pct, 3, 3, ",") : "");//dato input tranching		    	    
		    datiTecniciUTRequestDTO.setFlProtettoII(GenericUtils.stringToStringNoNull(fondoVA.getFlProtetto()));
		}
	}
	
	private static void valorizzaDatiPrincipaliVA(DatiPrincipaliVA datiPrincipaliVA, DatiTecniciUTRequestDTO datiTecniciUTRequestDTO) {
		datiTecniciUTRequestDTO.setDataDecorrenzaPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getDataDecorrenzaPrinc()));
		datiTecniciUTRequestDTO.setDurataAnniPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getDurataAnniPrinc()));
		datiTecniciUTRequestDTO.setDurataMesiPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getDurataMesiPrinc()));
		datiTecniciUTRequestDTO.setDataScadenzaPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getDataScadenzaPrinc()));
		datiTecniciUTRequestDTO.setEtaAssicPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getEtaAssicPrinc()));
		datiTecniciUTRequestDTO.setEtaAssicSecPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getEtaAssicSecPrinc()));
		datiTecniciUTRequestDTO.setFrazionamentoPrinc(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getFrazionamentoPrinc()));
		datiTecniciUTRequestDTO.setFrazPrimaRata(GenericUtils.stringToStringNoNull(datiPrincipaliVA.getFrazPrimaRata()));
	}
	
	private static void valorizzaDatiTecniciVA(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, DatiTecniciVA datiTecniciVA) {
		
		String preRataComplStr = GenericUtils.stringToString(datiTecniciVA.getPreRataCompl());
		BigDecimal preRataComplBD = (preRataComplStr != null) ? new BigDecimal(preRataComplStr) : null;
		String preRataComplCobol = GenericUtils.bigDecimalToString(preRataComplBD, 11, 2, ",");
		datiTecniciUTRequestDTO.setPreRataCompl(preRataComplCobol); //dato input tranching
//		datiTecniciUTRequestDTO.setPreRataCompl(GenericUtils.stringToStringNoNull(datiTecniciVA.getPreRataCompl()));//dato input tranching
		datiTecniciUTRequestDTO.setCodiceTariffa(GenericUtils.stringToStringNoNull(datiTecniciVA.getCodiceTariffa()));
		datiTecniciUTRequestDTO.setDescrizioneTariffa(GenericUtils.stringToStringNoNull(datiTecniciVA.getDescrizioneTariffa()));
		datiTecniciUTRequestDTO.setDataDecorrenza(GenericUtils.stringToStringNoNull(datiTecniciVA.getDataDecorrenza()));
		DurataVA durataVA = datiTecniciVA.getDurataVA();
		datiTecniciUTRequestDTO.setDurataAnni(GenericUtils.stringToStringNoNull(durataVA.getDurataAnni()));
		datiTecniciUTRequestDTO.setFiller(GenericUtils.stringToStringNoNull(durataVA.getFiller()));
		datiTecniciUTRequestDTO.setDurataMesi(GenericUtils.stringToStringNoNull(durataVA.getDurataMesi()));
		DurataPagPremiVA durataPagPremiVA = datiTecniciVA.getDurataPagPremiVA();
		datiTecniciUTRequestDTO.setDurataPagPremiAnni(GenericUtils.stringToStringNoNull(durataPagPremiVA.getDurataPagPremiAnni()));
		datiTecniciUTRequestDTO.setFillerPagPremi(GenericUtils.stringToStringNoNull(durataPagPremiVA.getFillerPagPremi()));
		datiTecniciUTRequestDTO.setDurataPagPremiMesi(GenericUtils.stringToStringNoNull(durataPagPremiVA.getDurataPagPremiMesi()));
		datiTecniciUTRequestDTO.setDataScadenza(GenericUtils.stringToStringNoNull(durataPagPremiVA.getDataScadenza()));
		datiTecniciUTRequestDTO.setCodiceFrazionamento(GenericUtils.stringToStringNoNull(durataPagPremiVA.getCodiceFrazionamento()));
		datiTecniciUTRequestDTO.setMesiRateoIniziale(GenericUtils.stringToStringNoNull(durataPagPremiVA.getMesiRateoIniziale()));
		datiTecniciUTRequestDTO.setDataInvestimento(GenericUtils.stringToStringNoNull(durataPagPremiVA.getDataInvestimento()));
	}
	
	private static void valorizzaSezionePremiVa(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, SezioniPremiVA sezioniPremiVA) {
		datiTecniciUTRequestDTO.setPremioNetto(GenericUtils.stringToStringNoNull(sezioniPremiVA.getPremioNetto()));//dato input tranching e altro
//		datiTecniciUTRequestDTO.setPremioRata(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setPremioIngr(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setCapitale(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDecrescenza(GenericUtils.stringToStringNoNull(sezioniPremiVA.getDecrescenza()));
		datiTecniciUTRequestDTO.setDescDecrescenza(GenericUtils.stringToStringNoNull(sezioniPremiVA.getDescDecrescenza()));
		datiTecniciUTRequestDTO.setPlafondIndex(GenericUtils.stringToStringNoNull(sezioniPremiVA.getPlafondIndex()));
		datiTecniciUTRequestDTO.setPlafond(GenericUtils.stringToStringNoNull(sezioniPremiVA.getPlafond()));
		datiTecniciUTRequestDTO.setPrevin(GenericUtils.stringToStringNoNull(sezioniPremiVA.getPrevin()));
		datiTecniciUTRequestDTO.setPrevinMin(GenericUtils.stringToStringNoNull(sezioniPremiVA.getPrevinMin()));
		datiTecniciUTRequestDTO.setRateoIni(GenericUtils.stringToStringNoNull(sezioniPremiVA.getRateoIni()));
		datiTecniciUTRequestDTO.setFlVisitaMedica(GenericUtils.stringToStringNoNull(sezioniPremiVA.getFlVisitaMedica()));
		datiTecniciUTRequestDTO.setFlDoublePremium(GenericUtils.stringToStringNoNull(sezioniPremiVA.getFlDoublePremium()));
		datiTecniciUTRequestDTO.setResiduoPlafond(GenericUtils.stringToStringNoNull(sezioniPremiVA.getResiduoPlafond()));
		datiTecniciUTRequestDTO.setTipoPremio(GenericUtils.stringToStringNoNull(sezioniPremiVA.getTipoPremio()));
		datiTecniciUTRequestDTO.setFlFumatore(GenericUtils.stringToStringNoNull(sezioniPremiVA.getFlFumatore()));
		datiTecniciUTRequestDTO.setFlFumatoreSec(GenericUtils.stringToStringNoNull(sezioniPremiVA.getFlFumatoreSec()));
	}
	
	private static void valorizzaElencoFondiVA(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, FondiVA fondiVA) {
		ElencoFondoDTO elencoFondi = new ElencoFondoDTO();
		elencoFondi.setNumElementiTrovati(fondiVA.getListaFondi().size());
		elencoFondi.setFlAltri(GenericUtils.stringToStringNoNull(fondiVA.getFlAltri()));
		elencoFondi.setPercFondoULTot(GenericUtils.stringToStringNoNull(fondiVA.getPercFondoULTot()));
		ArrayList<FondoDTO> fondiList = new ArrayList<FondoDTO>();
		for(FondoUL fondiUL : fondiVA.getListaFondi()){
			FondoDTO fondoDTO = new FondoDTO();
			fondoDTO.setCodice(GenericUtils.stringToStringNoNull(fondiUL.getCodice()));
			fondoDTO.setDescrizione(GenericUtils.stringToStringNoNull(fondiUL.getDescrizione()));
			fondoDTO.setPercentuale(GenericUtils.bigDecimalToStringNoNull(fondiUL.getPercentuale()));
			fondiList.add(fondoDTO);
		}
		elencoFondi.setFondi(fondiList);
		datiTecniciUTRequestDTO.setElencoFondi(elencoFondi);
	}
	
	private static void valorizzaDatiModPagamentoPrimaRata(DatiTecniciUTRequestDTO datiTecniciUTRequestDTO, PrimaRata modalitaPagamentoVA) {
		if(modalitaPagamentoVA != null){
			datiTecniciUTRequestDTO.setModPagPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getModPagPrimaRata()));
	//		datiTecniciUTRequestDTO.setCodPagatorePrimaRataVersamento(SrvConstants.SPACE);
			datiTecniciUTRequestDTO.setIntestatarioPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getIntestatarioPrimaRata()));
			datiTecniciUTRequestDTO.setCodiceIbanPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getCodiceIbanPrimaRata()));
			datiTecniciUTRequestDTO.setCodTipoAssegnoPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getCodTipoAssegnoPrimaRata()));
			datiTecniciUTRequestDTO.setNumeroAssegnoPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getNumeroAssegnoPrimaRata()));
			datiTecniciUTRequestDTO.setCodBancaPrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getCodBancaPrimaRata()));
			datiTecniciUTRequestDTO.setCodFilialePrimaRata(GenericUtils.stringToStringNoNull(modalitaPagamentoVA.getCodFilialePrimaRata()));
		}
	}

	public static PropostaRiepilogoRequestDTO valorizzaPropostaRiepilogoRequestDTO(InteractionDTO interactionDTO) {
		PropostaRiepilogoRequestDTO propostaRiepilogoRequestDTO = new PropostaRiepilogoRequestDTO();
		propostaRiepilogoRequestDTO.setFromRiepilogo(SrvConstants.SI);
		propostaRiepilogoRequestDTO.setInteractionDTO(interactionDTO);
		return propostaRiepilogoRequestDTO;
	}
	
	private static void valorizzaPremioGestioneSeparataPremioUnitLinked(PropostaRiepilogoResponseDTO propostaRiepilogoResponse, ControllaVAEmessoResponse response) {
		if (propostaRiepilogoResponse != null) {
		    List<UTPosizioneDettaglioDTO> listaUTRiepilogo = propostaRiepilogoResponse.getListaUTRiepilogo();
		    int numUT = propostaRiepilogoResponse.getNumUT();
		    // lista no null e che contenga almeno un elemento
		    if (listaUTRiepilogo != null && numUT > 0) {
		        // primo elemento (index 0)
		        UTPosizioneDettaglioDTO primoEle = listaUTRiepilogo.get(0);
		        if (primoEle != null) {
		            response.setUnitLinkedPremio(GenericUtils.stringToBigDecimal(primoEle.getPremioRata(), SrvConstants.ZERO));
		        }
		        // secondo elemento (index 1), solo se ne esiste almeno un altro
		        if (numUT > 1) {
		            UTPosizioneDettaglioDTO secondoEle = listaUTRiepilogo.get(1);
		            if (secondoEle != null) {
		                response.setGestioneSeparataPremio(GenericUtils.stringToBigDecimal(secondoEle.getPremioRata(), SrvConstants.ZERO));
		            }
		        }
		    }
		}
	}
	
	public static void valorizzaCdErrControllaPropostaDatiTecniciMainMultiInvest(List<ErroreCampo> listaDatiFigura, DatiTecniciUTResponseDTO datiTecniciResponse) {
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "preRataComplCdErr", datiTecniciResponse.getPreRataComplCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceTariffaCdErr", datiTecniciResponse.getCodiceTariffaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataDecorrenzaCdErr", datiTecniciResponse.getDataDecorrenzaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataAnniCdErr", datiTecniciResponse.getDurataAnniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataMesiCdErr", datiTecniciResponse.getDurataMesiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataPagPremiAnniCdErr", datiTecniciResponse.getDurataPagPremiAnniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataPagPremiMesiCdErr", datiTecniciResponse.getDurataPagPremiMesiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataScadenzaCdErr", datiTecniciResponse.getDataScadenzaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceFrazionamentoCdErr", datiTecniciResponse.getCodiceFrazionamentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "mesiRateoInizialeCdErr", datiTecniciResponse.getMesiRateoInizialeCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataInvestimentoCdErr", datiTecniciResponse.getDataInvestimentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "premioNettoCdErr", datiTecniciResponse.getPremioNettoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "premioRataCdErr", datiTecniciResponse.getPremioRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "premioIngrCdErr", datiTecniciResponse.getPremioIngrCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "capitaleCdErr", datiTecniciResponse.getCapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "decrescenzaCdErr", datiTecniciResponse.getDecrescenzaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "plafondIndexCdErr", datiTecniciResponse.getPlafondIndexCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "plafondCdErr", datiTecniciResponse.getPlafondCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "previnCdErr", datiTecniciResponse.getPrevinCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "previnMinCdErr", datiTecniciResponse.getPrevinMinCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rateoIniCdErr", datiTecniciResponse.getRateoIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flVisitaMedicaCdErr", datiTecniciResponse.getFlVisitaMedicaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flDoublePremiumCdErr", datiTecniciResponse.getFlDoublePremiumCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "residuoPlafondCdErr", datiTecniciResponse.getResiduoPlafondCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tipoPremioCdErr", datiTecniciResponse.getTipoPremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flFumatoreCdErr", datiTecniciResponse.getFlFumatoreCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flFumatoreSecCdErr", datiTecniciResponse.getFlFumatoreSecCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "contributoIscrittoCdErr", datiTecniciResponse.getContributoIscrittoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "contributoAziendaCdErr", datiTecniciResponse.getContributoAziendaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tfrCdErr", datiTecniciResponse.getTfrCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "elencoFondiCdErr", datiTecniciResponse.getElencoFondiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplInvCdErr", datiTecniciResponse.getSelComplInvCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "invaliditaPermanenteTipoCdErr", datiTecniciResponse.getInvaliditaPermanenteTipoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoInvaliditaPermanentePremioCdErr", datiTecniciResponse.getTassoImportoInvaliditaPermanentePremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "invaliditaPermanenteTassoCdErr", datiTecniciResponse.getInvaliditaPermanenteTassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteInvaliditaPermanentePremioCdErr", datiTecniciResponse.getRivalCostanteInvaliditaPermanentePremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "invaliditaPermanenteRenditaCdErr", datiTecniciResponse.getInvaliditaPermanenteRenditaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteInvaliditaPermanenteRenditaCdErr", datiTecniciResponse.getRivalCostanteInvaliditaPermanenteRenditaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "invaliditaPermanentePremioCdErr", datiTecniciResponse.getInvaliditaPermanentePremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplInfCdErr", datiTecniciResponse.getSelComplInfCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementareInfortuniTipoCdErr", datiTecniciResponse.getComplementareInfortuniTipoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoComplementareInfortuniPremioCdErr", datiTecniciResponse.getTassoImportoComplementareInfortuniPremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementareInfortuniTassoCdErr", datiTecniciResponse.getComplementareInfortuniTassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementareInfortuniPremioCdErr", datiTecniciResponse.getRivalCostanteComplementareInfortuniPremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementareInfortuniCapitaleCdErr", datiTecniciResponse.getComplementareInfortuniCapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementareInfortuniCapitaleCdErr", datiTecniciResponse.getRivalCostanteComplementareInfortuniCapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementareInfortuniPremioCdErr", datiTecniciResponse.getComplementareInfortuniPremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplMlgCdErr", datiTecniciResponse.getSelComplMlgCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoComplementare1PremioCdErr", datiTecniciResponse.getTassoImportoComplementare1PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare1TassoCdErr", datiTecniciResponse.getComplementare1TassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare1PremioCdErr", datiTecniciResponse.getRivalCostanteComplementare1PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare1CapitaleCdErr", datiTecniciResponse.getComplementare1CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare1CapitaleCdErr", datiTecniciResponse.getRivalCostanteComplementare1CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare1PremioCdErr", datiTecniciResponse.getComplementare1PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplLtcCdErr", datiTecniciResponse.getSelComplLtcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoComplementare2PremioCdErr", datiTecniciResponse.getTassoImportoComplementare2PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare2TassoCdErr", datiTecniciResponse.getComplementare2TassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare2PremioCdErr", datiTecniciResponse.getRivalCostanteComplementare2PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare2CapitaleCdErr", datiTecniciResponse.getComplementare2CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare2CapitaleCdErr", datiTecniciResponse.getRivalCostanteComplementare2CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare2PremioCdErr", datiTecniciResponse.getComplementare2PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplInaCdErr", datiTecniciResponse.getSelComplInaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoComplementare3PremioCdErr", datiTecniciResponse.getTassoImportoComplementare3PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare3TassoCdErr", datiTecniciResponse.getComplementare3TassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare3PremioCdErr", datiTecniciResponse.getRivalCostanteComplementare3PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare3CapitaleCdErr", datiTecniciResponse.getComplementare3CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare3CapitaleCdErr", datiTecniciResponse.getRivalCostanteComplementare3CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare3PremioCdErr", datiTecniciResponse.getComplementare3PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "selComplAlcCdErr", datiTecniciResponse.getSelComplAlcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoComplementare4PremioCdErr", datiTecniciResponse.getTassoImportoComplementare4PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare4TassoCdErr", datiTecniciResponse.getComplementare4TassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare4PremioCdErr", datiTecniciResponse.getRivalCostanteComplementare4PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare4CapitaleCdErr", datiTecniciResponse.getComplementare4CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "rivalCostanteComplementare4CapitaleCdErr", datiTecniciResponse.getRivalCostanteComplementare4CapitaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "complementare4PremioCdErr", datiTecniciResponse.getComplementare4PremioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoSanitarioCdErr", datiTecniciResponse.getTassoImportoSanitarioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoSanitario2CdErr", datiTecniciResponse.getTassoImportoSanitario2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoSanitario3CdErr", datiTecniciResponse.getTassoImportoSanitario3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoProfessioneCdErr", datiTecniciResponse.getTassoImportoProfessioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoProfessioneInfCdErr", datiTecniciResponse.getTassoImportoProfessioneInfCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoSportCdErr", datiTecniciResponse.getTassoImportoSportCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoImportoAltroCdErr", datiTecniciResponse.getTassoImportoAltroCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioCdErr", datiTecniciResponse.getSanitarioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitario2CdErr", datiTecniciResponse.getSanitario2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitario3CdErr", datiTecniciResponse.getSanitario3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneCdErr", datiTecniciResponse.getProfessioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneInfCdErr", datiTecniciResponse.getProfessioneInfCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sportCdErr", datiTecniciResponse.getSportCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "altroCdErr", datiTecniciResponse.getAltroCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioCalcCdErr", datiTecniciResponse.getSanitarioCalcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioCalc2CdErr", datiTecniciResponse.getSanitarioCalc2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioCalc3CdErr", datiTecniciResponse.getSanitarioCalc3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneCalcCdErr", datiTecniciResponse.getProfessioneCalcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneInfCalcCdErr", datiTecniciResponse.getProfessioneInfCalcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sportCalcCdErr", datiTecniciResponse.getSportCalcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "altroCalcCdErr", datiTecniciResponse.getAltroCalcCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annSanitarioCdErr", datiTecniciResponse.getAnnSanitarioCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annSanitario2CdErr", datiTecniciResponse.getAnnSanitario2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annSanitario3CdErr", datiTecniciResponse.getAnnSanitario3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annProfessioneCdErr", datiTecniciResponse.getAnnProfessioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annProfessioneInfCdErr", datiTecniciResponse.getAnnProfessioneInfCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annSportCdErr", datiTecniciResponse.getAnnSportCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annAltroCdErr", datiTecniciResponse.getAnnAltroCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flCessioneCIRTCdErr", datiTecniciResponse.getFlCessioneCIRTCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sopramortalitaCdErr", datiTecniciResponse.getSopramortalitaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioIniCdErr", datiTecniciResponse.getSanitarioIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioIni2CdErr", datiTecniciResponse.getSanitarioIni2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sanitarioIni3CdErr", datiTecniciResponse.getSanitarioIni3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneIniCdErr", datiTecniciResponse.getProfessioneIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "professioneInfIniCdErr", datiTecniciResponse.getProfessioneInfIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "sportIniCdErr", datiTecniciResponse.getSportIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "altroIniCdErr", datiTecniciResponse.getAltroIniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "costoPolizzaCdErr", datiTecniciResponse.getCostoPolizzaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "costoQuotaAgenteCdErr", datiTecniciResponse.getCostoQuotaAgenteCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "costoQuietanzaCdErr", datiTecniciResponse.getCostoQuietanzaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "costoSpeseMedicheCdErr", datiTecniciResponse.getCostoSpeseMedicheCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataPagAnniCdErr", datiTecniciResponse.getDurataPagAnniCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "durataPagMesiCdErr", datiTecniciResponse.getDurataPagMesiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "flTrasferimentiCdErr", datiTecniciResponse.getFlTrasferimentiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "differimentoRataCdErr", datiTecniciResponse.getDifferimentoRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "inizioRateazioneCdErr", datiTecniciResponse.getInizioRateazioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "coassicurazioneCdErr", datiTecniciResponse.getCoassicurazioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "riassicurazioneCdErr", datiTecniciResponse.getRiassicurazioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "modPagamentoCdErr", datiTecniciResponse.getModPagamentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceIbanCdErr", datiTecniciResponse.getCodiceIbanCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "nuovoFrazionamentoCdErr", datiTecniciResponse.getNuovoFrazionamentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "annoFrazionamentoCdErr", datiTecniciResponse.getAnnoFrazionamentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percAbbatProvCdErr", datiTecniciResponse.getPercAbbatProvCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "quotaAssicuratoCdErr", datiTecniciResponse.getQuotaAssicuratoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceStat1CdErr", datiTecniciResponse.getCodiceStat1CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceStat2CdErr", datiTecniciResponse.getCodiceStat2CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceStat3CdErr", datiTecniciResponse.getCodiceStat3CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceStat4CdErr", datiTecniciResponse.getCodiceStat4CdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "premioLimCdErr", datiTecniciResponse.getPremioLimCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "opzioneGestionaleCdErr", datiTecniciResponse.getOpzioneGestionaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceConvenzioneCdErr", datiTecniciResponse.getCodiceConvenzioneCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceDerogaCdErr", datiTecniciResponse.getCodiceDerogaCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataRichiestaClienteCdErr", datiTecniciResponse.getDataRichiestaClienteCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "totaleCdErr", datiTecniciResponse.getTotaleCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percentualeICdErr", datiTecniciResponse.getPercentualeICdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percentualeIICdErr", datiTecniciResponse.getPercentualeIICdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percentualeIIICdErr", datiTecniciResponse.getPercentualeIIICdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percentualeIVCdErr", datiTecniciResponse.getPercentualeIVCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "percentualeVCdErr", datiTecniciResponse.getPercentualeVCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataRichiestaClienteCdErr", datiTecniciResponse.getDataRichiestaClienteCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataPervenimentoCartaceoCdErr", datiTecniciResponse.getDataPervenimentoCartaceoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "dataIncassoCdErr", datiTecniciResponse.getDataIncassoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "origineFondiCdErr", datiTecniciResponse.getOrigineFondiCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "origineFondiAltroCdErr", datiTecniciResponse.getOrigineFondiAltroCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "modPagPrimaRataCdErr", datiTecniciResponse.getModPagPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codPagatorePrimaRataVersamentoCdErr", datiTecniciResponse.getCodPagatorePrimaRataVersamentoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "intestatarioPrimaRataCdErr", datiTecniciResponse.getIntestatarioPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codiceIbanPrimaRataCdErr", datiTecniciResponse.getCodiceIbanPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codTipoAssegnoPrimaRataCdErr", datiTecniciResponse.getCodTipoAssegnoPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "numeroAssegnoPrimaRataCdErr", datiTecniciResponse.getNumeroAssegnoPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codBancaPrimaRataCdErr", datiTecniciResponse.getCodBancaPrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "codFilialePrimaRataCdErr", datiTecniciResponse.getCodFilialePrimaRataCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "modelloCostiSelezionatoCdErr", datiTecniciResponse.getModelloCostiSelezionatoCdErr());
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "numVAMultiploCdErr", datiTecniciResponse.getNumVAMultiploCdErr());
		
		PropostaUtility.valorizzaErroreCampo(listaDatiFigura, "tassoInteresseMutuoCdErr", datiTecniciResponse.getTassoInteresseMutuoCdErr());
	}

	public static DatiTecniciUTRequestDTO valorizzaControllaVAEmessoAltroRequest(ControllaVAEmessoRequest body) {

		DatiTecniciUTRequestDTO datiTecniciUTRequestDTO = new DatiTecniciUTRequestDTO();
		datiTecniciUTRequestDTO.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
//		datiTecniciUTRequestDTO.setFlNavigazione(SrvConstants.SPACE);
		valorizzaCampiDatiVideoVA(body, datiTecniciUTRequestDTO);
		datiTecniciUTRequestDTO.setFlTipoOperazione(SrvConstants.EMESSO);//E
		datiTecniciUTRequestDTO.setFlVersamentoAgg(SrvConstants.SI);
		valorizzaCampiDatiVideoVAEmesso(body, datiTecniciUTRequestDTO);
	
//		DatiWorkflowVA datiWorkflowVA = body.getDatiWorkflowVA();
//		
//		datiTecniciUTRequestDTO.setIdPrenotazione(GenericUtils.stringToStringNoNull(datiWorkflowVA.getIdPrenotazione()));
//		datiTecniciUTRequestDTO.setTipoPrenotazione(GenericUtils.stringToStringNoNull(datiWorkflowVA.getTipoPrenotazione()));
//		Date dataPervenimentoCartaceo = datiWorkflowVA.getDataPervenimentoCartaceo();
//		datiTecniciUTRequestDTO.setDataPervenimentoCartaceo((dataPervenimentoCartaceo != null) ? DateUtils.dateLongToString(dataPervenimentoCartaceo.getTime(), DateUtils.DATA_PATTERN_FORMATO_HOST) : SrvConstants.SPACE);
//		Date dataIncasso = datiWorkflowVA.getDataIncasso();
//		datiTecniciUTRequestDTO.setDataIncasso((dataIncasso != null) ? DateUtils.dateLongToString(dataIncasso.getTime(), DateUtils.DATA_PATTERN_FORMATO_HOST) : SrvConstants.SPACE);				
		
		valorizzaDatiWorkflowVARequest(datiTecniciUTRequestDTO, SrvConstants.TIPO_PRATICA_VERSAMENTO_EMESSO_NO_TRANCHING);
		
		datiTecniciUTRequestDTO.setCodPagatorePrimaRata(GenericUtils.stringToStringNoNull(body.getCodPagatorePrimaRata()));//dato input tranching
		DatiPrincipaliVA datiPrincipaliVA = body.getDatiPrincipaliVA();
		valorizzaDatiPrincipaliVA(datiPrincipaliVA, datiTecniciUTRequestDTO);
		
		DatiTecniciVA datiTecniciVA = body.getDatiTecniciVA();
		valorizzaDatiTecniciVA(datiTecniciUTRequestDTO, datiTecniciVA);
		
		SezioniPremiVA sezioniPremiVA = body.getSezioniPremiVA();
		valorizzaSezionePremiVa(datiTecniciUTRequestDTO, sezioniPremiVA);
		
//		datiTecniciUTRequestDTO.setContributoIscritto(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setContributoAzienda(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTfr(SrvConstants.SPACE);

		FondiVA fondiVA = body.getFondiVA();
		valorizzaElencoFondiVA(datiTecniciUTRequestDTO, fondiVA);
		
//		datiTecniciUTRequestDTO.setFlInvaliditaPermanente(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezInvaliditaPermanente(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplInv(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezInvaliditaPermanenteTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteTasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezInv(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanenteRendita(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteInvaliditaPermanenteRendita(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setInvaliditaPermanentePremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementareInfortuni(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementareInfortuni(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementareInfortuniTipo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniTasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniCapitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementareInfortuniCapitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementareInfortuniPremio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplMlg(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl1(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare1Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare1Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplLtc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare2Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare2Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplIna(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare3Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare3Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlComplementare4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProtezComplementare4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelComplAlc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Tasso(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSelProtezCompl4(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setRivalCostanteComplementare4Capitale(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setComplementare4Premio(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoImportoAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitario3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessione(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneInf(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSport(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setAltro(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc2(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSanitarioCalc3(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setProfessioneInfCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setSportCalc(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setAltroCalc(SrvConstants.SPACE);
		
		datiTecniciUTRequestDTO.setAnnSanitario("00");
		datiTecniciUTRequestDTO.setAnnSanitario2("00");
		datiTecniciUTRequestDTO.setAnnSanitario3("00");
		datiTecniciUTRequestDTO.setAnnProfessione("00");
		datiTecniciUTRequestDTO.setAnnProfessioneInf("00");
		datiTecniciUTRequestDTO.setAnnSport("00");
		datiTecniciUTRequestDTO.setAnnAltro("00");
		
/*		datiTecniciUTRequestDTO.setFlCessioneCIRT(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSopramortalita(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni2(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSanitarioIni3(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setProfessioneIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setProfessioneInfIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setSportIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setAltroIni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoPolizza(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoQuotaAgente(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoQuietanza(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCostoSpeseMediche(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagAnni(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagVir(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDurataPagMesi(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setFlTrasferimenti(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDifferimentoRata(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setInizioRateazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCoassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesCoassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setRiassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesRiassicurazione(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setModPagamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setDesModPagamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceIban(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setNuovoFrazionamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setAnnoFrazionamento(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPercAbbatProv(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setQuotaAssicurato(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat1(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat2(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat3(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCodiceStat4(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setPremioLim(SrvConstants.SPACE);*/
		
		datiTecniciUTRequestDTO.setPosizioneUT(GenericUtils.stringToStringNoNull(body.getPosizioneUT()));
//		datiTecniciUTRequestDTO.setStatoUT(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setTassoInteresseMutuo(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setOrigineFondi("P");
//		datiTecniciUTRequestDTO.setOrigineFondiAltro(SrvConstants.SPACE);

		datiTecniciUTRequestDTO.setCgProgrMod("000000000");
//		datiTecniciUTRequestDTO.setCgModello(SrvConstants.SPACE);
		datiTecniciUTRequestDTO.setCgRegola(GenericUtils.stringToStringNoNull(body.getCgRegola()));
//		datiTecniciUTRequestDTO.setFlNuovoProdottoTaboo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setCodiceTariffaPremioAnnuo(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setCapitaleUnaTantum(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlVAPur(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setFlSelVAPur(SrvConstants.SPACE);
//		datiTecniciUTRequestDTO.setImportoVA(SrvConstants.SPACE);
		return datiTecniciUTRequestDTO;
	}
	
//	public static List<BlocchiWorkflowInfo> getListaBlocchi(List<String> blocchiWrk, PolizzaInfo polizzaInfo, String utente) {
//
//		List<BlocchiWorkflowInfo> blocchiWorkflowList = new ArrayList<BlocchiWorkflowInfo>();
//		BlocchiWorkflowInfo blocchiWorkflowInfo = null;
//		Long dataOdierna = (new Date()).getTime();
////		String tipoPrenotazione = "OC"; TODO
//		for(String bloccoWrk : blocchiWrk){
//			blocchiWorkflowInfo = new BlocchiWorkflowInfo();
//			blocchiWorkflowInfo.setCodiceSocieta(polizzaInfo.getCodSocieta());
//			blocchiWorkflowInfo.setCategoria(polizzaInfo.getNumeroCategoria());
//			blocchiWorkflowInfo.setAgenziaPol(polizzaInfo.getCodAgenzia());
//			blocchiWorkflowInfo.setNumColl(polizzaInfo.getNumeroCollettiva());
//			blocchiWorkflowInfo.setNumPolizza(polizzaInfo.getNumeroPolizza());
//			blocchiWorkflowInfo.setFlgPropostaPolizza(SrvConstants.TIPO_RAPP_RID_POLIZZA);
//			blocchiWorkflowInfo.setIdPrenotazione(0);
////			blocchiWorkflowInfo.setTipoPrenotazione(tipoPrenotazione);
//			blocchiWorkflowInfo.setDataPrenotazione(dataOdierna);
//			blocchiWorkflowInfo.setIdBlk(bloccoWrk);
//			blocchiWorkflowInfo.setSoggApprov(SrvConstants.SPACE);
//			blocchiWorkflowInfo.setDataApprov(DateUtils.DATE_01_01_0001_AS_LONG);
//			blocchiWorkflowInfo.setUserCreaz(utente);
//			blocchiWorkflowInfo.setUserVariaz(utente);
//			blocchiWorkflowList.add(blocchiWorkflowInfo);			
//		}
//		return blocchiWorkflowList;
//	}
	
	public static InizializzaRecessoResponse valorizzaViolation(InizializzaRecessoRequest body, List<String> validatorViolations) {
		InizializzaRecessoResponse inizializzaRecessoResponse = new InizializzaRecessoResponse();
		inizializzaRecessoResponse.setErrori(ServiceUtility.valorizzaErrori(validatorViolations));
		return inizializzaRecessoResponse;
	}
	
	public static PolizzaSimpleEstesa getPolizzaSimpleEstesaFrom(PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa, PolizzaInfoEstesa polizzaInfoEstesa) {
		PolizzaSimpleEstesa polizzaSimpleEstesa = new PolizzaSimpleEstesa();
		polizzaSimpleEstesa.setPip(polizzaInfoSimpleEstesa.isPip());
		polizzaSimpleEstesa.setUnitLinked(polizzaInfoSimpleEstesa.isUnitLinked());
		polizzaSimpleEstesa.setMultigaranzia(polizzaInfoSimpleEstesa.isMultigaranzia());
		polizzaSimpleEstesa.setIndex(polizzaInfoSimpleEstesa.isIndex());
		polizzaSimpleEstesa.setFatca(polizzaInfoSimpleEstesa.isFatca());
		polizzaSimpleEstesa.setTranching(polizzaInfoSimpleEstesa.isTranching());
		polizzaSimpleEstesa.setAmleto(polizzaInfoSimpleEstesa.isAmleto());
		polizzaSimpleEstesa.setPianoPensione(polizzaInfoSimpleEstesa.isPianoPensione());
		
		polizzaSimpleEstesa.setCodProdotto(GenericUtils.stringToString(polizzaInfoEstesa.getProdotto().getCodice()));
		polizzaSimpleEstesa.setUlFondo(GenericUtils.stringToString(polizzaInfoEstesa.getUlFondo()));
		polizzaSimpleEstesa.setDataDecorrenza(DateUtils.longToDate(polizzaInfoEstesa.getDataDecorrenza()));
		return polizzaSimpleEstesa;
	}	

	public static ControllaRecessoResponse valorizzaViolation(ControllaRecessoRequest body, List<String> validatorViolations) {
		ControllaRecessoResponse controllaRecessoResponse = new ControllaRecessoResponse();
		controllaRecessoResponse.setErrori(ServiceUtility.valorizzaErrori(validatorViolations));
		return controllaRecessoResponse;
	}

	public static RecessoDatiParametri valorizzaRecessoDatiParametri(it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri recessoDatiParametri) {
		RecessoDatiParametri recessoDatiParametriRest = new RecessoDatiParametri();

		if(isValidDate(recessoDatiParametri.getDataSistema())){
			recessoDatiParametriRest.setDataSistema(DateUtils.longToDate(recessoDatiParametri.getDataSistema()));
		}
		if(isValidDate(recessoDatiParametri.getDataPrenotazione())){
			recessoDatiParametriRest.setDataPrenotazione(DateUtils.longToDate(recessoDatiParametri.getDataPrenotazione()));
		}
		if(isValidDate(recessoDatiParametri.getDataRichiestaCliente())){
			recessoDatiParametriRest.setDataRichiestaCliente(DateUtils.longToDate(recessoDatiParametri.getDataRichiestaCliente()));
		}
		if(isValidDate(recessoDatiParametri.getDataRicevimentoRichiesta())){
			recessoDatiParametriRest.setDataRicevimentoRichiesta(DateUtils.longToDate(recessoDatiParametri.getDataRicevimentoRichiesta()));
		}
		if(isValidDate(recessoDatiParametri.getDataDisinvestimento())){
			recessoDatiParametriRest.setDataDisinvestimento(DateUtils.longToDate(recessoDatiParametri.getDataDisinvestimento()));
		}
		if(isValidDate(recessoDatiParametri.getDataDisinvestimentoCalcolata())){
			recessoDatiParametriRest.setDataDisinvestimentoCalcolata(DateUtils.longToDate(recessoDatiParametri.getDataDisinvestimentoCalcolata()));
		}
		if(isValidDate(recessoDatiParametri.getDataRichiestaCompleta())){
			recessoDatiParametriRest.setDataRichiestaCompleta(DateUtils.longToDate(recessoDatiParametri.getDataRichiestaCompleta()));
		}
		if(isValidDate(recessoDatiParametri.getDataQuietanzaIncassata())){
			recessoDatiParametriRest.setDataQuietanzaIncassata(DateUtils.longToDate(recessoDatiParametri.getDataQuietanzaIncassata()));
		}
		if(isValidDate(recessoDatiParametri.getDataDocumento())){
			recessoDatiParametriRest.setDataDocumento(DateUtils.longToDate(recessoDatiParametri.getDataDocumento()));
		}
		if(isValidDate(recessoDatiParametri.getDataInvioFU())){
			recessoDatiParametriRest.setDataInvioFU(DateUtils.longToDate(recessoDatiParametri.getDataInvioFU()));
		}
		if(isValidDate(recessoDatiParametri.getDataUltimaRicezioneDocFU())){
			recessoDatiParametriRest.setDataUltimaRicezioneDocFU(DateUtils.longToDate(recessoDatiParametri.getDataUltimaRicezioneDocFU()));
		}
		if(isValidDate(recessoDatiParametri.getDataInizioEventoPrior())){
			recessoDatiParametriRest.setDataInizioEventoPrior(DateUtils.longToDate(recessoDatiParametri.getDataInizioEventoPrior()));
		}
		recessoDatiParametriRest.setExistQuoteToInvest(recessoDatiParametri.isExistQuoteToInvest());
		String tipoOperazione = GenericUtils.stringToString(recessoDatiParametri.getTipoOperazione());
		recessoDatiParametriRest.setTipoOperazione((tipoOperazione != null) ? TipoOperazioneEnum.valueOf(tipoOperazione) : null);
		recessoDatiParametriRest.setMotivazioneRecesso(GenericUtils.stringToStringNoNull(recessoDatiParametri.getMotivazioneRecesso()));
		recessoDatiParametriRest.setRecessoSenzaCostiAttivo(recessoDatiParametri.isRecessoSenzaCostiAttivo());
		recessoDatiParametriRest.setEventoRibilNonConcluso(GenericUtils.stringToStringNoNull(recessoDatiParametri.getEventoRibilNonConcluso()));
		recessoDatiParametriRest.setDscrEventoRibilNonConcluso(GenericUtils.stringToStringNoNull(recessoDatiParametri.getDscrEventoRibilNonConcluso()));
		recessoDatiParametriRest.setEsisteRigaFU(GenericUtils.stringToStringNoNull(recessoDatiParametri.getEsisteRigaFU()));
		return recessoDatiParametriRest;
	}
	
	private static boolean isValidDate(Long date) {
		return ((date != null) && (date.longValue() != DateUtils.DATE_01_01_0001_AS_LONG));
	}

	public static SalvaPrenotazRiscattoResponse valorizzaViolation(SalvaPrenotazRiscattoRequest body, List<String> validatorViolations) {
		SalvaPrenotazRiscattoResponse response = new SalvaPrenotazRiscattoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}
	
	public static InoltraPrenotazRiscattoResponse valorizzaViolation(InoltraPrenotazRiscattoRequest body, List<String> validatorViolations) {
		InoltraPrenotazRiscattoResponse response = new InoltraPrenotazRiscattoResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}

	public static RiscattoRequestDTO valorizzaSalvaRiscatto(SalvaPrenotazRiscattoRequest body, Boolean direzione, String idPrenotazione) {
		String date01010001 = DateUtils.dateLongToString(DateUtils.DATE_01_01_0001_AS_LONG, DateUtils.DATA_PATTERN_FORMATO_HOST);
		RiscattoRequestDTO request = new RiscattoRequestDTO();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		DatiInputSalvaPrenotazioneRiscatto datiInput = body.getDatiInput();
		request.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
		request.setNumCategoria(polizzaInfo.getNumeroCategoria());
		request.setCategoria(Utils.valorizzaCategoria(polizzaInfo.getNumeroCategoria()));
		request.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		request.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		request.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		request.setIdPrenotazione(SrvConstants.ZERO);
		request.setCategNum(polizzaInfo.getNumeroCategoria());
		String aDateRichiesta = DateUtils.dateToString(datiInput.getDataRichiestaRiscatto(), DateUtils.DATA_PATTERN_FORMATO_HOST);
		request.setDataRichiestaRiscatto(aDateRichiesta);
		request.setDataRicevRichiestaRiscatto(aDateRichiesta);		
		String unaDataContabile = DateUtils.dateToString(datiInput.getDataContabile(), DateUtils.DATA_PATTERN_FORMATO_HOST);
		request.setDataContabile(unaDataContabile);
		request.setDistribuzioneRiscatto(GenericUtils.stringToStringNoNull(datiInput.getDistribuzioneRiscatto()));
		request.setTipoVincolo(GenericUtils.stringToStringNoNull(datiInput.getTipoVincolo()));
		request.setRichiedente(GenericUtils.stringToStringNoNull(datiInput.getRichiedente()));
		request.setDataConsDoc(date01010001);
		request.setDataRicezioneDoc(date01010001);		
		request.setSottoFunzionePrenotaz(SrvConstants.SPACE);
		request.setFlCompletaPIP(SrvConstants.NO);
		request.setFlFacta(GenericUtils.booleanToString(datiInput.isFatca()));
		request.setFlInserisci(SrvConstants.SPACES);
		request.setDataPrimaIscrizione(CommonUtility.dateToString(datiInput.getDataPrimaIscrizione()));
		request.setTipoIscritto(GenericUtils.stringToStringNoNull(datiInput.getTipoIscritto()));
		request.setAliquitaTfr(GenericUtils.stringToStringNoNull(datiInput.getAliquotaTFR()));
		request.setFlagArt11(GenericUtils.stringToStringNoNull(datiInput.getFlagArt11()));
		request.setTipoUtente((direzione) ? "D" : "A");
		request.setDataRichDocCliente(date01010001);
		request.setDataRichDocClienteIniziale(date01010001);
		request.setFlInoltra(SrvConstants.NO);
		request.setFlInviataRichDocCliente(SrvConstants.SPACES);
		request.setFlProtettaRichDocCliente(SrvConstants.SPACES);
		request.setDataSollecito1(date01010001);
		request.setFlInviataSollecito1(SrvConstants.SPACES);
		request.setDataAttivPratica(date01010001);
		request.setFlInviataAttivPratica(SrvConstants.SPACES);
		request.setDataSollecito2(date01010001);
		request.setFlInviataSollecito2(SrvConstants.SPACES);
		request.setFlDocumentiMancanti(SrvConstants.SPACES);
		request.setMotivoRiscatto(GenericUtils.stringToStringNoNull(datiInput.getMotivoRiscatto()));
		request.setMotivoRiscattoAltro(GenericUtils.stringToStringNoNull(datiInput.getMotivoRiscattoAltro()));
		request.setFlInibizioneBloccoMaxErogabile(SrvConstants.NO);
		request.setTipoRiscatto(GenericUtils.stringToStringNoNull(datiInput.getTipoRiscatto()));
		request.setMetodoRiscatto(GenericUtils.stringToStringNoNull(datiInput.getMetodoRiscatto()));
		request.setDistribuzioneRiscatto(GenericUtils.stringToStringNoNull(datiInput.getDistribuzioneRiscatto()));
		request.setTipoRiscattoFondi(GenericUtils.stringToStringNoNull(datiInput.getTipoRiscattoFondi()));
		request.setImportoRichiesto(SrvConstants.SPACE); //VERIFICARE IN QUANTO MANCA IN INPUT
		request.setDataDisinvestimento(CommonUtility.dateToString(datiInput.getDataDisinvestimento()));
		request.setImpPercRiscattoCalc(GenericUtils.bigDecimalToStringNoNull(datiInput.getImpPercRiscattoCalc()));
		request.setFlNettoPenalita(GenericUtils.stringToStringNoNull(datiInput.getFlNettoPenalita()));
		request.setFlgVincolo(GenericUtils.stringToStringNoNull(datiInput.getFlgVincolo()));
		request.setFlgManleva(GenericUtils.stringToStringNoNull(datiInput.getFlgManleva()));
		request.setFlgInps(GenericUtils.stringToStringNoNull(datiInput.getFlgInps()));
		request.setImportoInps(GenericUtils.bigDecimalToStringNoNull(datiInput.getImportoInps()));
		request.setImponibileIrpef(GenericUtils.bigDecimalToStringNoNull(datiInput.getImponibileIrpef()));
		request.setImportoIrpef(GenericUtils.bigDecimalToStringNoNull(datiInput.getImportoIrpef()));
		request.setPercInps(GenericUtils.bigDecimalToStringNoNull(datiInput.getPercInps()));
		request.setPercIrpef(GenericUtils.bigDecimalToStringNoNull(datiInput.getPercIrpef()));
		request.setFlCompletaPIP(SrvConstants.NO);
		if(datiInput.getComponenteGarantita() != null) {
			request.setComponenteGarantitaInput(valorizzaComponenteGarantita(datiInput.getComponenteGarantita()));
		}
		if(datiInput.getElencoFondi() != null) {
			request.setElencoFondiRiscattoInput(valorizzaListaFondiRiscatto(datiInput.getElencoFondi()));
		}
		request.setFlPrenotazDispositiva(SrvConstants.NO); //in prenotazione
		
//riscattoRequest.setPercentualeRiscatto(GenericUtils.formattaImportoBigDecimalToServiceHost(body.getPercentualeRiscatto()));
		BigDecimal aPctRiscatto = datiInput.getPercentualeRiscatto();
		if (aPctRiscatto != null) {
			String pctRiscatto = GenericUtils.formattaImportoBigDecimalToServiceHost(aPctRiscatto);
			request.setMetodoRiscatto(SrvConstants.RISCATTO_PARZIALE);
//BRUNO					riscattoRequest.setDistribuzioneRiscatto("4");
			request.setDistribuzioneRiscatto("2"); //TIPO RISCATTO PARZONE PERCENTUALE (METTERE COSTANTI)
			request.setPercentualeRiscatto(pctRiscatto);
			request.setValoreDa(pctRiscatto);	
		}
		return request;
	}
	
	private static ElencoFondiRiscattoDTO valorizzaListaFondiRiscatto(List<FondoRiscatto> elencoFondi) {
		ElencoFondiRiscattoDTO elencoFondiRiscattoDTO = new ElencoFondiRiscattoDTO();
		ArrayList<FondiRiscattoDTO> listaFondiRiscattiDTO = new ArrayList<FondiRiscattoDTO>();
		for(FondoRiscatto fondoRiscatto : elencoFondi) {
			FondiRiscattoDTO fondoDTO = new FondiRiscattoDTO();
			fondoDTO.setDescrFondo(GenericUtils.stringToStringNoNull(fondoRiscatto.getDescrFondo()));
			fondoDTO.setDataDisinvestimentoFondo(CommonUtility.dateToString(fondoRiscatto.getDataDisinvestimentoFondo()));
			fondoDTO.setNumeroQuote(GenericUtils.stringToStringNoNull(fondoRiscatto.getNumeroQuote()));
			fondoDTO.setValoreRiscatto(fondoRiscatto.getValoreRiscatto() != null ? GenericUtils.formattaImportoBigDecimalToServiceHost(fondoRiscatto.getValoreRiscatto()) : "");
			fondoDTO.setValoreMinQuota(GenericUtils.bigDecimalToStringNoNull(fondoRiscatto.getValoreMinQuota()));
			fondoDTO.setValoreQuota(GenericUtils.bigDecimalToStringNoNull(fondoRiscatto.getValoreQuota()));
			fondoDTO.setControvaloreQuota(GenericUtils.bigDecimalToStringNoNull(fondoRiscatto.getControvaloreQuota()));
			fondoDTO.setTipologia(GenericUtils.stringToStringNoNull(fondoRiscatto.getTipologia()));
			listaFondiRiscattiDTO.add(fondoDTO);
		}
		elencoFondiRiscattoDTO.setFondiRiscatto(listaFondiRiscattiDTO);
		return elencoFondiRiscattoDTO;
	}

	private static ComponenteGarantitaDTO valorizzaComponenteGarantita(ComponenteGarantita componenteGarantita) {
		ComponenteGarantitaDTO cgDTO = new ComponenteGarantitaDTO();
		cgDTO.setDataEffetto(CommonUtility.dateToString(componenteGarantita.getDataEffetto()));
		cgDTO.setDescrFondoRiv(GenericUtils.stringToStringNoNull(componenteGarantita.getDescrFondoRiv()));
		cgDTO.setRendimento(GenericUtils.bigDecimalToStringNoNull(componenteGarantita.getRendimento()));
		cgDTO.setValoreRiscattato(GenericUtils.bigDecimalToStringNoNull(componenteGarantita.getValoreRiscattato()));
		cgDTO.setValoreRiscatto(GenericUtils.bigDecimalToStringNoNull(componenteGarantita.getValoreRiscatto()));
		cgDTO.setValoreRivalutato(GenericUtils.bigDecimalToStringNoNull(componenteGarantita.getValoreRivalutato()));
		return cgDTO;
	}

	public static ScadenzaRequestDTO valorizzaSalvaScadenza(SalvaPrenotazioneScadenzaRequest body, String idPrenotazione) {
		ScadenzaRequestDTO request = new ScadenzaRequestDTO();
		PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		DatiInputSalvaPrenotazioneScadenza datiInput = body.getDatiInput();
		String dateHost = DateUtils.getCurrentDateHost();
		request.setInteractionDTO(InteractionUtility.valorizzaInteractionDTO(body.getHeaderCobolSrv()));
		request.setCategoria(Utils.valorizzaCategoria(polizzaInfo.getNumeroCategoria()));
		request.setAgenziaPolizza(polizzaInfo.getCodAgenzia());
		request.setNumeroColl(""+polizzaInfo.getNumeroCollettiva());
		request.setNumeroPolizza(""+polizzaInfo.getNumeroPolizza());
		request.setIdPrenotazione(SrvConstants.ZERO);
		request.setTipoVincolo(GenericUtils.stringToStringNoNull(datiInput.getTipoVincolo()));
		request.setDataConsDoc(dateHost);
		request.setDataRicezioneDoc(dateHost);
		request.setSottoFunzionePrenotaz(GenericUtils.stringToStringNoNull(datiInput.getSottoFunzionePrenotaz()));
		request.setFlFacta(GenericUtils.booleanToString(datiInput.isFatca()));
		request.setFlInserisci(SrvConstants.SPACES);
		request.setFlgVincolo(GenericUtils.booleanToString(datiInput.isFlgVincolo()));
		request.setAzioneEseguita("inserisci");
		return request;
	}
	
	public static StorniBase valorizzaStorniBase(PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa, it.sistinf.rest.cobol.model.user.UtenteInfo utente, int codSocieta, boolean checkGestioneBloccoConteggio) {
		StorniBase storniBase = new StorniBase();
		storniBase.setPolizzaInfoSimpleEstesa(polizzaInfoSimpleEstesa);
		String flagDirezione = GenericUtils.booleanToString(utente.isDirezione());
		InteractionDTO iDTO = InteractionDTO.getInteractionDTO(codSocieta, utente.getCodUtente(), flagDirezione);
		WsdlMap wMap = InteractionDTO.getInteractionWsdlMap(iDTO);
		WsdlMapUtils.addItemFor(wMap, SrvConstants.REST, SrvConstants.SI);
		storniBase.setWsdlMap(wMap);
		storniBase.setUserAlbedo(utente.getCodUtenteInterno());
		storniBase.setCheckGestioneBloccoConteggio(checkGestioneBloccoConteggio ? SrvConstants.SI : SrvConstants.NO);
		storniBase.setFlDirezione(utente.isDirezione() ? SrvConstants.SI : SrvConstants.NO);
		CaricaParametriRecessoRequest caricaParametriRecessoRequest = new CaricaParametriRecessoRequest();
		caricaParametriRecessoRequest.setStorniBase(storniBase);
		caricaParametriRecessoRequest.setProcessName(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO.name());
		return storniBase;
	}
	
	public static CaricaParametriRecessoRequest valorizzaCaricaParametriRecessoRequest(StorniBase storniBase) {
		CaricaParametriRecessoRequest caricaParametriRecessoRequest = new CaricaParametriRecessoRequest();
		caricaParametriRecessoRequest.setStorniBase(storniBase);
		caricaParametriRecessoRequest.setProcessName(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO.name());
		return caricaParametriRecessoRequest;
	}
	
	public static PolizzaInfoSimpleEstesa getPolizzaInfoSimpleEstesaFrom(PolizzaSimpleEstesa polizzaSimpleEstesa, PolizzaInfo polizzaInfo) {
		PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa = new PolizzaInfoSimpleEstesa();
		polizzaInfoSimpleEstesa.setCodAgenzia(polizzaInfo.getCodAgenzia());
		polizzaInfoSimpleEstesa.setCodSocieta(polizzaInfo.getCodSocieta());
		polizzaInfoSimpleEstesa.setNumeroCategoria(polizzaInfo.getNumeroCategoria());
		polizzaInfoSimpleEstesa.setNumeroCollettiva(polizzaInfo.getNumeroCollettiva());
		polizzaInfoSimpleEstesa.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
		polizzaInfoSimpleEstesa.setPip(polizzaSimpleEstesa.isPip());
		polizzaInfoSimpleEstesa.setUnitLinked(polizzaSimpleEstesa.isUnitLinked());
		polizzaInfoSimpleEstesa.setIndex(polizzaSimpleEstesa.isIndex());
		polizzaInfoSimpleEstesa.setMultigaranzia(polizzaSimpleEstesa.isMultigaranzia());
		polizzaInfoSimpleEstesa.setFatca(polizzaSimpleEstesa.isFatca());
		polizzaInfoSimpleEstesa.setTranching(polizzaSimpleEstesa.isTranching());
		polizzaInfoSimpleEstesa.setAmleto(polizzaSimpleEstesa.isAmleto());
		polizzaInfoSimpleEstesa.setPianoPensione(polizzaSimpleEstesa.isPianoPensione());
		return polizzaInfoSimpleEstesa;
	}

	public static it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri getRecessoDatiParametriFrom(RecessoDatiParametri recessoDatiParametriRest) {
		it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri recessoDatiParametri = new it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri();
		recessoDatiParametri.setDataDisinvestimento((recessoDatiParametriRest.getDataDisinvestimento() != null) ? recessoDatiParametriRest.getDataDisinvestimento().getTime() : null);
		recessoDatiParametri.setDataDisinvestimentoCalcolata((recessoDatiParametriRest.getDataDisinvestimentoCalcolata() != null) ? recessoDatiParametriRest.getDataDisinvestimentoCalcolata().getTime() : null);
		recessoDatiParametri.setDataDocumento((recessoDatiParametriRest.getDataDocumento() != null) ? recessoDatiParametriRest.getDataDocumento().getTime() : null);
		recessoDatiParametri.setDataInizioEventoPrior((recessoDatiParametriRest.getDataInizioEventoPrior() != null) ? recessoDatiParametriRest.getDataInizioEventoPrior().getTime() : null);
		recessoDatiParametri.setDataInvioFU((recessoDatiParametriRest.getDataInvioFU() != null) ? recessoDatiParametriRest.getDataInvioFU().getTime() : null);
		recessoDatiParametri.setDataPrenotazione((recessoDatiParametriRest.getDataPrenotazione() != null) ? recessoDatiParametriRest.getDataPrenotazione().getTime() : null);
		recessoDatiParametri.setDataQuietanzaIncassata((recessoDatiParametriRest.getDataQuietanzaIncassata() != null) ? recessoDatiParametriRest.getDataQuietanzaIncassata().getTime() : null);
		recessoDatiParametri.setDataRicevimentoRichiesta((recessoDatiParametriRest.getDataRicevimentoRichiesta() != null) ? recessoDatiParametriRest.getDataRicevimentoRichiesta().getTime() : null);
		recessoDatiParametri.setDataRichiestaCliente((recessoDatiParametriRest.getDataRichiestaCliente() != null) ? recessoDatiParametriRest.getDataRichiestaCliente().getTime() : null);
		recessoDatiParametri.setDataRichiestaCompleta((recessoDatiParametriRest.getDataRichiestaCompleta() != null) ? recessoDatiParametriRest.getDataRichiestaCompleta().getTime() : null);
		recessoDatiParametri.setDataSistema((recessoDatiParametriRest.getDataSistema() != null) ? recessoDatiParametriRest.getDataSistema().getTime() : null);
		recessoDatiParametri.setDataUltimaRicezioneDocFU((recessoDatiParametriRest.getDataUltimaRicezioneDocFU() != null) ? recessoDatiParametriRest.getDataUltimaRicezioneDocFU().getTime() : null);
		recessoDatiParametri.setDscrEventoRibilNonConcluso(GenericUtils.stringToString(recessoDatiParametriRest.getDscrEventoRibilNonConcluso()));
		recessoDatiParametri.setEsisteRigaFU(GenericUtils.stringToString(recessoDatiParametriRest.getEsisteRigaFU()));
		recessoDatiParametri.setEventoRibilNonConcluso(GenericUtils.stringToString(recessoDatiParametriRest.getEventoRibilNonConcluso()));
		recessoDatiParametri.setExistQuoteToInvest(recessoDatiParametriRest.isExistQuoteToInvest());
		recessoDatiParametri.setMotivazioneRecesso(GenericUtils.stringToString(recessoDatiParametriRest.getMotivazioneRecesso()));
		recessoDatiParametri.setRecessoSenzaCostiAttivo(recessoDatiParametriRest.isRecessoSenzaCostiAttivo());
		String tipoOperazione = PolizzaUtility.valorizzaTipoOperazione(recessoDatiParametriRest.getTipoOperazione());
		recessoDatiParametri.setTipoOperazione((tipoOperazione != null) ? tipoOperazione : "");
		return recessoDatiParametri;
	}
	
	private static String valorizzaTipoOperazione(TipoOperazioneEnum tipoEnum) {
		String tipoOperazione = null;
		if(TipoOperazioneEnum.RECESSO.equals(tipoEnum)) {
			tipoOperazione = "1";
		} else if(TipoOperazioneEnum.RIMBORSO.equals(tipoEnum)) {
			tipoOperazione = "2";
		} else if(TipoOperazioneEnum.RECESSO_SENZA_COSTI.equals(tipoEnum)) {
			tipoOperazione = "4";
		}
		return tipoOperazione;
	}

	public static ControllaParametriRecessoRequest valorizzaControllaParametriRecessoRequest(PolizzaSimpleEstesa polizzaSimpleEstesa, StorniBase storniBase, it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri recessoDatiParametri) {
		ControllaParametriRecessoRequest controllaParametriRecessoRequest = new ControllaParametriRecessoRequest();
		controllaParametriRecessoRequest.setStorniBase(storniBase);
		controllaParametriRecessoRequest.setRecessoDatiParametri(recessoDatiParametri);
		controllaParametriRecessoRequest.setCodProdotto(polizzaSimpleEstesa.getCodProdotto());
		controllaParametriRecessoRequest.setCodRaggrProf(polizzaSimpleEstesa.getUlFondo());
		controllaParametriRecessoRequest.setDataDecorrenza(polizzaSimpleEstesa.getDataDecorrenza().getTime());
		controllaParametriRecessoRequest.setProcessName(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO.name());
		return controllaParametriRecessoRequest;
	}
	
	public static void getDatiSoggettoTerzoInfoFrom(DatiSoggettoTerzoInfo datiSoggettoTerzoInfoInput, it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzoInfo, DatiValutazioneIntermediario dtValutIntermediario) {
		datiSoggettoTerzoInfo.setRichDirettaSoggTerzo(GenericUtils.booleanToStringOrNull(dtValutIntermediario.isRichDiretta()));
		datiSoggettoTerzoInfo.setComportamentoClienteSoggTerzo(GenericUtils.stringToString(dtValutIntermediario.getValutCliente()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermSoggTerzo(GenericUtils.stringToString(dtValutIntermediario.getAnniRapportoInterm()));
		datiSoggettoTerzoInfo.setOpeCoerenteSoggTerzo(GenericUtils.booleanToStringOrNull(dtValutIntermediario.isOpeCoerente()));
		datiSoggettoTerzoInfo.setProfRischioIntermSoggTerzo(GenericUtils.stringToString(dtValutIntermediario.getProfRischioInterm()));
		datiSoggettoTerzoInfo.setCodiceCutomerSoggTerzo(datiSoggettoTerzoInfoInput.getCodiceCutomerSoggTerzo());
		datiSoggettoTerzoInfo.setPresenteEsecutore(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getPresenteEsecutore()));
		datiSoggettoTerzoInfo.setFlagEsecutore(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getFlagEsecutore()));
		datiSoggettoTerzoInfo.setFlagSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getFlagSoggettoTerzo()));
		datiSoggettoTerzoInfo.setTipologiaSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getTipologiaSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBeneficiarioSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getRelazioneBeneficiarioSoggettoTerzo()));
		datiSoggettoTerzoInfo.setFlagDelega(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getFlagDelega()));
		datiSoggettoTerzoInfo.setTipoEsecutore(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getTipoEsecutore()));
		datiSoggettoTerzoInfo.setTipoRapp(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getTipoRappresentanza()));
		datiSoggettoTerzoInfo.setTipoRappAltro(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getTipoRappresentanzaAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getDesRelazioneBenSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBenefSoggettoTerzoAltro(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getRelazioneBeneficiarioSoggettoTerzoAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefSoggettoTerzoAltro(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getDesRelazioneBenefSoggettoTerzoAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefContraenteAltro(GenericUtils.stringToString(datiSoggettoTerzoInfoInput.getDesRelazioneBenefContraenteAltro()));
	}	

	public static CaricaStorniCommonRequest valorizzaCaricaStorniCommonRequest(StorniBase storniBase, ControllaParametriRecessoResponse controllaParametriResponse, UtenteInfoResponse utenteInfoResponse, it.sistinf.rest.cobol.model.user.UtenteInfo utenteInfo) {
		CaricaStorniCommonRequest caricaStorniCommonRequest = new CaricaStorniCommonRequest();
		caricaStorniCommonRequest.setStorniBase(storniBase);
		caricaStorniCommonRequest.setUtenteLogin(valorizzaCustomerUtenteLoginInfo(utenteInfoResponse, utenteInfo));
		caricaStorniCommonRequest.setRecessoDatiParametri(controllaParametriResponse.getRecessoDatiParametri());
		caricaStorniCommonRequest.setStorniRimborso(null);
		return caricaStorniCommonRequest;
	}
	
	public static CustomerUtenteLoginInfo valorizzaCustomerUtenteLoginInfo(UtenteInfoResponse utenteInfoResponse, it.sistinf.rest.cobol.model.user.UtenteInfo utenteInfo) {
	    CustomerUtenteLoginInfo utenteLoginInfo = new CustomerUtenteLoginInfo();
	    it.sistinf.rest.cobol.model.user.UtenteInfo utente = utenteInfoResponse.getUtente();
	    utenteLoginInfo.setCmpExt(GenericUtils.stringToStringNoNull(utente.getCmpExt()));
	    utenteLoginInfo.setDirezione(utente.isDirezione());
	    utenteLoginInfo.setUsername(GenericUtils.stringToString(utente.getCodUtente()));
	    valorizzaUtenteRapportoReteVenditaInfo(utenteInfoResponse, utenteLoginInfo);
		return utenteLoginInfo;
	}

	private static void valorizzaUtenteRapportoReteVenditaInfo(UtenteInfoResponse utenteInfoResponse, CustomerUtenteLoginInfo utenteLoginInfo) {
		if ((utenteInfoResponse.getReteDiVendita() != null) && (!utenteInfoResponse.getReteDiVendita().isEmpty())){
	    	UtenteReteVenditaInfo utenteReteVenditaInfo = new UtenteReteVenditaInfo();
	    	ReteVendita reteVendita = utenteInfoResponse.getReteDiVendita().get(0);	    	
	    	utenteReteVenditaInfo.setLivello0(GenericUtils.stringToStringNoNull(reteVendita.getGruppo()));
			utenteReteVenditaInfo.setLivello1(GenericUtils.stringToStringNoNull(reteVendita.getAgenzia()));
			utenteReteVenditaInfo.setLivello2(GenericUtils.stringToStringNoNull(reteVendita.getSubagenzia()));
			utenteReteVenditaInfo.setLivello3(GenericUtils.stringToStringNoNull(reteVendita.getCollocatore()));
	    	utenteLoginInfo.setRapportoReteVenditaInfo(utenteReteVenditaInfo);	    	
	    	List<UtenteReteVenditaInfo> utenteReteVenditaInfoList = new ArrayList<UtenteReteVenditaInfo>();
	    	utenteReteVenditaInfoList.add(utenteReteVenditaInfo);
	    	utenteLoginInfo.getUtenteReteVenditaInfo().addAll(utenteReteVenditaInfoList);    	
	    }
	}
	
	public static StorniBase buildStorniBase(ControllaRecessoRequest body, it.sistinf.rest.cobol.model.user.UtenteInfo utenteInfo) {
		PolizzaSimpleEstesa polizzaSimpleEstesa = body.getPolizzaSimpleEstesa();
		it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa = PolizzaUtility.getPolizzaInfoSimpleEstesaFrom(polizzaSimpleEstesa, polizzaInfo);
		boolean checkGestioneBloccoConteggio = body.isCheckGestioneBloccoConteggio();
		StorniBase storniBase = PolizzaUtility.valorizzaStorniBase(polizzaInfoSimpleEstesa, utenteInfo, polizzaInfo.getCodSocieta(), checkGestioneBloccoConteggio);
		return storniBase;
	}	

	public static ControllaRecessoResponse valorizzaControllaRecessoResponse(CaricaStorniCommonResponse caricaStorniCommonResponse, ControllaRecessoRequest body, int codiceCustomer) {
		ControllaRecessoResponse controllaRecessoResponse = new ControllaRecessoResponse();
		StorniRimborso storniRimborso = valorizzaStorniRimborso(caricaStorniCommonResponse.getStorniRimborso());	
		controllaRecessoResponse.setStorniRimborso(storniRimborso);
		StorniCommon storniCommon = valorizzaStorniCommon(caricaStorniCommonResponse.getStorniCommonInfo(), codiceCustomer);
		controllaRecessoResponse.setStorniCommon(storniCommon);		
		//campi che arrivano dall'inizializza e servono ai servizi successivi
		controllaRecessoResponse.setPolizzaSimpleEstesa(body.getPolizzaSimpleEstesa());
		controllaRecessoResponse.setCheckGestioneBloccoConteggio(body.isCheckGestioneBloccoConteggio());
		controllaRecessoResponse.setRecessoDatiParametri(body.getRecessoDatiParametri());
		return controllaRecessoResponse;
	}

	private static StorniCommon valorizzaStorniCommon(StorniCommonInfo storniCommonInfo, int codiceCustomer) {
		StorniCommon storniCommon = new StorniCommon();
		List<StorniFigure> elencoStorniFigure = valorizzaElencoStorniFigure(storniCommonInfo.getStorniFigureList(), codiceCustomer);
		storniCommon.setElencoStorniFigure(elencoStorniFigure);
		List<DatiPagamento> elencoStorniDatiPagamento = valorizzaElencoStorniDatiPagamento(storniCommonInfo.getDatiPagamList());
		storniCommon.setElencoStorniDatiPagamento(elencoStorniDatiPagamento);
		AllCustomerInfo esecutoreRest = null;
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo esecutore = storniCommonInfo.getEsecutore();
		if(esecutore.getCodiceCliente() > 0) {
			esecutoreRest = CustomerUtility.valorizzaAllCustomerInfo(storniCommonInfo.getEsecutore());			
		}
		storniCommon.setEsecutore(esecutoreRest);
		return storniCommon;
	}

	private static List<DatiPagamento> valorizzaElencoStorniDatiPagamento(List<StorniDatiPagamento> datiPagamList) {
		List<DatiPagamento> elencoDatiPagamento = new ArrayList<DatiPagamento>();
		for(StorniDatiPagamento datiPagamInfo : datiPagamList) {
			DatiPagamento datiPagamento = valorizzaDatiPagamento(datiPagamInfo);
			elencoDatiPagamento.add(datiPagamento);
		}		
		return elencoDatiPagamento;
	}

	private static DatiPagamento valorizzaDatiPagamento(StorniDatiPagamento datiPagamInfo) {
		DatiPagamento datiPagamento = new DatiPagamento();
		CoordinateBancarieInfo coordinateBancarie = datiPagamInfo.getCoordinateBancarie();
		datiPagamento.setCausale(GenericUtils.stringToString(datiPagamInfo.getCausale()));
		datiPagamento.setSwift(GenericUtils.stringToString(datiPagamInfo.getSwift()));
		datiPagamento.setRelContrCointest(GenericUtils.stringToString(datiPagamInfo.getRelContrCointest()));
		datiPagamento.setRelContrCointestAltro(GenericUtils.stringToString(datiPagamInfo.getRelContrCointestAltro()));
		datiPagamento.setPaese(GenericUtils.stringToString(datiPagamInfo.getPaese()));
		datiPagamento.setMotivoContoEstero(GenericUtils.stringToString(datiPagamInfo.getMotivo()));
		datiPagamento.setNumSottoRub(GenericUtils.stringToString(datiPagamInfo.getNumSottoRub()));
		datiPagamento.setInvioDatiCollettore(GenericUtils.stringToString(datiPagamInfo.getInvioDatiCollettore()));
		datiPagamento.setModPagamento(GenericUtils.stringToString(coordinateBancarie.getModalitaPagamento()));
		datiPagamento.setIntestatarioPrimaRata(GenericUtils.stringToString(coordinateBancarie.getIntestatarioConto()));
		datiPagamento.setContoCointestato((SrvConstants.SI.equals(datiPagamInfo.getFlagCointest())) ? true : false);
		datiPagamento.setCodiceIbanPrimaRata(GenericUtils.stringToString(coordinateBancarie.getIban()));
		return datiPagamento;
	}	
	
	private static List<StorniFigure> valorizzaElencoStorniFigure(List<StorniFigureInfo> storniFigureList, int codiceCustomer) {
		List<StorniFigure> elencoStorniFigure = new ArrayList<StorniFigure>();
		for(StorniFigureInfo storniFigureInfo : storniFigureList) {
			StorniFigure storniFigure = valorizzaStorniFigure(storniFigureInfo, codiceCustomer);
			elencoStorniFigure.add(storniFigure);
		}		
		return elencoStorniFigure;
	}

	private static StorniFigure valorizzaStorniFigure(StorniFigureInfo storniFigureInfo, int codiceCustomer) {
		StorniFigure storniFigure = new StorniFigure();
		AllCustomerInfo percipiente = CustomerUtility.valorizzaAllCustomerInfo(storniFigureInfo.getPercipiente());
		storniFigure.setPercipiente(percipiente);
		AllCustomerInfo soggettoTerzoRest = null;
		DatiSoggettoTerzoInfo datiSoggettoTerzo = null;
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo soggettoTerzo = storniFigureInfo.getSoggettoTerzo();
		if(soggettoTerzo.getCodiceCliente() > 0) {
			soggettoTerzoRest = CustomerUtility.valorizzaAllCustomerInfo(storniFigureInfo.getSoggettoTerzo());			
			datiSoggettoTerzo = valorizzaDatiSoggettoTerzoInfo(storniFigureInfo.getDatiSoggettoTerzo());
			storniFigure.setDatiValutazioneIntermediarioSoggettoTerzo(getDatiValutazioneIntermediarioSoggettoTerzo(storniFigureInfo.getDatiSoggettoTerzo()));
		}
		if(percipiente.getCodiceCliente().intValue() == codiceCustomer) {
			storniFigure.setRelazioneBeneficiarioContraente(SrvConstants.MEDESIMO_SOGGETTO);
		}
		storniFigure.setSoggettoTerzo(soggettoTerzoRest);
		storniFigure.setDatiSoggettoTerzo(datiSoggettoTerzo);
		storniFigure.setDatiValutazioneIntermediarioPercipiente(getDatiValutazioneIntermediarioPercipiente(storniFigureInfo.getDatiSoggettoTerzo()));
		return storniFigure;
	}

	private static DatiValutazioneIntermediario getDatiValutazioneIntermediarioPercipiente(it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzo) {
		DatiValutazioneIntermediario datiValutazioneIntermediario = new DatiValutazioneIntermediario();
		datiValutazioneIntermediario.setAnniRapportoInterm(GenericUtils.stringToString(datiSoggettoTerzo.getAnniRapportoIntermBen()));
		String opeCoerenteSoggTerzo = GenericUtils.stringToString(datiSoggettoTerzo.getOpeCoerenteBen());
		datiValutazioneIntermediario.setOpeCoerente(GenericUtils.stringToBoolean(opeCoerenteSoggTerzo));
		datiValutazioneIntermediario.setProfRischioInterm(GenericUtils.stringToString(datiSoggettoTerzo.getProfRischioIntermBen()));
		String richiestaDiretta = GenericUtils.stringToString(GenericUtils.stringToString(datiSoggettoTerzo.getRichDirettaBen()));
		datiValutazioneIntermediario.setRichDiretta(GenericUtils.stringToBoolean(richiestaDiretta));
		datiValutazioneIntermediario.setValutCliente(GenericUtils.stringToString(datiSoggettoTerzo.getComportamentoClienteBen()));
		return datiValutazioneIntermediario;
	}

	private static DatiValutazioneIntermediario getDatiValutazioneIntermediarioSoggettoTerzo(it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzo) {
		DatiValutazioneIntermediario datiValutazioneIntermediario = new DatiValutazioneIntermediario();
		datiValutazioneIntermediario.setAnniRapportoInterm(GenericUtils.stringToString(datiSoggettoTerzo.getAnniRapportoIntermSoggTerzo()));
		String opeCoerenteSoggTerzo = GenericUtils.stringToString(datiSoggettoTerzo.getOpeCoerenteSoggTerzo());
		datiValutazioneIntermediario.setOpeCoerente(GenericUtils.stringToBoolean(opeCoerenteSoggTerzo));
		datiValutazioneIntermediario.setProfRischioInterm(GenericUtils.stringToString(datiSoggettoTerzo.getProfRischioIntermSoggTerzo()));
		String richiestaDiretta = GenericUtils.stringToString(GenericUtils.stringToString(datiSoggettoTerzo.getRichDirettaSoggTerzo()));
		datiValutazioneIntermediario.setRichDiretta(GenericUtils.stringToBoolean(richiestaDiretta));
		datiValutazioneIntermediario.setValutCliente(GenericUtils.stringToString(datiSoggettoTerzo.getComportamentoClienteSoggTerzo()));
		return datiValutazioneIntermediario;
	}

	private static DatiSoggettoTerzoInfo valorizzaDatiSoggettoTerzoInfo(it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzo) {
		DatiSoggettoTerzoInfo datiSoggettoTerzoInfo = new DatiSoggettoTerzoInfo();
		datiSoggettoTerzoInfo.setCodiceCutomerSoggTerzo(datiSoggettoTerzo.getCodiceCutomerSoggTerzo());
		datiSoggettoTerzoInfo.setPresenteEsecutore(GenericUtils.stringToString(datiSoggettoTerzo.getPresenteEsecutore()));
//		datiSoggettoTerzoInfo.setRelazioneBeneficiarioContraente(GenericUtils.stringToString(datiSoggettoTerzo.getRelazioneBeneficiarioContraente()));
		datiSoggettoTerzoInfo.setFlagEsecutore(GenericUtils.stringToString(datiSoggettoTerzo.getFlagEsecutore()));
		datiSoggettoTerzoInfo.setFlagSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getFlagSoggettoTerzo()));
		datiSoggettoTerzoInfo.setTipologiaSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getTipologiaSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBeneficiarioSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getRelazioneBeneficiarioSoggettoTerzo()));
		datiSoggettoTerzoInfo.setFlagDelega(GenericUtils.stringToString(datiSoggettoTerzo.getFlagDelega()));
		datiSoggettoTerzoInfo.setTipoEsecutore(GenericUtils.stringToString(datiSoggettoTerzo.getTipoEsecutore()));
		datiSoggettoTerzoInfo.setComportamentoClienteBen(GenericUtils.stringToString(datiSoggettoTerzo.getComportamentoClienteBen()));
		datiSoggettoTerzoInfo.setComportamentoClienteSoggTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getComportamentoClienteSoggTerzo()));
		datiSoggettoTerzoInfo.setTipoRappresentanza(GenericUtils.stringToString(datiSoggettoTerzo.getTipoRapp()));
		datiSoggettoTerzoInfo.setTipoRappresentanzaAltro(GenericUtils.stringToString(datiSoggettoTerzo.getTipoRappAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenContr(GenericUtils.stringToString(datiSoggettoTerzo.getDesRelazioneBenContr()));
		datiSoggettoTerzoInfo.setDesRelazioneBenSoggettoTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getDesRelazioneBenSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBeneficiarioSoggettoTerzoAltro(GenericUtils.stringToString(datiSoggettoTerzo.getRelazioneBenefSoggettoTerzoAltro()));
//		datiSoggettoTerzoInfo.setRelazioneBeneficiarioContraenteAltro(GenericUtils.stringToString(datiSoggettoTerzo.getRelazioneBenefContraenteAltro()));
		datiSoggettoTerzoInfo.setRichDirettaBen(GenericUtils.stringToString(datiSoggettoTerzo.getRichDirettaBen()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermBen(GenericUtils.stringToString(datiSoggettoTerzo.getAnniRapportoIntermBen()));
		datiSoggettoTerzoInfo.setProfRischioIntermBen(GenericUtils.stringToString(datiSoggettoTerzo.getProfRischioIntermBen()));
		datiSoggettoTerzoInfo.setOpeCoerenteBen(GenericUtils.stringToString(datiSoggettoTerzo.getOpeCoerenteBen()));
		datiSoggettoTerzoInfo.setRichDirettaSoggTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getRichDirettaSoggTerzo()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermSoggTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getAnniRapportoIntermSoggTerzo()));
		datiSoggettoTerzoInfo.setProfRischioIntermSoggTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getProfRischioIntermSoggTerzo()));
		datiSoggettoTerzoInfo.setOpeCoerenteSoggTerzo(GenericUtils.stringToString(datiSoggettoTerzo.getOpeCoerenteSoggTerzo()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefSoggettoTerzoAltro(GenericUtils.stringToString(datiSoggettoTerzo.getDesRelazioneBenefSoggettoTerzoAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefContraenteAltro(GenericUtils.stringToString(datiSoggettoTerzo.getDesRelazioneBenefContraenteAltro()));
		return datiSoggettoTerzoInfo;
	}

	private static StorniRimborso valorizzaStorniRimborso(it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoInfo storniRimborsoInfo) {
		StorniRimborso storniRimborsoRest = new StorniRimborso();
		storniRimborsoRest.setTotaleRimborso(storniRimborsoInfo.getTotaleRimborso());
		storniRimborsoRest.setTotalePrestazioneUnit(storniRimborsoInfo.getTotalePrestazioneUnit());
		storniRimborsoRest.setTotaleRimborsoUnit(storniRimborsoInfo.getTotaleRimborsoUnit());
		storniRimborsoRest.setTotalePremioRiferimentoUnit(storniRimborsoInfo.getTotalePremioRiferimentoUnit());
		storniRimborsoRest.setTotaleRimborsoGestSep(storniRimborsoInfo.getTotaleRimborsoGestSep());
		storniRimborsoRest.setTotalePremioRiferimentoGestSep(storniRimborsoInfo.getTotalePremioRiferimentoGestSep());
		List<StorniRimborsoCompoGestSep> elencoRimborsoCompoGestSep = valorizzaStorniRimborsoCompoGestSep(storniRimborsoInfo);
		storniRimborsoRest.setElencoRimborsoCompoGestSep(elencoRimborsoCompoGestSep);
		List<StorniRimborsoCompoUnit> elencoRimborsoCompoUnit = valorizzaStorniRimborsoCompoUnit(storniRimborsoInfo);
		storniRimborsoRest.setElencoRimborsoCompoUnit(elencoRimborsoCompoUnit);		
		storniRimborsoRest.setTassoIntCorr(storniRimborsoInfo.getTassoIntCorr());
		storniRimborsoRest.setTassoIntPrec(storniRimborsoInfo.getTassoIntPrec());
		storniRimborsoRest.setGiorniIntCorr(storniRimborsoInfo.getGiorniIntCorr());
		storniRimborsoRest.setGiorniIntPrec(storniRimborsoInfo.getGiorniIntPrec());
		storniRimborsoRest.setDataLiquidPresunta(DateUtils.longToDate(storniRimborsoInfo.getDataLiquidPresunta()));
		return storniRimborsoRest;
	}

	private static List<StorniRimborsoCompoUnit> valorizzaStorniRimborsoCompoUnit(it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoInfo storniRimborsoInfo) {
		List<StorniRimborsoCompoUnit> elencoRimborsoCompoUnit = new ArrayList<StorniRimborsoCompoUnit>();
		List<StorniRimborsoCompoUnitInfo> compoUnitList = storniRimborsoInfo.getRimborsoCompoUnitList();
		for(StorniRimborsoCompoUnitInfo storniRimborsoCompoUnitInfo : compoUnitList) {
			StorniRimborsoCompoUnit storniRimborsoCompoUnit = valorizzaStorniRimborsoCompoUnit(storniRimborsoCompoUnitInfo);
			elencoRimborsoCompoUnit.add(storniRimborsoCompoUnit);
		}		
		return elencoRimborsoCompoUnit;
	}

	private static StorniRimborsoCompoUnit valorizzaStorniRimborsoCompoUnit(StorniRimborsoCompoUnitInfo storniRimborsoCompoUnitInfo) {
		StorniRimborsoCompoUnit storniRimborsoCompoUnit = new StorniRimborsoCompoUnit();
		storniRimborsoCompoUnit.setUlFondo(storniRimborsoCompoUnitInfo.getUlFondo());
		storniRimborsoCompoUnit.setDescrUlFondo(storniRimborsoCompoUnitInfo.getDescrUlFondo());
		storniRimborsoCompoUnit.setDataDisinv(DateUtils.longToDate(storniRimborsoCompoUnitInfo.getDataDisinv()));
		storniRimborsoCompoUnit.setPrestazione(storniRimborsoCompoUnitInfo.getPrestazione());
		storniRimborsoCompoUnit.setUltValore(storniRimborsoCompoUnitInfo.getUltValore());
		storniRimborsoCompoUnit.setControvalore(storniRimborsoCompoUnitInfo.getControvalore());
		return storniRimborsoCompoUnit;
	}

	private static List<StorniRimborsoCompoGestSep> valorizzaStorniRimborsoCompoGestSep(it.sistinf.albedoweb.services.polizza.storni.types.StorniRimborsoInfo storniRimborsoInfo) {
		List<StorniRimborsoCompoGestSep> elencoRimborsoCompoGestSep = new ArrayList<StorniRimborsoCompoGestSep>();
		List<StorniRimborsoCompoGestSepInfo> compoGestSepList = storniRimborsoInfo.getRimborsoCompoGestSepList();
		for(StorniRimborsoCompoGestSepInfo storniRimborsoCompoGestSepInfo : compoGestSepList) {
			StorniRimborsoCompoGestSep storniRimborsoCompoGestSep = valorizzaStorniRimborsoCompoUnit(storniRimborsoCompoGestSepInfo);
			elencoRimborsoCompoGestSep.add(storniRimborsoCompoGestSep);
		}		
		return elencoRimborsoCompoGestSep;
	}

	private static StorniRimborsoCompoGestSep valorizzaStorniRimborsoCompoUnit(StorniRimborsoCompoGestSepInfo storniRimborsoCompoGestSepInfo) {
		StorniRimborsoCompoGestSep storniRimborsoCompoGestSep = new StorniRimborsoCompoGestSep();
		storniRimborsoCompoGestSep.setFondoRival(GenericUtils.stringToString(storniRimborsoCompoGestSepInfo.getFondoRival()));
		storniRimborsoCompoGestSep.setDescrFondoRival(GenericUtils.stringToString(storniRimborsoCompoGestSepInfo.getDescrFondoRival()));
		storniRimborsoCompoGestSep.setDataEffetto(DateUtils.longToDate(storniRimborsoCompoGestSepInfo.getDataEffetto()));
		storniRimborsoCompoGestSep.setPrestazione(storniRimborsoCompoGestSepInfo.getPrestazione());
		storniRimborsoCompoGestSep.setRendimento(storniRimborsoCompoGestSepInfo.getRendimento());
		storniRimborsoCompoGestSep.setControvalore(storniRimborsoCompoGestSepInfo.getControvalore());
		List<Integer> numPosiz = storniRimborsoCompoGestSepInfo.getNumPosiz();
		storniRimborsoCompoGestSep.getElencoNumPosiz().addAll(numPosiz);
		storniRimborsoCompoGestSep.setFrazioneAnno(storniRimborsoCompoGestSepInfo.getFrazioneAnno());
		storniRimborsoCompoGestSep.setRendimentoLordo(storniRimborsoCompoGestSepInfo.getRendimentoLordo());
		storniRimborsoCompoGestSep.setRendimentoTrattenuto(storniRimborsoCompoGestSepInfo.getRendimentoTrattenuto());
		storniRimborsoCompoGestSep.setRendimentoNetto(storniRimborsoCompoGestSepInfo.getRendimentoNetto());
		storniRimborsoCompoGestSep.setTassoUtilizzato(storniRimborsoCompoGestSepInfo.getTassoUtilizzato());
		storniRimborsoCompoGestSep.setPrestazioneLorda(storniRimborsoCompoGestSepInfo.getPrestazioneLorda());		
		return storniRimborsoCompoGestSep;
	}
	
	public static InoltraPrenotazRiscattoResponse valorizzaInoltraPrenotazRiscattoResponse(InteractionDTO interactionDTO, Long idPrenotazione) {
		InoltraPrenotazRiscattoResponse response = new InoltraPrenotazRiscattoResponse();
		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
		response.setIdPrenotazione(idPrenotazione);
		response.setEsito(true);
		return response;
	}

	public static boolean isResponseError(CaricaStorniCommonResponse caricaStorniCommonResponse) {
		StorniRimborsoInfo storniRimborso = caricaStorniCommonResponse.getStorniRimborso();
		return (storniRimborso == null || storniRimborso.getTotaleRimborso() == null || new BigDecimal(0).compareTo(storniRimborso.getTotaleRimborso()) == 0);		
	}
	
	public static CustomerInfo buildCustomerInfo(UtenteInfoResponse utenteInfoResponse, String tipoRuolo, String utilizzatore, CustomerOtherInfo customerOther) {
		CustomerInfo customerInfo = new CustomerInfo();
		CustomerInteractionInfo customerInteraction = PropostaUtility.valorizzaCustomerInteractionInfo(utenteInfoResponse);
		customerInteraction.setRuolo(tipoRuolo);
		customerInteraction.setUtilizzatore(utilizzatore);
		customerInfo.setCustomerInteraction(customerInteraction);
		customerInfo.setCustomerValidation(getValidatorKeyEntry(utilizzatore, SrvConstants.CUSTOMER_VALIDATOR_MODIFICA, tipoRuolo));
		customerInfo.setCustomerOther(customerOther);
		return customerInfo;
	}
	
	private static ValidatorKeyEntry getValidatorKeyEntry(String utilizzatore, String funzione, String ruolo) {
		ValidatorKeyEntry validatorKeyEntry = new ValidatorKeyEntry();
		validatorKeyEntry.setFunzione(funzione);
		validatorKeyEntry.setProcesso(utilizzatore);
		validatorKeyEntry.setRuolo(ruolo);
		return validatorKeyEntry;
	}
	
	public static it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri valorizzaRecessoDatiParametriFrom(RecessoDatiParametri recessoDatiParametriRest) {
	    it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri recessoDatiParametri = new it.sistinf.albedoweb.services.polizza.storni.types.RecessoDatiParametri();
	    if (recessoDatiParametriRest.getDataSistema() != null) {
	        recessoDatiParametri.setDataSistema(recessoDatiParametriRest.getDataSistema().getTime());
	    }
	    if (recessoDatiParametriRest.getDataPrenotazione() != null) {
	        recessoDatiParametri.setDataPrenotazione(recessoDatiParametriRest.getDataPrenotazione().getTime());
	    }
	    if (recessoDatiParametriRest.getDataRichiestaCliente() != null) {
	        recessoDatiParametri.setDataRichiestaCliente(recessoDatiParametriRest.getDataRichiestaCliente().getTime());
	    }
	    if (recessoDatiParametriRest.getDataRicevimentoRichiesta() != null) {
	        recessoDatiParametri.setDataRicevimentoRichiesta(recessoDatiParametriRest.getDataRicevimentoRichiesta().getTime());
	    }
	    if (recessoDatiParametriRest.getDataDisinvestimento() != null) {
	        recessoDatiParametri.setDataDisinvestimento(recessoDatiParametriRest.getDataDisinvestimento().getTime());
	    }
	    if (recessoDatiParametriRest.getDataDisinvestimentoCalcolata() != null) {
	        recessoDatiParametri.setDataDisinvestimentoCalcolata(recessoDatiParametriRest.getDataDisinvestimentoCalcolata().getTime());
	    }
	    if (recessoDatiParametriRest.getDataRichiestaCompleta() != null) {
	        recessoDatiParametri.setDataRichiestaCompleta(recessoDatiParametriRest.getDataRichiestaCompleta().getTime());
	    }
	    if (recessoDatiParametriRest.getDataQuietanzaIncassata() != null) {
	        recessoDatiParametri.setDataQuietanzaIncassata(recessoDatiParametriRest.getDataQuietanzaIncassata().getTime());
	    }
	    if (recessoDatiParametriRest.getDataDocumento() != null) {
	        recessoDatiParametri.setDataDocumento(recessoDatiParametriRest.getDataDocumento().getTime());
	    }
	    if (recessoDatiParametriRest.getDataInvioFU() != null) {
	        recessoDatiParametri.setDataInvioFU(recessoDatiParametriRest.getDataInvioFU().getTime());
	    }
	    if (recessoDatiParametriRest.getDataUltimaRicezioneDocFU() != null) {
	        recessoDatiParametri.setDataUltimaRicezioneDocFU(recessoDatiParametriRest.getDataUltimaRicezioneDocFU().getTime());
	    }
	    if (recessoDatiParametriRest.getDataInizioEventoPrior() != null) {
	        recessoDatiParametri.setDataInizioEventoPrior(recessoDatiParametriRest.getDataInizioEventoPrior().getTime());
	    }
	    recessoDatiParametri.setExistQuoteToInvest(recessoDatiParametriRest.isExistQuoteToInvest());
	    TipoOperazioneEnum tipoOperazione = recessoDatiParametriRest.getTipoOperazione();
	    recessoDatiParametri.setTipoOperazione((tipoOperazione != null) ? tipoOperazione.name() : null);
	    recessoDatiParametri.setMotivazioneRecesso(recessoDatiParametriRest.getMotivazioneRecesso());
	    recessoDatiParametri.setRecessoSenzaCostiAttivo(recessoDatiParametriRest.isRecessoSenzaCostiAttivo());
	    recessoDatiParametri.setEventoRibilNonConcluso(recessoDatiParametriRest.getEventoRibilNonConcluso());
	    recessoDatiParametri.setDscrEventoRibilNonConcluso(recessoDatiParametriRest.getDscrEventoRibilNonConcluso());
	    recessoDatiParametri.setEsisteRigaFU(recessoDatiParametriRest.getEsisteRigaFU());
	    return recessoDatiParametri;
	}
	
	public static boolean isValutazioneFiguraOk(ControllaSingolaFiguraResponse controllaFigura) {
	    if (controllaFigura.isIsValutazioneErr() != null && controllaFigura.isIsValutazioneErr()) {
	        return false;
	    }
	    if (controllaFigura.isIsValutazioneSoggTerzoErr() != null && controllaFigura.isIsValutazioneSoggTerzoErr()) {
	        return false;
	    }
	    if (controllaFigura.isIsValutazioneLegRapErr() != null && controllaFigura.isIsValutazioneLegRapErr()) {
	        return false;
	    }
	    if (controllaFigura.isIsFlagSoggTerzoErr() != null && controllaFigura.isIsFlagSoggTerzoErr()) {
	        return false;
	    }
	    return true;
	}

	public static StorniDatiPagamento getStorniDatiPagamento(List<DatiPagamento> datiPagamento, boolean isUtenteDirezione, int codSoc) {
		StorniDatiPagamento storniDatiPagamento = new StorniDatiPagamento();
		if((datiPagamento != null) && (!datiPagamento.isEmpty())){
			DatiPagamento datiPagamentoInput = datiPagamento.get(0);			
			valorizzaStorniDatipagamento(isUtenteDirezione, codSoc, storniDatiPagamento, datiPagamentoInput);
		}
		return storniDatiPagamento;
	}

	private static void valorizzaStorniDatipagamento(boolean isUtenteDirezione, int codSoc, StorniDatiPagamento storniDatiPagamento, DatiPagamento datiPagamentoInput) {
		storniDatiPagamento.setCausale(GenericUtils.stringToString(datiPagamentoInput.getCausale()));
		storniDatiPagamento.setSwift(GenericUtils.stringToString(datiPagamentoInput.getSwift()));
		storniDatiPagamento.setFlagCointest(datiPagamentoInput.isContoCointestato() ? SrvConstants.SI : SrvConstants.NO);
		storniDatiPagamento.setCointestatario(GenericUtils.stringToString(datiPagamentoInput.getNomeCointestatario()));
		storniDatiPagamento.setRelContrCointest(GenericUtils.stringToString(datiPagamentoInput.getRelContrCointest()));
		storniDatiPagamento.setRelContrCointestAltro(GenericUtils.stringToString(datiPagamentoInput.getRelContrCointestAltro()));
		storniDatiPagamento.setPaese(GenericUtils.stringToString(datiPagamentoInput.getPaese()));
		storniDatiPagamento.setMotivo(GenericUtils.stringToString(datiPagamentoInput.getMotivoContoEstero()));
		storniDatiPagamento.setNumSottoRub(GenericUtils.stringToString(datiPagamentoInput.getNumSottoRub()));
		storniDatiPagamento.setInvioDatiCollettore(GenericUtils.stringToString(datiPagamentoInput.getNumSottoRub()));
		storniDatiPagamento.setFlDirezione(isUtenteDirezione ? SrvConstants.SI : SrvConstants.NO);
		CoordinateBancarieInfo coordinateBancarie = new CoordinateBancarieInfo();
		String iban = GenericUtils.stringToString(datiPagamentoInput.getCodiceIbanPrimaRata());
		coordinateBancarie.setIban(iban);
		coordinateBancarie.setModalitaPagamento(GenericUtils.stringToString(datiPagamentoInput.getModPagamento()));
		coordinateBancarie.setIntestatarioConto(GenericUtils.stringToString(datiPagamentoInput.getIntestatarioPrimaRata()));
		coordinateBancarie.setCodSoc(codSoc);
		storniDatiPagamento.setCoordinateBancarie(coordinateBancarie);
	}
	
	public static it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo valorizzaDatiSoggettoTerzoInfoPerBeneficiario(StorniFigure storniFigureInfo, DatiValutazioneIntermediario dtValutIntermediarioPercipiente) {
		it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzoInfo = new it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo();
		datiSoggettoTerzoInfo.setRelazioneBeneficiarioContraente(GenericUtils.stringToString(storniFigureInfo.getRelazioneBeneficiarioContraente()));
		datiSoggettoTerzoInfo.setRelazioneBenefContraenteAltro(GenericUtils.stringToString(storniFigureInfo.getRelazioneBeneficiarioContraenteAltro()));
		String flPresenzaSoggTerzo = storniFigureInfo.getSoggettoTerzo() != null ? SrvConstants.SI : SrvConstants.NO;
		datiSoggettoTerzoInfo.setFlagSoggettoTerzo(flPresenzaSoggTerzo);
		datiSoggettoTerzoInfo.setRichDirettaBen(GenericUtils.booleanToStringOrNull(dtValutIntermediarioPercipiente.isRichDiretta()));
		datiSoggettoTerzoInfo.setComportamentoClienteBen(GenericUtils.stringToString(dtValutIntermediarioPercipiente.getValutCliente()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermBen(GenericUtils.stringToString(dtValutIntermediarioPercipiente.getAnniRapportoInterm()));
		datiSoggettoTerzoInfo.setOpeCoerenteBen(GenericUtils.booleanToStringOrNull(dtValutIntermediarioPercipiente.isOpeCoerente()));
		datiSoggettoTerzoInfo.setProfRischioIntermBen(GenericUtils.stringToString(dtValutIntermediarioPercipiente.getProfRischioInterm()));
		return datiSoggettoTerzoInfo;
	}
	
	public static ExecutionContext buildExecutionContextForSalvaPrenotazioneRecesso(SalvaPrenotazioneRecessoRequest body, UtenteInfoResponse utenteInfoResponse, String cognomeRagSocialeContraente, String nomeContraente, String functionName, String tipoPratica, DominiInterface domini, Integer codSoc, boolean isDirezione, long idPrenotazione) {
		HeaderCobolSrv headerCobolSrv = CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), body.getPolizzaInfo().getCodSocieta());
		String permKey = headerCobolSrv.getPermKey();
		it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		PrenotazioneExecutionContext executionContext = initWorkFlowVAEmessoStartRequest(permKey, polizzaInfo, utenteInfoResponse, cognomeRagSocialeContraente, nomeContraente);
		executionContext.setProcessName(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO.name());
		((PrenotazioneToken)executionContext.getToken()).setProcessType(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO);
		((PrenotazioneToken)executionContext.getToken()).setTipologiaPratica(tipoPratica);
		executionContext.setProcessStep(functionName);
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));		
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionContext.setInteractionDto(interactionDtoMap);	
		Map<String, Serializable> javaServiceMap = new HashMap<String, Serializable>();		
		SalvaRecessoRequest salvaRecesso = valorizzaSalvaRecessoRequest(body, functionName, utenteInfoResponse, domini, isDirezione, codSoc, idPrenotazione);
		javaServiceMap.put(SrvConstants.JAVA_SERVICE_INPUT, salvaRecesso);
		executionContext.setJavaServicesInput(javaServiceMap);
		return executionContext;
	}
	
	public static ExecutionContext buildExecutionContextForInoltraPrenotazioneRecesso(SalvaPrenotazioneRecessoRequest body, UtenteInfoResponse utenteInfoResponse, String functionName, String tipoPratica, DominiInterface domini, Integer codSoc, boolean isDirezione, PrenotazioneToken token, long idPrenotazione) {
		HeaderCobolSrv headerCobolSrv = CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), body.getPolizzaInfo().getCodSocieta());
		it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo = body.getPolizzaInfo();
		PrenotazioneExecutionContext executionContext = initWorkFlowVAEmessoInoltraRequest(headerCobolSrv, token);
		executionContext.setProcessName(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO.name());
		((PrenotazioneToken)executionContext.getToken()).setProcessType(ProcessTypeEnumeration.WORKFLOW_STORNI_RECESSO);
		((PrenotazioneToken)executionContext.getToken()).setTipologiaPratica(tipoPratica);
		executionContext.setProcessStep(functionName);
		executionContext.setProcessId(idPrenotazione);
		InteractionDTO interactionDTO = InteractionUtility.valorizzaInteractionDTO(CommonUtility.valorizzaHeaderCobolWithPK(body.getUtente(), polizzaInfo.getCodSocieta()));		
		Map<String, Serializable> interactionDtoMap = new HashMap<String, Serializable>();
		interactionDtoMap.put(SrvConstants.WF_INTERACTION_DTO, interactionDTO);
		executionContext.setInteractionDto(interactionDtoMap);	
		Map<String, Serializable> javaServiceMap = new HashMap<String, Serializable>();		
		SalvaRecessoRequest salvaRecesso = valorizzaSalvaRecessoRequest(body, functionName, utenteInfoResponse, domini, isDirezione, codSoc, idPrenotazione);
		javaServiceMap.put(SrvConstants.JAVA_SERVICE_INPUT, salvaRecesso);
		executionContext.setJavaServicesInput(javaServiceMap);
		return executionContext;
	}	

	private static SalvaRecessoRequest valorizzaSalvaRecessoRequest(SalvaPrenotazioneRecessoRequest body, String functionName, UtenteInfoResponse utenteInfoResponse, DominiInterface domini, boolean isDirezione, Integer codsoc, long idPrenotazione) {
		SalvaRecessoRequest salvaRecessoRequest = new SalvaRecessoRequest();
		salvaRecessoRequest.setStorniBase(valorizzaStorniBaseRecesso(body.isCheckGestioneBloccoConteggio(), isDirezione, utenteInfoResponse, body.getPolizzaInfo(), idPrenotazione));
		salvaRecessoRequest.setStorniInfo(valorizzaStorniInfo(body.getStorniCommon(), body.getUtente(), utenteInfoResponse, domini, isDirezione, codsoc)); 
		salvaRecessoRequest.setParametriRecesso(getRecessoDatiParametriFrom(body.getRecessoDatiParametri()));
		salvaRecessoRequest.setProcessStep(functionName);
		salvaRecessoRequest.setFlConfermaPrenotazione(SrvConstants.NO);
		salvaRecessoRequest.setImpLordoLiqTot(body.getStorniRimborso().getTotaleRimborso());
		salvaRecessoRequest.getMessErrWarning().addAll(new ArrayList<CodeDescription>());
		salvaRecessoRequest.setFlErroriNoCompleta(true);			
		return salvaRecessoRequest;
	}

	private static StorniBase valorizzaStorniBaseRecesso(boolean checkGestioneBloccoConteggio, boolean isDirezione, UtenteInfoResponse utenteInfoResponse, PolizzaInfo polizzaInfo, long idPrenotazione) {
		StorniBase storniBase = new StorniBase();
		storniBase.setFlDirezione((isDirezione) ? SrvConstants.SI : SrvConstants.NO);
		storniBase.setCheckGestioneBloccoConteggio((checkGestioneBloccoConteggio) ? SrvConstants.SI : SrvConstants.NO);
		storniBase.setUserAlbedo(utenteInfoResponse.getUtente().getCodUtenteInterno());
		PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa = new PolizzaInfoSimpleEstesa();
		polizzaInfoSimpleEstesa.setCodSocieta(polizzaInfo.getCodSocieta());
		polizzaInfoSimpleEstesa.setCodAgenzia(polizzaInfo.getCodAgenzia());
		polizzaInfoSimpleEstesa.setNumeroCategoria(polizzaInfo.getNumeroCategoria());
		polizzaInfoSimpleEstesa.setNumeroCollettiva(polizzaInfo.getNumeroCollettiva());
		polizzaInfoSimpleEstesa.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
		polizzaInfoSimpleEstesa.setIdPrenotazione((int)idPrenotazione);
		storniBase.setPolizzaInfoSimpleEstesa(polizzaInfoSimpleEstesa);
		return storniBase;
	}

	private static StorniInfo valorizzaStorniInfo(StorniCommon storniCommon, String utente, UtenteInfoResponse utenteInfoResponse, DominiInterface domini, boolean isDirezione, Integer codsoc) {
		StorniInfo storniInfo = new StorniInfo();
		storniInfo.setStorniCommonInfo(valorizzaStorniCommonInfo(storniCommon, utente, utenteInfoResponse, domini, isDirezione, codsoc));
		DatiDocumentazioneInfo datiDocumentazioneInfo = new DatiDocumentazioneInfo();
		datiDocumentazioneInfo.setDataInvioDoc(DateUtils.DATE_01_01_0001_AS_LONG);
		datiDocumentazioneInfo.setDataRicezioneDoc(DateUtils.DATE_01_01_0001_AS_LONG);
		storniInfo.setDocumentazioneInfo(datiDocumentazioneInfo);
		return storniInfo;
	}

	private static StorniCommonInfo valorizzaStorniCommonInfo(StorniCommon storniCommon, String utente, UtenteInfoResponse utenteInfoResponse, DominiInterface domini, boolean isDirezione, Integer codSoc) {
		StorniCommonInfo storniCommonInfo = new StorniCommonInfo();
		List<StorniFigureInfo> storniFigureList = valorizzaStorniFigureList(storniCommon.getElencoStorniFigure(), storniCommon, utente, utenteInfoResponse, domini);
		storniCommonInfo.getStorniFigureList().addAll(storniFigureList);
		List<StorniDatiPagamento> datiPagamList = valorizzaDatiPagamList(storniCommon.getElencoStorniDatiPagamento(), isDirezione, codSoc);
		storniCommonInfo.getDatiPagamList().addAll(datiPagamList);
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo esecutore = CustomerUtility.getAllCustomerInfoSoap(storniCommon.getEsecutore(), utente, null, domini, utenteInfoResponse);
		storniCommonInfo.setEsecutore(esecutore);
		return storniCommonInfo;
	}

	private static List<StorniDatiPagamento> valorizzaDatiPagamList(List<DatiPagamento> elencoStorniDatiPagamento, boolean isDirezione, Integer codSoc) {
		List<StorniDatiPagamento> datiPagamList = new ArrayList<StorniDatiPagamento>();
		for(DatiPagamento datiPagamento : elencoStorniDatiPagamento) {
			StorniDatiPagamento storniDatiPagamento = new StorniDatiPagamento();
			valorizzaStorniDatipagamento(isDirezione, codSoc, storniDatiPagamento, datiPagamento);
			datiPagamList.add(storniDatiPagamento);
		}
		return datiPagamList;
	}

	private static List<StorniFigureInfo> valorizzaStorniFigureList(List<StorniFigure> elencoStorniFigureRest, StorniCommon storniCommon, String utente, UtenteInfoResponse utenteInfoResponse, DominiInterface domini) {
		List<StorniFigureInfo> storniFigureList = new ArrayList<StorniFigureInfo>();
		for(StorniFigure storniFigureRest : elencoStorniFigureRest) {
			StorniFigureInfo storniFigureInfo = valorizzaStorniFigureInfo(storniFigureRest, utente, domini, utenteInfoResponse);
			storniFigureList.add(storniFigureInfo);
		}
		return storniFigureList;
	}

	private static StorniFigureInfo valorizzaStorniFigureInfo(StorniFigure storniFigureRest, String utente, DominiInterface domini, UtenteInfoResponse utenteInfoResponse) {
		StorniFigureInfo storniFigureInfo = new StorniFigureInfo();
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo percipiente = CustomerUtility.getAllCustomerInfoSoap(storniFigureRest.getPercipiente(), utente, null, domini, utenteInfoResponse);
		storniFigureInfo.setPercipiente(percipiente);
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo soggettoTerzo = (storniFigureRest.getSoggettoTerzo() != null) ? CustomerUtility.getAllCustomerInfoSoap(storniFigureRest.getSoggettoTerzo(), utente, null, domini, utenteInfoResponse) : new it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo();					
		storniFigureInfo.setSoggettoTerzo(soggettoTerzo);
		it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzoInfo = (storniFigureRest.getDatiSoggettoTerzo() != null ? valorizzaDatiSoggettoTerzo(storniFigureRest.getDatiSoggettoTerzo()) : new it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo());
		storniFigureInfo.setDatiSoggettoTerzo(datiSoggettoTerzoInfo);
		return storniFigureInfo;
	}

	private static it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo valorizzaDatiSoggettoTerzo(DatiSoggettoTerzoInfo datiSoggettoTerzo) {
		it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo datiSoggettoTerzoInfo = new it.sistinf.albedoweb.services.polizza.percipienti.types.DatiSoggettoTerzoInfo();
		datiSoggettoTerzoInfo.setCodiceCutomerSoggTerzo(datiSoggettoTerzo.getCodiceCutomerSoggTerzo());
		datiSoggettoTerzoInfo.setPresenteEsecutore(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getPresenteEsecutore()));
		datiSoggettoTerzoInfo.setFlagEsecutore(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getFlagEsecutore()));
		datiSoggettoTerzoInfo.setFlagSoggettoTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getFlagSoggettoTerzo()));
		datiSoggettoTerzoInfo.setTipologiaSoggettoTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getTipologiaSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBeneficiarioSoggettoTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getRelazioneBeneficiarioSoggettoTerzo()));
		datiSoggettoTerzoInfo.setFlagDelega(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getFlagDelega()));
		datiSoggettoTerzoInfo.setTipoEsecutore(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getTipoEsecutore()));
		datiSoggettoTerzoInfo.setComportamentoClienteBen(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getComportamentoClienteBen()));
		datiSoggettoTerzoInfo.setComportamentoClienteSoggTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getComportamentoClienteSoggTerzo()));
		datiSoggettoTerzoInfo.setTipoRapp(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getTipoRappresentanza()));
		datiSoggettoTerzoInfo.setTipoRappAltro(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getTipoRappresentanzaAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenContr(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getDesRelazioneBenContr()));
		datiSoggettoTerzoInfo.setDesRelazioneBenSoggettoTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getDesRelazioneBenSoggettoTerzo()));
		datiSoggettoTerzoInfo.setRelazioneBenefSoggettoTerzoAltro(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getRelazioneBeneficiarioSoggettoTerzoAltro()));
		datiSoggettoTerzoInfo.setRichDirettaBen(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getRichDirettaBen()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermBen(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getAnniRapportoIntermBen()));
		datiSoggettoTerzoInfo.setProfRischioIntermBen(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getProfRischioIntermBen()));
		datiSoggettoTerzoInfo.setOpeCoerenteBen(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getOpeCoerenteBen()));
		datiSoggettoTerzoInfo.setRichDirettaSoggTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getRichDirettaSoggTerzo()));
		datiSoggettoTerzoInfo.setAnniRapportoIntermSoggTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getAnniRapportoIntermSoggTerzo()));
		datiSoggettoTerzoInfo.setProfRischioIntermSoggTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getProfRischioIntermSoggTerzo()));
		datiSoggettoTerzoInfo.setOpeCoerenteSoggTerzo(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getOpeCoerenteSoggTerzo()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefSoggettoTerzoAltro(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getDesRelazioneBenefSoggettoTerzoAltro()));
		datiSoggettoTerzoInfo.setDesRelazioneBenefContraenteAltro(GenericUtils.stringToStringNoNull(datiSoggettoTerzo.getDesRelazioneBenefContraenteAltro()));
		return datiSoggettoTerzoInfo;
	}
	
	public static ControllaVersamentoAggiuntivoRequest valorizzaControllaVARequest(ControllaSalvaVAEmessoRequest body, SelectTipoProdottoDettaglioGenericoResponse tipoProdottoResponse, SelectPolizzaResponse polizzaResponse, StrutturaReteInterface strutturaRete) throws Exception {
		ControllaVersamentoAggiuntivoRequest cvaRequest = new ControllaVersamentoAggiuntivoRequest();
		it.sistinf.albedoweb.services.polizza.polizza.types.PolizzaInfo polizzaInfo = polizzaResponse.getPolizzeInfoSelect().get(0);
		String utente = body.getHeaderCobolSrv().getIdUtente();
		int codCompagnia = body.getPolizzaInfo().getCodSocieta();
		String livello1 = GenericUtils.stringToString(polizzaInfo.getCodAgenziaGest());
		Livello1Info livello1Info = getLivello1Info(livello1, strutturaRete);
		String livello0 = GenericUtils.stringToString(livello1Info.getCodiceGruppo());
		boolean isAgenziaNonAdeguata = CustomerHelperProxy.isAgenziaNonAdeguata(codCompagnia, livello1);
		cvaRequest.setIsAgenziaNonAdeguata(isAgenziaNonAdeguata);
		String dataRichiesta = body.getDatiVideoVAEmesso().getDataRichiestaCliente();
		Date dataRiferimento = DateUtils.convertiStringaFormattataToData(dataRichiesta, DateUtils.DATA_PATTERN_FORMATO_ITA);
		String codProdotto = GenericUtils.stringToString(polizzaInfo.getCodProdotto());
		boolean flControlloAmleto = CustomerHelperProxy.getFlagControlliAmleto(codCompagnia, body.getPolizzaInfo().getNumeroCategoria(), codProdotto, dataRiferimento, livello0, livello1);
		cvaRequest.setFlControlloAmleto(flControlloAmleto);
		int codCli = polizzaResponse.getPolizzeInfoSelect().get(0).getContraente01();
		it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo customerInfo = AnagraficaUtility.getContraente(polizzaResponse);
		int codCustomer = customerInfo.getCodiceCliente();
		cvaRequest.setValutazioneIntermediarioInfo(PolizzaUtility.impostaValutazioneIntermediario(body.getValutazioneIntermediario(), codCli, codCustomer, utente));
		PropostaDatiRiepilogoInfo pdrInfo = new PropostaDatiRiepilogoInfo();
		pdrInfo.setLivello1Info(livello1Info);
		pdrInfo.setProdottoTipoDettaglio(tipoProdottoResponse.getProdottiSelect().get(0));
		pdrInfo.getModPagamentoInfo().addAll(valorizzaModPagamentoList(body.getPolizzaInfo(), utente, body.getModPagamentoInfoList()));
		List<OrigineFondiInfo> listaOrigFondi = new ArrayList<OrigineFondiInfo>();
		for(it.sistinf.rest.cobol.model.proposta.common.OrigineFondiInfo origineFondo : body.getOrigineFondiInfoList()){
			OrigineFondiInfo origine = valorizzaOrigineFondo(body.getPolizzaInfo(), utente, origineFondo, ""+codCustomer);
			listaOrigFondi.add(origine);
		}
		pdrInfo.getOrigineFondiInfo().addAll(listaOrigFondi);
		pdrInfo.setAnagraficaContraente(polizzaResponse.getAnagraficaRapporto().getAnagraficaPrincipaleRapporto().get(0).getAnagraficaByPolizza());
		cvaRequest.setPropostaDatiRiepilogoInfo(pdrInfo);
		return cvaRequest;
	}

	public static List<ModPagamentoInfo> valorizzaModPagamentoList(PolizzaInfo chiaveRapporto, String username, List<it.sistinf.rest.cobol.model.proposta.common.ModPagamentoInfo> modPagamentoInput) throws Exception {
		List<ModPagamentoInfo> listaModPag = new ArrayList<ModPagamentoInfo>();
		for(it.sistinf.rest.cobol.model.proposta.common.ModPagamentoInfo modPagInput : modPagamentoInput) {
			ModPagamentoInfo modPag = new ModPagamentoInfo();
			modPag = valorizzaModPagamento(chiaveRapporto, username, modPagInput);
			listaModPag.add(modPag);
		}
		return listaModPag;
	}	
	
	public static PolizzaInfoSimple valorizzaPolizzaInfoSimple(PolizzaInfo polizzaInfo) {
		PolizzaInfoSimple polizzaInfoSimple = new PolizzaInfoSimple();
		polizzaInfoSimple.setCodSocieta(polizzaInfo.getCodSocieta());
		polizzaInfoSimple.setNumeroCategoria(polizzaInfo.getNumeroCategoria());
		polizzaInfoSimple.setCodAgenzia(polizzaInfo.getCodAgenzia());
		polizzaInfoSimple.setNumeroCollettiva(polizzaInfo.getNumeroCollettiva());
		polizzaInfoSimple.setNumeroPolizza(polizzaInfo.getNumeroPolizza());
		return polizzaInfoSimple;
	}	
	
	public static AnagraficaRapportoUpdateRequest valorizzaAnagraficaRapportoUpdateRequest(AnagraficaRapportoInfo anagraficaRapportoInfo) {
		AnagraficaRapportoUpdateRequest anagraficaRapportoUpdateRequest = new AnagraficaRapportoUpdateRequest();
		anagraficaRapportoUpdateRequest.setAnagraficaRapportoInfo(anagraficaRapportoInfo);
		return anagraficaRapportoUpdateRequest;
	}

	protected static AnagraficaRapportoInfo valorizzaAnagraficaRapportoInfo(PolizzaInfo polizzaInfo) {
		AnagraficaRapportoInfo anagraficaRapportoInfo = new AnagraficaRapportoInfo();
		anagraficaRapportoInfo.setPolizzaInfoSimple(valorizzaPolizzaInfoSimple(polizzaInfo));
		return anagraficaRapportoInfo;
	}

	public static SalvaPrenotazioneScadenzaResponse valorizzaViolation(SalvaPrenotazioneScadenzaRequest body, List<String> validatorViolations) {
		SalvaPrenotazioneScadenzaResponse response = new SalvaPrenotazioneScadenzaResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}
	
	public static InoltraPrenotazioneScadenzaResponse valorizzaViolation(InoltraPrenotazioneScadenzaRequest body, List<String> validatorViolations) {
		InoltraPrenotazioneScadenzaResponse response = new InoltraPrenotazioneScadenzaResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}
	
	public static CustomerWithoutPolizza valorizzaCustomerWithoutPolizza(AllCustomerInfo allCustomerInfoInput){
		CustomerWithoutPolizza customerWithoutPolizza = new CustomerWithoutPolizza();
		customerWithoutPolizza.setEasyClientCode(GenericUtils.stringToString(allCustomerInfoInput.getEasyClientCode()));
		customerWithoutPolizza.setFlForzaturaUSStatus(GenericUtils.booleanToStringOrNull(allCustomerInfoInput.isFlForzaturaUSStatus()));
		customerWithoutPolizza.setMotivoForzaturaUsStatus(GenericUtils.stringToString(allCustomerInfoInput.getMotivoForzaturaUsStatus()));
		customerWithoutPolizza.setStatoDiNascita(GenericUtils.stringToString(allCustomerInfoInput.getStatoNascita()));
		customerWithoutPolizza.setNumCivicoDiResidenza(GenericUtils.stringToString(allCustomerInfoInput.getNumCivico()));
		customerWithoutPolizza.setEmail(GenericUtils.stringToString(allCustomerInfoInput.getEmail()));
		customerWithoutPolizza.setEmailCertificata(GenericUtils.stringToString(allCustomerInfoInput.getEmailCertificata()));
		customerWithoutPolizza.setNumCellulare(GenericUtils.stringToString(allCustomerInfoInput.getNumCellulare()));
		customerWithoutPolizza.setNumCivicoRecapito(GenericUtils.stringToString(allCustomerInfoInput.getNumCivicoRep()));
//		customerWithoutPolizza.setNumTelefonoResidenza(GenericUtils.stringToString(allCustomerInfoInput.get()));
//		customerWithoutPolizza.setCodIstatComuneDiNascita(GenericUtils.stringToString(allCustomerInfoInput.getCodIstatComuneDiNascita()));
//		customerWithoutPolizza.setInputCodIstatComuneDiNascita(GenericUtils.stringToString(allCustomerInfoInput.getInputcodIstatComuneDiNascita()));
//		customerWithoutPolizza.setCodIstatComuneDiResidenza(GenericUtils.stringToString(allCustomerInfoInput.getCodIstatComuneDiResidenza()));
//		customerWithoutPolizza.setInputCodIstatComuneDiResidenza(GenericUtils.stringToString(allCustomerInfoInput.getInputcodIstatComuneDiResidenza()));
//		customerWithoutPolizza.setToponimoIndirizzoDiResidenza(GenericUtils.stringToString(allCustomerInfoInput.getToponimoIndirizzoDiResidenza()));
//		customerWithoutPolizza.setInputToponimoIndirizzoDiResidenza(GenericUtils.stringToString(allCustomerInfoInput.getInputtoponimoIndirizzoDiResidenza()));
//		customerWithoutPolizza.setIndirizzoDiResidenzaEsteso(GenericUtils.stringToString(allCustomerInfoInput.getInputindirizzoDiResidenzaEsteso()));
//		customerWithoutPolizza.setInputIndirizzoDiResidenzaEsteso(GenericUtils.stringToString(allCustomerInfoInput.getInputindirizzoDiResidenzaEsteso()));
//		customerWithoutPolizza.setCodIstatComuneRecapito(GenericUtils.stringToString(allCustomerInfoInput.getCodIstatComuneRecapito()));
//		customerWithoutPolizza.setInputCodIstatComuneRecapito(GenericUtils.stringToString(allCustomerInfoInput.getInputcodIstatComuneRecapito()));
//		customerWithoutPolizza.setToponimoIndirizzoRecapito(GenericUtils.stringToString(allCustomerInfoInput.getToponimoIndirizzoRecapito()));
//		customerWithoutPolizza.setInputToponimoIndirizzoRecapito(GenericUtils.stringToString(allCustomerInfoInput.getInputtoponimoIndirizzoRecapito()));
//		customerWithoutPolizza.setIndirizzoRecapitoEsteso(GenericUtils.stringToString(allCustomerInfoInput.getInputindirizzoRecapitoEsteso()));
//		customerWithoutPolizza.setInputIndirizzoRecapitoEsteso(GenericUtils.stringToString(allCustomerInfoInput.getInputindirizzoRecapitoEsteso()));
		customerWithoutPolizza.setCodEnteRilascioAR(GenericUtils.stringToString(allCustomerInfoInput.getCodEnteRilascioAR()));
//		customerWithoutPolizza.setCodIstatComuneRilascioDocumentoAR(GenericUtils.stringToString(allCustomerInfoInput.getCodIstatComuneRilascioDocumentoAR()));
//		customerWithoutPolizza.setInputCodIstatComuneRilascioDocumentoAR(GenericUtils.stringToString(allCustomerInfoInput.getInputcodIstatComuneRilascioDocumentoAR()));
//		customerWithoutPolizza.setCodIstatComuneAttivitaPrevalente(GenericUtils.stringToString(allCustomerInfoInput.getCodIstatComuneAttivitaPrevalente()));
//		customerWithoutPolizza.setInputCodIstatComuneAttivitaPrevalente(GenericUtils.stringToString(allCustomerInfoInput.getInputcodIstatComuneAttivitaPrevalente()));
		
		return customerWithoutPolizza;
	}
	
	public static <T extends EsitoCobolResponse> T valorizzaEsitoCobolResponse(Class<T> responseClass, InteractionDTO interactionDTO, AreaErroreDTO areaErroreDTO, boolean esito) throws Exception{
		T response = responseClass.getDeclaredConstructor().newInstance();
		response.setHeaderCobolSrv(InteractionUtility.valorizzaHeaderCobolSrv(interactionDTO));
		response.setErrori(InteractionUtility.valorizzaErrori(areaErroreDTO));
		response.setEsito(esito);
		return response;
	}

	public static AggiornaAnagrafeResponse valorizzaViolation(AggiornaAnagrafeRequest body, List<String> validatorViolations) {
		AggiornaAnagrafeResponse response = new AggiornaAnagrafeResponse();
		response.setErrori(InteractionUtility.valorizzaErrori(validatorViolations));
		return response;
	}

	public static AnagraficaByPolizza valorizzaAnagraficaByPolizzaForAnagraficaRapportoUpdate(DatiAnagraficaByPolizza anagraficaByPolizzaInput, it.sistinf.albedoweb.services.common.anagrafe.types.AllCustomerInfo allCustomerInfo, int codSoc) throws Exception {
		AnagraficaByPolizza anagraficaByPolizza = PropostaUtility.valorizzaAnagraficaByPolizza(anagraficaByPolizzaInput, allCustomerInfo, codSoc);
		DatiGeneraliInfo dgi = anagraficaByPolizza.getDatiGenerali();
		List<FigureAnagraficheRelazionateInfo> listaFigureAnagraficheRelazionate = allCustomerInfo.getListaFigureAnagraficheRelazionate();
		if((listaFigureAnagraficheRelazionate != null) && (!listaFigureAnagraficheRelazionate.isEmpty())){
			FigureAnagraficheRelazionateInfo figAnagRel = listaFigureAnagraficheRelazionate.get(0);
			dgi.setCodiceClienteFigPrincipale(""+figAnagRel.getCodiceClientePrincipale());
			dgi.setTipoRelazioneFigPrincipale(figAnagRel.getRuoloPrincipale());
		}
		return anagraficaByPolizza;
	}
	
	/*public static void valorizza200ListAndRA2List(it.sistinf.rest.cobol.model.portafoglio.polizza.PolizzaInfo polizzaInfo, AnagraficaRapportoUpdateRequest request, AllCustomerInfo allCustomerInfoRest, List<AnagraficaRapportoCustomerInfo> anagrafichePrincipaliRapporto, String ruoloFiguraInput) {
		List<CodeDescription> map200 = new ArrayList<CodeDescription>();
		List<CodeDescription> mapRA2 = new ArrayList<CodeDescription>();
		String numPolizza = ""+polizzaInfo.getNumeroPolizza();
		String numCategoria = GenericUtils.stringToString(polizzaInfo.getNumeroCategoria());
		String ruolo = null;
		if((allCustomerInfoRest.getListaFigureAnagraficheRelazionate() != null) && (!allCustomerInfoRest.getListaFigureAnagraficheRelazionate().isEmpty())){
			// entro qui solo se sono LEGALE RAPP o TITOLARE
			FiguraAnagraficaRelazionataInfo figRel = allCustomerInfoRest.getListaFigureAnagraficheRelazionate().get(0);
			ruolo = figRel.getTipoRelazione() != null ? figRel.getTipoRelazione().name() : "";
			CodeDescription occorrenza = new CodeDescription();
			occorrenza.setCode("0");
			occorrenza.setDescription(ruolo);
			if(GenericUtils.isAnagraficaRa2(numPolizza, numCategoria, ruolo)){
				mapRA2.add(occorrenza);
			} else {
				map200.add(occorrenza);
			}
		} else {
			for(AnagraficaRapportoCustomerInfo anagraficaRapporto : anagrafichePrincipaliRapporto){
				AnagraficaByPolizza anagraficaByPolizza = anagraficaRapporto.getAnagraficaByPolizza();
				if((anagraficaByPolizza.getDatiGenerali().getCodiceCliente() > 0) && (ruoloFiguraInput.equals(anagraficaByPolizza.getDatiAltro().getTipoRelaz()))) {
					CodeDescription occorrenza = new CodeDescription();
					occorrenza.setCode(anagraficaByPolizza.getDatiGenerali().getCodCli().toString());
					occorrenza.setDescription(anagraficaByPolizza.getDatiAltro().getTipoRelaz());
					ruolo = GenericUtils.stringToString(anagraficaByPolizza.getDatiAltro().getTipoRelaz());
					if(GenericUtils.isAnagraficaRa2(numPolizza, numCategoria, ruolo)){
						mapRA2.add(occorrenza);
					} else {
						map200.add(occorrenza);
					}
				}
			}
		}
		request.getAnagrafiche200List().addAll(map200);
		request.getAnagraficheRA2List().addAll(mapRA2);
	}*/
	
	public static void valorizza200ListAndRA2List(PolizzaInfo polizzaInfo, AnagraficaRapportoUpdateRequest request, AllCustomerInfo allCustomerInfoRest, List<AnagraficaRapportoCustomerInfo> anagrafichePrincipaliRapporto, int codiceClienteFiguraInput) {
	    List<CodeDescription> lista200 = new ArrayList<CodeDescription>();
	    List<CodeDescription> listaRA2 = new ArrayList<CodeDescription>();
	    String numeroPolizza = String.valueOf(polizzaInfo.getNumeroPolizza());
	    String numeroCategoria = GenericUtils.stringToString(polizzaInfo.getNumeroCategoria());
	    String ruolo = GenericUtils.stringToString(request.getAnagraficaRapportoCustomerInfo().getAnagraficaByPolizza().getDatiAltro().getTipoRelaz());
	    List<FiguraAnagraficaRelazionataInfo> figureRelazionate = allCustomerInfoRest.getListaFigureAnagraficheRelazionate();
	    if((figureRelazionate != null && !figureRelazionate.isEmpty()) && (SrvConstants.RUOLO_TITOLARE_EFFETTIVO.equals(ruolo) || SrvConstants.RUOLO_RAPPRESENTANTE_LEGALE.equals(ruolo))){
	    	// SOLO PER Legale Rappresentante o Titolare. ldt in fase di inserimento passa la figura relazionata e nela lista la relazione che ha con il padre giuridico
	    	FiguraAnagraficaRelazionataInfo figura = figureRelazionate.get(0);
	        ruolo = figura.getTipoRelazione() != null ? figura.getTipoRelazione().name() : "";
	        CodeDescription occorrenza = new CodeDescription();
	        occorrenza.setCode("0");
	        occorrenza.setDescription(ruolo);
	        if(GenericUtils.isAnagraficaRa2(numeroPolizza, numeroCategoria, ruolo)){
	            listaRA2.add(occorrenza);
	        } else {
	            lista200.add(occorrenza);
	        }
	    } else {
	        for(AnagraficaRapportoCustomerInfo anagraficaRapporto : anagrafichePrincipaliRapporto){
	            AnagraficaByPolizza anagraficaByPolizza = anagraficaRapporto.getAnagraficaByPolizza();
	            DatiGeneraliInfo datiGenerali = anagraficaByPolizza.getDatiGenerali();
	            AltreInfo datiAltro = anagraficaByPolizza.getDatiAltro();
	            if(datiGenerali.getCodiceCliente() == codiceClienteFiguraInput){
	                ruolo = GenericUtils.stringToString(datiAltro.getTipoRelaz());
	                CodeDescription occorrenza = new CodeDescription();
	                occorrenza.setCode(String.valueOf(datiGenerali.getCodCli()));
	                occorrenza.setDescription(ruolo);
	                if(GenericUtils.isAnagraficaRa2(numeroPolizza, numeroCategoria, ruolo)){
	                    listaRA2.add(occorrenza);
	                } else {
	                    lista200.add(occorrenza);
	                }
	            }
	        }
	    }
	    request.getAnagrafiche200List().addAll(lista200);
	    request.getAnagraficheRA2List().addAll(listaRA2);
	}

	public static String getTipologiaProdotto(SelectTipoProdottoDettaglioGenericoResponse selectTipoProdottoDettaglioGenericoResponse) {
		String tipologiaProdotto = null;
		Map<String, Boolean> mapTipoProdotto = PolizzaUtility.getMappa(selectTipoProdottoDettaglioGenericoResponse);
		boolean isTranching = mapTipoProdotto.get(SrvConstants.IS_TRANCHING);
		boolean isViPensiono = mapTipoProdotto.get(SrvConstants.IS_VIPENSIONO);
		if(isTranching) {
			tipologiaProdotto = SrvConstants.TRANCHING_MINUSCOLO;
		} else if(isViPensiono) {
			tipologiaProdotto = SrvConstants.VI_PENSIONO;
		} else {
			tipologiaProdotto = SrvConstants.ALTRO;
		}
		return tipologiaProdotto;
	}	
}