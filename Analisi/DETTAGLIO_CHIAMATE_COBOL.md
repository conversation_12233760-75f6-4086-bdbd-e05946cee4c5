# DETTAGLIO COMPLETO DELLE 189 CHIAMATE COBOL

## Panoramica

Le **189 chiamate COBOL** identificate nel sistema NAW sono tutte **chiamate indirette** che utilizzano l'architettura **JCA (Java Connector Architecture)** per comunicare con sistemi mainframe COBOL. Non ci sono chiamate dirette a programmi COBOL.

## Distribuzione per Tecnologia

### 1. **javax.resource.cci.*** (104 occorrenze)
Interfacce Java Connector Architecture per comunicazione con sistemi legacy:
- `javax.resource.cci.Record` - Interfaccia per record di dati
- `javax.resource.cci.Streamable` - Interfaccia per streaming di dati
- `javax.resource.cci.RecordBytes` - Gestione byte array

### 2. **com.ibm.etools.marshall.*** (84 occorrenze)
Strumenti IBM per marshalling/unmarshalling dati verso COBOL:
- `com.ibm.etools.marshall.util.*` - Utility di conversione
- `com.ibm.etools.marshall.RecordBytes` - Gestione record binari
- Conversione tipi Java ↔ COBOL

### 3. **import javax.resource*** (1 occorrenza)
Import diretto delle librerie JCA

## Distribuzione per Area Funzionale

### 🏦 **POLIZZE (78 chiamate - 41.3%)**

#### Switch Polizze (9 chiamate)
- `Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java`
- `RichiestaSwitch.java`
- `Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__da.java`

#### Liquidazioni (30 chiamate)
- `Vttab074Recesso.java` - Gestione recessi
- `Vttab074Sinistro.java` - Gestione sinistri
- `Vttab074Cedola.java` - Gestione cedole
- `Vttab074Prestiti.java` - Gestione prestiti
- `Vttab074Rendita.java` - Gestione rendite
- `Vttab074Scadenza.java` - Gestione scadenze
- `Vttab074Riscatto.java` - Gestione riscatti

#### Storni (12 chiamate)
- `RichiestaPac.java` - Richieste PAC
- `RichiestaRidir.java` - Richieste ridirezione
- `RichiestaRecesso.java` - Richieste recesso

#### Gestione Polizze (15 chiamate)
- `IO022RECORD.java` - Record I/O polizze
- `DBSFLCDATISWITCH.java` - Dati switch
- `DATI027.java` - Dati posizione
- `VTTAB024.java` - Tabella posizioni

#### Switch Service (12 chiamate)
- `Dbsflcdatiswitch_dbsflc__switch__a.java`
- `Dbsflcdatiswitch_dbsflc__switch__da.java`
- `DBSFLCDATISWITCH.java`

### 👥 **ANAGRAFICA (24 chiamate - 12.7%)**

#### Refresh Anagrafica (15 chiamate)
- `RichiestaNewcol.java` - Nuove collezioni
- `RichiestaAnacus.java` - Anagrafica customer
- `RichiestaRefana.java` - Refresh anagrafica
- `RichiestaNewbusVeraggAumrat.java` - New business/aumenti
- `Richiestanewbusveraggaumrat_l2_l2__dati__trasformazione_l2__polizza__trasformata.java`
- `Richiestavtcdbass_l2_l2__dati__trasformazione_l2__polizza__trasformata.java`

#### Variazioni Anagrafica (3 chiamate)
- `RichiestaVarana.java` - Variazioni anagrafica

### 📊 **TABELLE DI SISTEMA (51 chiamate - 27.0%)**

#### Prodotti (6 chiamate)
- `ProdottoDatiComuniJ2C.java` - Dati comuni prodotti

#### Date Investimenti/Disinvestimenti (12 chiamate)
- `DateInvestDisinvestJ2C.java` - Date operazioni
- `SceltaModalitaPagamento.java` - Modalità pagamento
- `ProcessDateInvDisinvJ2C.java` - Processo date

#### Calendario (6 chiamate)
- `CalendarioRibilJ2C.java` - Calendario ribilanciamenti

#### Raggruppamenti Unit Link (12 chiamate)
- `RaggruppamentoUnitLinkJ2C_KO.java`
- `RaggruppamentoUnitLinkFiller22J2C_KO.java`
- `RaggruppamentoUnitLinkFiller23J2C_KO.java`
- `SceltaModalitaPagamentoJ2C_KO.java`

#### Tabelle VT190 (15 chiamate)
- `Vttab190prodotto130_dati__prodotto_vt190__premio__min__base.java`
- `Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__oneri.java`
- `Vttab190parametrigenerali999_dati__generali_vt190__irpef_vt190__scaglioni.java`
- `OpzioneGestionale.java`
- `Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__fondo__multiplo.java`
- `Vttab190Calendario420.java`
- `Vttab190Prodotto130.java`
- `Vttab190ParametriGenerali999.java`
- `Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto.java`
- `Vttab190calendario420_tabella__nav_dati__calendario.java`
- `Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__proventi.java`
- `Vttab190FondiRivalutazione030.java`

### 💼 **PRODOTTI (9 chiamate - 4.8%)**

#### Formule (6 chiamate)
- `FormulaInfo_Rec2.java` - Record formula 2
- `FormulaInfo_Rec1.java` - Record formula 1

#### Proposte (3 chiamate)
- `DATI017DBV.java` - Dati proposta
- `PropostaPropostaHelper.java` - Helper proposta
- `Dati017dbv_dati017__dbv_vt17__dati__unit__linked_vt17__fondi__ul.java`

### 📈 **GESTIONE EVENTI (21 chiamate - 11.1%)**

#### Eventi Assicurativi (15 chiamate)
- `EventiTassazionePip.java` - Tassazione PIP
- `EventiOpzContrAll.java` - Opzioni contrattuali ALL
- `EventiAddebitoZurich.java` - Addebiti Zurich
- `EstrattoConto.java` - Estratti conto
- `EventiStampeCollettive.java` - Stampe collettive
- `EventiVaProgrammati.java` - VA programmati
- `EventiOpzContrArpp.java` - Opzioni contrattuali ARPP

#### Dashboard Eventi (6 chiamate)
- `EventiTassazionePip.java` - Tassazione PIP (dashboard)
- `EstrattoConto.java` - Estratti conto (dashboard)

### 🔧 **UTILITY (6 chiamate - 3.2%)**

#### Helper (1 chiamata)
- `RichiestaHelper.java` - Helper richieste (import javax.resource.cci.Streamable)

## Pattern di Implementazione

### Struttura Standard delle Classi J2C

Tutte le classi seguono questo pattern:

```java
public class NomeClasse implements 
    javax.resource.cci.Record,
    javax.resource.cci.Streamable, 
    com.ibm.etools.marshall.RecordBytes {
    
    // Buffer per dati binari
    private byte[] buffer_ = null;
    
    // Metodi di serializzazione/deserializzazione
    public void read(java.io.InputStream inputStream) throws java.io.IOException
    public void write(java.io.OutputStream outputStream) throws java.io.IOException
    public byte[] getBytes()
    public void setBytes(byte[] bytes)
}
```

### Tecnologie di Marshalling

Le classi utilizzano utility IBM per conversione dati:
- **MarshallStringUtils** - Conversione stringhe
- **MarshallIntegerUtils** - Conversione interi
- **MarshallPackedDecimalUtils** - Conversione decimali packed
- **ConversionUtils** - Utility generiche
- **MarshallResource** - Gestione risorse

## Conclusioni

### Tipo di Integrazione
- **100% JCA-based**: Tutte le chiamate utilizzano Java Connector Architecture
- **0% chiamate dirette**: Nessuna chiamata diretta a programmi COBOL
- **Standard enterprise**: Architettura conforme agli standard Java EE

### Aree di Business Coinvolte
1. **Polizze** (41.3%) - Core business assicurativo
2. **Tabelle Sistema** (27.0%) - Configurazioni e parametri
3. **Anagrafica** (12.7%) - Gestione clienti
4. **Eventi** (11.1%) - Gestione eventi assicurativi
5. **Prodotti** (4.8%) - Definizione prodotti
6. **Utility** (3.2%) - Supporto tecnico

### Sistemi Mainframe Coinvolti
Le classi suggeriscono integrazione con:
- **Sistema polizze** (VT074, DBSFLC)
- **Sistema anagrafico** (ANACUS, REFANA, VARANA)
- **Sistema prodotti** (VT017, VT190)
- **Sistema tabelle** (VT190, VT024)
- **Sistema eventi** (gestione eventi assicurativi)

### Architettura di Comunicazione
```
Java Application Layer
        ↓
JCA Connector Layer (javax.resource.cci.*)
        ↓
IBM Marshalling Layer (com.ibm.etools.marshall.*)
        ↓
Mainframe COBOL Programs
```

Questa architettura garantisce:
- **Separazione delle responsabilità**
- **Gestione transazionale**
- **Conversione automatica dei tipi**
- **Gestione degli errori standardizzata**
- **Scalabilità enterprise**
