#!/usr/bin/env python3
"""
extract_method_calls.py

Estrae specificamente le chiamate ai metodi dei servizi EJB e Web Services.
Analizza i file Java per identificare tutte le chiamate ai metodi dei servizi.

Output:
  - **File CSV**: `method_calls.csv` con dettagli delle chiamate ai metodi
  - **File di testo**: `method_summary.txt` con riassunto delle chiamate

Prerequisiti:
  - Python 3.6+
"""
import os
import re
import csv
from collections import defaultdict

# Configurazione
SRC_PATH = "/Users/<USER>/Desktop/Conding/NAW/Source"
OUTPUT_DIR = "/Users/<USER>/Desktop/Conding/NAW/Analisi"
OUTPUT_CSV = os.path.join(OUTPUT_DIR, 'method_calls.csv')
OUTPUT_SUMMARY = os.path.join(OUTPUT_DIR, 'method_summary.txt')

# Pattern per identificare chiamate ai metodi
METHOD_CALL_PATTERNS = [
    # Chiamate dirette ai servizi (service.method())
    r'(\w+)\.(\w+)\s*\([^)]*\)\s*;',
    # Chiamate con assegnazione (result = service.method())
    r'(\w+)\s*=\s*(\w+)\.(\w+)\s*\([^)]*\)',
    # Chiamate in try-catch
    r'(\w+)\s*=\s*(\w+)\.(\w+)\s*\([^)]*\)',
]

# Nomi di servizi comuni (basati sui proxy trovati)
KNOWN_SERVICES = {
    'customer', 'polizza', 'portafoglio', 'prodotto', 'domini', 'anagrafica',
    'abilitazioni', 'rete', 'proposta', 'workflow', 'liquidazione', 'gestione',
    'prenotazione', 'fondo', 'modello', 'deroghe', 'switch', 'trasferimento',
    'pepcrimeservice', 'oasiws', 'easyws', 'syncrows', 'cheopews', 'codemqws'
}

# File estensioni da scansionare
SCAN_EXT = ('.java',)


def extract_method_calls(src_path):
    """
    Estrae tutte le chiamate ai metodi dei servizi.
    Restituisce lista di tuple (file, linea, service, method, full_call, context).
    """
    results = []
    
    for root, _, files in os.walk(src_path):
        for fname in files:
            if not fname.lower().endswith(SCAN_EXT):
                continue
            
            path = os.path.join(root, fname)
            try:
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                for lineno, line in enumerate(lines, 1):
                    line_stripped = line.strip()
                    if not line_stripped or line_stripped.startswith('//') or line_stripped.startswith('*'):
                        continue
                    
                    # Cerca pattern di chiamate ai metodi
                    for pattern in METHOD_CALL_PATTERNS:
                        matches = re.finditer(pattern, line_stripped, re.IGNORECASE)
                        for match in matches:
                            groups = match.groups()
                            
                            # Determina service e method in base al numero di gruppi
                            if len(groups) == 2:  # service.method()
                                service, method = groups
                                full_call = match.group(0)
                            elif len(groups) == 3:  # result = service.method() o service.method()
                                if '=' in match.group(0):
                                    _, service, method = groups
                                else:
                                    service, method = groups[1], groups[2]
                                full_call = match.group(0)
                            else:
                                continue
                            
                            # Filtra solo se sembra essere un servizio
                            if (service.lower() in KNOWN_SERVICES or 
                                service.lower().endswith('proxy') or
                                service.lower().endswith('service') or
                                service.lower().endswith('impl') or
                                service.lower().endswith('interface')):
                                
                                # Estrai contesto
                                context_lines = []
                                start_idx = max(0, lineno - 2)
                                end_idx = min(len(lines), lineno + 1)
                                
                                for i in range(start_idx, end_idx):
                                    prefix = ">>> " if i == lineno - 1 else "    "
                                    context_lines.append(f"{prefix}{i+1:4d}: {lines[i].rstrip()}")
                                
                                context = "\n".join(context_lines)
                                
                                results.append({
                                    'file': path,
                                    'line': lineno,
                                    'service': service,
                                    'method': method,
                                    'full_call': full_call,
                                    'context': context,
                                    'line_content': line_stripped
                                })
                                
            except Exception as e:
                print(f"Errore leggendo {path}: {e}")
                continue
    
    return results


def write_method_csv(results, output_file):
    """Scrive i risultati in formato CSV."""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Service', 'Method', 'File_Path', 'Line_Number', 'Full_Call', 'Line_Content', 'Context'])
        
        for call in results:
            relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
            writer.writerow([
                call['service'],
                call['method'],
                relative_path,
                call['line'],
                call['full_call'],
                call['line_content'],
                call['context'].replace('\n', ' | ')
            ])


def write_method_summary(results, output_file):
    """Scrive un riassunto delle chiamate ai metodi."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("RIASSUNTO CHIAMATE AI METODI DEI SERVIZI\n")
        f.write("=" * 60 + "\n\n")
        
        # Raggruppa per servizio
        service_methods = defaultdict(lambda: defaultdict(list))
        
        for call in results:
            service_methods[call['service']][call['method']].append(call)
        
        f.write(f"Totale servizi chiamati: {len(service_methods)}\n")
        f.write(f"Totale chiamate ai metodi: {len(results)}\n\n")
        
        # Scrivi dettagli per ogni servizio
        for service, methods in sorted(service_methods.items()):
            total_calls = sum(len(calls) for calls in methods.values())
            f.write(f"\n{'='*60}\n")
            f.write(f"SERVIZIO: {service} ({total_calls} chiamate totali)\n")
            f.write(f"{'='*60}\n")
            
            for method, calls in sorted(methods.items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"\n  {method}() - {len(calls)} chiamate\n")
                f.write(f"  {'-' * (len(method) + 15)}\n")
                
                # Mostra alcuni esempi
                for i, call in enumerate(calls[:3]):
                    relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
                    f.write(f"    {relative_path}:{call['line']}\n")
                    f.write(f"    -> {call['full_call']}\n\n")
                
                if len(calls) > 3:
                    f.write(f"    ... e altri {len(calls) - 3} esempi\n\n")
        
        # Statistiche generali
        f.write(f"\n{'='*60}\n")
        f.write("STATISTICHE GENERALI\n")
        f.write(f"{'='*60}\n")
        
        # Top servizi per numero di chiamate
        service_call_counts = defaultdict(int)
        for call in results:
            service_call_counts[call['service']] += 1
        
        f.write("\nTop 10 servizi più chiamati:\n")
        for service, count in sorted(service_call_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            f.write(f"  {service}: {count} chiamate\n")
        
        # Top metodi per numero di chiamate
        method_call_counts = defaultdict(int)
        for call in results:
            method_call_counts[call['method']] += 1
        
        f.write("\nTop 10 metodi più chiamati:\n")
        for method, count in sorted(method_call_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            f.write(f"  {method}(): {count} chiamate\n")
        
        # Distribuzione per file
        file_call_counts = defaultdict(int)
        for call in results:
            relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
            file_call_counts[relative_path] += 1
        
        f.write("\nTop 10 file con più chiamate ai servizi:\n")
        for file_path, count in sorted(file_call_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            f.write(f"  {file_path}: {count} chiamate\n")


def main():
    # Verifica directories
    if not os.path.isdir(SRC_PATH):
        print(f"Errore: '{SRC_PATH}' non è una directory valida.")
        return
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("Estrazione chiamate ai metodi dei servizi in corso...")
    print(f"Directory sorgenti: {SRC_PATH}")
    print(f"Directory output: {OUTPUT_DIR}")
    print()

    # Estrazione
    results = extract_method_calls(SRC_PATH)
    
    if not results:
        print("Nessuna chiamata ai metodi rilevata.")
        return

    # Calcola totali
    unique_services = len(set(call['service'] for call in results))
    unique_methods = len(set(call['method'] for call in results))
    
    print(f"Trovate {len(results)} chiamate ai metodi")
    print(f"Servizi unici chiamati: {unique_services}")
    print(f"Metodi unici chiamati: {unique_methods}")
    print()
    
    # Scrittura file CSV
    write_method_csv(results, OUTPUT_CSV)
    print(f"Dettagli salvati in: {OUTPUT_CSV}")
    
    # Scrittura riassunto
    write_method_summary(results, OUTPUT_SUMMARY)
    print(f"Riassunto salvato in: {OUTPUT_SUMMARY}")
    
    print("\nEstrazione completata!")


if __name__ == '__main__':
    main()
