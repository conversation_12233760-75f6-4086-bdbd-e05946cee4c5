MAPPATURA SERVIZI E CHIAMATE ESTERNE
============================================================

Totale servizi unici trovati: 444


============================================================
SERVIZIO: ${BEYONDOC.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:162
  Linea: <property name="endpointAddress" value="${BEYONDOC.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${Cheope.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:109
  Linea: <property name="endpointAddress" value="${Cheope.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${CodeMq.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:124
  Linea: <property name="endpointAddress" value="${CodeMq.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${DataAnonymize.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:143
  Linea: <property name="endpointAddress" value="${DataAnonymize.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${Easy.endpoint}
============================================================

REST_ENDPOINT (2 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:48
  Linea: <property name="endpointAddress" value="${Easy.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:59
  Linea: <property name="endpointAddress" value="${Easy.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${MovOasi.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:78
  Linea: <property name="endpointAddress" value="${MovOasi.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${Oasi.endpoint}
============================================================

REST_ENDPOINT (2 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:14
  Linea: <property name="endpointAddress" value="${Oasi.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:26
  Linea: <property name="endpointAddress" value="${Oasi.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${Syncro.endpoint}
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:89
  Linea: <property name="endpointAddress" value="${Syncro.endpoint}"/>
  Pattern: endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${applicationDatasource}
============================================================

DATABASE_CONFIG (2 occorrenze):
----------------------------------------
  File: Services.Common.Persistency/src/spring-conf/spring-ibatis.xml:13
  Linea: <property name="dataSource" ref="${applicationDatasource}" />
  Pattern: dataSource["\']?\s*ref\s*=\s*["\']([^"\']+)["\']

  File: Services.Common.Impl/src/spring-conf/PolizzaTrasferimentoDao.xml:16
  Linea: <property name="dataSource" ref="${applicationDatasource}" />
  Pattern: dataSource["\']?\s*ref\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${jndiDatasourceName}
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Persistency/src/spring-conf/datasource.xml:10
  Linea: <property name="jndiName" value="${jndiDatasourceName}" />
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}AbilitazioneAzione
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:27
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}AbilitazioneAzione"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}AbilitazioneUtente
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:21
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}AbilitazioneUtente"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}Aegis
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:241
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}Aegis"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}AnagraficaAnagrafica
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:57
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}AnagraficaAnagrafica"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}CoeffCostoMgmFee
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:63
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}CoeffCostoMgmFee"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}Customer
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:45
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}Customer"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}CustomerAccessori
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:69
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}CustomerAccessori"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}DerogheService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:111
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}DerogheService"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}Domini
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:51
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}Domini"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}FondoTrasferimento
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:129
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}FondoTrasferimento"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:171
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}LocalitaStradario
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:81
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}LocalitaStradario"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}ModelloMatematicoFormula
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:99
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}ModelloMatematicoFormula"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PianiSpostamento
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:93
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PianiSpostamento"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PolizzaGestioneRid
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:123
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PolizzaGestioneRid"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:183
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PolizzaPolizza
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:147
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PolizzaPolizza"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PolizzaPosizione
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:153
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PolizzaPosizione"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PolizzaStorni
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:177
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PolizzaStorni"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PortafoglioRapporto
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:9
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PortafoglioRapporto"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PrenotazionePostVendita
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:165
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PrenotazionePostVendita"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}Print
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:247
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}Print"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}ProdottoProdotto
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:39
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}ProdottoProdotto"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}ProdottoTariffa
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:87
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}ProdottoTariffa"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}PropostaProposta
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:15
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PropostaProposta"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}QuestionarioAura
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:117
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}QuestionarioAura"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}QuestionarioStruttura
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:135
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}QuestionarioStruttura"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}StrutturaRete
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:33
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}StrutturaRete"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}SyncroPolicy
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:159
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}SyncroPolicy"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}TabellaTracciato
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:105
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}TabellaTracciato"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}WorkFlowMgr
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:75
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}WorkFlowMgr"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ${prefixJNDINameEJBProxy}ZbRete
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:141
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}ZbRete"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /anagrafe/aggiorna
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:1002
  Linea: @Path("/anagrafe/aggiorna")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /anagrafica/salvaSoggettoTerzo
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:451
  Linea: @Path("/anagrafica/salvaSoggettoTerzo")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /continuousMonitoring
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:174
  Linea: @Path("/continuousMonitoring")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /continuousMonitoringLiquidazioni
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:193
  Linea: @Path("/continuousMonitoringLiquidazioni")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /controllaOpzContrattuali
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:250
  Linea: @Path("/controllaOpzContrattuali")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /disimpegna
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:412
  Linea: @Path("/disimpegna")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /funzioniPolizza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:136
  Linea: @Path("/funzioniPolizza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /grigliaCompatibilita
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:155
  Linea: @Path("/grigliaCompatibilita")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /inizializza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:288
  Linea: @Path("/inizializza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /inizializzaProposta
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:330
  Linea: @Path("/inizializzaProposta")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /inizializzaVAEmesso
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:351
  Linea: @Path("/inizializzaVAEmesso")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /opzContrattuali/inoltra
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:231
  Linea: @Path("/opzContrattuali/inoltra")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /opzContrattuali/salva
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:212
  Linea: @Path("/opzContrattuali/salva")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /opzioniContrattuali
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:269
  Linea: @Path("/opzioniContrattuali")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /percipienti/elenco
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:707
  Linea: @Path("/percipienti/elenco")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /percipienti/inserisci
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:730
  Linea: @Path("/percipienti/inserisci")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /polizza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:112
  Linea: @Path("/polizza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /recesso/controlla
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:772
  Linea: @Path("/recesso/controlla")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /recesso/controllaPerInserimento
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:880
  Linea: @Path("/recesso/controllaPerInserimento")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /recesso/inizializza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:753
  Linea: @Path("/recesso/inizializza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /recesso/inoltra
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:983
  Linea: @Path("/recesso/inoltra")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /recesso/salva
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:918
  Linea: @Path("/recesso/salva")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /riscatto/calcola
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:577
  Linea: @Path("/riscatto/calcola")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /riscatto/controlla
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:490
  Linea: @Path("/riscatto/controlla")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /riscatto/inizializza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:532
  Linea: @Path("/riscatto/inizializza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /riscatto/inoltra
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:899
  Linea: @Path("/riscatto/inoltra")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /riscatto/salva
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:835
  Linea: @Path("/riscatto/salva")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /scadenza/controlla
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:813
  Linea: @Path("/scadenza/controlla")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /scadenza/inizializza
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:791
  Linea: @Path("/scadenza/inizializza")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /scadenza/inoltra
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:1041
  Linea: @Path("/scadenza/inoltra")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /scadenza/salva
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:1021
  Linea: @Path("/scadenza/salva")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /storicoOpzioniContrattuali
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:311
  Linea: @Path("/storicoOpzioniContrattuali")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /vaEmesso/controlla
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:661
  Linea: @Path("/vaEmesso/controlla")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /vaEmesso/controllaPerInserimento
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:937
  Linea: @Path("/vaEmesso/controllaPerInserimento")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /vaEmesso/inoltra
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:470
  Linea: @Path("/vaEmesso/inoltra")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: /vaEmesso/salva
============================================================

REST_ENDPOINT (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:431
  Linea: @Path("/vaEmesso/salva")
  Pattern: @Path\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneAzione
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:26
  Linea: <bean id="AbilitazioneAzioneProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: AbilitazioneAzioneBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3141
  Linea: return (AbilitazioneAzioneInterface) FactoryBean.getBean("AbilitazioneAzioneBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneAzioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/abilitazione/azione/service/AbilitazioneAzioneInterfaceBindingImpl.java:48
  Linea: serviceImpl = (AbilitazioneAzioneInterface) FactoryBean.getBean("AbilitazioneAzioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneAzioneProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/abilitazione/Abilitazioni.java:63
  Linea: abilitazioneAzione = (AbilitazioneAzioneInterface) FactoryBean.getBean("AbilitazioneAzioneProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneUtente
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:20
  Linea: <bean id="AbilitazioneUtenteProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: AbilitazioneUtenteBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java:65
  Linea: abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean("AbilitazioneUtenteBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneUtenteImpl
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java:67
  Linea: abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean("AbilitazioneUtenteImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/abilitazione/utente/service/AbilitazioneUtenteInterfaceBindingImpl.java:64
  Linea: serviceImpl = (AbilitazioneUtenteInterface) FactoryBean.getBean("AbilitazioneUtenteImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AbilitazioneUtenteProxy
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/abilitazione/Abilitazioni.java:62
  Linea: abilitazioneUtente = (AbilitazioneUtenteInterface) FactoryBean.getBean("AbilitazioneUtenteProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:395
  Linea: utente = (AbilitazioneUtenteInterface) FactoryBean.getBean("AbilitazioneUtenteProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Abilitazioni
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:14
  Linea: <bean id="Abilitazioni" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (4 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:402
  Linea: abilitazioni = (AbilitazioniInterface) FactoryBean.getBean("Abilitazioni");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:119
  Linea: abilitazioni = (AbilitazioniInterface) FactoryBean.getBean("Abilitazioni");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:76
  Linea: abilitazioni = (AbilitazioniInterface) FactoryBean.getBean("Abilitazioni");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 1 esempi


============================================================
SERVIZIO: Aegis
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:240
  Linea: <bean id="AegisProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: AegisImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/aegis/service/AegisInterfaceBindingImpl.java:29
  Linea: serviceImpl = (AegisInterface) FactoryBean.getBean("AegisImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AegisProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.Impl/src/com/zurich/it/oil/externalservices/datadisposal/impl/DataDisposalImpl.java:52
  Linea: aegisImpl = (AegisInterface) FactoryBean.getBean("AegisProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagrafeEasyBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java:2042
  Linea: return (AnagrafeEasyInterface) FactoryBean.getBean("AnagrafeEasyBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagrafeEasyImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/easy/anagrafe/service/AnagrafeEasyInterfaceBindingImpl.java:44
  Linea: serviceImpl = (AnagrafeEasyInterface) FactoryBean.getBean("AnagrafeEasyImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagrafeEasyWSProxy
============================================================

WEB_SERVICE_DEFINITION (2 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:44
  Linea: <bean id="AnagrafeEasyWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"

  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:55
  Linea: <bean id="AnagrafeEasyWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: Anagrafica
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:34
  Linea: <bean id="Anagrafica" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:406
  Linea: anagraficaRest = (AnagraficaInterface) FactoryBean.getBean("Anagrafica");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagraficaAnagrafica
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:56
  Linea: <bean id="AnagraficaAnagraficaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: AnagraficaAnagraficaBusinessDelegate
============================================================

EJB_PROXY_USAGE (5 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java:22
  Linea: return (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java:346
  Linea: AnagraficaAnagraficaInterface anagraficaImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4001
  Linea: return (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 2 esempi


============================================================
SERVIZIO: AnagraficaAnagraficaImpl
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java:24
  Linea: return (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/AnagraficaAnagraficaInterfaceBindingImpl.java:26
  Linea: serviceImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagraficaAnagraficaProxy
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:397
  Linea: anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/anagrafica/Anagrafica.java:66
  Linea: anagraficaAnagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java:24
  Linea: return (AnagraficaAnagraficaInterface) FactoryBean.getBean("AnagraficaAnagraficaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AnagraficaEsternaEasyImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/esterna/service/AnagraficaEsternaInterfaceBindingImpl.java:30
  Linea: serviceImpl = (AnagraficaEsternaInterface) FactoryBean.getBean("AnagraficaEsternaEasyImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AssociazioneCampagnaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/campagnaprovvigionale/associazione/service/AssociazioneCampagnaInterfaceBindingImpl.java:54
  Linea: serviceImpl = (AssociazioneCampagnaInterface) FactoryBean.getBean("AssociazioneCampagnaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: AuditTracingImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/audit/tracing/service/AuditTracingInterfaceBindingImpl.java:22
  Linea: serviceImpl = (AuditTracingInterface) FactoryBean.getBean("AuditTracingImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: BloccoAutorizzativo
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:104
  Linea: <bean id="BloccoAutorizzativo" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:409
  Linea: bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean("BloccoAutorizzativo");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: BlueLifeLogger
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:128
  Linea: BluelifeLogger bluelifeLogger = (BluelifeLogger) FactoryBean.getBean("BlueLifeLogger");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: BonificaAnagraficaAmletoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/bonifica/anagrafica/amleto/service/BonificaAnagraficaAmletoInterfaceBindingImpl.java:26
  Linea: serviceImpl = (BonificaAnagraficaAmletoInterface) FactoryBean.getBean("BonificaAnagraficaAmletoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: BonificaMultiInvestImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/bonificamulti/service/BonificaMultiInvestInterfaceBindingImpl.java:53
  Linea: serviceImpl = (BonificaMultiInvestInterface) FactoryBean.getBean("BonificaMultiInvestImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CampagnaBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4021
  Linea: return (CampagnaInterface) FactoryBean.getBean("CampagnaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CampagnaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/campagnaprovvigionale/campagna/service/CampagnaInterfaceBindingImpl.java:44
  Linea: serviceImpl = (CampagnaInterface) FactoryBean.getBean("CampagnaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CatalogoProdottiBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3989
  Linea: return (CatalogoProdottiInterface) FactoryBean.getBean("CatalogoProdottiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CatalogoProdottiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/catalogoprodotti/catalogoprodotti/service/CatalogoProdottiInterfaceBindingImpl.java:31
  Linea: serviceImpl = (CatalogoProdottiInterface) FactoryBean.getBean("CatalogoProdottiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CheopeWSProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:104
  Linea: <bean id="CheopeWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: CodaMqImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/osb/codamq/service/CodaMqInterfaceBindingImpl.java:21
  Linea: serviceImpl = (CodaMqInterface) FactoryBean.getBean("CodaMqImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CodeMqWSProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:120
  Linea: <bean id="CodeMqWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: CoeffCostoMgmFee
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:62
  Linea: <bean id="CoeffCostoMgmFeeProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: CoeffCostoMgmFeeBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4009
  Linea: return (CoeffCostoMgmFeeInterface) FactoryBean.getBean("CoeffCostoMgmFeeBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CoeffCostoMgmFeeImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/deroghe/coeffcostomgmfee/service/CoeffCostoMgmFeeInterfaceBindingImpl.java:57
  Linea: serviceImpl = (CoeffCostoMgmFeeInterface) FactoryBean.getBean("CoeffCostoMgmFeeImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CoeffMgmFee
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:39
  Linea: <bean id="CoeffMgmFee" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: CollettivaNonGestitaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/collettiva/collettivanongestita/service/CollettivaNonGestitaInterfaceBindingImpl.java:49
  Linea: serviceImpl = (CollettivaNonGestitaInterface) FactoryBean.getBean("CollettivaNonGestitaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CollettoreImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/interfacce/collettore/service/CollettoreInterfaceBindingImpl.java:21
  Linea: serviceImpl = (CollettoreInterface) FactoryBean.getBean("CollettoreImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ContiZPlatformImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/gestionetabelle/tabellevita/service/GestioneTabelleTabelleVitaInterfaceBindingImpl.java:31
  Linea: serviceImpl = (GestioneTabelleTabelleVitaInterface) FactoryBean.getBean("ContiZPlatformImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CorporateEventsImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/corporateevents/service/CorporateEventsInterfaceBindingImpl.java:51
  Linea: serviceImpl = (CorporateEventsInterface) FactoryBean.getBean("CorporateEventsImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Cust
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:29
  Linea: <bean id="Cust" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:411
  Linea: customer = (CustInterface) FactoryBean.getBean("Cust");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:78
  Linea: cust = (CustInterface) FactoryBean.getBean("Cust");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustAccessori
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:24
  Linea: <bean id="CustAccessori" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: Customer
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:44
  Linea: <bean id="CustomerProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: CustomerAccessori
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:68
  Linea: <bean id="CustomerAccessoriProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: CustomerAccessoriBusinessDelegate
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java:26
  Linea: return (CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java:2798
  Linea: SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriBusinessDelegate")).selectClasseDiRischio(classeDiRischioRequest);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustomerAccessoriImpl
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java:28
  Linea: return (CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/customeraccessori/service/CustomerAccessoriInterfaceBindingImpl.java:35
  Linea: serviceImpl = (CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustomerAccessoriProxy
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/CustAccessori.java:31
  Linea: customerAccessori = (CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java:28
  Linea: return (CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java:2798
  Linea: SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean("CustomerAccessoriProxy")).selectClasseDiRischio(classeDiRischioRequest);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustomerBusinessDelegate
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java:18
  Linea: return (CustomerInterface) FactoryBean.getBean("CustomerBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagraficaRapporto/helpers/AnagraficaRapportoHelper.java:3020
  Linea: return (CustomerInterface) FactoryBean.getBean("CustomerBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java:2052
  Linea: return (CustomerInterface) FactoryBean.getBean("CustomerBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustomerImpl
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java:20
  Linea: return (CustomerInterface) FactoryBean.getBean("CustomerImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/customer/service/CustomerInterfaceBindingImpl.java:79
  Linea: serviceImpl = (CustomerInterface) FactoryBean.getBean("CustomerImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: CustomerProxy
============================================================

EJB_PROXY_USAGE (5 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:412
  Linea: customerSoap = (CustomerInterface) FactoryBean.getBean("CustomerProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:123
  Linea: customer = (CustomerInterface) FactoryBean.getBean("CustomerProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:80
  Linea: customer = (CustomerInterface) FactoryBean.getBean("CustomerProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 2 esempi


============================================================
SERVIZIO: DashboardDatamartImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/dashboard/datamart/service/DashboardDatamartInterfaceBindingImpl.java:95
  Linea: serviceImpl = (DashboardDatamartInterface) FactoryBean.getBean("DashboardDatamartImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: DashboardEventiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/dashboard/eventi/service/DashboardEventiInterfaceBindingImpl.java:37
  Linea: serviceImpl = (DashboardEventiInterface) FactoryBean.getBean("DashboardEventiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: DataAnonymizeWSProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:139
  Linea: <bean id="DataAnonymizeWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: DataDisposalImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/com/zurich/it/oil/externalservices/datadisposal/DataDisposalBindingImpl.java:26
  Linea: serviceImpl = (DataDisposalPortType) FactoryBean.getBean("DataDisposalImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: DefCondizioniBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3153
  Linea: return (GestioneTabelleDefCondizioniInterface) FactoryBean.getBean("DefCondizioniBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Deroga
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:84
  Linea: <bean id="Deroga" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: DerogheService
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:110
  Linea: <bean id="DerogheServiceProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: DerogheServiceBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3820
  Linea: return (DerogheServiceInterface) FactoryBean.getBean("DerogheServiceBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: DerogheServiceProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:394
  Linea: deroga = (DerogheServiceInterface) FactoryBean.getBean("DerogheServiceProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Domini
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:50
  Linea: <bean id="DominiProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: DominiBusinessDelegate
============================================================

EJB_PROXY_USAGE (29 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/helper/RibilanciamentoFondiHelper.java:74
  Linea: DominiInterface domini = (DominiInterface) FactoryBean.getBean("DominiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java:38
  Linea: DominiInterface domini = (DominiInterface) FactoryBean.getBean("DominiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java:14
  Linea: return (DominiInterface) FactoryBean.getBean("DominiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 26 esempi


============================================================
SERVIZIO: DominiImpl
============================================================

EJB_PROXY_USAGE (4 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java:16
  Linea: return (DominiInterface) FactoryBean.getBean("DominiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java:988
  Linea: DominiInterface impl =(DominiInterface) FactoryBean.getBean("DominiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/domini/naw/service/DominiInterfaceBindingImpl.java:61
  Linea: serviceImpl = (DominiInterface) FactoryBean.getBean("DominiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 1 esempi


============================================================
SERVIZIO: DominiProxy
============================================================

EJB_PROXY_USAGE (19 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java:61
  Linea: domini = (DominiInterface) FactoryBean.getBean("DominiProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:399
  Linea: domini = (DominiInterface) FactoryBean.getBean("DominiProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:126
  Linea: domini = (DominiInterface) FactoryBean.getBean("DominiProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 16 esempi


============================================================
SERVIZIO: DominiService
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml:59
  Linea: <!-- 	<bean id="DominiServiceProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"> -->
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: Dominio
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:54
  Linea: <bean id="Dominio" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:408
  Linea: dominioRest = (DominioInterface) FactoryBean.getBean("Dominio");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:125
  Linea: dominioRest = (DominioInterface) FactoryBean.getBean("Dominio");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:81
  Linea: dominio = (DominioInterface) FactoryBean.getBean("Dominio");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: EleCodeBarcodeBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/barcode/types/BarcodeHelper.java:492
  Linea: EleCodeBarcodeInterface eleCodeBarcodeInterface = (EleCodeBarcodeInterface) FactoryBean.getBean("EleCodeBarcodeBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: EleCodeBarcodeImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elecode/barcode/service/EleCodeBarcodeInterfaceBindingImpl.java:24
  Linea: serviceImpl = (EleCodeBarcodeInterface) FactoryBean.getBean("EleCodeBarcodeImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: EleCodeImgcodeImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elecode/imgcode/service/EleCodeImgcodeInterfaceBindingImpl.java:20
  Linea: serviceImpl = (EleCodeImgcodeInterface) FactoryBean.getBean("EleCodeImgcodeImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: EventiPolizzaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/eventi/service/EventiPolizzaInterfaceBindingImpl.java:32
  Linea: serviceImpl = (EventiPolizzaInterface) FactoryBean.getBean("EventiPolizzaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FlussoEbaasImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/flussoebaas/flussoebaas/service/FlussoEbaasFlussoEbaasInterfaceBindingImpl.java:39
  Linea: serviceImpl = (FlussoEbaasFlussoEbaasInterface) FactoryBean.getBean("FlussoEbaasImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FlussoPrometeiaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/flusso/prometeia/service/FlussoPrometeiaInterfaceBindingImpl.java:28
  Linea: serviceImpl = (FlussoPrometeiaInterface) FactoryBean.getBean("FlussoPrometeiaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FlussoReteVenditaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/retevendita/flusso/service/FlussoReteVenditaInterfaceBindingImpl.java:26
  Linea: serviceImpl = (FlussoReteVenditaInterface) FactoryBean.getBean("FlussoReteVenditaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FondoTrasferimento
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:128
  Linea: <bean id="FondoTrasferimentoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: FondoTrasferimentoBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3149
  Linea: return (FondoTrasferimentoInterface) FactoryBean.getBean("FondoTrasferimentoBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FondoTrasferimentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/trasferimento/service/FondoTrasferimentoInterfaceBindingImpl.java:43
  Linea: serviceImpl = (FondoTrasferimentoInterface) FactoryBean.getBean("FondoTrasferimentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FondoTrasferimentoProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java:62
  Linea: fondo = (FondoTrasferimentoInterface) FactoryBean.getBean("FondoTrasferimentoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: FondoUtilitaRetrocesseImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/utilitaretrocesse/service/FondoUtilitaRetrocesseInterfaceBindingImpl.java:27
  Linea: serviceImpl = (FondoUtilitaRetrocesseInterface) FactoryBean.getBean("FondoUtilitaRetrocesseImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Form2WebServiceProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:158
  Linea: <bean id="Form2WebServiceProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: Formula
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:79
  Linea: <bean id="Formula" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: FunctionGenericImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/generic/service/FunctionGenericInterfaceBindingImpl.java:32
  Linea: serviceImpl = (FunctionGenericInterface) FactoryBean.getBean("FunctionGenericImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GdprImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/gdpr/service/GdprInterfaceBindingImpl.java:24
  Linea: serviceImpl = (GdprInterface) FactoryBean.getBean("GdprImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GestioneEventiBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3157
  Linea: return (GestioneEventiInterface) FactoryBean.getBean("GestioneEventiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GestioneEventiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/gestioneeventi/eventi/service/GestioneEventiInterfaceBindingImpl.java:54
  Linea: serviceImpl = (GestioneEventiInterface) FactoryBean.getBean("GestioneEventiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GestioneSeparataBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4025
  Linea: return (GestioneSeparataInterface) FactoryBean.getBean("GestioneSeparataBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GestioneSeparataImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/gestioneseparata/service/GestioneSeparataInterfaceBindingImpl.java:42
  Linea: serviceImpl = (GestioneSeparataInterface) FactoryBean.getBean("GestioneSeparataImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: GestioneTabelleTabelleVita
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:170
  Linea: <bean id="GestioneTabelleTabelleVitaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: LocalitaStradario
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:80
  Linea: <bean id="LocalitaStradarioProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: LocalitaStradarioBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4017
  Linea: return (LocalitaStradarioInterface) FactoryBean.getBean("LocalitaStradarioBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: LocalitaStradarioImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/localita/stradario/service/LocalitaStradarioInterfaceBindingImpl.java:54
  Linea: serviceImpl = (LocalitaStradarioInterface) FactoryBean.getBean("LocalitaStradarioImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: LocalitaViarioImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/localita/viario/service/LocalitaViarioInterfaceBindingImpl.java:26
  Linea: serviceImpl = (LocalitaViarioInterface) FactoryBean.getBean("LocalitaViarioImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: MessaggioReteVenditaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/retevendita/messaggio/service/MessaggioReteVenditaInterfaceBindingImpl.java:32
  Linea: serviceImpl = (MessaggioReteVenditaInterface) FactoryBean.getBean("MessaggioReteVenditaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ModelloMatematicoFormula
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:98
  Linea: <bean id="ModelloMatematicoFormulaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ModelloMatematicoFormulaBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3145
  Linea: return (ModelloMatematicoFormulaInterface) FactoryBean.getBean("ModelloMatematicoFormulaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ModelloMatematicoFormulaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/modelloMatematico/formula/service/ModelloMatematicoFormulaInterfaceBindingImpl.java:32
  Linea: serviceImpl = (ModelloMatematicoFormulaInterface) FactoryBean.getBean("ModelloMatematicoFormulaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: MonitorGidBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/utility/CommonServicesUtils.java:18
  Linea: return (MonitorGidInterface) FactoryBean.getBean("MonitorGidBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: MonitorGidImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/monitor/gid/service/MonitorGidInterfaceBindingImpl.java:23
  Linea: serviceImpl = (MonitorGidInterface) FactoryBean.getBean("MonitorGidImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: MovimentiContabiliOasiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/oasi/movimenticontabili/service/MovimentiContabiliOasiInterfaceBindingImpl.java:41
  Linea: serviceImpl = (MovimentiContabiliOasiInterface) FactoryBean.getBean("MovimentiContabiliOasiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: MovimentiOasiWSProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:74
  Linea: <bean id="MovimentiOasiWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: NawSyncroRelImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/syncro/nawsyncrorel/service/SyncroNawSyncroRelInterfaceBindingImpl.java:31
  Linea: serviceImpl = (SyncroNawSyncroRelInterface) FactoryBean.getBean("NawSyncroRelImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: NumeratoriImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/numeratori/numeratori/service/NumeratoriInterfaceBindingImpl.java:30
  Linea: serviceImpl = (NumeratoriInterface) FactoryBean.getBean("NumeratoriImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: OpenSecProxy
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/service/impl/PropostaPropostaImpl.java:7824
  Linea: OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean("OpenSecProxy")).controlloIbanIncassoZurich(modPagamentoPrimaRata, modPagamentoPrimaRata.getUserVariaz());
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Impl/src/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/impl/AnagraficaAnagraficaImpl.java:1659
  Linea: OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean("OpenSecProxy")).controlloIbanIncassoZurich(modPagamentoInfo, request.getUser());
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: OpzioniConversioneODifferimentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/opzioniconversioneodifferimento/service/OpzioniConversioneODifferimentoInterfaceBindingImpl.java:26
  Linea: serviceImpl = (OpzioniConversioneODifferimentoInterface) FactoryBean.getBean("OpzioniConversioneODifferimentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: OrchestratoreConfigurazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/orchestratore/configurazione/service/OrchestratoreConfigurazioneInterfaceBindingImpl.java:22
  Linea: serviceImpl = (OrchestratoreConfigurazioneInterface) FactoryBean.getBean("OrchestratoreConfigurazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PDNDImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/pdnd/service/PDNDInterfaceBindingImpl.java:46
  Linea: serviceImpl = (PDNDInterface) FactoryBean.getBean("PDNDImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PaperlessImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/paperless/service/PaperlessInterfaceBindingImpl.java:25
  Linea: serviceImpl = (PaperlessInterface) FactoryBean.getBean("PaperlessImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ParametriBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3824
  Linea: return (ParametriServiceInterface) FactoryBean.getBean("ParametriBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ParametriCollettivaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/ParametriCollettivaInterfaceBindingImpl.java:44
  Linea: serviceImpl = (ParametriCollettivaInterface) FactoryBean.getBean("ParametriCollettivaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ParametriImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/parametri/ejb/ParametriServiceEJB.java:30
  Linea: serviceImpl = (ParametriServiceInterface) FactoryBean.getBean("ParametriImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PepCrimeProxy
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:124
  Linea: pepCrimeService = (PepCrimeService) FactoryBean.getBean("PepCrimeProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:82
  Linea: pepCrimeService = (PepCrimeService) FactoryBean.getBean("PepCrimeProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/Cust.java:93
  Linea: pepCrimeService = (PepCrimeService) FactoryBean.getBean("PepCrimeProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PercipientiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/percipienti/service/PercipientiInterfaceBindingImpl.java:28
  Linea: serviceImpl = (PercipientiInterface) FactoryBean.getBean("PercipientiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PianiSpostamento
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:92
  Linea: <bean id="PianiSpostamentoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PianiSpostamentoBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4013
  Linea: return (PianiSpostamentoInterface) FactoryBean.getBean("PianiSpostamentoBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PianiSpostamentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/pianispostamento/service/PianiSpostamentoInterfaceBindingImpl.java:30
  Linea: serviceImpl = (PianiSpostamentoInterface) FactoryBean.getBean("PianiSpostamentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Polizza
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:109
  Linea: <bean id="Polizza" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:130
  Linea: polizza = (PolizzaInterface) FactoryBean.getBean("Polizza");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaAccountingImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/accounting/service/PolizzaAccountingInterfaceBindingImpl.java:31
  Linea: serviceImpl = (PolizzaAccountingInterface) FactoryBean.getBean("PolizzaAccountingImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaCambioFrazionamentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/cambiofrazionamento/service/PolizzaCambioFrazionamentoInterfaceBindingImpl.java:21
  Linea: serviceImpl = (PolizzaCambioFrazionamentoInterface) FactoryBean.getBean("PolizzaCambioFrazionamentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaCambioRuoloImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/cambioruolo/service/PolizzaCambioRuoloInterfaceBindingImpl.java:28
  Linea: serviceImpl = (PolizzaCambioRuoloInterface) FactoryBean.getBean("PolizzaCambioRuoloImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaCoassicurazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/coassicurazione/service/PolizzaCoassicurazioneInterfaceBindingImpl.java:20
  Linea: serviceImpl = (PolizzaCoassicurazioneInterface) FactoryBean.getBean("PolizzaCoassicurazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaCollettoreImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/collettore/service/PolizzaCollettoreInterfaceBindingImpl.java:40
  Linea: serviceImpl = (PolizzaCollettoreInterface) FactoryBean.getBean("PolizzaCollettoreImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaCostiAnnuiRendicontatiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/costiannuirendicontati/service/PolizzaCostiAnnuiRendicontatiInterfaceBindingImpl.java:23
  Linea: serviceImpl = (PolizzaCostiAnnuiRendicontatiInterface) FactoryBean.getBean("PolizzaCostiAnnuiRendicontatiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaDurImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/dur/service/PolizzaDurInterfaceBindingImpl.java:40
  Linea: serviceImpl = (PolizzaDurInterface) FactoryBean.getBean("PolizzaDurImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaGestioneRid
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:122
  Linea: <bean id="PolizzaGestioneRidProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PolizzaGestioneRidImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/gestionerid/service/PolizzaGestioneRidInterfaceBindingImpl.java:57
  Linea: serviceImpl = (PolizzaGestioneRidInterface) FactoryBean.getBean("PolizzaGestioneRidImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaLimitazioneOperativitaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/limitazioneoperativita/service/PolizzaLimitazioneOperativitaInterfaceBindingImpl.java:44
  Linea: serviceImpl = (PolizzaLimitazioneOperativitaInterface) FactoryBean.getBean("PolizzaLimitazioneOperativitaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaLiquidazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/liquidazione/service/PolizzaLiquidazioneInterfaceBindingImpl.java:67
  Linea: serviceImpl = (PolizzaLiquidazioneInterface) FactoryBean.getBean("PolizzaLiquidazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaMgmFeeBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4028
  Linea: return (PolizzaMgmFeeInterface) FactoryBean.getBean("PolizzaMgmFeeBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaMgmFeeImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/mgmfee/service/PolizzaMgmFeeInterfaceBindingImpl.java:34
  Linea: serviceImpl = (PolizzaMgmFeeInterface) FactoryBean.getBean("PolizzaMgmFeeImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaMovimentazioniULImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/movimentazioniul/service/PolizzaMovimentazioniULInterfaceBindingImpl.java:26
  Linea: serviceImpl = (PolizzaMovimentazioniULInterface) FactoryBean.getBean("PolizzaMovimentazioniULImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaMovimentiContabiliImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/movimenticontabili/service/PolizzaMovimentiContabiliInterfaceBindingImpl.java:38
  Linea: serviceImpl = (PolizzaMovimentiContabiliInterface) FactoryBean.getBean("PolizzaMovimentiContabiliImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaOpzioniContrattuali
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:182
  Linea: <bean id="PolizzaOpzioniContrattualiProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PolizzaOpzioniContrattualiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/opzionicontrattuali/service/PolizzaOpzioniContrattualiInterfaceBindingImpl.java:27
  Linea: serviceImpl = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean("PolizzaOpzioniContrattualiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaOpzioniContrattualiProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:405
  Linea: polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean("PolizzaOpzioniContrattualiProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaPianoVersamentiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/pianoversamenti/service/PolizzaPianoVersamentiInterfaceBindingImpl.java:72
  Linea: serviceImpl = (PolizzaPianoVersamentiInterface) FactoryBean.getBean("PolizzaPianoVersamentiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaPolizza
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:146
  Linea: <bean id="PolizzaPolizzaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PolizzaPolizzaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/polizza/service/PolizzaPolizzaInterfaceBindingImpl.java:21
  Linea: serviceImpl = (PolizzaPolizzaInterface) FactoryBean.getBean("PolizzaPolizzaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaPolizzaProxy
============================================================

EJB_PROXY_USAGE (4 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:390
  Linea: polizza = (PolizzaPolizzaInterface) FactoryBean.getBean("PolizzaPolizzaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:120
  Linea: polizza = (PolizzaPolizzaInterface) FactoryBean.getBean("PolizzaPolizzaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:77
  Linea: polizza = (PolizzaPolizzaInterface) FactoryBean.getBean("PolizzaPolizzaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 1 esempi


============================================================
SERVIZIO: PolizzaPosizione
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:152
  Linea: <bean id="PolizzaPosizioneProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PolizzaPosizioneBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java:2597
  Linea: return (PolizzaPosizioneInterface) FactoryBean.getBean("PolizzaPosizioneBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaPosizioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/posizione/service/PolizzaPosizioneInterfaceBindingImpl.java:105
  Linea: serviceImpl = (PolizzaPosizioneInterface) FactoryBean.getBean("PolizzaPosizioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaPosizioneProxy
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:393
  Linea: posizione = (PolizzaPosizioneInterface) FactoryBean.getBean("PolizzaPosizioneProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java:101
  Linea: SelectPosizioneSimpleT024Response posizResponse = ((PolizzaPosizioneInterface) FactoryBean.getBean("PolizzaPosizioneProxy")).selectPosizioneSimple(posizRequest);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java:2597
  Linea: return (PolizzaPosizioneInterface) FactoryBean.getBean("PolizzaPosizioneProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaRiattivazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/riattivazione/service/PolizzaRiattivazioneInterfaceBindingImpl.java:22
  Linea: serviceImpl = (PolizzaRiattivazioneInterface) FactoryBean.getBean("PolizzaRiattivazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaStorni
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:176
  Linea: <bean id="PolizzaStorniProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PolizzaStorniImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/storni/service/PolizzaStorniInterfaceBindingImpl.java:64
  Linea: serviceImpl = (PolizzaStorniInterface) FactoryBean.getBean("PolizzaStorniImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaStorniProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:404
  Linea: storni = (PolizzaStorniInterface) FactoryBean.getBean("PolizzaStorniProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaSwitchImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/_switch/service/PolizzaSwitchInterfaceBindingImpl.java:43
  Linea: serviceImpl = (PolizzaSwitchInterface) FactoryBean.getBean("PolizzaSwitchImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaTassazioneDatiCristallizzatiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/tassazione/daticristallizzati/service/PolizzaTassazioneDatiCristallizzatiInterfaceBindingImpl.java:22
  Linea: serviceImpl = (PolizzaTassazioneDatiCristallizzatiInterface) FactoryBean.getBean("PolizzaTassazioneDatiCristallizzatiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaTrasferimentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/trasferimento/service/PolizzaTrasferimentoInterfaceBindingImpl.java:66
  Linea: serviceImpl = (PolizzaTrasferimentoInterface) FactoryBean.getBean("PolizzaTrasferimentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PolizzaVariazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/variazione/service/VariazioneInterfaceBindingImpl.java:22
  Linea: serviceImpl = (VariazioneInterface) FactoryBean.getBean("PolizzaVariazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Portafoglio
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:44
  Linea: <bean id="Portafoglio" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:407
  Linea: portafoglioRest = (PortafoglioInterface) FactoryBean.getBean("Portafoglio");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PortafoglioBonificiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/bonifici/service/PortafoglioBonificiInterfaceBindingImpl.java:32
  Linea: serviceImpl = (PortafoglioBonificiInterface) FactoryBean.getBean("PortafoglioBonificiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PortafoglioEstrazionePolizzaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/estrazione/service/PortafoglioEstrazionePolizzaInterfaceBindingImpl.java:34
  Linea: serviceImpl = (PortafoglioEstrazionePolizzaInterface) FactoryBean.getBean("PortafoglioEstrazionePolizzaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PortafoglioRapporto
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:8
  Linea: <bean id="PortafoglioRapportoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PortafoglioRapportoBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3137
  Linea: return (PortafoglioRapportoInterface) FactoryBean.getBean("PortafoglioRapportoBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PortafoglioRapportoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/rapporto/service/PortafoglioRapportoInterfaceBindingImpl.java:83
  Linea: serviceImpl = (PortafoglioRapportoInterface) FactoryBean.getBean("PortafoglioRapportoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PortafoglioRapportoProxy
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:117
  Linea: portafoglio = (PortafoglioRapportoInterface) FactoryBean.getBean("PortafoglioRapportoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:83
  Linea: portafoglioRapporto = (PortafoglioRapportoInterface) FactoryBean.getBean("PortafoglioRapportoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PremiumTranchingImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/premiumtranching/service/PremiumTranchingInterfaceBindingImpl.java:24
  Linea: serviceImpl = (PremiumTranchingInterface) FactoryBean.getBean("PremiumTranchingImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PrenotazioneDocumentazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/documentazione/service/PrenotazioneDocumentazioneInterfaceBindingImpl.java:29
  Linea: serviceImpl = (PrenotazioneDocumentazioneInterface) FactoryBean.getBean("PrenotazioneDocumentazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PrenotazioneLiquidazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/liquidazione/service/PrenotazioneLiquidazioneInterfaceBindingImpl.java:23
  Linea: serviceImpl = (PrenotazioneLiquidazioneInterface) FactoryBean.getBean("PrenotazioneLiquidazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PrenotazionePostVendita
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:164
  Linea: <bean id="PrenotazionePostVenditaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PrenotazionePostVenditaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/postvendita/service/PrenotazionePostVenditaInterfaceBindingImpl.java:65
  Linea: serviceImpl = (PrenotazionePostVenditaInterface) FactoryBean.getBean("PrenotazionePostVenditaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PrenotazionePostVenditaProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:398
  Linea: postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean("PrenotazionePostVenditaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PrenotazioneTrasfAgenziaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/trasfagenzia/service/PrenotazioneTrasfAgenziaInterfaceBindingImpl.java:29
  Linea: serviceImpl = (PrenotazioneTrasfAgenziaInterface) FactoryBean.getBean("PrenotazioneTrasfAgenziaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Print
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:246
  Linea: <bean id="PrintProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PrintDataImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/eventi/printdata/service/PrintDataServiceInterfaceBindingImpl.java:22
  Linea: serviceImpl = (PrintDataInterface) FactoryBean.getBean("PrintDataImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Prodotto
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:19
  Linea: <bean id="Prodotto" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:410
  Linea: prodottoRest = (ProdottoInterface) FactoryBean.getBean("Prodotto");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java:79
  Linea: prodotto = (ProdottoInterface) FactoryBean.getBean("Prodotto");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java:131
  Linea: prodotto = (ProdottoInterface) FactoryBean.getBean("Prodotto");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoFormulaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/formula/service/ProdottoFormulaInterfaceBindingImpl.java:37
  Linea: serviceImpl = (ProdottoFormulaInterface) FactoryBean.getBean("ProdottoFormulaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoProdotto
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:38
  Linea: <bean id="ProdottoProdottoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ProdottoProdottoBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3133
  Linea: return (ProdottoProdottoInterface) FactoryBean.getBean("ProdottoProdottoBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoProdottoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/prodotto/service/ProdottoProdottoInterfaceBindingImpl.java:22
  Linea: serviceImpl = (ProdottoProdottoInterface) FactoryBean.getBean("ProdottoProdottoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoProdottoProxy
============================================================

EJB_PROXY_USAGE (3 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java:72
  Linea: prodotto = (ProdottoProdottoInterface) FactoryBean.getBean("ProdottoProdottoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:400
  Linea: prodotto = (ProdottoProdottoInterface) FactoryBean.getBean("ProdottoProdottoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:122
  Linea: prodotto = (ProdottoProdottoInterface) FactoryBean.getBean("ProdottoProdottoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoTariffa
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:86
  Linea: <bean id="ProdottoTariffaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ProdottoTariffaBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4005
  Linea: return (ProdottoTariffaInterface) FactoryBean.getBean("ProdottoTariffaBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoTariffaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/tariffa/service/ProdottoTariffaInterfaceBindingImpl.java:49
  Linea: serviceImpl = (ProdottoTariffaInterface) FactoryBean.getBean("ProdottoTariffaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProdottoVettoreImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/vettore/service/ProdottoVettoreInterfaceBindingImpl.java:21
  Linea: serviceImpl = (ProdottoVettoreInterface) FactoryBean.getBean("ProdottoVettoreImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ProfiloInvestimentoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/profiloinvestimento/service/ProfiloInvestimentoInterfaceBindingImpl.java:20
  Linea: serviceImpl = (ProfiloInvestimentoInterface) FactoryBean.getBean("ProfiloInvestimentoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Proposta
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:59
  Linea: <bean id="Proposta" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PropostaProposta
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:14
  Linea: <bean id="PropostaPropostaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: PropostaPropostaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/proposta/proposta/service/PropostaPropostaInterfaceBindingImpl.java:20
  Linea: serviceImpl = (PropostaPropostaInterface) FactoryBean.getBean("PropostaPropostaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PropostaPropostaProxy
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:391
  Linea: proposta = (PropostaPropostaInterface) FactoryBean.getBean("PropostaPropostaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:118
  Linea: proposta = (PropostaPropostaInterface) FactoryBean.getBean("PropostaPropostaProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: PropostaService
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml:45
  Linea: <!--     <bean id="PropostaServiceProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"> -->
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: Questionario
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:89
  Linea: <bean id="Questionario" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: QuestionarioAura
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:116
  Linea: <bean id="QuestionarioAuraProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: QuestionarioAuraImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/questionario/aura/service/QuestionarioAuraInterfaceBindingImpl.java:38
  Linea: serviceImpl = (QuestionarioAuraInterface) FactoryBean.getBean("QuestionarioAuraImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: QuestionarioStruttura
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:134
  Linea: <bean id="QuestionarioStrutturaProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: QuestionarioStrutturaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/questionario/struttura/service/QuestionarioStrutturaInterfaceBindingImpl.java:58
  Linea: serviceImpl = (QuestionarioStrutturaInterface) FactoryBean.getBean("QuestionarioStrutturaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RaggruppamentoFondiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/raggruppamento/service/RaggruppamentoFondiInterfaceBindingImpl.java:61
  Linea: serviceImpl = (RaggruppamentoFondiInterface) FactoryBean.getBean("RaggruppamentoFondiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RctImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/interfacce/contabilita/rct/service/RctInterfaceBindingImpl.java:21
  Linea: serviceImpl = (RctInterface) FactoryBean.getBean("RctImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RelazioneFigureRapportoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/relazionefigurerapporto/service/RelazioneFigureRapportoInterfaceBindingImpl.java:32
  Linea: serviceImpl = (RelazioneFigureRapportoInterface) FactoryBean.getBean("RelazioneFigureRapportoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RemoteCommandImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/utils/remotecommand/service/RemoteCommandInterfaceBindingImpl.java:21
  Linea: serviceImpl = (RemoteCommandInterface) FactoryBean.getBean("RemoteCommandImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ReportPrenotazioniImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/report/prenotazioni/service/ReportPrenotazioniInterfaceBindingImpl.java:29
  Linea: serviceImpl = (ReportPrenotazioniInterface) FactoryBean.getBean("ReportPrenotazioniImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RestrizioneAccessoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/restrizioneaccesso/service/RestrizioneAccessoInterfaceBindingImpl.java:38
  Linea: serviceImpl = (RestrizioneAccessoInterface) FactoryBean.getBean("RestrizioneAccessoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Rete
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:49
  Linea: <bean id="Rete" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ReteZb
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:94
  Linea: <bean id="ReteZb" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: RiallocazioneImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/riallocazione/service/RiallocazioneInterfaceBindingImpl.java:48
  Linea: riallocazioneImpl = (RiallocazioneInterface) FactoryBean.getBean("RiallocazioneImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RibilanciamentoFondiBusinessDelegate
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/helper/RibilanciamentoFondiHelper.java:182
  Linea: return (RibilanciamentoFondiInterface) FactoryBean.getBean("RibilanciamentoFondiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3993
  Linea: return (RibilanciamentoFondiInterface) FactoryBean.getBean("RibilanciamentoFondiBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: RibilanciamentoFondiImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/ribilanciamento/service/RibilanciamentoFondiInterfaceBindingImpl.java:94
  Linea: serviceImpl = (RibilanciamentoFondiInterface) FactoryBean.getBean("RibilanciamentoFondiImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: StampaEsternaImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/stampa/esterna/service/StampaEsternaInterfaceBindingImpl.java:22
  Linea: serviceImpl = (StampaEsternaInterface) FactoryBean.getBean("StampaEsternaImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: StampaVariabileBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/service/impl/GestioneEventiImpl.java:525
  Linea: return (StampaVariabileInterface) FactoryBean.getBean("StampaVariabileBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: StampaVariabileImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/stampa/variabile/service/StampaVariabileInterfaceBindingImpl.java:25
  Linea: serviceImpl = (StampaVariabileInterface) FactoryBean.getBean("StampaVariabileImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: StrutturaRete
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:32
  Linea: <bean id="StrutturaReteProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: StrutturaReteBusinessDelegate
============================================================

EJB_PROXY_USAGE (4 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:3985
  Linea: return (StrutturaReteInterface) FactoryBean.getBean("StrutturaReteBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/custom/DominioReteVenditaLivello1Custom.java:161
  Linea: return (StrutturaReteInterface) FactoryBean.getBean("StrutturaReteBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java:2697
  Linea: return (StrutturaReteInterface) FactoryBean.getBean("StrutturaReteBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 1 esempi


============================================================
SERVIZIO: StrutturaReteImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/struttura/rete/service/StrutturaReteInterfaceBindingImpl.java:69
  Linea: serviceImpl = (StrutturaReteInterface) FactoryBean.getBean("StrutturaReteImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: StrutturaReteProxy
============================================================

EJB_PROXY_USAGE (5 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:403
  Linea: strutturaRete = (StrutturaReteInterface) FactoryBean.getBean("StrutturaReteProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java:694
  Linea: SelectLivello1Response livello1Response = ((StrutturaReteInterface) FactoryBean.getBean("StrutturaReteProxy")).selectLivello1(agenziaRequest);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java:711
  Linea: SelectLivello2Response livello2Response = ((StrutturaReteInterface) FactoryBean.getBean("StrutturaReteProxy")).selectLivello2(subAgenziaRequest);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 2 esempi


============================================================
SERVIZIO: SyncroCustomerImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/syncrocustomer/service/SyncroCustomerInterfaceBindingImpl.java:25
  Linea: serviceImpl = (SyncroCustomerInterface) FactoryBean.getBean("SyncroCustomerImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: SyncroPolicy
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:158
  Linea: <bean id="SyncroPolicyProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: SyncroPolicyImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/syncropolicy/service/SyncroPolicyInterfaceBindingImpl.java:30
  Linea: serviceImpl = (SyncroPolicyInterface) FactoryBean.getBean("SyncroPolicyImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: SyncroPolicyProxy
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:396
  Linea: stampa = (SyncroPolicyInterface) FactoryBean.getBean("SyncroPolicyProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: SyncroWSProxy
============================================================

WEB_SERVICE_DEFINITION (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/external-services.xml:85
  Linea: <bean id="SyncroWSProxy" class="org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean" lazy-init="true">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"


============================================================
SERVIZIO: TabellaTracciato
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:104
  Linea: <bean id="TabellaTracciatoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: TabellaTracciatoBusinessDelegate
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java:4032
  Linea: return (TabellaTracciatoInterface) FactoryBean.getBean("TabellaTracciatoBusinessDelegate");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: TabellaTracciatoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/tabella/tracciato/service/TabellaTracciatoInterfaceBindingImpl.java:19
  Linea: serviceImpl = (TabellaTracciatoInterface) FactoryBean.getBean("TabellaTracciatoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: TabellaTracciatoProxy
============================================================

EJB_PROXY_USAGE (6 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java:73
  Linea: tracciato = (TabellaTracciatoInterface) FactoryBean.getBean("TabellaTracciatoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:392
  Linea: tracciato = (TabellaTracciatoInterface) FactoryBean.getBean("TabellaTracciatoProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java:468
  Linea: SelectProdottoDatiComuni130Response selectProdottoComuni130Response = ((TabellaTracciatoInterface) FactoryBean.getBean("TabellaTracciatoProxy")).selectProdottoDatiComuni130(selectProdottoComuni130Request);
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  ... e altri 3 esempi


============================================================
SERVIZIO: Tariffa
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:74
  Linea: <bean id="Tariffa" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: Test
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:9
  Linea: <bean id="Test" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: Tracciato
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:99
  Linea: <bean id="Tracciato" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: TrascodificaDominiEasyImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/easy/trascodifica/service/TrascodificaDominiEasyInterfaceBindingImpl.java:20
  Linea: serviceImpl = (TrascodificaDominiEasyInterface) FactoryBean.getBean("TrascodificaDominiEasyImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: UnderwritingImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/proposta/underwriting/service/UnderwritingInterfaceBindingImpl.java:29
  Linea: serviceImpl = (UnderwritingInterface) FactoryBean.getBean("UnderwritingImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: VariazioneRischioAssicuratoImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/variazionerischioassicurato/service/VariazioneRischioAssicuratoInterfaceBindingImpl.java:22
  Linea: serviceImpl = (VariazioneRischioAssicuratoInterface) FactoryBean.getBean("VariazioneRischioAssicuratoImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: Viario
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:69
  Linea: <bean id="Viario" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: WorkFlowMgr
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:74
  Linea: <bean id="WorkFlowMgrProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: WorkFlowMgrProxy
============================================================

EJB_PROXY_USAGE (2 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:401
  Linea: workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean("WorkFlowMgrProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']

  File: REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java:121
  Linea: workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean("WorkFlowMgrProxy");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: WorkflowAutorizzativo
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:64
  Linea: <bean id="WorkflowAutorizzativo" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: WorkflowEmissivo
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:114
  Linea: <bean id="WorkflowEmissivo" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: WorkflowOpzContrattuali
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:119
  Linea: <bean id="WorkflowOpzContrattuali" class="org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ZbRete
============================================================

EJB_PROXY_DEFINITION (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:140
  Linea: <bean id="ZbReteProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
  Pattern: <bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">


============================================================
SERVIZIO: ZbReteImpl
============================================================

EJB_PROXY_USAGE (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/zbank/zbrete/service/ZbReteInterfaceBindingImpl.java:44
  Linea: serviceImpl = (ZbReteInterface) FactoryBean.getBean("ZbReteImpl");
  Pattern: FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: albedoWebSIALBW04Domini
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml:60
  Linea: <!-- 	  	<property name="jndiName" value="albedoWebSIALBW04Domini"/>                                    -->
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: albedoWebSIALBW04PropostaProposta
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml:46
  Linea: <!-- 	  	<property name="jndiName" value="albedoWebSIALBW04PropostaProposta"/>                                    -->
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Abilitazioni
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:15
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Abilitazioni"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Anagrafica
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:35
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Anagrafica"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:105
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:40
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Cust
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:30
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Cust"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}CustAccessori
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:25
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}CustAccessori"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Deroga
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:85
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Deroga"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Dominio
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:55
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Dominio"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Formula
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:80
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Formula"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Polizza
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:110
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Polizza"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Portafoglio
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:45
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Portafoglio"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Prodotto
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:20
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Prodotto"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Proposta
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:60
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Proposta"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Questionario
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:90
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Questionario"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Rete
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:50
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Rete"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}ReteZb
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:95
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}ReteZb"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Tariffa
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:75
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Tariffa"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Test
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:10
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Test"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Tracciato
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:100
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Tracciato"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}Viario
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:70
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}Viario"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:65
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:115
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/internal-services.xml:120
  Linea: <property name="jndiName" value="ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali"/>
  Pattern: jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AbilitazioneAzioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/azione/service/AbilitazioneAzioneInterfacePortProxy.java:69
  Linea: _service = (it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneService)ctx.lookup("java:comp/env/service/AbilitazioneAzioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AbilitazioneUtenteService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utente/service/AbilitazioneUtenteInterfacePortProxy.java:81
  Linea: _service = (it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteService)ctx.lookup("java:comp/env/service/AbilitazioneUtenteService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AegisService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/aegis/service/AegisInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.function.aegis.service.AegisService)ctx.lookup("java:comp/env/service/AegisService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AnagrafeEasyService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/easy/anagrafe/service/AnagrafeEasyInterfacePortProxy.java:67
  Linea: _service = (it.sistinf.albedoweb.services.easy.anagrafe.service.AnagrafeEasyService)ctx.lookup("java:comp/env/service/AnagrafeEasyService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AnagraficaAnagraficaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/AnagraficaAnagraficaInterfacePortProxy.java:165
  Linea: _service = (it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaService)ctx.lookup("java:comp/env/service/AnagraficaAnagraficaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AnagraficaEsternaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/esterna/service/AnagraficaEsternaInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.anagrafica.esterna.service.AnagraficaEsternaService)ctx.lookup("java:comp/env/service/AnagraficaEsternaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AssociazioneCampagnaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/campagnaprovvigionale/associazione/service/AssociazioneCampagnaInterfacePortProxy.java:75
  Linea: _service = (it.sistinf.albedoweb.services.campagnaprovvigionale.associazione.service.AssociazioneCampagnaService)ctx.lookup("java:comp/env/service/AssociazioneCampagnaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/AuditTracingService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/audit/tracing/service/AuditTracingInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.audit.tracing.service.AuditTracingService)ctx.lookup("java:comp/env/service/AuditTracingService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/BonificaAnagraficaAmletoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/bonifica/anagrafica/amleto/service/BonificaAnagraficaAmletoInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.bonifica.anagrafica.amleto.service.BonificaAnagraficaAmletoService)ctx.lookup("java:comp/env/service/BonificaAnagraficaAmletoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/BonificaMultiInvestService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/bonificamulti/service/BonificaMultiInvestInterfacePortProxy.java:73
  Linea: _service = (it.sistinf.albedoweb.services.polizza.bonificamulti.service.BonificaMultiInvestService)ctx.lookup("java:comp/env/service/BonificaMultiInvestService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CampagnaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/campagnaprovvigionale/campagna/service/CampagnaInterfacePortProxy.java:65
  Linea: _service = (it.sistinf.albedoweb.services.campagnaprovvigionale.campagna.service.CampagnaService)ctx.lookup("java:comp/env/service/CampagnaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CatalogoProdottiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/catalogoprodotti/catalogoprodotti/service/CatalogoProdottiInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.catalogoprodotti.catalogoprodotti.service.CatalogoProdottiService)ctx.lookup("java:comp/env/service/CatalogoProdottiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CodaMqService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/osb/codamq/service/CodaMqInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.osb.codamq.service.CodaMqService)ctx.lookup("java:comp/env/service/CodaMqService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CoeffCostoMgmFeeService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/deroghe/coeffcostomgmfee/service/CoeffCostoMgmFeeInterfacePortProxy.java:79
  Linea: _service = (it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeService)ctx.lookup("java:comp/env/service/CoeffCostoMgmFeeService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CollettivaNonGestitaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/collettiva/collettivanongestita/service/CollettivaNonGestitaInterfacePortProxy.java:69
  Linea: _service = (it.sistinf.albedoweb.services.collettiva.collettivanongestita.service.CollettivaNonGestitaService)ctx.lookup("java:comp/env/service/CollettivaNonGestitaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CollettoreService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/interfacce/collettore/service/CollettoreInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.interfacce.collettore.service.CollettoreService)ctx.lookup("java:comp/env/service/CollettoreService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CoordinatedServicesService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/core/coordinatedservices/service/CoordinatedServicesInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.core.coordinatedservices.service.CoordinatedServicesService)ctx.lookup("java:comp/env/service/CoordinatedServicesService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CorporateEventsService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/corporateevents/service/CorporateEventsInterfacePortProxy.java:73
  Linea: _service = (it.sistinf.albedoweb.services.fondo.corporateevents.service.CorporateEventsService)ctx.lookup("java:comp/env/service/CorporateEventsService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CustomerAccessoriService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customeraccessori/service/CustomerAccessoriInterfacePortProxy.java:57
  Linea: _service = (it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriService)ctx.lookup("java:comp/env/service/CustomerAccessoriService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/CustomerService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/service/CustomerInterfacePortProxy.java:99
  Linea: _service = (it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerService)ctx.lookup("java:comp/env/service/CustomerService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/DashboardDatamartService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/dashboard/datamart/service/DashboardDatamartInterfacePortProxy.java:115
  Linea: _service = (it.sistinf.albedoweb.services.dashboard.datamart.service.DashboardDatamartService)ctx.lookup("java:comp/env/service/DashboardDatamartService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/DashboardEventiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/dashboard/eventi/service/DashboardEventiInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.dashboard.eventi.service.DashboardEventiService)ctx.lookup("java:comp/env/service/DashboardEventiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/DataDisposal
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/com/zurich/it/oil/externalservices/datadisposal/DataDisposalPortProxy.java:46
  Linea: _service = (com.zurich.it.oil.externalservices.datadisposal.DataDisposal)ctx.lookup("java:comp/env/service/DataDisposal");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/DominiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/domini/naw/service/DominiInterfacePortProxy.java:81
  Linea: _service = (it.sistinf.albedoweb.services.domini.naw.service.DominiService)ctx.lookup("java:comp/env/service/DominiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/EleCodeBarcodeService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/barcode/service/EleCodeBarcodeInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.elecode.barcode.service.EleCodeBarcodeService)ctx.lookup("java:comp/env/service/EleCodeBarcodeService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/EleCodeImgcodeService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/imgcode/service/EleCodeImgcodeInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.elecode.imgcode.service.EleCodeImgcodeService)ctx.lookup("java:comp/env/service/EleCodeImgcodeService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/EventiPolizzaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/eventi/service/EventiPolizzaInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.polizza.eventi.service.EventiPolizzaService)ctx.lookup("java:comp/env/service/EventiPolizzaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FlussoEbaasFlussoEbaasService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/flussoebaas/flussoebaas/service/FlussoEbaasFlussoEbaasInterfacePortProxy.java:59
  Linea: _service = (it.sistinf.albedoweb.services.flussoebaas.flussoebaas.service.FlussoEbaasFlussoEbaasService)ctx.lookup("java:comp/env/service/FlussoEbaasFlussoEbaasService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FlussoPrometeiaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/flusso/prometeia/service/FlussoPrometeiaInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.flusso.prometeia.service.FlussoPrometeiaService)ctx.lookup("java:comp/env/service/FlussoPrometeiaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FlussoReteVenditaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/retevendita/flusso/service/FlussoReteVenditaInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.retevendita.flusso.service.FlussoReteVenditaService)ctx.lookup("java:comp/env/service/FlussoReteVenditaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FondoTrasferimentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/trasferimento/service/FondoTrasferimentoInterfacePortProxy.java:65
  Linea: _service = (it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoService)ctx.lookup("java:comp/env/service/FondoTrasferimentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FondoUtilitaRetrocesseService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/utilitaretrocesse/service/FondoUtilitaRetrocesseInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.fondo.utilitaretrocesse.service.FondoUtilitaRetrocesseService)ctx.lookup("java:comp/env/service/FondoUtilitaRetrocesseService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/FunctionGenericService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/generic/service/FunctionGenericInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.function.generic.service.FunctionGenericService)ctx.lookup("java:comp/env/service/FunctionGenericService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/GdprService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/gdpr/service/GdprInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.function.gdpr.service.GdprService)ctx.lookup("java:comp/env/service/GdprService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/GestioneEventiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/gestioneeventi/eventi/service/GestioneEventiInterfacePortProxy.java:69
  Linea: _service = (it.sistinf.albedoweb.services.gestioneeventi.eventi.service.GestioneEventiService)ctx.lookup("java:comp/env/service/GestioneEventiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/GestioneSeparataService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/gestioneseparata/service/GestioneSeparataInterfacePortProxy.java:65
  Linea: _service = (it.sistinf.albedoweb.services.fondo.gestioneseparata.service.GestioneSeparataService)ctx.lookup("java:comp/env/service/GestioneSeparataService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/GestioneTabelleTabelleVitaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/gestionetabelle/tabellevita/service/GestioneTabelleTabelleVitaInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaService)ctx.lookup("java:comp/env/service/GestioneTabelleTabelleVitaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/LocalitaStradarioService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/localita/stradario/service/LocalitaStradarioInterfacePortProxy.java:75
  Linea: _service = (it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioService)ctx.lookup("java:comp/env/service/LocalitaStradarioService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/LocalitaViarioService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/localita/viario/service/LocalitaViarioInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.localita.viario.service.LocalitaViarioService)ctx.lookup("java:comp/env/service/LocalitaViarioService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/MessaggioReteVenditaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/retevendita/messaggio/service/MessaggioReteVenditaInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.retevendita.messaggio.service.MessaggioReteVenditaService)ctx.lookup("java:comp/env/service/MessaggioReteVenditaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ModelloMatematicoFormulaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/modellomatematico/formula/service/ModelloMatematicoFormulaInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaService)ctx.lookup("java:comp/env/service/ModelloMatematicoFormulaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/MonitorGidService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/monitor/gid/service/MonitorGidInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.monitor.gid.service.MonitorGidService)ctx.lookup("java:comp/env/service/MonitorGidService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/MovimentiContabiliOasiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/oasi/movimenticontabili/service/MovimentiContabiliOasiInterfacePortProxy.java:63
  Linea: _service = (it.sistinf.albedoweb.services.oasi.movimenticontabili.service.MovimentiContabiliOasiService)ctx.lookup("java:comp/env/service/MovimentiContabiliOasiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/NumeratoriService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/numeratori/numeratori/service/NumeratoriInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.numeratori.numeratori.service.NumeratoriService)ctx.lookup("java:comp/env/service/NumeratoriService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/OpzioniConversioneODifferimentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/opzioniconversioneodifferimento/service/OpzioniConversioneODifferimentoInterfacePortProxy.java:48
  Linea: _service = (it.sistinf.albedoweb.services.polizza.opzioniconversioneodifferimento.service.OpzioniConversioneODifferimentoService)ctx.lookup("java:comp/env/service/OpzioniConversioneODifferimentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/OrchestratoreConfigurazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/orchestratore/configurazione/service/OrchestratoreConfigurazioneInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.orchestratore.configurazione.service.OrchestratoreConfigurazioneService)ctx.lookup("java:comp/env/service/OrchestratoreConfigurazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PDNDService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/pdnd/service/PDNDInterfacePortProxy.java:69
  Linea: _service = (it.sistinf.albedoweb.services.anagrafica.pdnd.service.PDNDService)ctx.lookup("java:comp/env/service/PDNDService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PaperlessService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/paperless/service/PaperlessInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.elis.paperless.service.PaperlessService)ctx.lookup("java:comp/env/service/PaperlessService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ParametriCollettivaService
============================================================

JNDI_LOOKUP (2 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/ParametriCollettivaInterfacePortProxy.java:65
  Linea: _service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup("java:comp/env/service/ParametriCollettivaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']

  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/PercipientiInterfacePortProxy.java:59
  Linea: _service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup("java:comp/env/service/ParametriCollettivaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PercipientiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/percipienti/service/PercipientiInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.polizza.percipienti.service.PercipientiService)ctx.lookup("java:comp/env/service/PercipientiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PianiSpostamentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/pianispostamento/service/PianiSpostamentoInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoService)ctx.lookup("java:comp/env/service/PianiSpostamentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaAccountingService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/accounting/service/PolizzaAccountingInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.polizza.accounting.service.PolizzaAccountingService)ctx.lookup("java:comp/env/service/PolizzaAccountingService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaCambioFrazionamentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/cambiofrazionamento/service/PolizzaCambioFrazionamentoInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.polizza.cambiofrazionamento.service.PolizzaCambioFrazionamentoService)ctx.lookup("java:comp/env/service/PolizzaCambioFrazionamentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaCambioRuoloService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/cambioruolo/service/PolizzaCambioRuoloInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.polizza.cambioruolo.service.PolizzaCambioRuoloService)ctx.lookup("java:comp/env/service/PolizzaCambioRuoloService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaCoassicurazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/coassicurazione/service/PolizzaCoassicurazioneInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.polizza.coassicurazione.service.PolizzaCoassicurazioneService)ctx.lookup("java:comp/env/service/PolizzaCoassicurazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaCollettoreService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/collettore/service/PolizzaCollettoreInterfacePortProxy.java:61
  Linea: _service = (it.sistinf.albedoweb.services.polizza.collettore.service.PolizzaCollettoreService)ctx.lookup("java:comp/env/service/PolizzaCollettoreService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaCostiAnnuiRendicontatiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/costiannuirendicontati/service/PolizzaCostiAnnuiRendicontatiInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.polizza.costiannuirendicontati.service.PolizzaCostiAnnuiRendicontatiService)ctx.lookup("java:comp/env/service/PolizzaCostiAnnuiRendicontatiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaDurService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/dur/service/PolizzaDurInterfacePortProxy.java:61
  Linea: _service = (it.sistinf.albedoweb.services.polizza.dur.service.PolizzaDurService)ctx.lookup("java:comp/env/service/PolizzaDurService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaGestioneRidService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/gestionerid/service/PolizzaGestioneRidInterfacePortProxy.java:76
  Linea: _service = (it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidService)ctx.lookup("java:comp/env/service/PolizzaGestioneRidService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaLimitazioneOperativitaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/limitazioneoperativita/service/PolizzaLimitazioneOperativitaInterfacePortProxy.java:65
  Linea: _service = (it.sistinf.albedoweb.services.polizza.limitazioneoperativita.service.PolizzaLimitazioneOperativitaService)ctx.lookup("java:comp/env/service/PolizzaLimitazioneOperativitaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaLiquizioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/liquidazione/service/PolizzaLiquizioneInterfacePortProxy.java:89
  Linea: _service = (it.sistinf.albedoweb.services.polizza.liquidazione.service.PolizzaLiquizioneService)ctx.lookup("java:comp/env/service/PolizzaLiquizioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaMgmFeeService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/mgmfee/service/PolizzaMgmFeeInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.polizza.mgmfee.service.PolizzaMgmFeeService)ctx.lookup("java:comp/env/service/PolizzaMgmFeeService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaMovimentazioniULService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/movimentazioniul/service/PolizzaMovimentazioniULInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.polizza.movimentazioniul.service.PolizzaMovimentazioniULService)ctx.lookup("java:comp/env/service/PolizzaMovimentazioniULService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaMovimentiContabiliService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/movimenticontabili/service/PolizzaMovimentiContabiliInterfacePortProxy.java:57
  Linea: _service = (it.sistinf.albedoweb.services.polizza.movimenticontabili.service.PolizzaMovimentiContabiliService)ctx.lookup("java:comp/env/service/PolizzaMovimentiContabiliService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaOpzioniContrattualiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/opzionicontrattuali/service/PolizzaOpzioniContrattualiInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiService)ctx.lookup("java:comp/env/service/PolizzaOpzioniContrattualiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaPianoVersamentiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/pianoversamenti/service/PolizzaPianoVersamentiInterfacePortProxy.java:95
  Linea: _service = (it.sistinf.albedoweb.services.polizza.pianoversamenti.service.PolizzaPianoVersamentiService)ctx.lookup("java:comp/env/service/PolizzaPianoVersamentiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaPolizzaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/polizza/service/PolizzaPolizzaInterfacePortProxy.java:283
  Linea: _service = (it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaService)ctx.lookup("java:comp/env/service/PolizzaPolizzaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaPosizioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/posizione/service/PolizzaPosizioneInterfacePortProxy.java:127
  Linea: _service = (it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneService)ctx.lookup("java:comp/env/service/PolizzaPosizioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaRiattivazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/riattivazione/service/PolizzaRiattivazioneInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.polizza.riattivazione.service.PolizzaRiattivazioneService)ctx.lookup("java:comp/env/service/PolizzaRiattivazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaStorniService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/service/PolizzaStorniInterfacePortProxy.java:83
  Linea: _service = (it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniService)ctx.lookup("java:comp/env/service/PolizzaStorniService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaSwitchService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/_switch/service/PolizzaSwitchInterfacePortProxy.java:63
  Linea: _service = (it.sistinf.albedoweb.services.polizza._switch.service.PolizzaSwitchService)ctx.lookup("java:comp/env/service/PolizzaSwitchService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaTassazioneDatiCristallizzatiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/tassazione/daticristallizzati/service/PolizzaTassazioneDatiCristallizzatiInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.polizza.tassazione.daticristallizzati.service.PolizzaTassazioneDatiCristallizzatiService)ctx.lookup("java:comp/env/service/PolizzaTassazioneDatiCristallizzatiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PolizzaTrasferimentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/trasferimento/service/PolizzaTrasferimentoInterfacePortProxy.java:75
  Linea: _service = (it.sistinf.albedoweb.services.polizza.trasferimento.service.PolizzaTrasferimentoService)ctx.lookup("java:comp/env/service/PolizzaTrasferimentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PortafoglioBonificiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/bonifici/service/PortafoglioBonificiInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.portafoglio.bonifici.service.PortafoglioBonificiService)ctx.lookup("java:comp/env/service/PortafoglioBonificiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PortafoglioEstrazionePolizzaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/estrazione/service/PortafoglioEstrazionePolizzaInterfacePortProxy.java:55
  Linea: _service = (it.sistinf.albedoweb.services.portafoglio.estrazione.service.PortafoglioEstrazionePolizzaService)ctx.lookup("java:comp/env/service/PortafoglioEstrazionePolizzaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PortafoglioRapportoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/rapporto/service/PortafoglioRapportoInterfacePortProxy.java:103
  Linea: _service = (it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoService)ctx.lookup("java:comp/env/service/PortafoglioRapportoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PremiumTranchingService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/premiumtranching/service/PremiumTranchingInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.polizza.premiumtranching.service.PremiumTranchingService)ctx.lookup("java:comp/env/service/PremiumTranchingService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PrenotazioneDocumentazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/documentazione/service/PrenotazioneDocumentazioneInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.prenotazione.documentazione.service.PrenotazioneDocumentazioneService)ctx.lookup("java:comp/env/service/PrenotazioneDocumentazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PrenotazioneLiquidazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/liquidazione/service/PrenotazioneLiquidazioneInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.prenotazione.liquidazione.service.PrenotazioneLiquidazioneService)ctx.lookup("java:comp/env/service/PrenotazioneLiquidazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PrenotazionePostVenditaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/postvendita/service/PrenotazionePostVenditanterfacePortProxy.java:87
  Linea: _service = (it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaService)ctx.lookup("java:comp/env/service/PrenotazionePostVenditaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PrenotazioneTrasfAgenziaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/trasfagenzia/service/PrenotazioneTrasfAgenziaInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.prenotazione.trasfagenzia.service.PrenotazioneTrasfAgenziaService)ctx.lookup("java:comp/env/service/PrenotazioneTrasfAgenziaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PrintDataService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/eventi/printdata/service/PrintDataServiceInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.eventi.printdata.service.PrintDataService)ctx.lookup("java:comp/env/service/PrintDataService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ProdottoFormulaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/formula/service/ProdottoFormulaInterfacePortProxy.java:59
  Linea: _service = (it.sistinf.albedoweb.services.prodotto.formula.service.ProdottoFormulaService)ctx.lookup("java:comp/env/service/ProdottoFormulaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ProdottoProdottoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/prodotto/service/ProdottoProdottoInterfacePortProxy.java:153
  Linea: _service = (it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoService)ctx.lookup("java:comp/env/service/ProdottoProdottoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ProdottoTariffaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/tariffa/service/ProdottoTariffaInterfacePortProxy.java:71
  Linea: _service = (it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaService)ctx.lookup("java:comp/env/service/ProdottoTariffaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ProdottoVettoreService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/vettore/service/ProdottoVettoreInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.prodotto.vettore.service.ProdottoVettoreService)ctx.lookup("java:comp/env/service/ProdottoVettoreService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ProfiloInvestimentoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/profiloinvestimento/service/ProfiloInvestimentoInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.fondo.profiloinvestimento.service.ProfiloInvestimentoService)ctx.lookup("java:comp/env/service/ProfiloInvestimentoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/PropostaPropostaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/proposta/proposta/service/PropostaPropostaInterfacePortProxy.java:155
  Linea: _service = (it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaService)ctx.lookup("java:comp/env/service/PropostaPropostaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/QuestionarioAuraService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/questionario/aura/service/QuestionarioAuraInterfacePortProxy.java:59
  Linea: _service = (it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraService)ctx.lookup("java:comp/env/service/QuestionarioAuraService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/QuestionarioStrutturaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/questionario/struttura/service/QuestionarioStrutturaInterfacePortProxy.java:81
  Linea: _service = (it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaService)ctx.lookup("java:comp/env/service/QuestionarioStrutturaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RaggruppamentoFondiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/raggruppamento/service/RaggruppamentoFondiInterfacePortProxy.java:83
  Linea: _service = (it.sistinf.albedoweb.services.fondo.raggruppamento.service.RaggruppamentoFondiService)ctx.lookup("java:comp/env/service/RaggruppamentoFondiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RctService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/interfacce/contabilita/rct/service/RctInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.interfacce.contabilita.rct.service.RctService)ctx.lookup("java:comp/env/service/RctService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RelazioneFigureRapportoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/relazionefigurerapporto/service/RelazioneFigureRapportoInterfacePortProxy.java:53
  Linea: _service = (it.sistinf.albedoweb.services.polizza.relazionefigurerapporto.service.RelazioneFigureRapportoService)ctx.lookup("java:comp/env/service/RelazioneFigureRapportoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RemoteCommandService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/utils/remotecommand/service/RemoteCommandInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.utils.remotecommand.service.RemoteCommandService)ctx.lookup("java:comp/env/service/RemoteCommandService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ReportPrenotazioniService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/report/prenotazioni/service/ReportPrenotazioniInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.report.prenotazioni.service.ReportPrenotazioniService)ctx.lookup("java:comp/env/service/ReportPrenotazioniService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RestrizioneAccessoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/restrizioneaccesso/service/RestrizioneAccessoInterfacePortProxy.java:59
  Linea: _service = (it.sistinf.albedoweb.services.portafoglio.restrizioneaccesso.service.RestrizioneAccessoService)ctx.lookup("java:comp/env/service/RestrizioneAccessoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RiallocazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/riallocazione/service/RiallocazioneInterfacePortProxy.java:67
  Linea: _service = (it.sistinf.albedoweb.services.polizza.riallocazione.service.RiallocazioneService)ctx.lookup("java:comp/env/service/RiallocazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/RibilanciamentoFondiService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/service/RibilanciamentoFondiInterfacePortProxy.java:115
  Linea: _service = (it.sistinf.albedoweb.services.fondo.ribilanciamento.service.RibilanciamentoFondiService)ctx.lookup("java:comp/env/service/RibilanciamentoFondiService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/StampaEsternaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/stampa/esterna/service/StampaEsternaInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.stampa.esterna.service.StampaEsternaService)ctx.lookup("java:comp/env/service/StampaEsternaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/StampaService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/core/stampe/service/StampaInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.core.stampe.service.StampaService)ctx.lookup("java:comp/env/service/StampaService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/StampaVariabileService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/stampa/variabile/service/StampaVariabileInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.stampa.variabile.service.StampaVariabileService)ctx.lookup("java:comp/env/service/StampaVariabileService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/StrutturaReteService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/struttura/rete/service/StrutturaReteInterfacePortProxy.java:89
  Linea: _service = (it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteService)ctx.lookup("java:comp/env/service/StrutturaReteService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/SyncroCustomerService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/syncrocustomer/service/SyncroCustomerInterfacePortProxy.java:47
  Linea: _service = (it.sistinf.albedoweb.services.elis.syncrocustomer.service.SyncroCustomerService)ctx.lookup("java:comp/env/service/SyncroCustomerService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/SyncroNawSyncroRelService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/syncro/nawsyncrorel/service/SyncroNawSyncroRelInterfacePortProxy.java:45
  Linea: _service = (it.sistinf.albedoweb.services.syncro.nawsyncrorel.service.SyncroNawSyncroRelService)ctx.lookup("java:comp/env/service/SyncroNawSyncroRelService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/SyncroPolicyService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/syncropolicy/service/SyncroPolicyInterfacePortProxy.java:49
  Linea: _service = (it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyService)ctx.lookup("java:comp/env/service/SyncroPolicyService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/TabellaTracciatoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/service/TabellaTracciatoInterfacePortProxy.java:169
  Linea: _service = (it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoService)ctx.lookup("java:comp/env/service/TabellaTracciatoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/TrascodificaDominiEasyService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/easy/trascodifica/service/TrascodificaDominiEasyInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.easy.trascodifica.service.TrascodificaDominiEasyService)ctx.lookup("java:comp/env/service/TrascodificaDominiEasyService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/UnderwritingService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/proposta/underwriting/service/UnderwritingInterfacePortProxy.java:51
  Linea: _service = (it.sistinf.albedoweb.services.proposta.underwriting.service.UnderwritingService)ctx.lookup("java:comp/env/service/UnderwritingService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/VariazioneRischioAssicuratoService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/variazionerischioassicurato/service/VariazioneRischioAssicuratoInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.polizza.variazionerischioassicurato.service.VariazioneRischioAssicuratoService)ctx.lookup("java:comp/env/service/VariazioneRischioAssicuratoService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/VariazioneService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/variazione/service/VariazioneInterfacePortProxy.java:43
  Linea: _service = (it.sistinf.albedoweb.services.polizza.variazione.service.VariazioneService)ctx.lookup("java:comp/env/service/VariazioneService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: java:comp/env/service/ZbReteService
============================================================

JNDI_LOOKUP (1 occorrenze):
----------------------------------------
  File: Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/zbank/zbrete/service/ZbReteInterfacePortProxy.java:67
  Linea: _service = (it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteService)ctx.lookup("java:comp/env/service/ZbReteService");
  Pattern: lookup\s*\(\s*["\']([^"\']+)["\']


============================================================
SERVIZIO: jndiDatasourceName
============================================================

DATABASE_CONFIG (1 occorrenze):
----------------------------------------
  File: Services.Common.Persistency/src/spring-conf/datasource.xml:10
  Linea: <property name="jndiName" value="${jndiDatasourceName}" />
  Pattern: <property\s+name="jndiName"\s+value="\$\{([^}]+)\}


============================================================
SERVIZIO: prefixJNDINameEJBProxy
============================================================

DATABASE_CONFIG (32 occorrenze):
----------------------------------------
  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:9
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PortafoglioRapporto"/>
  Pattern: <property\s+name="jndiName"\s+value="\$\{([^}]+)\}

  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:15
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}PropostaProposta"/>
  Pattern: <property\s+name="jndiName"\s+value="\$\{([^}]+)\}

  File: REST.Services.Cobol.Web/src/spring-conf/external-services.xml:21
  Linea: <property name="jndiName" value="${prefixJNDINameEJBProxy}AbilitazioneUtente"/>
  Pattern: <property\s+name="jndiName"\s+value="\$\{([^}]+)\}

  ... e altri 29 esempi


============================================================
STATISTICHE PER CATEGORIA
============================================================

EJB_PROXY_USAGE:
  Totale chiamate: 275
  Servizi unici: 172
  Top 5 servizi:
    DominiBusinessDelegate: 29 chiamate
    DominiProxy: 19 chiamate
    TabellaTracciatoProxy: 6 chiamate
    StrutturaReteProxy: 5 chiamate
    CustomerProxy: 5 chiamate

JNDI_LOOKUP:
  Totale chiamate: 170
  Servizi unici: 169
  Top 5 servizi:
    java:comp/env/service/ParametriCollettivaService: 2 chiamate
    ${prefixJNDINameEJBProxy}PortafoglioRapporto: 1 chiamate
    ${prefixJNDINameEJBProxy}PropostaProposta: 1 chiamate
    ${prefixJNDINameEJBProxy}AbilitazioneUtente: 1 chiamate
    ${prefixJNDINameEJBProxy}AbilitazioneAzione: 1 chiamate

EJB_PROXY_DEFINITION:
  Totale chiamate: 57
  Servizi unici: 57
  Top 5 servizi:
    PortafoglioRapporto: 1 chiamate
    PropostaProposta: 1 chiamate
    AbilitazioneUtente: 1 chiamate
    AbilitazioneAzione: 1 chiamate
    StrutturaRete: 1 chiamate

REST_ENDPOINT:
  Totale chiamate: 46
  Servizi unici: 44
  Top 5 servizi:
    ${Oasi.endpoint}: 2 chiamate
    ${Easy.endpoint}: 2 chiamate
    /polizza: 1 chiamate
    /funzioniPolizza: 1 chiamate
    /grigliaCompatibilita: 1 chiamate

DATABASE_CONFIG:
  Totale chiamate: 35
  Servizi unici: 3
  Top 5 servizi:
    prefixJNDINameEJBProxy: 32 chiamate
    ${applicationDatasource}: 2 chiamate
    jndiDatasourceName: 1 chiamate

WEB_SERVICE_DEFINITION:
  Totale chiamate: 8
  Servizi unici: 7
  Top 5 servizi:
    AnagrafeEasyWSProxy: 2 chiamate
    MovimentiOasiWSProxy: 1 chiamate
    SyncroWSProxy: 1 chiamate
    CheopeWSProxy: 1 chiamate
    CodeMqWSProxy: 1 chiamate
