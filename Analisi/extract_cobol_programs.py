#!/usr/bin/env python3
"""
extract_cobol_programs.py

Estrae i nomi dei programmi COBOL dai file di configurazione XML.
Analizza i file di regole per identificare tutti i programmi COBOL chiamati.

Output:
  - **File CSV**: `cobol_programs.csv` con dettagli dei programmi
  - **File di testo**: `cobol_programs_summary.txt` con riassunto

Prerequisiti:
  - Python 3.6+
"""
import os
import re
import csv
import xml.etree.ElementTree as ET
from collections import defaultdict

# Configurazione
SRC_PATH = "/Users/<USER>/Desktop/Conding/NAW/Source"
RULES_PATH = os.path.join(SRC_PATH, "Lib/shared-props/rules")
OUTPUT_DIR = "/Users/<USER>/Desktop/Conding/NAW/Analisi"
OUTPUT_CSV = os.path.join(OUTPUT_DIR, 'cobol_programs.csv')
OUTPUT_SUMMARY = os.path.join(OUTPUT_DIR, 'cobol_programs_summary.txt')

# Pattern per identificare nomi di programmi COBOL
COBOL_PROGRAM_PATTERNS = [
    r'<program>([A-Z0-9]+)</program>',
    r'<initialProgram>([A-Z0-9]+)</initialProgram>',
    r'program="([A-Z0-9]+)"',
    r'initialProgram="([A-Z0-9]+)"'
]

# Pattern per transazioni CICS
TRANSACTION_PATTERNS = [
    r'<transaction>([A-Z0-9]+)</transaction>',
    r'<initialTransaction>([A-Z0-9]+)</initialTransaction>',
    r'transaction="([A-Z0-9]+)"',
    r'initialTransaction="([A-Z0-9]+)"'
]

# Pattern per connector ID
CONNECTOR_PATTERNS = [
    r'<connectorId>([A-Z0-9]+)</connectorId>',
    r'connectorId="([A-Z0-9]+)"'
]


def extract_cobol_programs_from_xml(file_path):
    """
    Estrae programmi COBOL da un file XML di configurazione.
    Restituisce lista di dizionari con informazioni sui programmi.
    """
    programs = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Cerca pattern nei file XML
        for line_num, line in enumerate(content.split('\n'), 1):
            line_stripped = line.strip()
            
            # Estrai programmi COBOL
            for pattern in COBOL_PROGRAM_PATTERNS:
                matches = re.finditer(pattern, line_stripped, re.IGNORECASE)
                for match in matches:
                    program_name = match.group(1)
                    
                    # Cerca informazioni aggiuntive nel contesto
                    rule_id = extract_rule_id(content, line_num)
                    transaction = extract_transaction_from_context(content, line_num)
                    connector = extract_connector_from_context(content, line_num)
                    
                    programs.append({
                        'file': file_path,
                        'line': line_num,
                        'rule_id': rule_id,
                        'program_name': program_name,
                        'program_type': 'MAIN' if 'program>' in line else 'INITIAL',
                        'transaction': transaction,
                        'connector': connector,
                        'line_content': line_stripped,
                        'context': extract_context(content.split('\n'), line_num - 1)
                    })
                    
    except Exception as e:
        print(f"Errore leggendo {file_path}: {e}")
    
    return programs


def extract_rule_id(content, line_num):
    """Estrae l'ID della regola dal contesto."""
    lines = content.split('\n')
    
    # Cerca verso l'alto per trovare <id>
    for i in range(max(0, line_num - 20), line_num):
        if i < len(lines):
            match = re.search(r'<id>([^<]+)</id>', lines[i])
            if match:
                return match.group(1)
    return ""


def extract_transaction_from_context(content, line_num):
    """Estrae la transazione dal contesto."""
    lines = content.split('\n')
    
    # Cerca nelle linee vicine
    for i in range(max(0, line_num - 5), min(len(lines), line_num + 5)):
        for pattern in TRANSACTION_PATTERNS:
            match = re.search(pattern, lines[i])
            if match:
                return match.group(1)
    return ""


def extract_connector_from_context(content, line_num):
    """Estrae il connector ID dal contesto."""
    lines = content.split('\n')
    
    # Cerca nelle linee vicine
    for i in range(max(0, line_num - 5), min(len(lines), line_num + 5)):
        for pattern in CONNECTOR_PATTERNS:
            match = re.search(pattern, lines[i])
            if match:
                return match.group(1)
    return ""


def extract_context(lines, line_index):
    """Estrae il contesto intorno alla linea specificata."""
    start = max(0, line_index - 2)
    end = min(len(lines), line_index + 3)
    
    context_lines = []
    for i in range(start, end):
        prefix = ">>> " if i == line_index else "    "
        context_lines.append(f"{prefix}{i+1:4d}: {lines[i].strip()}")
    
    return "\n".join(context_lines)


def scan_all_xml_files(rules_path):
    """
    Scansiona tutti i file XML nella directory delle regole.
    Restituisce lista di tutti i programmi COBOL trovati.
    """
    all_programs = []
    
    for root, dirs, files in os.walk(rules_path):
        for file in files:
            if file.lower().endswith('.xml'):
                file_path = os.path.join(root, file)
                programs = extract_cobol_programs_from_xml(file_path)
                all_programs.extend(programs)
    
    return all_programs


def write_programs_csv(programs, output_file):
    """Scrive i risultati in formato CSV."""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([
            'Rule_ID', 'Program_Name', 'Program_Type', 'Transaction', 
            'Connector', 'File_Path', 'Line_Number', 'Line_Content', 'Context'
        ])
        
        for program in programs:
            relative_path = program['file'].replace(SRC_PATH, '').lstrip('/')
            writer.writerow([
                program['rule_id'],
                program['program_name'],
                program['program_type'],
                program['transaction'],
                program['connector'],
                relative_path,
                program['line'],
                program['line_content'],
                program['context'].replace('\n', ' | ')
            ])


def write_programs_summary(programs, output_file):
    """Scrive un riassunto dei programmi COBOL trovati."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("RIASSUNTO PROGRAMMI COBOL IDENTIFICATI\n")
        f.write("=" * 60 + "\n\n")
        
        # Raggruppa per programma
        program_rules = defaultdict(list)
        for program in programs:
            program_rules[program['program_name']].append(program)
        
        f.write(f"Totale programmi COBOL unici: {len(program_rules)}\n")
        f.write(f"Totale chiamate ai programmi: {len(programs)}\n\n")
        
        # Scrivi dettagli per ogni programma
        for program_name, calls in sorted(program_rules.items()):
            f.write(f"\n{'='*60}\n")
            f.write(f"PROGRAMMA COBOL: {program_name} ({len(calls)} chiamate)\n")
            f.write(f"{'='*60}\n")
            
            # Raggruppa per tipo
            by_type = defaultdict(list)
            for call in calls:
                by_type[call['program_type']].append(call)
            
            for prog_type, type_calls in by_type.items():
                f.write(f"\n{prog_type} ({len(type_calls)} chiamate):\n")
                f.write("-" * 40 + "\n")
                
                for call in type_calls[:5]:  # Mostra solo i primi 5
                    relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
                    f.write(f"  Regola: {call['rule_id']}\n")
                    f.write(f"  File: {relative_path}:{call['line']}\n")
                    f.write(f"  Transazione: {call['transaction']}\n")
                    f.write(f"  Connector: {call['connector']}\n")
                    f.write(f"  Linea: {call['line_content']}\n\n")
                
                if len(type_calls) > 5:
                    f.write(f"  ... e altri {len(type_calls) - 5} esempi\n\n")
        
        # Statistiche per transazione
        f.write(f"\n{'='*60}\n")
        f.write("STATISTICHE PER TRANSAZIONE\n")
        f.write(f"{'='*60}\n")
        
        transaction_counts = defaultdict(int)
        for program in programs:
            if program['transaction']:
                transaction_counts[program['transaction']] += 1
        
        f.write("\nTransazioni più utilizzate:\n")
        for transaction, count in sorted(transaction_counts.items(), key=lambda x: x[1], reverse=True):
            f.write(f"  {transaction}: {count} programmi\n")
        
        # Statistiche per connector
        f.write(f"\nSTATISTICHE PER CONNECTOR\n")
        f.write("-" * 40 + "\n")
        
        connector_counts = defaultdict(int)
        for program in programs:
            if program['connector']:
                connector_counts[program['connector']] += 1
        
        f.write("\nConnector più utilizzati:\n")
        for connector, count in sorted(connector_counts.items(), key=lambda x: x[1], reverse=True):
            f.write(f"  {connector}: {count} programmi\n")


def main():
    # Verifica directories
    if not os.path.isdir(RULES_PATH):
        print(f"Errore: '{RULES_PATH}' non è una directory valida.")
        return
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("Estrazione programmi COBOL in corso...")
    print(f"Directory regole: {RULES_PATH}")
    print(f"Directory output: {OUTPUT_DIR}")
    print()

    # Estrazione
    programs = scan_all_xml_files(RULES_PATH)
    
    if not programs:
        print("Nessun programma COBOL rilevato.")
        return

    # Calcola totali
    unique_programs = len(set(program['program_name'] for program in programs))
    unique_rules = len(set(program['rule_id'] for program in programs))
    
    print(f"Trovati {len(programs)} riferimenti a programmi COBOL")
    print(f"Programmi COBOL unici: {unique_programs}")
    print(f"Regole che utilizzano COBOL: {unique_rules}")
    print()
    
    # Scrittura file CSV
    write_programs_csv(programs, OUTPUT_CSV)
    print(f"Dettagli salvati in: {OUTPUT_CSV}")
    
    # Scrittura riassunto
    write_programs_summary(programs, OUTPUT_SUMMARY)
    print(f"Riassunto salvato in: {OUTPUT_SUMMARY}")
    
    print("\nEstrazione completata!")


if __name__ == '__main__':
    main()
