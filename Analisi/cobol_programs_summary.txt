RIASSUNTO PROGRAMMI COBOL IDENTIFICATI
============================================================

Totale programmi COBOL unici: 857
Totale chiamate ai programmi: 1759


============================================================
PROGRAMMA COBOL: AWLSE012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-INCASSO-TITOLO-AR
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.controlloIncassoTitoloRataScaduta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE012</program>


============================================================
PROGRAMMA COBOL: AWLSE013 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RIPORTA-INCASSO-TITOLO-AR
  File: Lib/shared-props/rules/incasso/incasso.antiriclaggio.riportaIncassoTitoloRataScaduta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE013</program>


============================================================
PROGRAMMA COBOL: AWLSE014 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-PROPOSTA-CONTROLLA-DATI
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.proposta.controllaDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE014</program>


============================================================
PROGRAMMA COBOL: AWLSE015 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-PROPOSTA-RIPORTA-DATI
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.proposta.riportaDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE015</program>


============================================================
PROGRAMMA COBOL: AWLSE016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-DIRETTO-CONTROLLA-DATI
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.incassoDiretto.controllaDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE016</program>


============================================================
PROGRAMMA COBOL: AWLSE017 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-DIRETTO-RIPORTA-DATI
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.incassoDiretto.riportaDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE017</program>


============================================================
PROGRAMMA COBOL: AWLSE018 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INIT-NUOVA-RICHIESTA-RID
  File: Lib/shared-props/rules/contabilita/rid/richiesta/richiestaRid.inizializza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE018</program>


============================================================
PROGRAMMA COBOL: AWLSE019 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-RICHIESTA-RID
  File: Lib/shared-props/rules/contabilita/rid/richiesta/richiestaRid.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE019</program>


============================================================
PROGRAMMA COBOL: AWLSE020 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DELEGA-RID
  File: Lib/shared-props/rules/contabilita/rid/gestione/gestioneRid.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE020</program>


============================================================
PROGRAMMA COBOL: AWLSE021 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLI-PRELIMINARI-DELEGA-RID
  File: Lib/shared-props/rules/contabilita/rid/gestione/gestioneRid.controlliPreliminari.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE021</program>


============================================================
PROGRAMMA COBOL: AWLSE022 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-DELEGA-RID
  File: Lib/shared-props/rules/contabilita/rid/gestione/gestioneRid.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE022</program>


============================================================
PROGRAMMA COBOL: AWLSE023 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DISPOSIZIONI-INCASSO-RID
  File: Lib/shared-props/rules/contabilita/rid/disposizioniIncasso/disposizioniIncassoRid.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE023</program>


============================================================
PROGRAMMA COBOL: AWLSE024 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-INCASSA-TITOLO
  File: Lib/shared-props/rules/incasso/incasso.eseguiIncassoTitolo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE024</program>


============================================================
PROGRAMMA COBOL: AWLSE025 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-STORNA-TITOLO
  File: Lib/shared-props/rules/incasso/incasso.eseguiStornoTitolo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE025</program>


============================================================
PROGRAMMA COBOL: AWLSE026 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-STORNA-INCASSO
  File: Lib/shared-props/rules/incasso/incasso.eseguiStornoIncasso.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE026</program>


============================================================
PROGRAMMA COBOL: AWLSE027 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-MOD-PAGAMENTO-TITOLO
  File: Lib/shared-props/rules/incasso/incasso.caricaModPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE027</program>


============================================================
PROGRAMMA COBOL: AWLSE028 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-INCASSA-TITOLO-MOD-PAGAMENTO
  File: Lib/shared-props/rules/incasso/incasso.eseguiIncassoTitoloModPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSE028</program>


============================================================
PROGRAMMA COBOL: AWLSL005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-GESTIONE-RID
  File: Lib/shared-props/rules/contabilita/rid/gestione/gestioneRid.ricerca.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSL005</program>


============================================================
PROGRAMMA COBOL: AWLSL006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-DISPOSIZIONI-INCASSO-RID
  File: Lib/shared-props/rules/contabilita/rid/disposizioniIncasso/disposizioniIncassoRid.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>AWLSL006</program>


============================================================
PROGRAMMA COBOL: BWLSE002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FASE-BATCH-PRIMARIE-DETTAGLIO-RIVALUTAZ-INDICIZ
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.rivalutazione.carica.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE002</program>


============================================================
PROGRAMMA COBOL: BWLSE003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FASE-BATCH-PRIMARIE-SALVA-RIVALUTAZ-INDICIZ
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.rivalutazione.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE003</program>


============================================================
PROGRAMMA COBOL: BWLSE004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FASE-BATCH-PRIMARIE-DETTAGLIO-QUIETANZAMENTO
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.quietanzamento.carica.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE004</program>


============================================================
PROGRAMMA COBOL: BWLSE005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FASE-BATCH-PRIMARIE-SALVA-QUIETANZAMENTO
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.quietanzamento.salva.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE005</program>


============================================================
PROGRAMMA COBOL: BWLSE006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-RISERVA-MATEMATICA
  File: Lib/shared-props/rules/fasebatch/primarie/riservaMatematica/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE006</program>


============================================================
PROGRAMMA COBOL: BWLSE007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-RISERVA-MATEMATICA
  File: Lib/shared-props/rules/fasebatch/primarie/riservaMatematica/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE007</program>


============================================================
PROGRAMMA COBOL: BWLSE008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-MODELLI-MINISTERIALI
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.modelliMinisteriali.carica.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE008</program>


============================================================
PROGRAMMA COBOL: BWLSE009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-MODELLI-MINISTERIALI
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.modelliMinisteriali.salva.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE009</program>


============================================================
PROGRAMMA COBOL: BWLSE012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-BOLLATI
  File: Lib/shared-props/rules/fasebatch/secondarie/bollati/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE012</program>


============================================================
PROGRAMMA COBOL: BWLSE013 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-BOLLATI
  File: Lib/shared-props/rules/fasebatch/secondarie/bollati/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE013</program>


============================================================
PROGRAMMA COBOL: BWLSE014 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-RIASSICURAZIONE
  File: Lib/shared-props/rules/fasebatch/secondarie/riassicurazione/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE014</program>


============================================================
PROGRAMMA COBOL: BWLSE015 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-RIASSICURAZIONE
  File: Lib/shared-props/rules/fasebatch/secondarie/riassicurazione/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE015</program>


============================================================
PROGRAMMA COBOL: BWLSE018 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-LETTERE-FISCALI
  File: Lib/shared-props/rules/fasebatch/secondarie/lettereFiscali/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE018</program>


============================================================
PROGRAMMA COBOL: BWLSE019 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-LETTERE-FISCALI
  File: Lib/shared-props/rules/fasebatch/secondarie/lettereFiscali/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE019</program>


============================================================
PROGRAMMA COBOL: BWLSE020 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-STATISTICA
  File: Lib/shared-props/rules/fasebatch/secondarie/statistiche/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE020</program>


============================================================
PROGRAMMA COBOL: BWLSE021 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-STATISTICA
  File: Lib/shared-props/rules/fasebatch/secondarie/statistiche/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE021</program>


============================================================
PROGRAMMA COBOL: BWLSE024 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-POLIZZE-IN-SCADENZA
  File: Lib/shared-props/rules/fasebatch/secondarie/polizzeInScadenza/caricaPolizzeInScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE024</program>


============================================================
PROGRAMMA COBOL: BWLSE025 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-POLIZZE-IN-SCADENZA
  File: Lib/shared-props/rules/fasebatch/secondarie/polizzeInScadenza/salvaDettaglioPolizzeInScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE025</program>


============================================================
PROGRAMMA COBOL: BWLSE036 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-ANAGRAFE-TRIB
  File: Lib/shared-props/rules/fasebatch/secondarie/anagrafeTributaria/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE036</program>


============================================================
PROGRAMMA COBOL: BWLSE037 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-ANAGRAFE-TRIB
  File: Lib/shared-props/rules/fasebatch/secondarie/anagrafeTributaria/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE037</program>


============================================================
PROGRAMMA COBOL: BWLSE038 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-NUOVA-FISCALITA
  File: Lib/shared-props/rules/fasebatch/secondarie/nuovaFiscalita/caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE038</program>


============================================================
PROGRAMMA COBOL: BWLSE039 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-NUOVA-FISCALITA
  File: Lib/shared-props/rules/fasebatch/secondarie/nuovaFiscalita/salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE039</program>


============================================================
PROGRAMMA COBOL: BWLSE046 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-GESTIONE-SCADENZE
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneScadenze.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE046</program>


============================================================
PROGRAMMA COBOL: BWLSE047 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-GESTIONE-SCADENZE
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneScadenze.salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE047</program>


============================================================
PROGRAMMA COBOL: BWLSE048 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-GESTIONE-STORNI
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneStorni.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE048</program>


============================================================
PROGRAMMA COBOL: BWLSE049 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-GESTIONE-STORNI
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneStorni.salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE049</program>


============================================================
PROGRAMMA COBOL: BWLSE056 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FASE-BATCH-PRIMARIE-ELIMINA-RIVALUTAZ-INDICIZ
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.rivalutazione.elimina.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE056</program>


============================================================
PROGRAMMA COBOL: BWLSE058 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-RISERVA-MATEMATICA
  File: Lib/shared-props/rules/fasebatch/primarie/riservaMatematica/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE058</program>


============================================================
PROGRAMMA COBOL: BWLSE059 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DETTAGLIO-MODELLI-MINISTERIALI
  File: Lib/shared-props/rules/fasebatch/primarie/faseBatch.primarie.modelliMinisteriali.elimina.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE059</program>


============================================================
PROGRAMMA COBOL: BWLSE061 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DETTAGLIO-BOLLATI
  File: Lib/shared-props/rules/fasebatch/secondarie/bollati/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE061</program>


============================================================
PROGRAMMA COBOL: BWLSE062 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DETTAGLIO-RIASSICURAZIONE
  File: Lib/shared-props/rules/fasebatch/secondarie/riassicurazione/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE062</program>


============================================================
PROGRAMMA COBOL: BWLSE064 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-DETTAGLIO-LETTERE-FISCALI
  File: Lib/shared-props/rules/fasebatch/secondarie/lettereFiscali/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE064</program>


============================================================
PROGRAMMA COBOL: BWLSE065 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-DETTAGLIO-STATISTICA
  File: Lib/shared-props/rules/fasebatch/secondarie/statistiche/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE065</program>


============================================================
PROGRAMMA COBOL: BWLSE067 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-POLIZZE-IN-SCADENZA
  File: Lib/shared-props/rules/fasebatch/secondarie/polizzeInScadenza/eliminaDettaglioPolizzeInScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE067</program>


============================================================
PROGRAMMA COBOL: BWLSE073 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-ANAGRAFE-TRIB
  File: Lib/shared-props/rules/fasebatch/secondarie/anagrafeTributaria/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE073</program>


============================================================
PROGRAMMA COBOL: BWLSE074 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-NUOVA-FISCALITA
  File: Lib/shared-props/rules/fasebatch/secondarie/nuovaFiscalita/eliminaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE074</program>


============================================================
PROGRAMMA COBOL: BWLSE078 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-DETTAGLIO-GESTIONE-SCADENZE
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneScadenze.cancellaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE078</program>


============================================================
PROGRAMMA COBOL: BWLSE079 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-DETTAGLIO-GESTIONE-STORNI
  File: Lib/shared-props/rules/fasebatch/gestionePortafoglio/gestioneStorni.cancellaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSE079</program>


============================================================
PROGRAMMA COBOL: BWLSL102 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA_RICHIESTE_FASE_BATCH
  File: Lib/shared-props/rules/fasebatch/listaRichieste/listaRichiesteFaseBatch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>BWLSL102</program>


============================================================
PROGRAMMA COBOL: CWLSL001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-POLIZZE-DANNI
  File: Lib/shared-props/rules/danni/comuni/rapporti.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>CWLSL001</program>


============================================================
PROGRAMMA COBOL: DUMMYPROG (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PROGRAMMA-GENERICO
  File: Lib/shared-props/rules/testServizi/testServiziCobol.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DUMMYPROG</program>


============================================================
PROGRAMMA COBOL: DWLSE006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-MESSAGGIO
  File: Lib/shared-props/rules/diba/gestioneMessaggi/gestioneMessaggi.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE006</program>


============================================================
PROGRAMMA COBOL: DWLSE007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-MESSAGGIO
  File: Lib/shared-props/rules/diba/gestioneMessaggi/gestioneMessaggi.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE007</program>


============================================================
PROGRAMMA COBOL: DWLSE008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-MESSAGGIO
  File: Lib/shared-props/rules/diba/gestioneMessaggi/gestioneMessaggi.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE008</program>


============================================================
PROGRAMMA COBOL: DWLSE009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-MESSAGGIO
  File: Lib/shared-props/rules/diba/gestioneMessaggi/gestioneMessaggi.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE009</program>


============================================================
PROGRAMMA COBOL: DWLSE010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PARAMETRI-DIBA
  File: Lib/shared-props/rules/diba/parametri/parametri.diba.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE010</program>


============================================================
PROGRAMMA COBOL: DWLSE011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PARAMETRI-DIBA
  File: Lib/shared-props/rules/diba/parametri/parametri.diba.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE011</program>


============================================================
PROGRAMMA COBOL: DWLSE013 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-INDICE-ISTAT
  File: Lib/shared-props/rules/diba/indiciIstat/indiciIstat.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE013</program>


============================================================
PROGRAMMA COBOL: DWLSE014 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-INDICE-ISTAT
  File: Lib/shared-props/rules/diba/indiciIstat/indiciIstat.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE014</program>


============================================================
PROGRAMMA COBOL: DWLSE015 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-INDICE-ISTAT
  File: Lib/shared-props/rules/diba/indiciIstat/indiciIstat.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE015</program>


============================================================
PROGRAMMA COBOL: DWLSE017 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CONDIZIONE
  File: Lib/shared-props/rules/diba/condizioni/condizioni.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE017</program>


============================================================
PROGRAMMA COBOL: DWLSE018 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-CONDIZIONE
  File: Lib/shared-props/rules/diba/condizioni/condizioni.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE018</program>


============================================================
PROGRAMMA COBOL: DWLSE019 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-CONDIZIONE
  File: Lib/shared-props/rules/diba/condizioni/condizioni.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE019</program>


============================================================
PROGRAMMA COBOL: DWLSE020 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CONDIZIONE
  File: Lib/shared-props/rules/diba/condizioni/condizioni.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE020</program>


============================================================
PROGRAMMA COBOL: DWLSE022 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-SOCIETA
  File: Lib/shared-props/rules/diba/societa/societa.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE022</program>


============================================================
PROGRAMMA COBOL: DWLSE023 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-SOCIETA
  File: Lib/shared-props/rules/diba/societa/societa.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE023</program>


============================================================
PROGRAMMA COBOL: DWLSE024 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-SOCIETA
  File: Lib/shared-props/rules/diba/societa/societa.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE024</program>


============================================================
PROGRAMMA COBOL: DWLSE025 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-SOCIETA
  File: Lib/shared-props/rules/diba/societa/societa.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE025</program>


============================================================
PROGRAMMA COBOL: DWLSE027 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-PROFESSIONE
  File: Lib/shared-props/rules/diba/professione/professione.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE027</program>


============================================================
PROGRAMMA COBOL: DWLSE028 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-PROFESSIONE
  File: Lib/shared-props/rules/diba/professione/professione.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE028</program>


============================================================
PROGRAMMA COBOL: DWLSE029 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PROFESSIONE
  File: Lib/shared-props/rules/diba/professione/professione.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE029</program>


============================================================
PROGRAMMA COBOL: DWLSE030 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-SOTTOSISTEMA
  File: Lib/shared-props/rules/diba/sottosistemi/sottosistemi.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE030</program>


============================================================
PROGRAMMA COBOL: DWLSE031 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-SOTTOSISTEMA
  File: Lib/shared-props/rules/diba/sottosistemi/sottosistemi.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE031</program>


============================================================
PROGRAMMA COBOL: DWLSE036 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-SOTTOSISTEMA
  File: Lib/shared-props/rules/diba/sottosistemi/sottosistemi.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE036</program>


============================================================
PROGRAMMA COBOL: DWLSE037 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-CODICE-NUMERATORE
  File: Lib/shared-props/rules/diba/codiciNumeratore/codiciNumeratore.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE037</program>


============================================================
PROGRAMMA COBOL: DWLSE038 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-CODICE-NUMERATORE
  File: Lib/shared-props/rules/diba/codiciNumeratore/codiciNumeratore.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE038</program>


============================================================
PROGRAMMA COBOL: DWLSE039 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CODICE-NUMERATORE
  File: Lib/shared-props/rules/diba/codiciNumeratore/codiciNumeratore.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE039</program>


============================================================
PROGRAMMA COBOL: DWLSE040 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-DIVISE
  File: Lib/shared-props/rules/diba/divise/divise.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE040</program>


============================================================
PROGRAMMA COBOL: DWLSE041 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-DIVISE
  File: Lib/shared-props/rules/diba/divise/divise.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE041</program>


============================================================
PROGRAMMA COBOL: DWLSE042 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DIVISE
  File: Lib/shared-props/rules/diba/divise/divise.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE042</program>


============================================================
PROGRAMMA COBOL: DWLSE043 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-CAMBI
  File: Lib/shared-props/rules/diba/cambi/cambi.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE043</program>


============================================================
PROGRAMMA COBOL: DWLSE044 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-CAMBI
  File: Lib/shared-props/rules/diba/cambi/cambi.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE044</program>


============================================================
PROGRAMMA COBOL: DWLSE045 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CAMBI
  File: Lib/shared-props/rules/diba/cambi/cambi.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE045</program>


============================================================
PROGRAMMA COBOL: DWLSE046 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-GENERALI
  File: Lib/shared-props/rules/tabelleSistema/parametriGenerali/parametriGenerali.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE046</program>


============================================================
PROGRAMMA COBOL: DWLSE047 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PARAMETRI-GENERALI
  File: Lib/shared-props/rules/tabelleSistema/parametriGenerali/parametriGenerali.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE047</program>


============================================================
PROGRAMMA COBOL: DWLSE048 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATE-ELABORAZIONE
  File: Lib/shared-props/rules/tabelleSistema/dateElaborazione/dateElaborazione.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE048</program>


============================================================
PROGRAMMA COBOL: DWLSE049 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DATE-ELABORAZIONE
  File: Lib/shared-props/rules/tabelleSistema/dateElaborazione/dateElaborazione.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE049</program>


============================================================
PROGRAMMA COBOL: DWLSE050 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-MODALITA-PAGAMENTO
  File: Lib/shared-props/rules/tabelleSistema/modalitaPagamento/modalitaPagamento.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE050</program>


============================================================
PROGRAMMA COBOL: DWLSE051 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODALITA-PAGAMENTO
  File: Lib/shared-props/rules/tabelleSistema/modalitaPagamento/modalitaPagamento.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE051</program>


============================================================
PROGRAMMA COBOL: DWLSE052 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-MODALITA-PAGAMENTO
  File: Lib/shared-props/rules/tabelleSistema/modalitaPagamento/modalitaPagamento.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE052</program>


============================================================
PROGRAMMA COBOL: DWLSE054 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-CONVENZIONE
  File: Lib/shared-props/rules/tabelleSistema/convenzione/convenzioni.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE054</program>


============================================================
PROGRAMMA COBOL: DWLSE055 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-CONVENZIONE
  File: Lib/shared-props/rules/tabelleSistema/convenzione/convenzioni.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE055</program>


============================================================
PROGRAMMA COBOL: DWLSE056 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CONVENZIONE
  File: Lib/shared-props/rules/tabelleSistema/convenzione/convenzioni.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE056</program>


============================================================
PROGRAMMA COBOL: DWLSE057 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CONVENZIONE
  File: Lib/shared-props/rules/tabelleSistema/convenzione/convenzioni.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE057</program>


============================================================
PROGRAMMA COBOL: DWLSE058 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-PIANO-CONTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE058</program>


============================================================
PROGRAMMA COBOL: DWLSE059 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-DESCRIZIONI-VOCI
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.elencoVoci.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE059</program>


============================================================
PROGRAMMA COBOL: DWLSE060 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-PIANO-CONTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE060</program>


============================================================
PROGRAMMA COBOL: DWLSE061 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PIANO-CONTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE061</program>


============================================================
PROGRAMMA COBOL: DWLSE062 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PIANO-CONTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE062</program>


============================================================
PROGRAMMA COBOL: DWLSE063 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-FONDO-RIVALUTAZIONE
  File: Lib/shared-props/rules/tabelleSistema/fondiRivalutazione/fondiRivalutazione.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE063</program>


============================================================
PROGRAMMA COBOL: DWLSE064 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-FONDO-RIVALUTAZIONE
  File: Lib/shared-props/rules/tabelleSistema/fondiRivalutazione/fondiRivalutazione.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE064</program>


============================================================
PROGRAMMA COBOL: DWLSE065 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-FONDO-RIVALUTAZIONE
  File: Lib/shared-props/rules/tabelleSistema/fondiRivalutazione/fondiRivalutazione.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE065</program>


============================================================
PROGRAMMA COBOL: DWLSE066 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-FONDO-RIVALUTAZIONE
  File: Lib/shared-props/rules/tabelleSistema/fondiRivalutazione/fondiRivalutazione.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE066</program>


============================================================
PROGRAMMA COBOL: DWLSE068 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/fondiUL/fondiUL.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE068</program>


============================================================
PROGRAMMA COBOL: DWLSE069 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/fondiUL/fondiUL.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE069</program>


============================================================
PROGRAMMA COBOL: DWLSE070 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/fondiUL/fondiUL.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE070</program>


============================================================
PROGRAMMA COBOL: DWLSE071 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TRATTATO-COASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoCoass/trattatoCoass.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE071</program>


============================================================
PROGRAMMA COBOL: DWLSE072 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-TRATTATO-COASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoCoass/trattatoCoass.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE072</program>


============================================================
PROGRAMMA COBOL: DWLSE073 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-TRATTATO-COASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoCoass/trattatoCoass.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE073</program>


============================================================
PROGRAMMA COBOL: DWLSE074 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TRATTATO-COASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoCoass/trattatoCoass.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE074</program>


============================================================
PROGRAMMA COBOL: DWLSE075 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-RAGGRUPPAMENTO
  File: Lib/shared-props/rules/fondiInvestimento/raggruppamentiUL/raggruppamentiUL.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE075</program>


============================================================
PROGRAMMA COBOL: DWLSE076 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-RAGGRUPPAMENTO
  File: Lib/shared-props/rules/fondiInvestimento/raggruppamentiUL/raggruppamentiUL.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE076</program>


============================================================
PROGRAMMA COBOL: DWLSE077 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-RAGGRUPPAMENTO
  File: Lib/shared-props/rules/fondiInvestimento/raggruppamentiUL/raggruppamentiUL.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE077</program>


============================================================
PROGRAMMA COBOL: DWLSE078 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-RAGGRUPPAMENTO
  File: Lib/shared-props/rules/fondiInvestimento/raggruppamentiUL/raggruppamentiUL.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE078</program>


============================================================
PROGRAMMA COBOL: DWLSE079 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-SCONTO-RETROCESSIONE
  File: Lib/shared-props/rules/tabelleSistema/scontoRetrocessione/scontoRetrocessione.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE079</program>


============================================================
PROGRAMMA COBOL: DWLSE080 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-SCONTO-RETROCESSIONE
  File: Lib/shared-props/rules/tabelleSistema/scontoRetrocessione/scontoRetrocessione.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE080</program>


============================================================
PROGRAMMA COBOL: DWLSE081 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-SCONTO-RETROCESSIONE
  File: Lib/shared-props/rules/tabelleSistema/scontoRetrocessione/scontoRetrocessione.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE081</program>


============================================================
PROGRAMMA COBOL: DWLSE082 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-SCONTO-RETROCESSIONE
  File: Lib/shared-props/rules/tabelleSistema/scontoRetrocessione/scontoRetrocessione.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE082</program>


============================================================
PROGRAMMA COBOL: DWLSE083 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-PERCORSI-FONDO
  File: Lib/shared-props/rules/tabelleSistema/percorsiFondo/percorsiFondo.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE083</program>


============================================================
PROGRAMMA COBOL: DWLSE084 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-PERCORSI-FONDO
  File: Lib/shared-props/rules/tabelleSistema/percorsiFondo/percorsiFondo.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE084</program>


============================================================
PROGRAMMA COBOL: DWLSE085 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PERCORSI-FONDO
  File: Lib/shared-props/rules/tabelleSistema/percorsiFondo/percorsiFondo.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE085</program>


============================================================
PROGRAMMA COBOL: DWLSE086 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-QUOTA-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/quoteFondoUL/quoteFondoUL.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE086</program>


============================================================
PROGRAMMA COBOL: DWLSE087 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-QUOTA-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/quoteFondoUL/quoteFondoUL.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE087</program>


============================================================
PROGRAMMA COBOL: DWLSE088 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-QUOTA-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/quoteFondoUL/quoteFondoUL.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE088</program>


============================================================
PROGRAMMA COBOL: DWLSE089 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/calendariNav.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE089</program>


============================================================
PROGRAMMA COBOL: DWLSE090 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/calendariNav.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE090</program>


============================================================
PROGRAMMA COBOL: DWLSE091 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/calendariNav.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE091</program>


============================================================
PROGRAMMA COBOL: DWLSE092 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/calendariNav.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE092</program>


============================================================
PROGRAMMA COBOL: DWLSE093 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-DATA-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/dataCalendarioNav.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE093</program>


============================================================
PROGRAMMA COBOL: DWLSE094 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-DATA-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/dataCalendarioNav.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE094</program>


============================================================
PROGRAMMA COBOL: DWLSE095 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DATA-CALENDARIO-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/dataCalendarioNav.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE095</program>


============================================================
PROGRAMMA COBOL: DWLSE096 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TESTO-LIBERO
  File: Lib/shared-props/rules/tabelleSistema/testoLibero/testoLibero.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE096</program>


============================================================
PROGRAMMA COBOL: DWLSE097 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-TESTO-LIBERO
  File: Lib/shared-props/rules/tabelleSistema/testoLibero/testoLibero.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE097</program>


============================================================
PROGRAMMA COBOL: DWLSE098 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-TESTO-LIBERO
  File: Lib/shared-props/rules/tabelleSistema/testoLibero/testoLibero.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE098</program>


============================================================
PROGRAMMA COBOL: DWLSE099 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TESTO-LIBERO
  File: Lib/shared-props/rules/tabelleSistema/testoLibero/testoLibero.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE099</program>


============================================================
PROGRAMMA COBOL: DWLSE100 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-FONDI-COMPARTI
  File: Lib/shared-props/rules/tabelleSistema/compartiFondo/compartiFondo.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE100</program>


============================================================
PROGRAMMA COBOL: DWLSE101 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-FONDI-COMPARTI
  File: Lib/shared-props/rules/tabelleSistema/compartiFondo/compartiFondo.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE101</program>


============================================================
PROGRAMMA COBOL: DWLSE102 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-ALIQUOTE-FISCALI-ANNO
  File: Lib/shared-props/rules/tabelleSistema/aliquoteFiscaliAnno/aliquoteFiscaliAnno.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE102</program>


============================================================
PROGRAMMA COBOL: DWLSE103 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-ALIQUOTE-FISCALI-ANNO
  File: Lib/shared-props/rules/tabelleSistema/aliquoteFiscaliAnno/aliquoteFiscaliAnno.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE103</program>


============================================================
PROGRAMMA COBOL: DWLSE104 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-ALIQUOTE-FISCALI-ANNO
  File: Lib/shared-props/rules/tabelleSistema/aliquoteFiscaliAnno/aliquoteFiscaliAnno.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE104</program>


============================================================
PROGRAMMA COBOL: DWLSE105 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-ALIQUOTE-FISCALI-ANNO
  File: Lib/shared-props/rules/tabelleSistema/aliquoteFiscaliAnno/aliquoteFiscaliAnno.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE105</program>


============================================================
PROGRAMMA COBOL: DWLSE106 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TRATTATO-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE106</program>


============================================================
PROGRAMMA COBOL: DWLSE107 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-TRATTATO-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE107</program>


============================================================
PROGRAMMA COBOL: DWLSE108 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-TRATTATO-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE108</program>


============================================================
PROGRAMMA COBOL: DWLSE109 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TRATTATO-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE109</program>


============================================================
PROGRAMMA COBOL: DWLSE110 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-COMPAGNIA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/compagniaRiass.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE110</program>


============================================================
PROGRAMMA COBOL: DWLSE111 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-COMPAGNIA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/compagniaRiass.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE111</program>


============================================================
PROGRAMMA COBOL: DWLSE112 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-COMPAGNIA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/compagniaRiass.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE112</program>


============================================================
PROGRAMMA COBOL: DWLSE113 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-COMPAGNIA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/compagniaRiass.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE113</program>


============================================================
PROGRAMMA COBOL: DWLSE114 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PROVVIGIONI
  File: Lib/shared-props/rules/tabelleSistema/provvigioni/provvigioni.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE114</program>


============================================================
PROGRAMMA COBOL: DWLSE115 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-PROVVIGIONI
  File: Lib/shared-props/rules/tabelleSistema/provvigioni/provvigioni.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE115</program>


============================================================
PROGRAMMA COBOL: DWLSE116 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-PROVVIGIONI
  File: Lib/shared-props/rules/tabelleSistema/provvigioni/provvigioni.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE116</program>


============================================================
PROGRAMMA COBOL: DWLSE117 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PROVVIGIONI
  File: Lib/shared-props/rules/tabelleSistema/provvigioni/provvigioni.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE117</program>


============================================================
PROGRAMMA COBOL: DWLSE118 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-EROGAZIONI_PERIODICHE
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/erogazioniPeriodiche.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE118</program>


============================================================
PROGRAMMA COBOL: DWLSE119 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-EROGAZIONI_PERIODICHE
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/erogazioniPeriodiche.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE119</program>


============================================================
PROGRAMMA COBOL: DWLSE120 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-EROGAZIONI_PERIODICHE
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/erogazioniPeriodiche.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE120</program>


============================================================
PROGRAMMA COBOL: DWLSE121 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-EROGAZIONI_PERIODICHE
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/erogazioniPeriodiche.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE121</program>


============================================================
PROGRAMMA COBOL: DWLSE122 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-RESCISSIONE-TARIFFE
  File: Lib/shared-props/rules/tabelleSistema/rescissione/rescissioneTariffe.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE122</program>


============================================================
PROGRAMMA COBOL: DWLSE123 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-RESCISSIONE-TARIFFE
  File: Lib/shared-props/rules/tabelleSistema/rescissione/rescissioneTariffe.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE123</program>


============================================================
PROGRAMMA COBOL: DWLSE124 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-RESCISSIONE-TARIFFE
  File: Lib/shared-props/rules/tabelleSistema/rescissione/rescissioneTariffe.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE124</program>


============================================================
PROGRAMMA COBOL: DWLSE130 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-VALORI-CEDOLA
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/valori/valoriCedole.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE130</program>


============================================================
PROGRAMMA COBOL: DWLSE131 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-VALORI-CEDOLA
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/valori/valoriCedole.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE131</program>


============================================================
PROGRAMMA COBOL: DWLSE132 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-VALORI-CEDOLA
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/valori/valoriCedole.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE132</program>


============================================================
PROGRAMMA COBOL: DWLSE133 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-VALORI-CEDOLA
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/valori/valoriCedole.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE133</program>


============================================================
PROGRAMMA COBOL: DWLSE134 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/titoliIndex.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE134</program>


============================================================
PROGRAMMA COBOL: DWLSE135 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/titoliIndex.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE135</program>


============================================================
PROGRAMMA COBOL: DWLSE136 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/titoliIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE136</program>


============================================================
PROGRAMMA COBOL: DWLSE137 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-QUOTA-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/quoteTitoloIndex.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE137</program>


============================================================
PROGRAMMA COBOL: DWLSE138 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-QUOTA-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/quoteTitoloIndex.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE138</program>


============================================================
PROGRAMMA COBOL: DWLSE139 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-QUOTA-TITOLO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/quoteTitoloIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE139</program>


============================================================
PROGRAMMA COBOL: DWLSE140 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/prodottiIndex.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE140</program>


============================================================
PROGRAMMA COBOL: DWLSE141 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/prodottiIndex.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE141</program>


============================================================
PROGRAMMA COBOL: DWLSE142 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/prodottiIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE142</program>


============================================================
PROGRAMMA COBOL: DWLSE143 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-TITOLI-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/titoliProdottiIndex.inserisci.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE143</program>


============================================================
PROGRAMMA COBOL: DWLSE144 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TITOLI-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/titoliProdottiIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE144</program>


============================================================
PROGRAMMA COBOL: DWLSE145 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-DATE-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/dateProdottiIndex.inserisci.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE145</program>


============================================================
PROGRAMMA COBOL: DWLSE146 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-DATE-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/dateProdottiIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE146</program>


============================================================
PROGRAMMA COBOL: DWLSE147 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-FONDO-ZIL
  File: Lib/shared-props/rules/prodottiIndex/fondiZIL/fondiZil.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE147</program>


============================================================
PROGRAMMA COBOL: DWLSE148 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-FONDO-ZIL
  File: Lib/shared-props/rules/prodottiIndex/fondiZIL/fondiZil.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE148</program>


============================================================
PROGRAMMA COBOL: DWLSE149 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-FONDI-ZIL
  File: Lib/shared-props/rules/prodottiIndex/fondiZIL/fondiZil.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE149</program>


============================================================
PROGRAMMA COBOL: DWLSE150 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-PERDITA-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/perditeIndex.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE150</program>


============================================================
PROGRAMMA COBOL: DWLSE151 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PERDITA-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/perditeIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE151</program>


============================================================
PROGRAMMA COBOL: DWLSE152 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-CEDOLA-PERDITA-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/cedolePerditeIndex.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE152</program>


============================================================
PROGRAMMA COBOL: DWLSE153 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CEDOLA-PERDITA-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/cedolePerditeIndex.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE153</program>


============================================================
PROGRAMMA COBOL: DWLSE154 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-GESTIONE-SPECIALE-PRODOTTO
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialeProdotto/gestioneSpecialeProdotto.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE154</program>


============================================================
PROGRAMMA COBOL: DWLSE155 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-GESTIONE-SPECIALE-PRODOTTO
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialeProdotto/gestioneSpecialeProdotto.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE155</program>


============================================================
PROGRAMMA COBOL: DWLSE156 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-GESTIONE-SPECIALE-PRODOTTO
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialeProdotto/gestioneSpecialeProdotto.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE156</program>


============================================================
PROGRAMMA COBOL: DWLSE157 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-GESTIONE-SPECIALE-POLIZZA
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialePolizza/gestioneSpecialePolizza.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE157</program>


============================================================
PROGRAMMA COBOL: DWLSE158 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-GESTIONE-SPECIALE-POLIZZA
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialePolizza/gestioneSpecialePolizza.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE158</program>


============================================================
PROGRAMMA COBOL: DWLSE159 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-GESTIONE-SPECIALE-POLIZZA
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialePolizza/gestioneSpecialePolizza.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE159</program>


============================================================
PROGRAMMA COBOL: DWLSE160 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-PERCENTUALE-ABBATTIMENTO
  File: Lib/shared-props/rules/tabelleSistema/percentualeAbbattimento/percentualeAbbattimento.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE160</program>


============================================================
PROGRAMMA COBOL: DWLSE161 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PERCENTUALE-ABBATTIMENTO
  File: Lib/shared-props/rules/tabelleSistema/percentualeAbbattimento/percentualeAbbattimento.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE161</program>


============================================================
PROGRAMMA COBOL: DWLSE162 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PERCENTUALE-ABBATTIMENTO
  File: Lib/shared-props/rules/tabelleSistema/percentualeAbbattimento/percentualeAbbattimento.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE162</program>


============================================================
PROGRAMMA COBOL: DWLSE163 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE163</program>


============================================================
PROGRAMMA COBOL: DWLSE164 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE164</program>


============================================================
PROGRAMMA COBOL: DWLSE165 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE165</program>


============================================================
PROGRAMMA COBOL: DWLSE166 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE166</program>


============================================================
PROGRAMMA COBOL: DWLSE167 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE167</program>


============================================================
PROGRAMMA COBOL: DWLSE168 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE168</program>


============================================================
PROGRAMMA COBOL: DWLSE169 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE169</program>


============================================================
PROGRAMMA COBOL: DWLSE170 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE170</program>


============================================================
PROGRAMMA COBOL: DWLSE171 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE171</program>


============================================================
PROGRAMMA COBOL: DWLSE172 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DEFAULT-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.caricaDefault.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE172</program>


============================================================
PROGRAMMA COBOL: DWLSE173 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-OPZIONI-GESTIONALI
  File: Lib/shared-props/rules/fondiInvestimento/opzioniGestionali/opzioniGestionali.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE173</program>


============================================================
PROGRAMMA COBOL: DWLSE174 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-OPZIONI-GESTIONALI
  File: Lib/shared-props/rules/fondiInvestimento/opzioniGestionali/opzioniGestionali.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE174</program>


============================================================
PROGRAMMA COBOL: DWLSE175 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-OPZIONI-GESTIONALI
  File: Lib/shared-props/rules/fondiInvestimento/opzioniGestionali/opzioniGestionali.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE175</program>


============================================================
PROGRAMMA COBOL: DWLSE176 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-OPZIONI-GESTIONALI
  File: Lib/shared-props/rules/fondiInvestimento/opzioniGestionali/opzioniGestionali.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE176</program>


============================================================
PROGRAMMA COBOL: DWLSE181 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PROFILO-INVESTIMENTO
  File: Lib/shared-props/rules/fondiInvestimento/profiloInvestimento/profiloInvestimento.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE181</program>


============================================================
PROGRAMMA COBOL: DWLSE182 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-PROFILO-INVESTIMENTO
  File: Lib/shared-props/rules/fondiInvestimento/profiloInvestimento/profiloInvestimento.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE182</program>


============================================================
PROGRAMMA COBOL: DWLSE183 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-PROFILO-INVESTIMENTO
  File: Lib/shared-props/rules/fondiInvestimento/profiloInvestimento/profiloInvestimento.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE183</program>


============================================================
PROGRAMMA COBOL: DWLSE184 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PROFILO-INVESTIMENTO
  File: Lib/shared-props/rules/fondiInvestimento/profiloInvestimento/profiloInvestimento.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSE184</program>


============================================================
PROGRAMMA COBOL: DWLSL005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MESSAGGI
  File: Lib/shared-props/rules/diba/gestioneMessaggi/gestioneMessaggi.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL005</program>


============================================================
PROGRAMMA COBOL: DWLSL012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-INDICI-ISTAT
  File: Lib/shared-props/rules/diba/indiciIstat/indiciIstat.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL012</program>


============================================================
PROGRAMMA COBOL: DWLSL016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CONDIZIONI
  File: Lib/shared-props/rules/diba/condizioni/condizioni.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL016</program>


============================================================
PROGRAMMA COBOL: DWLSL021 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-SOCIETA
  File: Lib/shared-props/rules/diba/societa/societa.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL021</program>


============================================================
PROGRAMMA COBOL: DWLSL026 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PROFESSIONE
  File: Lib/shared-props/rules/diba/professione/professione.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL026</program>


============================================================
PROGRAMMA COBOL: DWLSL038 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-BENEFICIARI-MORTE
  File: Lib/shared-props/rules/proposta/elenco.beneficiari.morte.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL038</program>


============================================================
PROGRAMMA COBOL: DWLSL039 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-BENEFICIARI-VITA
  File: Lib/shared-props/rules/proposta/elenco.beneficiari.vita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSL039</program>


============================================================
PROGRAMMA COBOL: DWLSLALQ (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-ALIQUOTE-FISCALI-ANNO
  File: Lib/shared-props/rules/tabelleSistema/aliquoteFiscaliAnno/aliquoteFiscaliAnno.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLALQ</program>


============================================================
PROGRAMMA COBOL: DWLSLAMB (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DOMINIO-AMBIENTE
  File: Lib/shared-props/rules/dominioAmbiente.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLAMB</program>


============================================================
PROGRAMMA COBOL: DWLSLCAL (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CALENDARI-APERTURA-MERCATI
  File: Lib/shared-props/rules/tabelleSistema/calendari/calendari.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCAL</program>


============================================================
PROGRAMMA COBOL: DWLSLCED (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CEDOLE-PERDITE-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/cedolePerditeIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCED</program>


============================================================
PROGRAMMA COBOL: DWLSLCLN (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CALENDARI-NAV
  File: Lib/shared-props/rules/tabelleSistema/calendariNAV/calendariNAV.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCLN</program>


============================================================
PROGRAMMA COBOL: DWLSLCMB (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CAMBI
  File: Lib/shared-props/rules/diba/cambi/cambi.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCMB</program>


============================================================
PROGRAMMA COBOL: DWLSLCOA (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TRATTATO-COASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoCoass/trattatoCoass.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCOA</program>


============================================================
PROGRAMMA COBOL: DWLSLCON (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CONVENZIONI
  File: Lib/shared-props/rules/tabelleSistema/convenzione/convenzioni.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCON</program>


============================================================
PROGRAMMA COBOL: DWLSLCRI (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-COMPAGNIA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/compagniaRiass.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLCRI</program>


============================================================
PROGRAMMA COBOL: DWLSLDIV (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-DIVISE
  File: Lib/shared-props/rules/diba/divise/divise.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLDIV</program>


============================================================
PROGRAMMA COBOL: DWLSLFCC (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-COMPARTI
  File: Lib/shared-props/rules/tabelleSistema/compartiFondo/compartiFondo.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLFCC</program>


============================================================
PROGRAMMA COBOL: DWLSLFEE (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MANAGEMENT-FEE
  File: Lib/shared-props/rules/tabelleSistema/managementFee/managementFee.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLFEE</program>


============================================================
PROGRAMMA COBOL: DWLSLFND (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-UL
  File: Lib/shared-props/rules/tabelleSistema/fondiUL/fondiUL.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLFND</program>


============================================================
PROGRAMMA COBOL: DWLSLFON (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-ZIL
  File: Lib/shared-props/rules/prodottiIndex/fondiZIL/fondiZil.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLFON</program>


============================================================
PROGRAMMA COBOL: DWLSLFRV (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-RIVALUTAZIONE
  File: Lib/shared-props/rules/tabelleSistema/fondiRivalutazione/fondiRivalutazione.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLFRV</program>


============================================================
PROGRAMMA COBOL: DWLSLGCD (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-EROGAZIONI_PERIODICHE
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/erogazioniPeriodiche.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLGCD</program>


============================================================
PROGRAMMA COBOL: DWLSLGS1 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-GESTIONE-SPECIALE-PRODOTTO
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialeProdotto/gestioneSpecialeProdotto.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLGS1</program>


============================================================
PROGRAMMA COBOL: DWLSLGS2 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-GESTIONE-SPECIALE-POLIZZA
  File: Lib/shared-props/rules/tabelleSistema/gestioneSpecialePolizza/gestioneSpecialePolizza.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLGS2</program>


============================================================
PROGRAMMA COBOL: DWLSLLPC (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PIANO-CONTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLLPC</program>


============================================================
PROGRAMMA COBOL: DWLSLMPG (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MODALITA-PAGAMENTO
  File: Lib/shared-props/rules/tabelleSistema/modalitaPagamento/modalitaPagamento.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLMPG</program>


============================================================
PROGRAMMA COBOL: DWLSLNPP (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-NUMERI-PROPOSTE-POLIZZE
  File: Lib/shared-props/rules/tabelleSistema/numeriPropostePolizze/numeriPropostePolizze.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLNPP</program>


============================================================
PROGRAMMA COBOL: DWLSLNUM (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CODICI-NUMERATORE
  File: Lib/shared-props/rules/diba/codiciNumeratore/codiciNumeratore.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLNUM</program>


============================================================
PROGRAMMA COBOL: DWLSLOPZ (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-OPZIONI-GESTIONALI
  File: Lib/shared-props/rules/fondiInvestimento/opzioniGestionali/opzioniGestionali.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLOPZ</program>


============================================================
PROGRAMMA COBOL: DWLSLPDT (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-DATE-PRODOTTI-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/dateProdottiIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPDT</program>


============================================================
PROGRAMMA COBOL: DWLSLPFI (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PROFILO-INVESTIMENTO
  File: Lib/shared-props/rules/fondiInvestimento/profiloInvestimento/profiloInvestimento.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPFI</program>


============================================================
PROGRAMMA COBOL: DWLSLPIL (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PERDITE-INDEX
  File: Lib/shared-props/rules/prodottiIndex/perdite/perditeIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPIL</program>


============================================================
PROGRAMMA COBOL: DWLSLPRC (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PERCORSI-FONDO
  File: Lib/shared-props/rules/tabelleSistema/percorsiFondo/percorsiFondo.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPRC</program>


============================================================
PROGRAMMA COBOL: DWLSLPRO (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PRODOTTI-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/prodottiIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPRO</program>


============================================================
PROGRAMMA COBOL: DWLSLPRT (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TITOLI-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/titoliProdottiIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPRT</program>


============================================================
PROGRAMMA COBOL: DWLSLPTT (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TITOLI-INSERIMENTO-PRODOTTO-INDEX
  File: Lib/shared-props/rules/prodottiIndex/prodotti/titoliProdottiIndexInserimento.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPTT</program>


============================================================
PROGRAMMA COBOL: DWLSLPVV (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PROVVIGIONI
  File: Lib/shared-props/rules/tabelleSistema/provvigioni/provvigioni.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLPVV</program>


============================================================
PROGRAMMA COBOL: DWLSLQTF (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-QUOTE-FONDO-UL
  File: Lib/shared-props/rules/tabelleSistema/quoteFondoUL/quoteFondoUL.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLQTF</program>


============================================================
PROGRAMMA COBOL: DWLSLQTI (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-QUOTE-TITOLI-INDEX
  File: Lib/shared-props/rules/prodottiIndex/quoteTitoloIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLQTI</program>


============================================================
PROGRAMMA COBOL: DWLSLRGP (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RAGGRUPPAMENTO
  File: Lib/shared-props/rules/fondiInvestimento/raggruppamentiUL/raggruppamentiUL.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLRGP</program>


============================================================
PROGRAMMA COBOL: DWLSLRIA (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TRATTATO-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLRIA</program>


============================================================
PROGRAMMA COBOL: DWLSLRSC (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RESCISSIONE-TARIFFE
  File: Lib/shared-props/rules/tabelleSistema/rescissione/rescissioneTariffe.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLRSC</program>


============================================================
PROGRAMMA COBOL: DWLSLSCO (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-SCONTO-RETROCESSIONE
  File: Lib/shared-props/rules/tabelleSistema/scontoRetrocessione/scontoRetrocessione.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLSCO</program>


============================================================
PROGRAMMA COBOL: DWLSLSOC (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-SOCIETA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/societaRiass.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLSOC</program>


============================================================
PROGRAMMA COBOL: DWLSLTIT (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TITOLI-INDEX
  File: Lib/shared-props/rules/prodottiIndex/titoliIndex.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLTIT</program>


============================================================
PROGRAMMA COBOL: DWLSLTLI (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TESTO-LIBERO
  File: Lib/shared-props/rules/tabelleSistema/testoLibero/testoLibero.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLTLI</program>


============================================================
PROGRAMMA COBOL: DWLSLTPG (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TIPO-PAGAMENTO
  File: Lib/shared-props/rules/tabelleSistema/liquidazione/pianoConto.tipoPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLTPG</program>


============================================================
PROGRAMMA COBOL: DWLSLVCD (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-VALORI-CEDOLA
  File: Lib/shared-props/rules/fondiInvestimento/erogazioniPeriodiche/valori/valoriCedole.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>DWLSLVCD</program>


============================================================
PROGRAMMA COBOL: MWLSE001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-VETTORIESTERNI
  File: Lib/shared-props/rules/modelloMatematico/vettoriEsterni/vettoriEsterni.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE001</program>


============================================================
PROGRAMMA COBOL: MWLSE002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-VETTORIESTERNI
  File: Lib/shared-props/rules/modelloMatematico/vettoriEsterni/vettoriEsterni.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE002</program>


============================================================
PROGRAMMA COBOL: MWLSE003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-VETTORIESTERNI
  File: Lib/shared-props/rules/modelloMatematico/vettoriEsterni/vettoriEsterni.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE003</program>


============================================================
PROGRAMMA COBOL: MWLSE004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-VETTORIESTERNI
  File: Lib/shared-props/rules/modelloMatematico/vettoriEsterni/vettoriEsterni.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE004</program>


============================================================
PROGRAMMA COBOL: MWLSE005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TAVMORTALITA
  File: Lib/shared-props/rules/modelloMatematico/tavoleMortalita/tavoleMortalita.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE005</program>


============================================================
PROGRAMMA COBOL: MWLSE006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVA-TAVMORTALITA
  File: Lib/shared-props/rules/modelloMatematico/tavoleMortalita/tavoleMortalita.nuova.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE006</program>


============================================================
PROGRAMMA COBOL: MWLSE007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-TAVMORTALITA
  File: Lib/shared-props/rules/modelloMatematico/tavoleMortalita/tavoleMortalita.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE007</program>


============================================================
PROGRAMMA COBOL: MWLSE008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-TAVMORTALITA
  File: Lib/shared-props/rules/modelloMatematico/tavoleMortalita/tavoleMortalita.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE008</program>


============================================================
PROGRAMMA COBOL: MWLSE009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-VARIABILI
  File: Lib/shared-props/rules/modelloMatematico/variabili/variabili.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE009</program>


============================================================
PROGRAMMA COBOL: MWLSE010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-VARIABILI
  File: Lib/shared-props/rules/modelloMatematico/variabili/variabili.inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE010</program>


============================================================
PROGRAMMA COBOL: MWLSE011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-VARIABILI
  File: Lib/shared-props/rules/modelloMatematico/variabili/variabili.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE011</program>


============================================================
PROGRAMMA COBOL: MWLSE012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-VARIABILI
  File: Lib/shared-props/rules/modelloMatematico/variabili/variabili.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE012</program>


============================================================
PROGRAMMA COBOL: MWLSE013 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-METALINGUAGGIO
  File: Lib/shared-props/rules/modelloMatematico/metalinguaggio/metalinguaggio.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE013</program>


============================================================
PROGRAMMA COBOL: MWLSE014 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-METALINGUAGGIO
  File: Lib/shared-props/rules/modelloMatematico/metalinguaggio/metalinguaggio.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE014</program>


============================================================
PROGRAMMA COBOL: MWLSE015 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: PD-ESEGUI-FORMULA-PER-POSIZIONI
  File: Lib/shared-props/rules/productBuilder/productBuilder.eseguiFormulaPerPosizioni.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>MWLSE015</program>

  Regola: PD-ESEGUI-FORMULA-PER-POSIZIONI-SB00
  File: Lib/shared-props/rules/productBuilder/productBuilder.eseguiFormulaPerPosizioniSB00.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSE015</program>


============================================================
PROGRAMMA COBOL: MWLSE016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLO-IMPOSTE-LIQUIDAZIONE
  File: Lib/shared-props/rules/liquidazione/imposte/calcola.imposte.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>MWLSE016</program>


============================================================
PROGRAMMA COBOL: MWLSE017 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-FORMULA-PROVVIGIONI
  File: Lib/shared-props/rules/backOffice/gestioni/riattivazionePianoVAProgrammatoCalcoloProvvigioni.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>MWLSE017</program>


============================================================
PROGRAMMA COBOL: MWLSLMOR (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TAVMORTALITA
  File: Lib/shared-props/rules/modelloMatematico/tavoleMortalita/tavoleMortalita.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSLMOR</program>


============================================================
PROGRAMMA COBOL: MWLSLVAR (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-VARIABILI
  File: Lib/shared-props/rules/modelloMatematico/variabili/variabili.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSLVAR</program>


============================================================
PROGRAMMA COBOL: MWLSLVET (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-VETTORIESTERNI
  File: Lib/shared-props/rules/modelloMatematico/vettoriEsterni/vettoriEsterni.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>MWLSLVET</program>


============================================================
PROGRAMMA COBOL: PWLBL001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RICERCA-CUSTOMER
  File: Lib/shared-props/rules/customer/customer.ricerca.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLBL001</program>


============================================================
PROGRAMMA COBOL: PWLSE000 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INIZIALIZZAZIONE-CUSTOMER
  File: Lib/shared-props/rules/customer/customer.inizializza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSE000</program>


============================================================
PROGRAMMA COBOL: PWLSE001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-CUSTOMER
  File: Lib/shared-props/rules/customer/customer.carica.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSE001</program>


============================================================
PROGRAMMA COBOL: PWLSE002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CUSTOMER
  File: Lib/shared-props/rules/customer/customer.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSE002</program>


============================================================
PROGRAMMA COBOL: PWLSE003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-CUSTOMER
  File: Lib/shared-props/rules/customer/customer.nuovo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSE003</program>


============================================================
PROGRAMMA COBOL: PWLSLRAE (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-GRUPPO-RAE
  File: Lib/shared-props/rules/customer/elenco.sottogruppo.rae.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSLRAE</program>


============================================================
PROGRAMMA COBOL: PWLSLSAE (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-SOTTOGRUPPO-SAE
  File: Lib/shared-props/rules/customer/elenco.gruppo.sae.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>PWLSLSAE</program>


============================================================
PROGRAMMA COBOL: RWLSE001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-CATALOGO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.nuovoCatalogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE001</program>


============================================================
PROGRAMMA COBOL: RWLSE002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-CATALOGO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.modificaCatalogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE002</program>


============================================================
PROGRAMMA COBOL: RWLSE003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-CATALOGHI-PRODOTTI
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.caricaCatalogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE003</program>


============================================================
PROGRAMMA COBOL: RWLSE004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-NUOVO-PRODOTTO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.nuovoProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE004</program>


============================================================
PROGRAMMA COBOL: RWLSE005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-PRODOTTO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.modificaProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE005</program>


============================================================
PROGRAMMA COBOL: RWLSE006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-PRODOTTI-PER-CATALOGO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.caricaProdottiPerCatalogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE006</program>


============================================================
PROGRAMMA COBOL: RWLSE007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-PRODOTTO-CATALOGO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.eliminaProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE007</program>


============================================================
PROGRAMMA COBOL: RWLSE008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-LIVELLO-ZERO
  File: Lib/shared-props/rules/reteVendita/livelloZero/inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE008</program>


============================================================
PROGRAMMA COBOL: RWLSE009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-LIVELLO-ZERO
  File: Lib/shared-props/rules/reteVendita/livelloZero/modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE009</program>


============================================================
PROGRAMMA COBOL: RWLSE010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-LIVELLO-ZERO
  File: Lib/shared-props/rules/reteVendita/livelloZero/dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE010</program>


============================================================
PROGRAMMA COBOL: RWLSE011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-LIVELLO-ZERO
  File: Lib/shared-props/rules/reteVendita/livelloZero/elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE011</program>


============================================================
PROGRAMMA COBOL: RWLSE012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-LIVELLO-UNO
  File: Lib/shared-props/rules/reteVendita/livelloUno/inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE012</program>


============================================================
PROGRAMMA COBOL: RWLSE013 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-LIVELLO-UNO
  File: Lib/shared-props/rules/reteVendita/livelloUno/modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE013</program>


============================================================
PROGRAMMA COBOL: RWLSE016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-LIVELLO-DUE
  File: Lib/shared-props/rules/reteVendita/livelloDue/inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE016</program>


============================================================
PROGRAMMA COBOL: RWLSE017 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-LIVELLO-DUE
  File: Lib/shared-props/rules/reteVendita/livelloDue/modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE017</program>


============================================================
PROGRAMMA COBOL: RWLSE018 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-LIVELLO-DUE
  File: Lib/shared-props/rules/reteVendita/livelloDue/dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE018</program>


============================================================
PROGRAMMA COBOL: RWLSE020 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVO-LIVELLO-TRE
  File: Lib/shared-props/rules/reteVendita/livelloTre/inserimento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE020</program>


============================================================
PROGRAMMA COBOL: RWLSE021 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-LIVELLO-TRE
  File: Lib/shared-props/rules/reteVendita/livelloTre/modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE021</program>


============================================================
PROGRAMMA COBOL: RWLSE022 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-LIVELLO-TRE
  File: Lib/shared-props/rules/reteVendita/livelloTre/dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE022</program>


============================================================
PROGRAMMA COBOL: RWLSE023 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-LIVELLO-UNO
  File: Lib/shared-props/rules/reteVendita/livelloUno/dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSE023</program>


============================================================
PROGRAMMA COBOL: RWLSL001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CATALOGHI-PRODOTTI
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.elencoCataloghi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL001</program>


============================================================
PROGRAMMA COBOL: RWLSL002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-LIVELLO-ZERO
  File: Lib/shared-props/rules/reteVendita/livelloZero/elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL002</program>


============================================================
PROGRAMMA COBOL: RWLSL003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-LIVELLO-UNO
  File: Lib/shared-props/rules/reteVendita/livelloUno/elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL003</program>


============================================================
PROGRAMMA COBOL: RWLSL004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PRODOTTI-PER-CATALOGO
  File: Lib/shared-props/rules/reteVendita/cataloghiProdotti/cataloghiProdotti.elencoProdottiPerCatalogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL004</program>


============================================================
PROGRAMMA COBOL: RWLSL005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-LIVELLO-DUE
  File: Lib/shared-props/rules/reteVendita/livelloDue/elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL005</program>


============================================================
PROGRAMMA COBOL: RWLSL006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-LIVELLO-TRE
  File: Lib/shared-props/rules/reteVendita/livelloTre/elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>RWLSL006</program>


============================================================
PROGRAMMA COBOL: SWLSL001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-SINISTRI
  File: Lib/shared-props/rules/sinistri/comuni/rapporti.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>SWLSL001</program>


============================================================
PROGRAMMA COBOL: VWLBL003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-MOVIMENTAZIONI-UL
  File: Lib/shared-props/rules/inquiry/movimentiUL/listaMovimentiUL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBL003</program>


============================================================
PROGRAMMA COBOL: VWLBLDUL (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DISINVESTIMENTI-UL-RICERCA
  File: Lib/shared-props/rules/inquiry/disinvestimentiUL/disinvestimentiUL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLDUL</program>


============================================================
PROGRAMMA COBOL: VWLBLIMP (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-INQUIRY-SINISTRO
  File: Lib/shared-props/rules/inquiry/inquirySinistro/dettaglioImposteInquirySinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLIMP</program>


============================================================
PROGRAMMA COBOL: VWLBLLIQ (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/elencoInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLLIQ</program>


============================================================
PROGRAMMA COBOL: VWLBLMB1 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-GESTIONE-CCOAVICVICGA
  File: Lib/shared-props/rules/archivio/funzioniDBVita/gestioneCCOAVICVICGA/gestioneCCOAVICVICGA.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLMB1</program>


============================================================
PROGRAMMA COBOL: VWLBLMB2 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-INQUIRY
  File: Lib/shared-props/rules/archivio/funzioniDBVita/inquiryOperazioni/inquiryOperazioni.ricerca.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLMB2</program>


============================================================
PROGRAMMA COBOL: VWLBLMB3 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-FLUSSO-COMUNICAZIONE
  File: Lib/shared-props/rules/archivio/funzioniDBVita/flussoComunicazione/elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLMB3</program>


============================================================
PROGRAMMA COBOL: VWLBLPRE (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INQUIRY-PROPOSTA-ELENCO
  File: Lib/shared-props/rules/inquiry/proposta/inquiryProposta.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLPRE</program>


============================================================
PROGRAMMA COBOL: VWLBLQT1 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-INQUIRY-POLIZZA
  File: Lib/shared-props/rules/inquiry/elencoInquiryPolizza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLQT1</program>


============================================================
PROGRAMMA COBOL: VWLBLSRV (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-STORICO-RIVALUTAZIONE
  File: Lib/shared-props/rules/inquiry/storicoRivalutazione/elencoStoricoRivalutazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLBLSRV</program>


============================================================
PROGRAMMA COBOL: VWLSE187 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-EVENTI-POLIZZA
  File: Lib/shared-props/rules/elencoEventiPolizza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE187</program>


============================================================
PROGRAMMA COBOL: VWLSE188 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE188</program>


============================================================
PROGRAMMA COBOL: VWLSE189 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE189</program>


============================================================
PROGRAMMA COBOL: VWLSE190 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FONDI-PER-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.recuperaFondi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE190</program>


============================================================
PROGRAMMA COBOL: VWLSE192 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-FONDI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.registraFondi.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE192</program>


============================================================
PROGRAMMA COBOL: VWLSE206 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-POLIZZA-COLLETTIVA
  File: Lib/shared-props/rules/collettive/polizza.dettaglioPolizzaCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE206</program>


============================================================
PROGRAMMA COBOL: VWLSE207 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-MANCATO-PERFEZIONAMENTO-COLLETTIVA
  File: Lib/shared-props/rules/backOffice/storni/mancatoPerfez.caricaParametriCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE207</program>


============================================================
PROGRAMMA COBOL: VWLSE208 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-MANCATO-PERFEZIONAMENTO-COLLETTIVA
  File: Lib/shared-props/rules/backOffice/storni/mancatoPerfez.controllaParametriCollettiva.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE208</program>


============================================================
PROGRAMMA COBOL: VWLSE210 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RECESSO-RIMBORSO-PREMI-COLLETTIVA
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.caricaParametriCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE210</program>


============================================================
PROGRAMMA COBOL: VWLSE211 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-PREMI-RIMBORSO-COLLETTIVA
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.caricaListaRimborsoPremiColl.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE211</program>


============================================================
PROGRAMMA COBOL: VWLSE212 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-RECESSO-RIMBORSO-PREMI-COLLETTIVA
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.registraRecessoRimborsoPremiColl.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE212</program>


============================================================
PROGRAMMA COBOL: VWLSE215 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TESTE-COLLETTIVA
  File: Lib/shared-props/rules/collettive/proposta.collettiva.elencoTeste.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE215</program>


============================================================
PROGRAMMA COBOL: VWLSE217 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-STORNI-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/caricaParametriScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE217</program>


============================================================
PROGRAMMA COBOL: VWLSE218 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-STORNI-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/controllaParametriScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE218</program>


============================================================
PROGRAMMA COBOL: VWLSE219 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-STORNI-LIQUIDAZIONE-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/caricaDettaglioLiquidazioneScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE219</program>


============================================================
PROGRAMMA COBOL: VWLSE220 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-STORNI-PARAMETRI-LIQUIDAZIONE-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/calcolaParametriLiquidazioneScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE220</program>


============================================================
PROGRAMMA COBOL: VWLSE221 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-STORNI-LIQUIDAZIONE-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/controllaParametriLiquidazioneScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE221</program>


============================================================
PROGRAMMA COBOL: VWLSE223 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-OPZIONI-DIFFERIMENTO-OLD
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.caricaParametriOld.xml:8
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE223</program>


============================================================
PROGRAMMA COBOL: VWLSE224 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-OPZIONI-DIFFERIMENTO-OLD
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.registraOld.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE224</program>


============================================================
PROGRAMMA COBOL: VWLSE225 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-OPZIONI-CONVERSIONE
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniConversione.cancella.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE225</program>


============================================================
PROGRAMMA COBOL: VWLSE227 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-DIFFERIMENTO-A-SCADENZA-NEW
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.caricaParametriNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE227</program>


============================================================
PROGRAMMA COBOL: VWLSE228 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-DIFFERIMENTO-A-SCADENZA-NEW
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.controllaParametriNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE228</program>


============================================================
PROGRAMMA COBOL: VWLSE229 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPORTI-DIFFERIMENTO-A-SCADENZA-NEW
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.caricaImportiNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE229</program>


============================================================
PROGRAMMA COBOL: VWLSE230 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONVALIDA-DIFFERIMENTO-A-SCADENZA-NEW
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.convalidaNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE230</program>


============================================================
PROGRAMMA COBOL: VWLSE231 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PERCIPIENTE-NEW-SCADENZA
  File: Lib/shared-props/rules/backOffice/percipientiNew/inserisciPercipienteScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE231</program>


============================================================
PROGRAMMA COBOL: VWLSE236 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-QUESTIONARIO-MEDICO
  File: Lib/shared-props/rules/questionari/medico/carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE236</program>


============================================================
PROGRAMMA COBOL: VWLSE237 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INVIA-QUESTIONARIO-MEDICO
  File: Lib/shared-props/rules/questionari/medico/invio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE237</program>


============================================================
PROGRAMMA COBOL: VWLSE238 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESISTENZA-QUESTIONARIO
  File: Lib/shared-props/rules/questionari/adeguatezzaAppropriatezza/esistenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE238</program>


============================================================
PROGRAMMA COBOL: VWLSE240 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-SESSIONE
  File: Lib/shared-props/rules/nuovaSessione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE240</program>


============================================================
PROGRAMMA COBOL: VWLSE241 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/dettaglioLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE241</program>


============================================================
PROGRAMMA COBOL: VWLSE242 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/modificaLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE242</program>


============================================================
PROGRAMMA COBOL: VWLSE243 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-BENEFICIARIO (AL MOMENTO NON USATO)
  File: Lib/shared-props/rules/inquiry/liquidazioni/dettaglioBeneficiario.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE243</program>


============================================================
PROGRAMMA COBOL: VWLSE245 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-QUIETANZA
  File: Lib/shared-props/rules/inquiry/quietanze/dettaglioQuietanza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE245</program>


============================================================
PROGRAMMA COBOL: VWLSE246 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-QUIETANZA
  File: Lib/shared-props/rules/inquiry/quietanze/modificaQuietanza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE246</program>


============================================================
PROGRAMMA COBOL: VWLSE249 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-MOVIMENTO-CONTABILE
  File: Lib/shared-props/rules/inquiry/movimentiContabili/dettaglioMovimentoContabile.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE249</program>


============================================================
PROGRAMMA COBOL: VWLSE250 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MOVIMENTO-CONTABILE
  File: Lib/shared-props/rules/inquiry/movimentiContabili/modificaMovimentoContabile.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE250</program>


============================================================
PROGRAMMA COBOL: VWLSE253 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-STORICO-RIVALUTAZIONE
  File: Lib/shared-props/rules/inquiry/storicoRivalutazione/dettaglioStoricoRivalutazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE253</program>


============================================================
PROGRAMMA COBOL: VWLSE254 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-STORICO-RIVALUTAZIONE
  File: Lib/shared-props/rules/inquiry/storicoRivalutazione/modificaStoricoRivalutazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE254</program>


============================================================
PROGRAMMA COBOL: VWLSE258 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-CORREZIONE
  File: Lib/shared-props/rules/archivio/funzioniDBVita/movimentoCorrezioneRCT/movimentoCorrezione.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE258</program>


============================================================
PROGRAMMA COBOL: VWLSE259 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-CORREZIONE-MOVIMENTI
  File: Lib/shared-props/rules/archivio/funzioniDBVita/movimentoCorrezioneRCT/movimentoCorrezione.correzione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE259</program>


============================================================
PROGRAMMA COBOL: VWLSE260 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-DISINVESTIMENTI-UNIT
  File: Lib/shared-props/rules/inquiry/disinvestimentiUL/dettaglioSaveDisinvestimentiUnit.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE260</program>


============================================================
PROGRAMMA COBOL: VWLSE261 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DISINVESTIMENTI-INDEX
  File: Lib/shared-props/rules/inquiry/disinvestimentiUL/dettaglioDisinvestimentiIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE261</program>


============================================================
PROGRAMMA COBOL: VWLSE262 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-DISINVESTIMENTI-INDEX
  File: Lib/shared-props/rules/inquiry/disinvestimentiUL/dettaglioSaveDisinvestimentiIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE262</program>


============================================================
PROGRAMMA COBOL: VWLSE265 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-CONCESSIONE-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriConcessione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE265</program>


============================================================
PROGRAMMA COBOL: VWLSE266 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONCESSIONE-PRESTITI-CONFERMA
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriConcessioneConferma.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE266</program>


============================================================
PROGRAMMA COBOL: VWLSE267 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DISINVESTIMENTI-UNIT
  File: Lib/shared-props/rules/inquiry/disinvestimentiUL/dettaglioDisinvestimentiUnit.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE267</program>


============================================================
PROGRAMMA COBOL: VWLSE268 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RIMBORSO-PRESTITI-CONFERMA
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriRimborsoConferma.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE268</program>


============================================================
PROGRAMMA COBOL: VWLSE269 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-TRASFORMAZIONI
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE269</program>


============================================================
PROGRAMMA COBOL: VWLSE270 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-TRASFORMAZIONI
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE270</program>


============================================================
PROGRAMMA COBOL: VWLSE271 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: ELENCO-TRASFORMAZIONI
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.caricaLista.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE271</program>

  Regola: SALVA-TRASFORMAZIONI
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE271</program>


============================================================
PROGRAMMA COBOL: VWLSE273 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-STORICO-POSIZIONE-UT
  File: Lib/shared-props/rules/inquiryPolizza/unitaTecniche.dettaglioStoricoPosizioneUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE273</program>


============================================================
PROGRAMMA COBOL: VWLSE274 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-POSIZIONE-RIASS
  File: Lib/shared-props/rules/inquiryPolizza/unitaTecniche.dettaglioRiassicurazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE274</program>


============================================================
PROGRAMMA COBOL: VWLSE275 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-GESTIONE-CCOAVICVICGA
  File: Lib/shared-props/rules/archivio/funzioniDBVita/gestioneCCOAVICVICGA/gestioneCCOAVICVICGA.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE275</program>


============================================================
PROGRAMMA COBOL: VWLSE276 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-LIMITAZIONE-RIVALUTAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneRivalutazione.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE276</program>


============================================================
PROGRAMMA COBOL: VWLSE277 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-LIMITAZIONE-RIVALUTAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneRivalutazione.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE277</program>


============================================================
PROGRAMMA COBOL: VWLSE287 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RISCHIO-COMUNE
  File: Lib/shared-props/rules/proposta/proposta.rischicomuni.elencoRapporti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE287</program>


============================================================
PROGRAMMA COBOL: VWLSE291 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PERCIPIENTE-PRENOTAZIONE
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/dettaglioPercipientePrenotazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE291</program>


============================================================
PROGRAMMA COBOL: VWLSE292 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-PERCIPIENTE-NEW
  File: Lib/shared-props/rules/backOffice/percipientiNew/cancellaPercipiente.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE292</program>


============================================================
PROGRAMMA COBOL: VWLSE296 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PERCIPIENTE-NEW-RISCATTO
  File: Lib/shared-props/rules/backOffice/percipientiNew/inserisciPercipienteRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE296</program>


============================================================
PROGRAMMA COBOL: VWLSE302 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-MODALITA-PAGAMENTO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaModalitaPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE302</program>


============================================================
PROGRAMMA COBOL: VWLSE303 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODAL-PAGAM-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.salvaModalitaPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE303</program>


============================================================
PROGRAMMA COBOL: VWLSE304 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-NEW-RISCATTO
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaPercipientiRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE304</program>


============================================================
PROGRAMMA COBOL: VWLSE305 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-NEW-SINISTRI
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaPercipientiSinistri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE305</program>


============================================================
PROGRAMMA COBOL: VWLSE306 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-NEW-SCADENZA
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaPercipientiScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE306</program>


============================================================
PROGRAMMA COBOL: VWLSE309 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PERCIPIENTE-NEW-RISCATTO
  File: Lib/shared-props/rules/backOffice/percipientiNew/dettaglioPercipienteRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE309</program>


============================================================
PROGRAMMA COBOL: VWLSE310 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PERCIPIENTE-NEW-SINISTRI
  File: Lib/shared-props/rules/backOffice/percipientiNew/dettaglioPercipienteSinistri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE310</program>


============================================================
PROGRAMMA COBOL: VWLSE311 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PERCIPIENTE-NEW-SCADENZA
  File: Lib/shared-props/rules/backOffice/percipientiNew/dettaglioPercipienteScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE311</program>


============================================================
PROGRAMMA COBOL: VWLSE312 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-FUG-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.FUG.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE312</program>


============================================================
PROGRAMMA COBOL: VWLSE313 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-FUG-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.FUG.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE313</program>


============================================================
PROGRAMMA COBOL: VWLSE315 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PERCIPIENTE-NEW-RISCATTO
  File: Lib/shared-props/rules/backOffice/percipientiNew/registraPercipienteRiscatto.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE315</program>


============================================================
PROGRAMMA COBOL: VWLSE316 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PERCIPIENTE-NEW-SINISTRI
  File: Lib/shared-props/rules/backOffice/percipientiNew/registraPercipienteSinistri.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE316</program>


============================================================
PROGRAMMA COBOL: VWLSE317 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PERCIPIENTE-NEW-SCADENZA
  File: Lib/shared-props/rules/backOffice/percipientiNew/registraPercipienteScadenza.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE317</program>


============================================================
PROGRAMMA COBOL: VWLSE318 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INQUIRY-SINISTRO
  File: Lib/shared-props/rules/inquiry/inquirySinistro/inquirySinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE318</program>


============================================================
PROGRAMMA COBOL: VWLSE319 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-INQUIRY-SINISTRO
  File: Lib/shared-props/rules/inquiry/inquirySinistro/dettaglioInquirySinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE319</program>


============================================================
PROGRAMMA COBOL: VWLSE322 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RISCATTO-PRODOTTI-UNIT-LINKED
  File: Lib/shared-props/rules/stampe/backOffice/storni/riscatto.prodottiUnitLinked.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE322</program>


============================================================
PROGRAMMA COBOL: VWLSE323 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RISCATTO-PRODOTTI-PIP
  File: Lib/shared-props/rules/stampe/backOffice/storni/riscatto.prodottiPip.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE323</program>


============================================================
PROGRAMMA COBOL: VWLSE324 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RISCATTO-PRODOTTI-INDEX-LINKED
  File: Lib/shared-props/rules/stampe/backOffice/storni/riscatto.prodottiIndexLinked.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE324</program>


============================================================
PROGRAMMA COBOL: VWLSE325 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RISCATTO-PRODOTTI-GESTIONE-SEPARATA
  File: Lib/shared-props/rules/stampe/backOffice/storni/riscatto.prodottiGestioneSeparata.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE325</program>


============================================================
PROGRAMMA COBOL: VWLSE326 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-OPZIONI-DIFFERIMENTO-NEW
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.caricaParametriNew.xml:8
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE326</program>


============================================================
PROGRAMMA COBOL: VWLSE327 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-OPZIONI-DIFFERIMENTO-NEW
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.registraNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE327</program>


============================================================
PROGRAMMA COBOL: VWLSE328 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CANCELLA-OPZIONI-DIFFERIMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.cancella.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE328</program>


============================================================
PROGRAMMA COBOL: VWLSE329 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-SQC-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.SQC.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE329</program>


============================================================
PROGRAMMA COBOL: VWLSE330 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-SQC-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.SQC.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE330</program>


============================================================
PROGRAMMA COBOL: VWLSE331 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-CQS-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.CQS.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE331</program>


============================================================
PROGRAMMA COBOL: VWLSE332 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CQS-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.CQS.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE332</program>


============================================================
PROGRAMMA COBOL: VWLSE333 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-PVT-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.PVT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE333</program>


============================================================
PROGRAMMA COBOL: VWLSE334 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PVT-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.PVT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE334</program>


============================================================
PROGRAMMA COBOL: VWLSE335 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-CTL-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.CTL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE335</program>


============================================================
PROGRAMMA COBOL: VWLSE336 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CTL-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.CTL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE336</program>


============================================================
PROGRAMMA COBOL: VWLSE337 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-INQUIRY-RISCATTO
  File: Lib/shared-props/rules/inquiry/inquiryRiscatto/dettaglioInquiryRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE337</program>


============================================================
PROGRAMMA COBOL: VWLSE338 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: INQUIRY-SCADENZA
  File: Lib/shared-props/rules/inquiry/inquiryScadenza/inquiryScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE338</program>

  Regola: INQUIRY-RENDITA
  File: Lib/shared-props/rules/inquiry/inquiryRendita/inquiryRendita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE338</program>


============================================================
PROGRAMMA COBOL: VWLSE339 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: DETTAGLIO-INQUIRY-SCADENZA
  File: Lib/shared-props/rules/inquiry/inquiryScadenza/dettaglioInquiryScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE339</program>

  Regola: DETTAGLIO-INQUIRY-RENDITA
  File: Lib/shared-props/rules/inquiry/inquiryRendita/dettaglioInquiryRendita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE339</program>


============================================================
PROGRAMMA COBOL: VWLSE340 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INQUIRY-RISCATTO
  File: Lib/shared-props/rules/inquiry/inquiryRiscatto/inquiryRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE340</program>


============================================================
PROGRAMMA COBOL: VWLSE342 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-UNIT
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiUnit.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE342</program>


============================================================
PROGRAMMA COBOL: VWLSE343 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-INDEX-ZLII
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiIndexZlii.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE343</program>


============================================================
PROGRAMMA COBOL: VWLSE344 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-INVESTIRE-CULTURA
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiInvestireCultura.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE344</program>


============================================================
PROGRAMMA COBOL: VWLSE345 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-TRADIZIONALE
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiTradizionale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE345</program>


============================================================
PROGRAMMA COBOL: VWLSE349 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FIGURE-ANAGRAFICHE-RIEPILOGO
  File: Lib/shared-props/rules/proposta/proposta.caricaFigureAnagraficheRiepilogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE349</program>


============================================================
PROGRAMMA COBOL: VWLSE350 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FIGURE-ANAGRAFICHE
  File: Lib/shared-props/rules/proposta/proposta.caricaFigureAnagrafiche.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE350</program>


============================================================
PROGRAMMA COBOL: VWLSE351 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-ANAGRAFICHE
  File: Lib/shared-props/rules/proposta/proposta.controlloFigureAnagrafiche.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE351</program>


============================================================
PROGRAMMA COBOL: VWLSE352 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-SINGOLA-ANAGRAFICA
  File: Lib/shared-props/rules/proposta/proposta.controlloSingolaFiguraAnagraficha.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE352</program>


============================================================
PROGRAMMA COBOL: VWLSE356 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CERTIFICATO-ESISTENZA
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/caricaCertificatoEsistenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE356</program>


============================================================
PROGRAMMA COBOL: VWLSE357 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-RICHIESTA-DOCUMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/salvaRichiestaCertificato.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE357</program>


============================================================
PROGRAMMA COBOL: VWLSE359 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-VARIAZIONE-MODPAGRENDITA
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniModPagRendita.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE359</program>


============================================================
PROGRAMMA COBOL: VWLSE360 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-VARIAZIONE-MODPAGRENDITA
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniModPagRendita.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE360</program>


============================================================
PROGRAMMA COBOL: VWLSE361 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PERCIPIENTE-NEW-SINISTRI
  File: Lib/shared-props/rules/backOffice/percipientiNew/inserisciPercipienteSinistri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE361</program>


============================================================
PROGRAMMA COBOL: VWLSE365 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-MODIFICA-DOCUMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/caricaModificaDocumento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE365</program>


============================================================
PROGRAMMA COBOL: VWLSE366 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-MODIFICA-DOCUMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/salvaModificaDocumento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE366</program>


============================================================
PROGRAMMA COBOL: VWLSE367 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-RICHIESTA-DOCUMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/ricercaRichiestaDocumento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE367</program>


============================================================
PROGRAMMA COBOL: VWLSE371 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-VARIAZIONI-CONTRATTO-DATIIDENT
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.caricaParametriDatiIdent.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE371</program>


============================================================
PROGRAMMA COBOL: VWLSE372 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-VARIAZIONI-CONTRATTO-DATIIDENT
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.registraDatiIdent.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE372</program>


============================================================
PROGRAMMA COBOL: VWLSE373 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-VARIAZIONI-CONTRATTO-DATIANAG
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.caricaParametriDatiAnag.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE373</program>


============================================================
PROGRAMMA COBOL: VWLSE374 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-VARIAZIONI-CONTRATTO-DATIANAG
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.registaVariazioniContrattoDatiAnag.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE374</program>


============================================================
PROGRAMMA COBOL: VWLSE375 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-VARIAZIONI-CONTRATTO-DATIANAG
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.controlloVariazioniContrattoDatiAnag.xml:7
  Transazione: SBEX
  Connector: A05TARE
  Linea: <program>VWLSE375</program>


============================================================
PROGRAMMA COBOL: VWLSE376 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: STAMPA-DOCUMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/certificatoEsistenza/stampaDocumento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE376</program>


============================================================
PROGRAMMA COBOL: VWLSE379 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA-STORNI-INCASSI
  File: Lib/shared-props/rules/backOffice/gestioni/storniIncassi/listaStorniIncassi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE379</program>


============================================================
PROGRAMMA COBOL: VWLSE380 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-STORNI-INCASSI
  File: Lib/shared-props/rules/backOffice/gestioni/storniIncassi/modificaStorniIncassi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE380</program>


============================================================
PROGRAMMA COBOL: VWLSE382 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-RIT-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.RIT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE382</program>


============================================================
PROGRAMMA COBOL: VWLSE383 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-RIT-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.RIT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE383</program>


============================================================
PROGRAMMA COBOL: VWLSE385 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VISUALIZZAZIONE-VARIAZIONI-CONTRATTO-UT
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContrattoUT.caricaVisualizzazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE385</program>


============================================================
PROGRAMMA COBOL: VWLSE386 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRAZIONE-VARIAZIONI-CONTRATTO-UT
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContrattoUT.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE386</program>


============================================================
PROGRAMMA COBOL: VWLSE387 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-GESTIONE-ANAGRAFICHE-RUOLO
  File: Lib/shared-props/rules/backOffice/gestioni/gestioneAnagraficheRuolo.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE387</program>


============================================================
PROGRAMMA COBOL: VWLSE388 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-GESTIONE-ANAGRAFICHE-RUOLO
  File: Lib/shared-props/rules/backOffice/gestioni/gestioneAnagraficaRuolo.registraFigureAnagrafiche.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE388</program>


============================================================
PROGRAMMA COBOL: VWLSE389 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-SINGOLA-ANAGRAFICA-GEST-RUOLO
  File: Lib/shared-props/rules/backOffice/gestioni/gestioneAnagraficaRuolo.controlloSingolaFiguraAnagraficha.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE389</program>


============================================================
PROGRAMMA COBOL: VWLSE390 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-BLOCCO-ATTIVAZIONE
  File: Lib/shared-props/rules/proposta/proposta.salvaBloccoAttivazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE390</program>


============================================================
PROGRAMMA COBOL: VWLSE392 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaParametriIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE392</program>


============================================================
PROGRAMMA COBOL: VWLSE393 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-CARICA-FONDI-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.controllaECaricaFondiIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE393</program>


============================================================
PROGRAMMA COBOL: VWLSE394 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.registraPrenotazIndex.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE394</program>


============================================================
PROGRAMMA COBOL: VWLSE395 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-NEW-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaPercipientiRiscattoIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE395</program>


============================================================
PROGRAMMA COBOL: VWLSE396 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PERCIPIENTE-NEW-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/percipientiNew/registraPercipienteRiscattoIndex.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE396</program>


============================================================
PROGRAMMA COBOL: VWLSE398 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-PIP
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiPip.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE398</program>


============================================================
PROGRAMMA COBOL: VWLSE399 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SINISTRO-PRODOTTI-GESTIONE-SEPARATA
  File: Lib/shared-props/rules/stampe/backOffice/storni/sinistro.prodottiGestioneSeparata.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE399</program>


============================================================
PROGRAMMA COBOL: VWLSE400 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-REFRESH-EBAAS
  File: Lib/shared-props/rules/backOffice/gestioneEbaas/refreshEbaas.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE400</program>


============================================================
PROGRAMMA COBOL: VWLSE401 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: RECESSO-PRE-GESTIONE-SEPARATA-TRADIZIONALE
  File: Lib/shared-props/rules/stampe/backOffice/storni/recesso.preGestioneSeparataTradizionale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE401</program>

  Regola: RECESSO-PRE-GESTIONE-SEPARATA-TRADIZIONALE-EX
  File: Lib/shared-props/rules/stampe/backOffice/storni/recesso.preGestioneSeparataTradizionaleEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE401</program>


============================================================
PROGRAMMA COBOL: VWLSE402 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-TRASFER-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE402</program>


============================================================
PROGRAMMA COBOL: VWLSE403 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-TRASFER-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE403</program>


============================================================
PROGRAMMA COBOL: VWLSE404 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-TRASFERIMENTO-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.conferma.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE404</program>


============================================================
PROGRAMMA COBOL: VWLSE405 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/backOffice/gestioni/trasferimentoFondiEntrata.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE405</program>


============================================================
PROGRAMMA COBOL: VWLSE406 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/backOffice/gestioni/trasferimentoFondiEntrata.salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE406</program>


============================================================
PROGRAMMA COBOL: VWLSE407 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/inquiry/trasferimentoFondiEntrata/trasferimentoFondiEntrata.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE407</program>


============================================================
PROGRAMMA COBOL: VWLSE408 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-INQUERY-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/inquiry/trasferimentoFondiEntrata/trasferimentoFondiEntrata.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE408</program>


============================================================
PROGRAMMA COBOL: VWLSE409 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-DETTAGLIO-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/inquiry/trasferimentoFondiEntrata/trasferimentoFondiEntrata.modificaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE409</program>


============================================================
PROGRAMMA COBOL: VWLSE410 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-DETTAGLIO-TRASFERIMENTO-FONDI-ENTRATA
  File: Lib/shared-props/rules/inquiry/trasferimentoFondiEntrata/trasferimentoFondiEntrata.annullaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE410</program>


============================================================
PROGRAMMA COBOL: VWLSE411 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-CONTRIBUTI-NONDEDOTTI
  File: Lib/shared-props/rules/inquiry/contributiNonDedotti/elencoContributiNonDedotti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE411</program>


============================================================
PROGRAMMA COBOL: VWLSE412 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CONTRIBUTI-NONDEDOTTI
  File: Lib/shared-props/rules/inquiry/contributiNonDedotti/salvaContributiNonDedotti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE412</program>


============================================================
PROGRAMMA COBOL: VWLSE413 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-TRASFER-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE413</program>


============================================================
PROGRAMMA COBOL: VWLSE414 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-PER-CALCOLI
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaValoriPerCalcoli.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE414</program>


============================================================
PROGRAMMA COBOL: VWLSE415 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaImposte.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE415</program>


============================================================
PROGRAMMA COBOL: VWLSE416 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CLAUSOLE
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.caricaClausole.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE416</program>


============================================================
PROGRAMMA COBOL: VWLSE417 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CLAUSOLE
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.salvaClausole.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE417</program>


============================================================
PROGRAMMA COBOL: VWLSE418 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-TECNICI-UT
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.controllaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE418</program>


============================================================
PROGRAMMA COBOL: VWLSE420 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-COMPAGNIA
  File: Lib/shared-props/rules/controlloCompagnia.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE420</program>


============================================================
PROGRAMMA COBOL: VWLSE421 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-DATA-SISTEMA
  File: Lib/shared-props/rules/tabelleSistema/dataSistema/dataSistema.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE421</program>


============================================================
PROGRAMMA COBOL: VWLSE422 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-CONTRIBUZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/dettaglioContribuzione.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE422</program>


============================================================
PROGRAMMA COBOL: VWLSE423 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-DETTAGLIO-CONTRIBUZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/dettaglioContribuzione.registraParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE423</program>


============================================================
PROGRAMMA COBOL: VWLSE424 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI-VINCOLI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciVincoliCarica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE424</program>


============================================================
PROGRAMMA COBOL: VWLSE425 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DATI-TECNICI-VINCOLI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciVincoliSalva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE425</program>


============================================================
PROGRAMMA COBOL: VWLSE427 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-MOVIMENTAZIONI-UNIT
  File: Lib/shared-props/rules/inquiry/movimentiUL/dettaglioMovimentazioniUnit.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE427</program>


============================================================
PROGRAMMA COBOL: VWLSE428 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-MOVIMENTAZIONI-INDEX
  File: Lib/shared-props/rules/inquiry/movimentiUL/dettaglioMovimentazioniIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE428</program>


============================================================
PROGRAMMA COBOL: VWLSE429 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-ANTICIPAZIONI
  File: Lib/shared-props/rules/backOffice/storni/anticipazioni/anticipazioni.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE429</program>


============================================================
PROGRAMMA COBOL: VWLSE430 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-ANTICIPAZIONI
  File: Lib/shared-props/rules/backOffice/storni/anticipazioni/anticipazioni.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE430</program>


============================================================
PROGRAMMA COBOL: VWLSE431 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-VALORI-PER-CALCOLI
  File: Lib/shared-props/rules/backOffice/percipientiNew/salvaValoriPerCalcoli.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE431</program>


============================================================
PROGRAMMA COBOL: VWLSE432 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-INQUIRY-SCADENZA
  File: Lib/shared-props/rules/inquiry/inquiryScadenza/caricaImposteInquiryScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE432</program>

  Regola: CARICA-IMPOSTE-INQUIRY-RENDITA
  File: Lib/shared-props/rules/inquiry/inquiryRendita/caricaImposteInquiryRendita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE432</program>


============================================================
PROGRAMMA COBOL: VWLSE433 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FONDI-UL-GARANZIE-AGG
  File: Lib/shared-props/rules/inquiry/quietanze/fondiULGaranzieAgg.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE433</program>


============================================================
PROGRAMMA COBOL: VWLSE434 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: FLUSSO-RCT
  File: Lib/shared-props/rules/inquiry/quietanze/flussoRCT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE434</program>


============================================================
PROGRAMMA COBOL: VWLSE435 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-LIQUIDAZIONE-ANNULLA-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/caricaLiquidazionePerAnnullo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE435</program>


============================================================
PROGRAMMA COBOL: VWLSE436 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-LIQUIDAZIONE-ANNULLA-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/confermaAnnullaScadenza.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE436</program>


============================================================
PROGRAMMA COBOL: VWLSE437 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-RICHIESTA-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/richiestaFondiUscita.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE437</program>


============================================================
PROGRAMMA COBOL: VWLSE438 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: REGISTRA-TRASF-FONDI-OUT-PRENOTAZ
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.registraPrenotazione.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE438</program>

  Regola: CONFERMA-RICHIESTA-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/richiestaFondiUscita.conferma.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE438</program>


============================================================
PROGRAMMA COBOL: VWLSE439 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-DATI-FONDI-OUT-PRENOTAZ
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.inserisciDatiFondoPrenotaz.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE439</program>


============================================================
PROGRAMMA COBOL: VWLSE441 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TRASFORMAZIONI-DA-SALVA-PROPOSTA
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.caricaListaDaSalvaProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE441</program>


============================================================
PROGRAMMA COBOL: VWLSE442 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-PROPOSTA-DA-TRASFORMAZIONE
  File: Lib/shared-props/rules/proposta/propostaDaTrasformazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE442</program>


============================================================
PROGRAMMA COBOL: VWLSE444 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TRASFORMAZIONI-DA-PROPOSTA
  File: Lib/shared-props/rules/backOffice/storni/trasformazioni.caricaListaDaProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE444</program>


============================================================
PROGRAMMA COBOL: VWLSE445 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: SALVA-PROPOSTA-PARZIALE-FULL-JAVA
  File: Lib/shared-props/rules/proposta/proposta.salvataggioParzialeFullJava.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE445</program>

  Regola: SALVA-PROPOSTA-PARZIALE
  File: Lib/shared-props/rules/proposta/proposta.salvataggioParziale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE445</program>


============================================================
PROGRAMMA COBOL: VWLSE446 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-PRENOTAZ-SCADENZA
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/caricaPercipientiPrenotazioneScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE446</program>


============================================================
PROGRAMMA COBOL: VWLSE447 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PERCIPIENTE-PRENOTAZIONE
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/inserisciPercipientePrenotazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE447</program>


============================================================
PROGRAMMA COBOL: VWLSE448 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/prenotazione/scadenza.registraPrenotazScadenza.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE448</program>


============================================================
PROGRAMMA COBOL: VWLSE449 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-PRENOTAZ-RISCATTO
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/caricaPercipientiPrenotazRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE449</program>


============================================================
PROGRAMMA COBOL: VWLSE450 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-RISCATTO-FONDI-UL
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.registraPrenotazFondiUL.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE450</program>


============================================================
PROGRAMMA COBOL: VWLSE451 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-RISCATTO-TRADIZIONALE
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.registraPrenotazTradizionali.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE451</program>


============================================================
PROGRAMMA COBOL: VWLSE452 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PALETTE
  File: Lib/shared-props/rules/proposta/proposta.caricaPalette.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE452</program>


============================================================
PROGRAMMA COBOL: VWLSE454 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-QUESTIONARIO
  File: Lib/shared-props/rules/proposta/salvaQuestionario.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE454</program>


============================================================
PROGRAMMA COBOL: VWLSE455 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-VALORI-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.controllaValoriRiscattoIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE455</program>


============================================================
PROGRAMMA COBOL: VWLSE456 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA-RAPPORTI-REIMPIEGO
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/caricaListaRapportiReimpiego.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE456</program>


============================================================
PROGRAMMA COBOL: VWLSE457 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUMERO-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.numeroProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE457</program>


============================================================
PROGRAMMA COBOL: VWLSE458 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CARICA-GARANZIE-DATI-TECNICI-UT-SBEX
  File: Lib/shared-props/rules/proposta/proposta.garanzie.caricaUT_SBEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE458</program>

  Regola: CARICA-GARANZIE-DATI-TECNICI-UT
  File: Lib/shared-props/rules/proposta/proposta.garanzie.caricaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE458</program>


============================================================
PROGRAMMA COBOL: VWLSE459 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-GARANZIE-LISTA-UT
  File: Lib/shared-props/rules/proposta/proposta.garanzie.caricaListaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE459</program>


============================================================
PROGRAMMA COBOL: VWLSE461 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE461</program>

  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT-SBEX
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUT_SBEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE461</program>


============================================================
PROGRAMMA COBOL: VWLSE462 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-PRENOTAZ-SINISTRO
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/caricaPercipientiPrenotazioneSinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE462</program>


============================================================
PROGRAMMA COBOL: VWLSE463 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA-RAPPORTI-REIMPIEGO-INDEX
  File: Lib/shared-props/rules/backOffice/percipientiPrenotazione/caricaListaRapportiReimpiegoIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE463</program>


============================================================
PROGRAMMA COBOL: VWLSE464 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: TRASFERIMENTO-POLIZZE
  File: Lib/shared-props/rules/trasferimentoPolizze/trasferimentopolizze.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE464</program>


============================================================
PROGRAMMA COBOL: VWLSE465 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-RAPPORTI-ASSICURATO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.caricaListaRapportiAssicurato.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE465</program>


============================================================
PROGRAMMA COBOL: VWLSE466 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CONFERMA-ADEGUATEZZA
  File: Lib/shared-props/rules/proposta/salvaConfermaAdeguatezza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE466</program>


============================================================
PROGRAMMA COBOL: VWLSE467 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CONFERMA-ADEGUATEZZA
  File: Lib/shared-props/rules/proposta/caricaConfermaAdeguatezza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE467</program>


============================================================
PROGRAMMA COBOL: VWLSE468 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-PRENOTAZ-RISCATTO-INDEX
  File: Lib/shared-props/rules/backOffice/percipientiNew/riscatto.modificaPercipientePrenotazIndex.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE468</program>


============================================================
PROGRAMMA COBOL: VWLSE469 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CORRETTIVI-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/dettaglioCorrettiviInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE469</program>


============================================================
PROGRAMMA COBOL: VWLSE470 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-GLOBALE-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.controlloGlobaleProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE470</program>


============================================================
PROGRAMMA COBOL: VWLSE472 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-PIANO-SMOBILIZZO
  File: Lib/shared-props/rules/backOffice/gestioni/pianoSmobilizzo/pianoSmobilizzo.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE472</program>


============================================================
PROGRAMMA COBOL: VWLSE473 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-PIANO-SMOBILIZZO
  File: Lib/shared-props/rules/backOffice/gestioni/pianoSmobilizzo/pianoSmobilizzo.salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE473</program>


============================================================
PROGRAMMA COBOL: VWLSE474 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/dettaglioImposteInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE474</program>


============================================================
PROGRAMMA COBOL: VWLSE475 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-CONCESSIONE-PRESTITI-PRENOTAZ
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriConcessionePrenotaz.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE475</program>


============================================================
PROGRAMMA COBOL: VWLSE476 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PRESTITO-PRENOTAZ
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.inserisciConcessionePrestitoPrenotaz.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE476</program>


============================================================
PROGRAMMA COBOL: VWLSE477 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONCESSIONE-PRESTITI-CONFERMA-PRENOTAZ
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriConcessioneConfermaPrenotaz.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE477</program>


============================================================
PROGRAMMA COBOL: VWLSE479 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-BLOCCHI
  File: Lib/shared-props/rules/proposta/proposta.caricaListaBlocchi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE479</program>


============================================================
PROGRAMMA COBOL: VWLSE481 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CORRETTIVI-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/salvaCorrettiviInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE481</program>


============================================================
PROGRAMMA COBOL: VWLSE482 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-IMPOSTE-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/salvaImposteInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE482</program>


============================================================
PROGRAMMA COBOL: VWLSE483 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-RIALLOCAZIONE
  File: Lib/shared-props/rules/riallocazione/riallocazione.dettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE483</program>


============================================================
PROGRAMMA COBOL: VWLSE484 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-RIALLOCAZIONE
  File: Lib/shared-props/rules/riallocazione/riallocazione.salva.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE484</program>


============================================================
PROGRAMMA COBOL: VWLSE485 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-RID
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.caricaDatiRid.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE485</program>


============================================================
PROGRAMMA COBOL: VWLSE486 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-RID
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.controllaDatiRid.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE486</program>


============================================================
PROGRAMMA COBOL: VWLSE487 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-RAPPORTI-ADERENTE
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.caricaListaRapportiAderente.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE487</program>


============================================================
PROGRAMMA COBOL: VWLSE490 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VERSAMENTO-AGGIUNTIVO
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivo.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE490</program>


============================================================
PROGRAMMA COBOL: VWLSE491 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-VERSAMENTO-AGGIUNTIVO
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivo.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE491</program>


============================================================
PROGRAMMA COBOL: VWLSE493 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-RIATTIVAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/riattivazione.calcola.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE493</program>


============================================================
PROGRAMMA COBOL: VWLSE494 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: APPLICA-CONVENZIONE-DEROGA
  File: Lib/shared-props/rules/proposta/proposta.applicaConvenzioneDeroga.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE494</program>


============================================================
PROGRAMMA COBOL: VWLSE495 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZIONE-RIATTIVAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/riattivazione.registraPrenotazione.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE495</program>


============================================================
PROGRAMMA COBOL: VWLSE496 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-PIP
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaImpostePIP.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE496</program>


============================================================
PROGRAMMA COBOL: VWLSE497 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-IMPOSTE-PIP
  File: Lib/shared-props/rules/backOffice/percipientiNew/salvaImpostePIP.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE497</program>


============================================================
PROGRAMMA COBOL: VWLSE499 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VERSAMENTO-AGGIUNTIVO-MAIN
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivo.caricaMain.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE499</program>


============================================================
PROGRAMMA COBOL: VWLSE500 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-GARANZIE-LISTA-UT-MAIN
  File: Lib/shared-props/rules/proposta/proposta.garanzie.caricaListaUTMain.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE500</program>


============================================================
PROGRAMMA COBOL: VWLSE502 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-PARAMETRI-LIQUIDAZ-TRASFER-FONDI-OUT
  File: Lib/shared-props/rules/backOffice/storni/trasferimentoFondiUscita.calcolaParametriLiquidazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE502</program>


============================================================
PROGRAMMA COBOL: VWLSE504 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PALETTE-MULTIGARANZIA
  File: Lib/shared-props/rules/proposta/proposta.caricaPaletteMultigaranzia.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE504</program>


============================================================
PROGRAMMA COBOL: VWLSE505 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT-MAIN
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUTMain.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE505</program>


============================================================
PROGRAMMA COBOL: VWLSE506 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-RIALLOCAZIONE
  File: Lib/shared-props/rules/riallocazione/riallocazione.annulla.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE506</program>


============================================================
PROGRAMMA COBOL: VWLSE507 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-MULTIGARANZIA
  File: Lib/shared-props/rules/stampe/backOffice/storni/multigaranzia.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE507</program>


============================================================
PROGRAMMA COBOL: VWLSE508 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-TIPO-PRODOTTO
  File: Lib/shared-props/rules/stampe/backOffice/storni/caricaTipoProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE508</program>


============================================================
PROGRAMMA COBOL: VWLSE509 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CLAUSOLE-AUTOMATICO
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.salvaClausoleAutomatico.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE509</program>


============================================================
PROGRAMMA COBOL: VWLSE510 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: SALVA-ANAGRAFICA-RAPPORTO
  File: Lib/shared-props/rules/anagrafica/anagrafica.salvaAnagraficaRapporto.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE510</program>

  Regola: SALVA-ENTE-PIGNORATARIO
  File: Lib/shared-props/rules/vincoloPegno/entepignoratario.salva.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE510</program>


============================================================
PROGRAMMA COBOL: VWLSE511 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PREVENTIVO-A-SCADENZA
  File: Lib/shared-props/rules/stampe/preventivoAScadenza/preventivo.scadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE511</program>


============================================================
PROGRAMMA COBOL: VWLSE513 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-UT-CON-PREVALENTE
  File: Lib/shared-props/rules/polizza.elencoUnitaTecnichePrevalente.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE513</program>


============================================================
PROGRAMMA COBOL: VWLSE515 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-AVANTI-RECESSO
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.controllaAvanti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE515</program>


============================================================
PROGRAMMA COBOL: VWLSE516 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-RECESSO-RIMBORSO-PREMI
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.registraPrenotazioneRecessoRimborsoPremi.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE516</program>


============================================================
PROGRAMMA COBOL: VWLSE517 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-DATI-IDENTIFICATIVI-CONVENZIONE
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.aggiornaDatIdentificativiConvenzione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE517</program>


============================================================
PROGRAMMA COBOL: VWLSE518 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VERSAMENTO-AGGIUNTIVO-VIPENSIONO-LISTA-UT
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivoViPensiono.caricaListaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE518</program>


============================================================
PROGRAMMA COBOL: VWLSE519 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-VERSAMENTO-AGGIUNTIVO-VIPENSIONO
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivoViPensiono.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE519</program>


============================================================
PROGRAMMA COBOL: VWLSE521 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FIGURE-TOBE-GESTIONE-ANAGRAFICHE-RUOLO
  File: Lib/shared-props/rules/backOffice/gestioni/gestioneAnagraficheRuolo.caricaFigureToBe.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE521</program>


============================================================
PROGRAMMA COBOL: VWLSE522 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-VARIAZIONE-PUR
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.calcola.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE522</program>


============================================================
PROGRAMMA COBOL: VWLSE523 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: ANNULLO-VARIAZIONE-PUR
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.annullo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE523</program>

  Regola: ANNULLO-VARIAZIONE-PUR-SBEX
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.annulloSBEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE523</program>


============================================================
PROGRAMMA COBOL: VWLSE524 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FONDI-MULTIRAMO
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.caricaFondiMultiRamo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE524</program>


============================================================
PROGRAMMA COBOL: VWLSE525 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-FONDI-MULTIRAMO
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.salvaFondiMultiRamo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE525</program>


============================================================
PROGRAMMA COBOL: VWLSE526 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FONDI-MULTIRAMO-DATI-TECNICI-UT
  File: Lib/shared-props/rules/proposta/proposta.garanzie.caricaFondiMultiRamo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE526</program>


============================================================
PROGRAMMA COBOL: VWLSE527 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-PIP-INQUIRY-LIQUIDAZIONI
  File: Lib/shared-props/rules/inquiry/liquidazioni/dettaglioImpostePIPInquiryLiquidazioni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE527</program>


============================================================
PROGRAMMA COBOL: VWLSE529 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PROPOSTA-DIREZIONE-SENZA-CONTRIBUTI
  File: Lib/shared-props/rules/proposta/proposta.salvataggioDirezioneSenzaContributi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE529</program>


============================================================
PROGRAMMA COBOL: VWLSE530 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ATTIVAZIONE-PROPOSTA-DIREZIONE-SENZA-CONTRIBUTI
  File: Lib/shared-props/rules/proposta/proposta.attivaPropostaDirezioneSenzaContributi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE530</program>


============================================================
PROGRAMMA COBOL: VWLSE531 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-IDENTIFICATIVI-FONDI
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.aggiornaFondiMultiRamo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE531</program>


============================================================
PROGRAMMA COBOL: VWLSE533 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: QUOTATION-CALCOLO-PREMIO
  File: Lib/shared-props/rules/core/quotation/quotation.calcolopremio.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE533</program>


============================================================
PROGRAMMA COBOL: VWLSE534 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FONDI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.caricaFondiSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE534</program>


============================================================
PROGRAMMA COBOL: VWLSE535 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-FONDI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.salvaFondiSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE535</program>


============================================================
PROGRAMMA COBOL: VWLSE536 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PALETTE-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.caricaPaletteSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE536</program>


============================================================
PROGRAMMA COBOL: VWLSE537 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-FONDI-ALLOCAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/switch.caricaDettaglioFondiAllocazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE537</program>


============================================================
PROGRAMMA COBOL: VWLSE538 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-LINEA-INVESTIMENTO-IN
  File: Lib/shared-props/rules/backOffice/gestioni/switch.aggiornaLineaInvestimetntoIn.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE538</program>


============================================================
PROGRAMMA COBOL: VWLSE539 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-QUESTIONARIO-SWITCH
  File: Lib/shared-props/rules/proposta/salvaQuestionarioSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE539</program>


============================================================
PROGRAMMA COBOL: VWLSE540 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: QUOTATION-CALCOLO-PREVENTIVO
  File: Lib/shared-props/rules/core/quotation/quotation.calcolopreventivo.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE540</program>


============================================================
PROGRAMMA COBOL: VWLSE541 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-CONFERMA-ADEGUATEZZA-SWITCH
  File: Lib/shared-props/rules/proposta/salvaConfermaAdeguatezzaSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE541</program>


============================================================
PROGRAMMA COBOL: VWLSE542 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-CONFERMA-ADEGUATEZZA-SWITCH
  File: Lib/shared-props/rules/proposta/caricaConfermaAdeguatezzaSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE542</program>


============================================================
PROGRAMMA COBOL: VWLSE543 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-RIVAL-E-UL
  File: Lib/shared-props/rules/inquiryPolizza/polizza.elencoFondiRivalEUL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE543</program>


============================================================
PROGRAMMA COBOL: VWLSE544 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-FONDI-SWITCH
  File: Lib/shared-props/rules/backOffice/gestioni/switch.annullaFondi.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE544</program>


============================================================
PROGRAMMA COBOL: VWLSE545 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PARAMETRI-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/rimborsoPrestiti/parametriRimborsoPrestiti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE545</program>


============================================================
PROGRAMMA COBOL: VWLSE546 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/rimborsoPrestiti/calcolaRimborsoPrestiti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE546</program>


============================================================
PROGRAMMA COBOL: VWLSE547 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PRENOTAZ-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/rimborsoPrestiti/salvaRimborsoPrestitiPrenotaz.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE547</program>


============================================================
PROGRAMMA COBOL: VWLSE548 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-PRENOTAZ-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/rimborsoPrestiti/confermaRimborsoPrestitiPrenotaz.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE548</program>


============================================================
PROGRAMMA COBOL: VWLSE550 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-DATA-RIALLOCAZIONE
  File: Lib/shared-props/rules/riallocazione/riallocazione.calcolaData.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE550</program>


============================================================
PROGRAMMA COBOL: VWLSE552 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/rimborsoPrestiti/elencoRimborsoPrestiti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE552</program>


============================================================
PROGRAMMA COBOL: VWLSE553 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLO-DATA-FINE-PIANO-SMOBILIZZO
  File: Lib/shared-props/rules/backOffice/gestioni/pianoSmobilizzo/pianoSmobilizzo.calcoloDataFinePiano.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE553</program>


============================================================
PROGRAMMA COBOL: VWLSE554 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-SENZA-SEGUITO-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.confermaSenzaSeguito.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE554</program>


============================================================
PROGRAMMA COBOL: VWLSE557 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RISCATTO-CALCOLO-VALORE
  File: Lib/shared-props/rules/core/riscatto/riscatto.calcolovalore.xml:8
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE557</program>


============================================================
PROGRAMMA COBOL: VWLSE558 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERSAMENTO-SALVA-ATTIVA-PROPOSTA-DIREZIONE
  File: Lib/shared-props/rules/proposta/versamentoAggiuntivo.salvaAttivaPropostaDirezione.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE558</program>


============================================================
PROGRAMMA COBOL: VWLSE559 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VERSAMENTO-AGGIUNTIVO-MULTIINVEST-LISTA-UT
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivoMultiInvest.caricaListaUTMain.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE559</program>


============================================================
PROGRAMMA COBOL: VWLSE560 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT-MAIN-MULTIINVEST
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUTMainMultiInvest.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE560</program>


============================================================
PROGRAMMA COBOL: VWLSE564 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PERCIPIENTI-SINISTRO-RENDITA
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaPercipientiSinistroRendita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE564</program>


============================================================
PROGRAMMA COBOL: VWLSE565 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-SINISTRO-IN-RENDITA
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.confermaInRendita.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE565</program>


============================================================
PROGRAMMA COBOL: VWLSE567 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERISCI-PERCIPIENTE-SINISTRO-RENDITA
  File: Lib/shared-props/rules/backOffice/percipientiNew/inserisciPercipienteSinistroRendita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE567</program>


============================================================
PROGRAMMA COBOL: VWLSE568 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: RESPINGI-LIQUIDAZ-SINISTRO
  File: Lib/shared-props/rules/backOffice/percipientiNew/respingiLiquidazioneSinistri.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE568</program>

  Regola: RESPINGI-PRENOTAZ-SINISTRO
  File: Lib/shared-props/rules/backOffice/percipientiNew/respingiPercipienteSinistri.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE568</program>


============================================================
PROGRAMMA COBOL: VWLSE570 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-ANNULLO-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.caricaParametriAnnulloSinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE570</program>


============================================================
PROGRAMMA COBOL: VWLSE571 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-ANNULLA-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.annullaSinistro.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE571</program>


============================================================
PROGRAMMA COBOL: VWLSE572 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-ANNULLA-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/scadenza/caricaAnnulloScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE572</program>


============================================================
PROGRAMMA COBOL: VWLSE573 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-PROPOSTA-COLLETTIVA
  File: Lib/shared-props/rules/collettive/proposta.collettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE573</program>


============================================================
PROGRAMMA COBOL: VWLSE574 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PRODOTTO-SELEZIONATO-PER-COLLETTIVA
  File: Lib/shared-props/rules/collettive/prodotto.prodottoCollSelezionato.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE574</program>


============================================================
PROGRAMMA COBOL: VWLSE577 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-IDENTIFICATIVI-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.caricaDatIdentificativiCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE577</program>


============================================================
PROGRAMMA COBOL: VWLSE578 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-IDENTIFICATIVI-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.controllaDatIdentificativiCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE578</program>


============================================================
PROGRAMMA COBOL: VWLSE579 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: SALVA-PROPOSTA-PARZ-FIN-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.salvataggioCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE579</program>

  Regola: SALVA-PROPOSTA-PARZ-FIN-COLLETTIVA-FULL-JAVA
  File: Lib/shared-props/rules/proposta/proposta.salvataggioCollettivaFullJava.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE579</program>


============================================================
PROGRAMMA COBOL: VWLSE580 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: APRI-PROPOSTA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.apriPropostaCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE580</program>


============================================================
PROGRAMMA COBOL: VWLSE581 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-GLOBALE-PROPOSTA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.controlloGlobalePropostaCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE581</program>


============================================================
PROGRAMMA COBOL: VWLSE582 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-RIEPILOGO-PROPOSTA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.caricaRiepilogoCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE582</program>


============================================================
PROGRAMMA COBOL: VWLSE583 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MANDA-SENZA-SEGUITO-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.senzaSeguitoCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE583</program>


============================================================
PROGRAMMA COBOL: VWLSE584 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: APRI-PROPOSTA-TESTA
  File: Lib/shared-props/rules/proposta/proposta.apriPropostaTesta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE584</program>


============================================================
PROGRAMMA COBOL: VWLSE588 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INQUIRY-DETTAGLIO-PIANO-SMOBILIZZO
  File: Lib/shared-props/rules/inquiry/pianoSmobilizzo/pianoSmobilizzo.inquiryDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE588</program>


============================================================
PROGRAMMA COBOL: VWLSE589 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-MOD-PAG-GLOBALE
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.controllaModPagGlobale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE589</program>


============================================================
PROGRAMMA COBOL: VWLSE590 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-MOD-PAG-PRIMA-RATA
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.caricaModPagPrimaRata.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE590</program>


============================================================
PROGRAMMA COBOL: VWLSE591 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-MOD-PAG-PRIMA-RATA
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.controllaModPagPrimaRata.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE591</program>


============================================================
PROGRAMMA COBOL: VWLSE592 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERSAMENTO-CARICA-ESECUTORE
  File: Lib/shared-props/rules/proposta/proposta.versamentoCaricaEsecutore.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE592</program>


============================================================
PROGRAMMA COBOL: VWLSE593 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERSAMENTO-SALVA-ESECUTORE
  File: Lib/shared-props/rules/proposta/proposta.versamentoSalvaEsecutore.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE593</program>


============================================================
PROGRAMMA COBOL: VWLSE594 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-IMPOSTE-PIP-COL
  File: Lib/shared-props/rules/backOffice/percipientiNew/caricaImpostePIP_Coll.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE594</program>


============================================================
PROGRAMMA COBOL: VWLSE595 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-IMPOSTE-PIP-COL
  File: Lib/shared-props/rules/backOffice/percipientiNew/salvaImpostePIP_Coll.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE595</program>


============================================================
PROGRAMMA COBOL: VWLSE597 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-ANNULLA-SCADENZA-BATCH
  File: Lib/shared-props/rules/backOffice/storni/scadenza/caricaAnnulloScadenzaBatch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE597</program>


============================================================
PROGRAMMA COBOL: VWLSE598 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-ANNULLA-SCADENZA-BATCH
  File: Lib/shared-props/rules/backOffice/storni/scadenza/confermaAnnullaScadenzaBatch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE598</program>


============================================================
PROGRAMMA COBOL: VWLSE599 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: GESTIONE-PROPOSTA-ATTIVABILE
  File: Lib/shared-props/rules/proposta/proposta.gestionePropostaAttivabile.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE599</program>


============================================================
PROGRAMMA COBOL: VWLSE600 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-SCU-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.dettaglio.SCU.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE600</program>


============================================================
PROGRAMMA COBOL: VWLSE601 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-SCU-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.salva.SCU.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE601</program>


============================================================
PROGRAMMA COBOL: VWLSE602 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-RESPINGI-PREN-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.annullaRespingiPrenotaz.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE602</program>


============================================================
PROGRAMMA COBOL: VWLSE603 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-ANNULLO-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaParametriAnnulloRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE603</program>


============================================================
PROGRAMMA COBOL: VWLSE605 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VARIAZIONE-CAPITALE
  File: Lib/shared-props/rules/proposta/proposta.variazioneCapitale.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE605</program>


============================================================
PROGRAMMA COBOL: VWLSE606 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-VARIAZIONE-CAPITALE
  File: Lib/shared-props/rules/proposta/proposta.variazioneCapitale.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE606</program>


============================================================
PROGRAMMA COBOL: VWLSE615 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-ANNULLA-LIQ-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.confermaAnnullaLiqRiscatto.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE615</program>


============================================================
PROGRAMMA COBOL: VWLSE616 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: EXIT-SWITCH
  File: Lib/shared-props/rules/exitSwitch.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE616</program>


============================================================
PROGRAMMA COBOL: VWLSE617 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ATTIVAZIONE-ONLINE-PROPOSTA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.attivazioneOnlineCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE617</program>


============================================================
PROGRAMMA COBOL: VWLSE618 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-ALTRI-DATI
  File: Lib/shared-props/rules/polizza.elencoAltriDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE618</program>


============================================================
PROGRAMMA COBOL: VWLSE619 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INTERFACCIA-COLLETTORE
  File: Lib/shared-props/rules/collettore/interfacciaCollettore.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE619</program>


============================================================
PROGRAMMA COBOL: VWLSE621 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERIFICA-TIPO-PRESTAZIONE
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.verificaTipoPrestazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE621</program>


============================================================
PROGRAMMA COBOL: VWLSE622 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERSAMENTO-SALVA-ATTIVA-PROPOSTA-DIREZIONE-MULTIINVEST-WORKFLOW
  File: Lib/shared-props/rules/proposta/versamentoAggiuntivoMultiInvest.salvaAttivaPropostaDirezioneWorkflow.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE622</program>


============================================================
PROGRAMMA COBOL: VWLSE623 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-MOD-PAG-GLOBALE-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.controllaModPagGlobaleCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE623</program>


============================================================
PROGRAMMA COBOL: VWLSE624 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-MOD-PAG-PRIMA-RATA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.caricaModPagPrimaRataCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE624</program>


============================================================
PROGRAMMA COBOL: VWLSE625 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-MOD-PAG-PRIMA-RATA-COLLETTIVA
  File: Lib/shared-props/rules/proposta/proposta.riepilogo.controllaModPagPrimaRataCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE625</program>


============================================================
PROGRAMMA COBOL: VWLSE627 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-SINISTRO-MULTINVEST
  File: Lib/shared-props/rules/backOffice/storni/sinistro/prenotazione/sinistro.registraPrenotazSinistroMultinvest.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE627</program>


============================================================
PROGRAMMA COBOL: VWLSE629 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INTERFACCIA-CONTABILITA-RCT
  File: Lib/shared-props/rules/contabilita/rct/interfacciaContabilitaRct.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE629</program>


============================================================
PROGRAMMA COBOL: VWLSE631 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FIGURE-ANAGRAFICHE-RELAZIONATE
  File: Lib/shared-props/rules/proposta/proposta.caricaFigureAnagraficheRelazionate.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE631</program>


============================================================
PROGRAMMA COBOL: VWLSE634 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VERSAMENTO-SALVA-ATTIVA-PROPOSTA-DIREZIONE-INCASSO-TRADIZIONALE-WORKFLOW
  File: Lib/shared-props/rules/proposta/versamentoAggiuntivoTradizionale.salvaAttivaPropostaDirezioneWorkflow.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE634</program>


============================================================
PROGRAMMA COBOL: VWLSE635 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VA-TRADIZIONALE
  File: Lib/shared-props/rules/proposta/proposta.versamentoAggiuntivoTradizionale.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE635</program>


============================================================
PROGRAMMA COBOL: VWLSE636 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT-NUOVA-TABOO
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUTNuovaTaboo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE636</program>

  Regola: CONTROLLA-GARANZIE-DATI-TECNICI-UT-NUOVA-TABOO-SBEX
  File: Lib/shared-props/rules/proposta/proposta.garanzie.controllaUTNuovaTaboo_SBEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE636</program>


============================================================
PROGRAMMA COBOL: VWLSE638 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICARICA-PER-SOVRAPPREMI
  File: Lib/shared-props/rules/proposta/proposta.garanzie.ricaricaPerSovrappremi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE638</program>


============================================================
PROGRAMMA COBOL: VWLSE639 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-VARIAZIONE-RISCHIO-ASS
  File: Lib/shared-props/rules/backOffice/gestioni/variazioneRischioAssicurato.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE639</program>


============================================================
PROGRAMMA COBOL: VWLSE640 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-DATI-VARIAZIONE-RISCHIO-ASS
  File: Lib/shared-props/rules/backOffice/gestioni/variazioneRischioAssicurato.calcolaDati.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE640</program>


============================================================
PROGRAMMA COBOL: VWLSE641 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DATI-VARIAZIONE-RISCHIO-ASS
  File: Lib/shared-props/rules/backOffice/gestioni/variazioneRischioAssicurato.salvaDati.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE641</program>


============================================================
PROGRAMMA COBOL: VWLSE642 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PRENOTAZIONE-VARIAZIONE-RISCHIO-ASS
  File: Lib/shared-props/rules/backOffice/gestioni/variazioneRischioAssicurato.caricaPrenotazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE642</program>


============================================================
PROGRAMMA COBOL: VWLSE643 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INIZIALIZZA-TARIFFE
  File: Lib/shared-props/rules/backOffice/gestioni/garanzia/garanzie.inizializzaGaranzia.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE643</program>


============================================================
PROGRAMMA COBOL: VWLSE644 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LEGGI-TARIFFE
  File: Lib/shared-props/rules/backOffice/gestioni/garanzia/garanzie.leggiGaranzia.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE644</program>


============================================================
PROGRAMMA COBOL: VWLSE647 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-TARIFFE
  File: Lib/shared-props/rules/backOffice/gestioni/garanzia/garanzie.salvaGaranzia.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE647</program>


============================================================
PROGRAMMA COBOL: VWLSE648 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-DECADENZA
  File: Lib/shared-props/rules/backOffice/storni/sinistro/decadenza.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE648</program>


============================================================
PROGRAMMA COBOL: VWLSE649 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-DECADENZA
  File: Lib/shared-props/rules/backOffice/storni/sinistro/decadenza.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE649</program>


============================================================
PROGRAMMA COBOL: VWLSE650 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONFERMA-DECADENZA
  File: Lib/shared-props/rules/backOffice/storni/sinistro/decadenza.conferma.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE650</program>


============================================================
PROGRAMMA COBOL: VWLSE651 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZIONE-DECADENZA
  File: Lib/shared-props/rules/backOffice/storni/sinistro/decadenza.registraPrenotazione.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE651</program>


============================================================
PROGRAMMA COBOL: VWLSE652 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PALETTE-RIATTIVAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/riattivazione.caricaPaletteRiattivazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE652</program>


============================================================
PROGRAMMA COBOL: VWLSE653 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-QUESTIONARIO-RIATTIVAZIONE
  File: Lib/shared-props/rules/proposta/salvaQuestionarioRiattivazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE653</program>


============================================================
PROGRAMMA COBOL: VWLSE654 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: UPDATE-DESIGNAZIONE-BENEFICIARIO-TESTE
  File: Lib/shared-props/rules/collettive/polizza.updateDesignazioneBeneficiarioTeste.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE654</program>


============================================================
PROGRAMMA COBOL: VWLSE655 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RETTIFICA-QUIETANZA
  File: Lib/shared-props/rules/contabilita/quietanza/rettificaQuietanza.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>VWLSE655</program>


============================================================
PROGRAMMA COBOL: VWLSE656 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICARICA-UT-PER-CONVENZIONE
  File: Lib/shared-props/rules/proposta/proposta.garanzie.ricaricaUTPerConvenzione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE656</program>


============================================================
PROGRAMMA COBOL: VWLSE657 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INQUIRY-RISCHI-COMUNI-UW
  File: Lib/shared-props/rules/proposta/proposta.rischicomuni.inquiryRischiComuniUW.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE657</program>


============================================================
PROGRAMMA COBOL: VWLSE658 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-PREVENTIVO-RISCATTO-NETTO
  File: Lib/shared-props/rules/backOffice/storni/preventivoRiscattoNetto/preventivoRiscattoNetto.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE658</program>


============================================================
PROGRAMMA COBOL: VWLSE659 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-PREVENTIVO-RISCATTO-NETTO
  File: Lib/shared-props/rules/backOffice/storni/preventivoRiscattoNetto/preventivoRiscattoNetto.calcoloPreventivo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE659</program>


============================================================
PROGRAMMA COBOL: VWLSE660 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-PREVENTIVO-CONVERSIONE-RENDITA
  File: Lib/shared-props/rules/backOffice/storni/preventivoRendita.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE660</program>


============================================================
PROGRAMMA COBOL: VWLSE661 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-PREVENTIVO-CONVERSIONE-RENDITA
  File: Lib/shared-props/rules/backOffice/storni/preventivoRendita.calcolaPreventivo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE661</program>


============================================================
PROGRAMMA COBOL: VWLSE662 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: STAMPA-PREVENTIVO-CONVERSIONE-RENDITA
  File: Lib/shared-props/rules/backOffice/storni/preventivoRendita.preparaStampa.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE662</program>


============================================================
PROGRAMMA COBOL: VWLSE663 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-CUMULO-QST-ESTERNI
  File: Lib/shared-props/rules/proposta/calcolaCumuloPerQuestionariEsterni.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE663</program>


============================================================
PROGRAMMA COBOL: VWLSE664 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-IDENTIFICATIVI-DECORRENZA
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.aggiornaDecorrenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE664</program>


============================================================
PROGRAMMA COBOL: VWLSE665 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICALCOLA-DECORRENZA
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.ricalcolaDecorrenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE665</program>


============================================================
PROGRAMMA COBOL: VWLSE666 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-VARIAZIONI-UT
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.controllaVariazioniUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSE666</program>


============================================================
PROGRAMMA COBOL: VWLSEVDB (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-VALORI-MESSA-BILANCIO
  File: Lib/shared-props/rules/backOffice/storni/valoriMessaBilancio/valoriMessaBilancio.calcoloPreventivo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSEVDB</program>


============================================================
PROGRAMMA COBOL: VWLSLTIT (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TITOLI
  File: Lib/shared-props/rules/incasso/incasso.elencoTitoli.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWLSLTIT</program>


============================================================
PROGRAMMA COBOL: VWSINCCZ (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: GESTIONE-EVENTO-INCASSO-ZURICH
  File: Lib/shared-props/rules/proposta/gestioneEventoIncassoZurich.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>VWSINCCZ</program>


============================================================
PROGRAMMA COBOL: WDBL0041 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-UTENTI
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.utente.elenco.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WDBL0041</program>


============================================================
PROGRAMMA COBOL: WNDISPC0 (811 chiamate)
============================================================

INITIAL (811 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-MESE
  File: Lib/shared-props/rules/estrattoConto.movimentiDelMese.xml:5
  Transazione: SB00
  Connector: A05TARE
  Linea: <initialProgram>WNDISPC0</initialProgram>

  Regola: ELENCO-MOVIMENTI-GIORNO
  File: Lib/shared-props/rules/estrattoConto.movimentiDelGiorno.xml:5
  Transazione: SB00
  Connector: A05TARE
  Linea: <initialProgram>WNDISPC0</initialProgram>

  Regola: ANDAMENTO-FONDI
  File: Lib/shared-props/rules/elencoAndamentoFondi.xml:5
  Transazione: SB00
  Connector: A05TARE
  Linea: <initialProgram>WNDISPC0</initialProgram>

  Regola: DISIMPEGNO-POLIZZA
  File: Lib/shared-props/rules/disimpegnoPolizza.xml:5
  Transazione: SB00
  Connector: A05TARE
  Linea: <initialProgram>WNDISPC0</initialProgram>

  Regola: EXIT-SWITCH
  File: Lib/shared-props/rules/exitSwitch.xml:5
  Transazione: SB00
  Connector: A05TARE
  Linea: <initialProgram>WNDISPC0</initialProgram>

  ... e altri 806 esempi


============================================================
PROGRAMMA COBOL: WNDISPC1 (68 chiamate)
============================================================

INITIAL (68 chiamate):
----------------------------------------
  Regola: RETTIFICA-QUIETANZA
  File: Lib/shared-props/rules/contabilita/quietanza/rettificaQuietanza.xml:5
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <initialProgram>WNDISPC1</initialProgram>

  Regola: INTERFACCIA-CONTABILITA-RCT
  File: Lib/shared-props/rules/contabilita/rct/interfacciaContabilitaRct.xml:5
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <initialProgram>WNDISPC1</initialProgram>

  Regola: CALCOLO-IMPOSTE-LIQUIDAZIONE
  File: Lib/shared-props/rules/liquidazione/imposte/calcola.imposte.xml:5
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <initialProgram>WNDISPC1</initialProgram>

  Regola: PD-ESEGUI-FORMULA-PER-POSIZIONI
  File: Lib/shared-props/rules/productBuilder/productBuilder.eseguiFormulaPerPosizioni.xml:5
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <initialProgram>WNDISPC1</initialProgram>

  Regola: PD-COMPILA-FORMULA-LUW
  File: Lib/shared-props/rules/productBuilder/productBuilder.compilaFormulaLUW.xml:5
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <initialProgram>WNDISPC1</initialProgram>

  ... e altri 63 esempi


============================================================
PROGRAMMA COBOL: WSAUTH02 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ABIL-APRI-POLIZZA
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.apriPolizza-elencoAttiPortafoglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSAUTH02</program>


============================================================
PROGRAMMA COBOL: WSAUTH03 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ABIL-APRI-UT
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.apriUT-elencoAttiPortafoglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSAUTH03</program>


============================================================
PROGRAMMA COBOL: WSAUTH04 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ABIL-APRI-POLIZZA-COLL
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.apriPolizzaCollettiva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSAUTH04</program>


============================================================
PROGRAMMA COBOL: WSDB0000 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-NUOVO-PROFILO
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.profilo.init.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0000</program>


============================================================
PROGRAMMA COBOL: WSDB0030 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INIT-AZIONE
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.azione.init.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0030</program>


============================================================
PROGRAMMA COBOL: WSDB0041 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-UTENTE
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.utente.salva.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0041</program>


============================================================
PROGRAMMA COBOL: WSDB0042 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELIMINA-UTENTE
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.utente.elimina.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0042</program>


============================================================
PROGRAMMA COBOL: WSDB0043 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MODIFICA-UTENTE
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.utente.modifica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0043</program>


============================================================
PROGRAMMA COBOL: WSDB0044 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-UTENTE
  File: Lib/shared-props/rules/abilitazioni/abilitazioni.utente.carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSDB0044</program>


============================================================
PROGRAMMA COBOL: WSER0001 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LOGON
  File: Lib/shared-props/rules/logon.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0001</program>


============================================================
PROGRAMMA COBOL: WSER0002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INIT-CONT-VITA
  File: Lib/shared-props/rules/inizializzaContestoVita.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0002</program>


============================================================
PROGRAMMA COBOL: WSER0003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RICERCA-ANAGRAFICA
  File: Lib/shared-props/rules/anagrafica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0003</program>


============================================================
PROGRAMMA COBOL: WSER0004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NUOVA-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0004</program>


============================================================
PROGRAMMA COBOL: WSER0005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PRODOTTI
  File: Lib/shared-props/rules/prodotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0005</program>


============================================================
PROGRAMMA COBOL: WSER0006 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CARICA-QUESTIONARIO-APPROPIATEZZA
  File: Lib/shared-props/rules/questionari/appropiatezza/carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0006</program>

  Regola: CARICA-QUESTIONARIO-ADEGUATEZZA
  File: Lib/shared-props/rules/questionari/adeguatezza/carica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0006</program>


============================================================
PROGRAMMA COBOL: WSER0007 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: INVIA-QUESTIONARIO-APPROPIATEZZA
  File: Lib/shared-props/rules/questionari/appropiatezza/invio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0007</program>

  Regola: INVIA-QUESTIONARIO-ADEGUATEZZA
  File: Lib/shared-props/rules/questionari/adeguatezza/invio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0007</program>


============================================================
PROGRAMMA COBOL: WSER0008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DECODIFICA-ERRORE
  File: Lib/shared-props/rules/decodificaErrore.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0008</program>


============================================================
PROGRAMMA COBOL: WSER0009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-ANAGRAFICA
  File: Lib/shared-props/rules/proposta/proposta.dettaglioAnagrafica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0009</program>


============================================================
PROGRAMMA COBOL: WSER0010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DATI-ANTIRICLAGGIO
  File: Lib/shared-props/rules/proposta/proposta.datiAntiriciclaggio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0010</program>


============================================================
PROGRAMMA COBOL: WSER0011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-ANAGRAFICHE-AL
  File: Lib/shared-props/rules/proposta/proposta.controlloFigureAnagraficheAL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0011</program>


============================================================
PROGRAMMA COBOL: WSER0014 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-ASSICURATO
  File: Lib/shared-props/rules/proposta/teste/proposta.testa.controlloAssicurato.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0014</program>


============================================================
PROGRAMMA COBOL: WSER0016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-RUOLI
  File: Lib/shared-props/rules/proposta/proposta.beneficiari.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0016</program>


============================================================
PROGRAMMA COBOL: WSER0017 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INSERIMENTO-BENEFICIARI
  File: Lib/shared-props/rules/proposta/proposta.inserimentoBeneficiari.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0017</program>


============================================================
PROGRAMMA COBOL: WSER0018 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-DATI-TECNICI
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0018</program>


============================================================
PROGRAMMA COBOL: WSER0022 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-ADEGUATEZZA-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.controlloAdeguatezza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0022</program>


============================================================
PROGRAMMA COBOL: WSER0023 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.salvataggio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0023</program>


============================================================
PROGRAMMA COBOL: WSER0029 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: STAMPA-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.stampaProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0029</program>


============================================================
PROGRAMMA COBOL: WSER0030 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-RAPPORTI
  File: Lib/shared-props/rules/elencoRapporti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0030</program>


============================================================
PROGRAMMA COBOL: WSER0033 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NEW-DETTAGLIO-POLIZZA
  File: Lib/shared-props/rules/inquiryPolizza/polizza.dettaglioPolizza_new.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0033</program>


============================================================
PROGRAMMA COBOL: WSER0034 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PRODOTTO-SELEZIONATO
  File: Lib/shared-props/rules/prodotto.prodottoSelezionato.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0034</program>


============================================================
PROGRAMMA COBOL: WSER0035 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: EXIT
  File: Lib/shared-props/rules/exit.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0035</program>

  Regola: CANCELLA-WORKING-AREA-RIASS
  File: Lib/shared-props/rules/tabelleSistema/trattatoRiass/trattatoRiass.cancellaWorkingArea.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0035</program>


============================================================
PROGRAMMA COBOL: WSER0036 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FIGURE-ANAGRAFICHE-AL
  File: Lib/shared-props/rules/proposta/proposta.caricaFigureAnagraficheAL.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0036</program>


============================================================
PROGRAMMA COBOL: WSER0037 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciCarica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0037</program>


============================================================
PROGRAMMA COBOL: WSER0038 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-POSIZIONI-UT
  File: Lib/shared-props/rules/unitaTecniche.elencoPosizioniUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0038</program>


============================================================
PROGRAMMA COBOL: WSER0040 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: NEW-DETTAGLIO-POSIZIONE-UT
  File: Lib/shared-props/rules/inquiryPolizza/unitaTecniche.dettaglioPosizioneUT_new.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0040</program>


============================================================
PROGRAMMA COBOL: WSER0043 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-UT
  File: Lib/shared-props/rules/polizza.elencoUnitaTecniche.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0043</program>


============================================================
PROGRAMMA COBOL: WSER0044 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI
  File: Lib/shared-props/rules/elencoFondi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0044</program>


============================================================
PROGRAMMA COBOL: WSER0046 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PATRIMONIO
  File: Lib/shared-props/rules/elencoPatrimonio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0046</program>


============================================================
PROGRAMMA COBOL: WSER0047 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ATTIVAZIONE-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.attivaProposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0047</program>


============================================================
PROGRAMMA COBOL: WSER0049 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-TITOLI-OLD
  File: Lib/shared-props/rules/incasso/incasso.elencoTitoliOLD.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0049</program>


============================================================
PROGRAMMA COBOL: WSER0050 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-RATA-SCADUTA
  File: Lib/shared-props/rules/incasso/incasso.incassaRataScaduta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0050</program>


============================================================
PROGRAMMA COBOL: WSER0051 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-GIORNO
  File: Lib/shared-props/rules/estrattoConto.movimentiDelGiorno.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0051</program>


============================================================
PROGRAMMA COBOL: WSER0052 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-MESE
  File: Lib/shared-props/rules/estrattoConto.movimentiDelMese.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0052</program>


============================================================
PROGRAMMA COBOL: WSER0058 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-MANCATO-PERFEZIONAMENTO
  File: Lib/shared-props/rules/backOffice/storni/mancatoPerfez.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0058</program>


============================================================
PROGRAMMA COBOL: WSER0059 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-MANCATO-PERFEZIONAMENTO
  File: Lib/shared-props/rules/backOffice/storni/mancatoPerfez.controllaParametri.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0059</program>


============================================================
PROGRAMMA COBOL: WSER0060 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0060</program>


============================================================
PROGRAMMA COBOL: WSER0061 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0061</program>


============================================================
PROGRAMMA COBOL: WSER0063 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-LIQUIDAZIONE
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaParametriLiquidazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0063</program>


============================================================
PROGRAMMA COBOL: WSER0065 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-LIQUIDAZIONE
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.controllaParametriLiquidazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0065</program>


============================================================
PROGRAMMA COBOL: WSER0068 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DISIMPEGNO-POLIZZA
  File: Lib/shared-props/rules/disimpegnoPolizza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0068</program>


============================================================
PROGRAMMA COBOL: WSER0074 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CONFERMA-ANNULLO-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.confermaAnnulloRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0074</program>

  Regola: CONFERMA-LIQUIDAZIONE-RISCATTO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.confermaLiquidazioneRiscatto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0074</program>


============================================================
PROGRAMMA COBOL: WSER0075 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-FONDI-RISCATTO-UL
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaFondi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0075</program>


============================================================
PROGRAMMA COBOL: WSER0079 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-VALORI-RISCATTO-LIQUIDAZIONE
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.calcolaValoriRiscattoLiquidazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0079</program>


============================================================
PROGRAMMA COBOL: WSER0080 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA-MOD-PAGAMENTO
  File: Lib/shared-props/rules/backOffice/riscatto.listaModalitaPagamento.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0080</program>


============================================================
PROGRAMMA COBOL: WSER0081 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-FONDI-PRENOTAZ-RISCATTO-UL
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.controllaFondi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0081</program>


============================================================
PROGRAMMA COBOL: WSER0084 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-PRENOTAZ-RISCATTO-UL
  File: Lib/shared-props/rules/backOffice/storni/riscatto/prenotazione/riscatto.caricaParametriPrenotaz.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0084</program>


============================================================
PROGRAMMA COBOL: WSER0085 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-LIQUIDAZIONE-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.caricaParametriLiquidazioneSinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0085</program>


============================================================
PROGRAMMA COBOL: WSER0086 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-IMPORTO-LIQUIDAZIONE-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.calcolaImportoLiquidazioneSinistro.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0086</program>


============================================================
PROGRAMMA COBOL: WSER0087 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-PRENOTAZ-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/prenotazione/sinistro.registraPrenotazSinistro.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0087</program>


============================================================
PROGRAMMA COBOL: WSER0092 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-STORNO-ALTRE-CAUSE
  File: Lib/shared-props/rules/backOffice/storni/stornoAltreCause.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0092</program>


============================================================
PROGRAMMA COBOL: WSER0093 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-STORNO-ALTRE-CAUSE
  File: Lib/shared-props/rules/backOffice/storni/stornoAltreCause.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0093</program>


============================================================
PROGRAMMA COBOL: WSER0094 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RECESSO-RIMBORSO-PREMI
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0094</program>


============================================================
PROGRAMMA COBOL: WSER0095 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-PREMI-RIMBORSO
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.caricaListaRimborsoPremi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0095</program>


============================================================
PROGRAMMA COBOL: WSER0096 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-RECESSO-RIMBORSO-PREMI
  File: Lib/shared-props/rules/backOffice/storni/recessoRimborsoPremi.registraRecessoRimborsoPremi.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0096</program>


============================================================
PROGRAMMA COBOL: WSER0097 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-INSOLVENZA-RIDUZIONE
  File: Lib/shared-props/rules/backOffice/storni/insolvenzaRiduzione.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0097</program>


============================================================
PROGRAMMA COBOL: WSER0098 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-INSOLVENZA-RIDUZIONE
  File: Lib/shared-props/rules/backOffice/storni/insolvenzaRiduzione.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0098</program>


============================================================
PROGRAMMA COBOL: WSER0099 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-INSOLVENZA-RIDUZIONE
  File: Lib/shared-props/rules/backOffice/storni/insolvenzaRiduzione.registraInsolvenzaRiduzione.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0099</program>


============================================================
PROGRAMMA COBOL: WSER0100 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-CAMBIO-FRAZIONAMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/cambioFrazionamento.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0100</program>


============================================================
PROGRAMMA COBOL: WSER0101 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-CAMBIO-FRAZIONAMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/cambioFrazionamento.registra.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0101</program>


============================================================
PROGRAMMA COBOL: WSER0102 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETT-STORICO-VARIAZIONE-PUR
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.dettaglioStorico.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0102</program>


============================================================
PROGRAMMA COBOL: WSER0103 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-VARIAZIONE-PUR
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0103</program>


============================================================
PROGRAMMA COBOL: WSER0104 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: REGISTRA-VARIAZIONE-PUR
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.registra.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0104</program>

  Regola: REGISTRA-VARIAZIONE-PUR-SB00
  File: Lib/shared-props/rules/backOffice/gestioni/variazionePUR.registraSB00.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0104</program>


============================================================
PROGRAMMA COBOL: WSER0105 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0105</program>


============================================================
PROGRAMMA COBOL: WSER0106 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-SINISTRO
  File: Lib/shared-props/rules/backOffice/storni/sinistro/sinistro.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0106</program>


============================================================
PROGRAMMA COBOL: WSER0107 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-DIFFERIMENTO-A-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0107</program>


============================================================
PROGRAMMA COBOL: WSER0108 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RECUPERA-IMPORTO-DIFFERIMENTO-A-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.recuperoImportoDifferito.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0108</program>


============================================================
PROGRAMMA COBOL: WSER0109 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RECUPERA-CAPITALE-DIFFERIMENTO-A-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.recuperoDatiCapitale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0109</program>


============================================================
PROGRAMMA COBOL: WSER0110 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLA-CAPITALE-DIFFERIMENTO-A-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.calcolaDatiCapitale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0110</program>


============================================================
PROGRAMMA COBOL: WSER0111 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-DIFFERIMENTO-A-SCADENZA
  File: Lib/shared-props/rules/backOffice/storni/differimentoScadenza.registraDifferimentoScadenza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0111</program>


============================================================
PROGRAMMA COBOL: WSER0112 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RIATTIVAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/riattivazione.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0112</program>


============================================================
PROGRAMMA COBOL: WSER0124 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-VARIAZIONI-CONTRATTO
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0124</program>


============================================================
PROGRAMMA COBOL: WSER0125 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-VARIAZIONI-CONTRATTO
  File: Lib/shared-props/rules/backOffice/gestioni/variazioniContratto.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0125</program>


============================================================
PROGRAMMA COBOL: WSER0126 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0126</program>


============================================================
PROGRAMMA COBOL: WSER0127 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0127</program>


============================================================
PROGRAMMA COBOL: WSER0128 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONCESSIONE-PRESTITI-CALCOLA
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaRiepilogoConcessione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0128</program>


============================================================
PROGRAMMA COBOL: WSER0131 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-RIMBORSO-PRESTITI
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriRimborso.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0131</program>


============================================================
PROGRAMMA COBOL: WSER0132 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: RIMBORSO-PRESTITI-CONTROLLA
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriRimborsoCalcola.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0132</program>


============================================================
PROGRAMMA COBOL: WSER0133 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: VARIAZIONE-PRESTITI-CONFERMA
  File: Lib/shared-props/rules/backOffice/gestioni/prestiti.caricaParametriVariazioneInteressi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0133</program>


============================================================
PROGRAMMA COBOL: WSER0135 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-RISCATTO-LIQUIDAZIONE-PER-ANNULLO
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.caricaLiquidazionePerAnnullo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0135</program>


============================================================
PROGRAMMA COBOL: WSER0136 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-STABILIZZAZIONE-PREMIO
  File: Lib/shared-props/rules/backOffice/gestioni/stabilizzazionePremio.caricaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0136</program>


============================================================
PROGRAMMA COBOL: WSER0137 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: SALVA-DETTAGLIO-STABILIZZAZIONE-PREMIO
  File: Lib/shared-props/rules/backOffice/gestioni/stabilizzazionePremio.salvaDettaglio.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0137</program>


============================================================
PROGRAMMA COBOL: WSER0141 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-DELEGA-RID
  File: Lib/shared-props/rules/proposta/proposta.delegaRid.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0141</program>


============================================================
PROGRAMMA COBOL: WSER0142 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-DELEGA-RID
  File: Lib/shared-props/rules/proposta/proposta.delegaRid.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0142</program>


============================================================
PROGRAMMA COBOL: WSER0143 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-IDENTIFICATIVI-CANALE
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.controllaDatiCanale.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0143</program>


============================================================
PROGRAMMA COBOL: WSER0145 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-MOVIMENTO-AR
  File: Lib/shared-props/rules/proposta/proposta.antiriciclaggio.caricaDettaglioMovimentoAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0145</program>


============================================================
PROGRAMMA COBOL: WSER0146 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-MOVIMENTO-AR
  File: Lib/shared-props/rules/proposta/proposta.antiriciclaggio.registraMovimentoAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0146</program>


============================================================
PROGRAMMA COBOL: WSER0147 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-AR
  File: Lib/shared-props/rules/proposta/proposta.antiriciclaggio.elencoMovimentiAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0147</program>


============================================================
PROGRAMMA COBOL: WSER0148 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-IDENTIFICATIVI
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.caricaDatIdentificativi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0148</program>


============================================================
PROGRAMMA COBOL: WSER0149 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-IDENTIFICATIVI
  File: Lib/shared-props/rules/proposta/proposta.datiIdentificativi.controllaDatIdentificativi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0149</program>


============================================================
PROGRAMMA COBOL: WSER0150 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: IMPOSTA-NUOVA-UT
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.impostaNuovaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0150</program>


============================================================
PROGRAMMA COBOL: WSER0151 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI-UT
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.caricaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0151</program>


============================================================
PROGRAMMA COBOL: WSER0152 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: SALVA-PROPOSTA-DIREZIONE
  File: Lib/shared-props/rules/proposta/proposta.salvataggioDirezione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0152</program>

  Regola: SALVA-PROPOSTA-DIREZIONE-SBEX
  File: Lib/shared-props/rules/proposta/proposta.salvataggioDirezione_SBEX.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSER0152</program>


============================================================
PROGRAMMA COBOL: WSER0154 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-PROPOSTE
  File: Lib/shared-props/rules/elencoProposte.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0154</program>


============================================================
PROGRAMMA COBOL: WSER0155 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI-COMPL-SOVRAP-VINCOLI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciCSVCarica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0155</program>


============================================================
PROGRAMMA COBOL: WSER0156 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-TECNICI-COMPL-SOVRAP-VINCOLI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciCSVControlla.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0156</program>


============================================================
PROGRAMMA COBOL: WSER0157 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-TECNICI-UT-PER-VINCOLI
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.controllaUTPerVincoli.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0157</program>


============================================================
PROGRAMMA COBOL: WSER0158 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI-PROVVIGIONI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciProvvigioniCarica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0158</program>


============================================================
PROGRAMMA COBOL: WSER0159 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-TECNICI-PROVVIGIONI
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciProvvigioniControlla.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0159</program>


============================================================
PROGRAMMA COBOL: WSER0160 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-GLOBALE-UT
  File: Lib/shared-props/rules/proposta/proposta.unitaTecniche.controlloGlobaleUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0160</program>


============================================================
PROGRAMMA COBOL: WSER0161 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-PREMI-UT
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.caricaPremiUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0161</program>


============================================================
PROGRAMMA COBOL: WSER0164 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-RIEPILOGO-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.caricaRiepilogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0164</program>


============================================================
PROGRAMMA COBOL: WSER0165 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLO-NUMERO-UT
  File: Lib/shared-props/rules/proposta/proposta.unitaTecniche.calcoloNumeroUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0165</program>


============================================================
PROGRAMMA COBOL: WSER0168 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ATTIVAZIONE-PROPOSTA-DIREZIONE
  File: Lib/shared-props/rules/proposta/proposta.attivaPropostaDirezione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0168</program>


============================================================
PROGRAMMA COBOL: WSER0171 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CALCOLO-PREMI-RIEPILOGO-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.calcolaPremiRiepilogo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0171</program>


============================================================
PROGRAMMA COBOL: WSER0172 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DATI-TECNICI-UT-POSIZIONE
  File: Lib/shared-props/rules/proposta/proposta.datiTecnici.dettaglioUTPerPosizione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0172</program>


============================================================
PROGRAMMA COBOL: WSER0173 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: LISTA-UT-PROPOSTA
  File: Lib/shared-props/rules/proposta/proposta.listaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0173</program>


============================================================
PROGRAMMA COBOL: WSER0174 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-UT-PER-TARIFFA
  File: Lib/shared-props/rules/proposta/proposta.datiTecniciAggiornaPremioTariffa.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0174</program>


============================================================
PROGRAMMA COBOL: WSER0175 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DATI-TECNICI-UT-SPORTELLO
  File: Lib/shared-props/rules/proposta/proposta.sportelloBancario.unitaTecniche.caricaDatiTecniciUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0175</program>


============================================================
PROGRAMMA COBOL: WSER0176 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-DATI-TECNICI-UT-SPORTELLO
  File: Lib/shared-props/rules/proposta/proposta.sportelloBancario.unitaTecniche.controllaDatiTecniciUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0176</program>


============================================================
PROGRAMMA COBOL: WSER0177 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: IMPOSTA-NUOVA-UT-SPORTELLO
  File: Lib/shared-props/rules/proposta/proposta.sportelloBancario.unitaTecniche.impostaNuovaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0177</program>


============================================================
PROGRAMMA COBOL: WSER0178 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: AGGIORNA-TARIFFA-UNITA-TECNICA-SPORTELLO
  File: Lib/shared-props/rules/proposta/proposta.sportelloBancario.unitaTecniche.aggiornaTariffa.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0178</program>


============================================================
PROGRAMMA COBOL: WSER0179 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANNULLA-UNITA-TECNICA
  File: Lib/shared-props/rules/proposta/proposta.annullaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0179</program>


============================================================
PROGRAMMA COBOL: WSER0181 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CONFERMA-LIQUIDAZIONE-RISCATTO-FIP-PIP
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.confermaLiquidazioneRiscattoFipPip.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0181</program>

  Regola: CONFERMA-ANNULLO-RISCATTO-FIP-PIP
  File: Lib/shared-props/rules/backOffice/storni/riscatto/riscatto.confermaAnnulloRiscattoFipPip.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0181</program>


============================================================
PROGRAMMA COBOL: WSER0182 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: DETTAGLIO-DATI-TECNICI-UT-SPORTELLO-POSIZIONE
  File: Lib/shared-props/rules/proposta/proposta.sportelloBancario.unitaTecniche.dettaglioUTPerPosizione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0182</program>


============================================================
PROGRAMMA COBOL: WSER0185 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: MANDA-SENZA-SEGUITO
  File: Lib/shared-props/rules/proposta/proposta.senzaSeguito.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0185</program>


============================================================
PROGRAMMA COBOL: WSER0186 (2 chiamate)
============================================================

MAIN (2 chiamate):
----------------------------------------
  Regola: CARICA-MAX-VINCOLABILE
  File: Lib/shared-props/rules/backOffice/gestioni/vincoloPegno.caricaMaxVincolabile.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0186</program>

  Regola: CARICA-PARAMETRI-STABILIZZAZIONE-PREMIO
  File: Lib/shared-props/rules/backOffice/gestioni/stabilizzazionePremio.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0186</program>


============================================================
PROGRAMMA COBOL: WSER0223 (3 chiamate)
============================================================

MAIN (3 chiamate):
----------------------------------------
  Regola: CARICA-PARAMETRI-OPZIONI-DIFFERIMENTO-OLD
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.caricaParametriOld.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <!--  	<program>WSER0223</program>-->

  Regola: CARICA-PARAMETRI-OPZIONI-DIFFERIMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.caricaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0223</program>

  Regola: CARICA-PARAMETRI-OPZIONI-DIFFERIMENTO-NEW
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.caricaParametriNew.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <!--  	<program>WSER0223</program>-->


============================================================
PROGRAMMA COBOL: WSER0224 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLA-PARAMETRI-OPZIONI-DIFFERIMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.controllaParametri.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0224</program>


============================================================
PROGRAMMA COBOL: WSER0225 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-OPZIONI-DIFFERIMENTO
  File: Lib/shared-props/rules/backOffice/gestioni/opzioniDifferimento.registra.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSER0225</program>


============================================================
PROGRAMMA COBOL: WSERD008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ANDAMENTO-FONDI
  File: Lib/shared-props/rules/elencoAndamentoFondi.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD008</program>


============================================================
PROGRAMMA COBOL: WSERD039 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-FONDI-DESTINAZIONE
  File: Lib/shared-props/rules/backOffice/gestioni/switch.elencoFondiDestinazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD039</program>


============================================================
PROGRAMMA COBOL: WSERD052 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-LISTA-LIMITAZIONE-OPERATIVITA
  File: Lib/shared-props/rules/backOffice/gestioni/limitazioneOperativita.caricaLista.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD052</program>


============================================================
PROGRAMMA COBOL: WSERD072 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-PRESTAZIONE
  File: Lib/shared-props/rules/proposta/valoriPrestazione.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD072</program>


============================================================
PROGRAMMA COBOL: WSERD076 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-TARIFFA
  File: Lib/shared-props/rules/proposta/valoriTariffa.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD076</program>


============================================================
PROGRAMMA COBOL: WSERD091 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-TARIFFA-VA
  File: Lib/shared-props/rules/proposta/valoriTariffaVersamentoAggiuntivo.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD091</program>


============================================================
PROGRAMMA COBOL: WSERD092 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-VALORI-TARIFFA-AGGIUNGI-UT
  File: Lib/shared-props/rules/proposta/valoriTariffaAggiungiUTPolizza.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSERD092</program>


============================================================
PROGRAMMA COBOL: WSPB0002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-ELENCO-PRODOTTI
  File: Lib/shared-props/rules/productBuilder/productBuilder.elencoProdotti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0002</program>


============================================================
PROGRAMMA COBOL: WSPB0003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-ELENCO-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.elencoUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0003</program>


============================================================
PROGRAMMA COBOL: WSPB0004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-ELENCO-FORMULE
  File: Lib/shared-props/rules/productBuilder/productBuilder.elencoFormule.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0004</program>


============================================================
PROGRAMMA COBOL: WSPB0005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-DETTAGLIO-PRODOTTO
  File: Lib/shared-props/rules/productBuilder/productBuilder.dettaglioProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0005</program>


============================================================
PROGRAMMA COBOL: WSPB0006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-SALVA-PRODOTTO
  File: Lib/shared-props/rules/productBuilder/productBuilder.salvaProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0006</program>


============================================================
PROGRAMMA COBOL: WSPB0007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-NUOVO-PRODOTTO
  File: Lib/shared-props/rules/productBuilder/productBuilder.nuovoProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0007</program>


============================================================
PROGRAMMA COBOL: WSPB0010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-CANCELLA-PRODOTTO
  File: Lib/shared-props/rules/productBuilder/productBuilder.cancellaProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0010</program>


============================================================
PROGRAMMA COBOL: WSPB0011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-DETTAGLIO-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.dettaglioUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0011</program>


============================================================
PROGRAMMA COBOL: WSPB0012 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-SALVA-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.salvaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0012</program>


============================================================
PROGRAMMA COBOL: WSPB0015 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COPIA-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.copiaUnitaTecnica.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0015</program>


============================================================
PROGRAMMA COBOL: WSPB0016 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-CANCELLA-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.cancellaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0016</program>


============================================================
PROGRAMMA COBOL: WSPB0021 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-DETTAGLIO-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.dettaglioFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0021</program>


============================================================
PROGRAMMA COBOL: WSPB0022 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-ELENCO-VARIABILI
  File: Lib/shared-props/rules/productBuilder/productBuilder.elencoVariabili.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0022</program>


============================================================
PROGRAMMA COBOL: WSPB0028 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COPIA-PRODOTTO
  File: Lib/shared-props/rules/productBuilder/productBuilder.copiaProdotto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0028</program>


============================================================
PROGRAMMA COBOL: WSPB0031 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COPIA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.copiaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0031</program>


============================================================
PROGRAMMA COBOL: WSPB0033 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-CARICA-PROVA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.caricaProvaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0033</program>


============================================================
PROGRAMMA COBOL: WSPB0035 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-ESEGUI-PROVA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.eseguiProvaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0035</program>


============================================================
PROGRAMMA COBOL: WSPB0037 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-DEBUG-PROVA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.debugProvaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0037</program>


============================================================
PROGRAMMA COBOL: WSPB0039 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-NUOVA-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.nuovaUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0039</program>


============================================================
PROGRAMMA COBOL: WSPB0040 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-RICERCA-ELENCO-PRODOTTI
  File: Lib/shared-props/rules/productBuilder/productBuilder.ricercaElencoProdotti.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0040</program>


============================================================
PROGRAMMA COBOL: WSPB0043 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-SALVA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.salvaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0043</program>


============================================================
PROGRAMMA COBOL: WSPB0044 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COMPILA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.compilaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0044</program>


============================================================
PROGRAMMA COBOL: WSPB0045 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COMPILA-FORMULE-UT
  File: Lib/shared-props/rules/productBuilder/productBuilder.compilaFormuleUT.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0045</program>


============================================================
PROGRAMMA COBOL: WSPB0046 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-NUOVA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.nuovaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0046</program>


============================================================
PROGRAMMA COBOL: WSPB0047 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-CANCELLA-FORMULA
  File: Lib/shared-props/rules/productBuilder/productBuilder.cancellaFormula.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSPB0047</program>


============================================================
PROGRAMMA COBOL: WSPB0048 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: PD-COMPILA-FORMULA-LUW
  File: Lib/shared-props/rules/productBuilder/productBuilder.compilaFormulaLUW.xml:7
  Transazione: SBEX
  Connector: A05TAREEX
  Linea: <program>WSPB0048</program>


============================================================
PROGRAMMA COBOL: WSYN0002 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-INCASSO-PROPOSTA
  File: Lib/shared-props/rules/incasso/incasso.caricaIncassoproposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0002</program>


============================================================
PROGRAMMA COBOL: WSYN0003 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: INCASSO-PROPOSTA
  File: Lib/shared-props/rules/incasso/incasso.eseguiIncassoproposta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0003</program>


============================================================
PROGRAMMA COBOL: WSYN0004 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-INCASSO-TITOLO
  File: Lib/shared-props/rules/incasso/incasso.caricaIncassoTitoloRataScaduta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0004</program>


============================================================
PROGRAMMA COBOL: WSYN0005 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-INCASSO-TITOLO
  File: Lib/shared-props/rules/incasso/incasso.eseguiIncassoTitoloRataScaduta.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0005</program>


============================================================
PROGRAMMA COBOL: WSYN0006 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-INCASSO-DIRETTO
  File: Lib/shared-props/rules/incasso/incasso.caricaIncassoDiretto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0006</program>


============================================================
PROGRAMMA COBOL: WSYN0007 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ESEGUI-INCASSO-DIRETTO
  File: Lib/shared-props/rules/incasso/incasso.eseguiIncassoDiretto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0007</program>


============================================================
PROGRAMMA COBOL: WSYN0008 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CARICA-DETTAGLIO-MOVIMENTO-AR-SYNCRO
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.caricaDettaglioMovimentoAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0008</program>


============================================================
PROGRAMMA COBOL: WSYN0009 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: REGISTRA-MOVIMENTO-AR-SYNCRO
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.registraMovimentoAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0009</program>


============================================================
PROGRAMMA COBOL: WSYN0010 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: CONTROLLO-INC-DIR-MOVIMENTO-AR-SYNCRO
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.controlloPerIncassoDiretto.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0010</program>


============================================================
PROGRAMMA COBOL: WSYN0011 (1 chiamate)
============================================================

MAIN (1 chiamate):
----------------------------------------
  Regola: ELENCO-MOVIMENTI-AR-SYNCRO
  File: Lib/shared-props/rules/incasso/incasso.antiriciclaggio.elencoMovimentiAR.xml:7
  Transazione: SB00
  Connector: A05TARE
  Linea: <program>WSYN0011</program>


============================================================
STATISTICHE PER TRANSAZIONE
============================================================

Transazioni più utilizzate:
  SB00: 1623 programmi
  SBEX: 136 programmi

STATISTICHE PER CONNECTOR
----------------------------------------

Connector più utilizzati:
  A05TARE: 1625 programmi
  A05TAREEX: 134 programmi
