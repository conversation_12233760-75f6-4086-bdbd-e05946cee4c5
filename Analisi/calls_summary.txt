RIASSUNTO CHIAMATE ESTERNE TROVATE
==================================================

Totale chiamate trovate: 23507


COBOL_LEGACY: 189 chiamate
--------------------------------
  javax\.resource\.cci\.: 104 occorrenze
  com\.ibm\.etools\.marshall\.: 84 occorrenze
  import\s+javax\.resource(\.|\w)*: 1 occorrenze

Esempi:
  Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java:9 -> import com.ibm.etools.marshall.util.*;...
  Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java:20 -> javax.resource.cci.Record, javax.resource.cci.Streamable,...
  Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java:21 -> com.ibm.etools.marshall.RecordBytes {...
  Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/RichiestaSwitch.java:9 -> import com.ibm.etools.marshall.util.*;...
  Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/RichiestaSwitch.java:20 -> public class RichiestaSwitch implements javax.resource.cci.Record,...
  ... e altri 184 esempi


DATABASE_CALLS: 2892 chiamate
----------------------------------
  SqlMapClient: 2882 occorrenze
  DataSource: 8 occorrenze
  ResultSet: 2 occorrenze

Esempi:
  Services.Common.Persistency/src/spring-conf/spring-ibatis.xml:11 -> <bean id="blueLifeSqlMapConfig" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">...
  Services.Common.Persistency/src/spring-conf/spring-ibatis.xml:13 -> <property name="dataSource" ref="${applicationDatasource}" />...
  Services.Common.Persistency/src/spring-conf/datasource.xml:9 -> <bean id="applicationDatasourceOnLine" class="org.springframework.jndi.JndiObjectFactoryBean">...
  Services.Common.Persistency/src/spring-conf/datasource.xml:10 -> <property name="jndiName" value="${jndiDatasourceName}" />...
  Services.Common.Persistency/src/it/sistinf/albedoweb/services/prodotto/prodotto/service/persistency/ProdottoProdottoDao.java:80 -> getSqlMapClient().insert("PRODOTTO.insertAliquoteProvvigionali", map);...
  ... e altri 2887 esempi


EJB_CALLS: 527 chiamate
-----------------------------
  FactoryBean\.getBean\s*\(: 303 occorrenze
  @Stateless: 119 occorrenze
  ejblocal:: 46 occorrenze
  SimpleRemoteStatelessSessionProxyFactoryBean: 34 occorrenze
  LocalStatelessSessionProxyFactoryBean: 23 occorrenze
  @EJB\s+: 2 occorrenze

Esempi:
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:8 -> <session name="Test" simple-binding-name="ejblocal:albedoTest"/>...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:9 -> <session name="Abilitazioni" simple-binding-name="ejblocal:albedoAbilitazioni"/>...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:10 -> <session name="Prodotto" simple-binding-name="ejblocal:albedoProdotto"/>...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:11 -> <session name="CustAccessori" simple-binding-name="ejblocal:albedoCustAccessori"/>...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:12 -> <session name="Cust" simple-binding-name="ejblocal:albedoCust"/>...
  ... e altri 522 esempi


EXTERNAL_APIS: 13903 chiamate
---------------------------------
  http://: 12435 occorrenze
  endpoint: 1385 occorrenze
  basic\.auth\.: 44 occorrenze
  client_id: 15 occorrenze
  client_secret: 14 occorrenze
  URL\s*\(: 5 occorrenze
  URLConnection: 5 occorrenze

Esempi:
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-web-bnd.xml:3 -> xmlns="http://websphere.ibm.com/xml/ns/javaee"...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-web-bnd.xml:4 -> xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-web-bnd.xml:5 -> xsi:schemaLocation="http://websphere.ibm.com/xml/ns/javaee http://websphere.ibm.com/xml/ns/javaee/ib...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:3 -> xmlns="http://websphere.ibm.com/xml/ns/javaee"...
  REST.Services.Cobol.Web/WebContent/WEB-INF/ibm-ejb-jar-bnd.xml:4 -> xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"...
  ... e altri 13898 esempi


REST_CALLS: 333 chiamate
------------------------------
  @POST: 120 occorrenze
  @DELETE: 93 occorrenze
  @Path\s*\(: 36 occorrenze
  @PUT: 35 occorrenze
  Response\.ok\(\): 35 occorrenze
  @GET: 14 occorrenze

Esempi:
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java:70 -> @PostConstruct...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java:59 -> @PostConstruct...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/abilitazione/Abilitazioni.java:60 -> @PostConstruct...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java:388 -> @PostConstruct...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/anagrafica/Anagrafica.java:64 -> @PostConstruct...
  ... e altri 328 esempi


SPRING_BEANS: 1950 chiamate
--------------------------------
  <property\s+name=: 1389 occorrenze
  <bean\s+id=: 459 occorrenze
  ref=: 94 occorrenze
  @Bean: 5 occorrenze
  ApplicationContext: 3 occorrenze

Esempi:
  REST.Services.Cobol.Web/src/beanRefFactory.xml:8 -> <!--bean id="albedoWeb" class="org.springframework.context.support.ClassPathXmlApplicationContext"--...
  REST.Services.Cobol.Web/src/beanRefFactory.xml:9 -> <bean id="bluelife" class="org.springframework.context.support.ClassPathXmlApplicationContext">...
  REST.Services.Cobol.Web/src/spring-conf/external-services.xml:8 -> <bean id="PortafoglioRapportoProxy" class="org.springframework.ejb.access.SimpleRemoteStatelessSessi...
  REST.Services.Cobol.Web/src/spring-conf/external-services.xml:9 -> <property name="jndiName" value="${prefixJNDINameEJBProxy}PortafoglioRapporto"/>...
  REST.Services.Cobol.Web/src/spring-conf/external-services.xml:10 -> <property name="businessInterface" value="it.sistinf.albedoweb.services.portafoglio.rapporto.service...
  ... e altri 1945 esempi


WEB_SERVICES: 3713 chiamate
--------------------------------
  \.wsdl: 1611 occorrenze
  @WebMethod: 1329 occorrenze
  SOAPBinding: 448 occorrenze
  @WebService: 234 occorrenze
  serviceInterface: 61 occorrenze
  JaxWsPortProxyFactoryBean: 10 occorrenze
  wsdlDocumentUrl: 10 occorrenze
  endpointAddress: 10 occorrenze

Esempi:
  REST.Services.Cobol.Web/src/spring-conf/external-services.xml:112 -> <property name="businessInterface" value="it.sistinf.albedoweb.services.deroghe.DerogheServiceInterf...
  REST.Services.Cobol.Web/src/spring-conf/external-services.xml:113 -> <property name="homeInterface" value="it.sistinf.albedoweb.services.deroghe.RemoteDerogheServiceInte...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java:6 -> import it.sistinf.albedoweb.common.util.WsdlMapUtils;...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java:5 -> import it.sistinf.albedoweb.common.util.WsdlMapUtils;...
  REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java:11 -> import it.sistinf.albedoweb.services.common.types.WsdlMap;...
  ... e altri 3708 esempi

