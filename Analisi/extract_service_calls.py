#!/usr/bin/env python3
"""
extract_service_calls.py

Estrae specificamente le chiamate ai servizi EJB e le configurazioni di servizi esterni.
Analizza i file di configurazione Spring e le implementazioni Java per identificare
tutti i servizi chiamati e le loro configurazioni.

Output:
  - **File CSV**: `service_calls.csv` con dettagli delle chiamate ai servizi
  - **File di testo**: `service_mapping.txt` con mappatura dei servizi

Prerequisiti:
  - Python 3.6+
"""
import os
import re
import csv
from collections import defaultdict

# Configurazione
SRC_PATH = "/Users/<USER>/Desktop/Conding/NAW/Source"
OUTPUT_DIR = "/Users/<USER>/Desktop/Conding/NAW/Analisi"
OUTPUT_CSV = os.path.join(OUTPUT_DIR, 'service_calls.csv')
OUTPUT_MAPPING = os.path.join(OUTPUT_DIR, 'service_mapping.txt')

# Pattern per identificare servizi e chiamate
SERVICE_PATTERNS = {
    'EJB_PROXY_DEFINITION': [
        r'<bean\s+id="([^"]+)Proxy"\s+class="org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean">',
        r'<bean\s+id="([^"]+)"\s+class="org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean">',
    ],
    'EJB_PROXY_USAGE': [
        r'FactoryBean\.getBean\s*\(\s*["\']([^"\']+)["\']',
        r'@EJB\s*\(\s*name\s*=\s*["\']([^"\']+)["\']',
    ],
    'WEB_SERVICE_DEFINITION': [
        r'<bean\s+id="([^"]+)"\s+class="org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean"',
    ],
    'REST_ENDPOINT': [
        r'@Path\s*\(\s*["\']([^"\']+)["\']',
        r'endpointAddress["\']?\s*value\s*=\s*["\']([^"\']+)["\']',
    ],
    'JNDI_LOOKUP': [
        r'jndiName["\']?\s*value\s*=\s*["\']([^"\']+)["\']',
        r'lookup\s*\(\s*["\']([^"\']+)["\']',
    ],
    'DATABASE_CONFIG': [
        r'<property\s+name="jndiName"\s+value="\$\{([^}]+)\}',
        r'dataSource["\']?\s*ref\s*=\s*["\']([^"\']+)["\']',
    ]
}

# File estensioni da scansionare
SCAN_EXT = ('.java', '.xml', '.properties')


def extract_service_calls(src_path):
    """
    Estrae tutte le chiamate ai servizi e le configurazioni.
    Restituisce dizionario con categoria -> lista di tuple (file, linea, match, context).
    """
    results = defaultdict(list)
    
    for root, _, files in os.walk(src_path):
        for fname in files:
            if not fname.lower().endswith(SCAN_EXT):
                continue
            
            path = os.path.join(root, fname)
            try:
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                for lineno, line in enumerate(lines, 1):
                    line_stripped = line.strip()
                    if not line_stripped or line_stripped.startswith('//') or line_stripped.startswith('*'):
                        continue
                    
                    # Cerca pattern in ogni categoria
                    for category, patterns in SERVICE_PATTERNS.items():
                        for pattern in patterns:
                            matches = re.finditer(pattern, line, re.IGNORECASE)
                            for match in matches:
                                # Estrai contesto (linee precedenti e successive)
                                context_lines = []
                                start_idx = max(0, lineno - 3)
                                end_idx = min(len(lines), lineno + 2)
                                
                                for i in range(start_idx, end_idx):
                                    prefix = ">>> " if i == lineno - 1 else "    "
                                    context_lines.append(f"{prefix}{i+1:4d}: {lines[i].rstrip()}")
                                
                                context = "\n".join(context_lines)
                                
                                # Estrai il valore catturato dal gruppo
                                captured_value = match.group(1) if match.groups() else match.group(0)
                                
                                results[category].append({
                                    'file': path,
                                    'line': lineno,
                                    'pattern': pattern,
                                    'matched_text': line_stripped,
                                    'captured_value': captured_value,
                                    'context': context
                                })
                                
            except Exception as e:
                print(f"Errore leggendo {path}: {e}")
                continue
    
    return results


def write_service_csv(results, output_file):
    """Scrive i risultati in formato CSV."""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Category', 'Service_Name', 'File_Path', 'Line_Number', 'Pattern', 'Full_Line', 'Context'])
        
        for category, calls in results.items():
            for call in calls:
                relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
                writer.writerow([
                    category,
                    call['captured_value'],
                    relative_path,
                    call['line'],
                    call['pattern'],
                    call['matched_text'],
                    call['context'].replace('\n', ' | ')
                ])


def write_service_mapping(results, output_file):
    """Scrive una mappatura dei servizi trovati."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("MAPPATURA SERVIZI E CHIAMATE ESTERNE\n")
        f.write("=" * 60 + "\n\n")
        
        # Raggruppa per servizio
        service_map = defaultdict(lambda: defaultdict(list))
        
        for category, calls in results.items():
            for call in calls:
                service_name = call['captured_value']
                service_map[service_name][category].append(call)
        
        f.write(f"Totale servizi unici trovati: {len(service_map)}\n\n")
        
        # Scrivi dettagli per ogni servizio
        for service_name, categories in sorted(service_map.items()):
            f.write(f"\n{'='*60}\n")
            f.write(f"SERVIZIO: {service_name}\n")
            f.write(f"{'='*60}\n")
            
            for category, calls in categories.items():
                f.write(f"\n{category} ({len(calls)} occorrenze):\n")
                f.write("-" * 40 + "\n")
                
                for call in calls[:3]:  # Mostra solo i primi 3 esempi
                    relative_path = call['file'].replace(SRC_PATH, '').lstrip('/')
                    f.write(f"  File: {relative_path}:{call['line']}\n")
                    f.write(f"  Linea: {call['matched_text']}\n")
                    f.write(f"  Pattern: {call['pattern']}\n\n")
                
                if len(calls) > 3:
                    f.write(f"  ... e altri {len(calls) - 3} esempi\n\n")
        
        # Statistiche per categoria
        f.write(f"\n{'='*60}\n")
        f.write("STATISTICHE PER CATEGORIA\n")
        f.write(f"{'='*60}\n")
        
        for category, calls in sorted(results.items(), key=lambda x: len(x[1]), reverse=True):
            unique_services = len(set(call['captured_value'] for call in calls))
            f.write(f"\n{category}:\n")
            f.write(f"  Totale chiamate: {len(calls)}\n")
            f.write(f"  Servizi unici: {unique_services}\n")
            
            # Top 5 servizi più chiamati in questa categoria
            service_counts = defaultdict(int)
            for call in calls:
                service_counts[call['captured_value']] += 1
            
            f.write("  Top 5 servizi:\n")
            for service, count in sorted(service_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                f.write(f"    {service}: {count} chiamate\n")


def main():
    # Verifica directories
    if not os.path.isdir(SRC_PATH):
        print(f"Errore: '{SRC_PATH}' non è una directory valida.")
        return
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("Estrazione chiamate ai servizi in corso...")
    print(f"Directory sorgenti: {SRC_PATH}")
    print(f"Directory output: {OUTPUT_DIR}")
    print()

    # Estrazione
    results = extract_service_calls(SRC_PATH)
    
    if not results:
        print("Nessuna chiamata ai servizi rilevata.")
        return

    # Calcola totali
    total_calls = sum(len(calls) for calls in results.values())
    total_services = len(set(
        call['captured_value'] 
        for calls in results.values() 
        for call in calls
    ))
    
    print(f"Trovate {total_calls} chiamate ai servizi")
    print(f"Servizi unici identificati: {total_services}")
    print(f"Categorie: {len(results)}")
    print()
    
    # Stampa riassunto per categoria
    for category, calls in sorted(results.items(), key=lambda x: len(x[1]), reverse=True):
        unique_services = len(set(call['captured_value'] for call in calls))
        print(f"{category}: {len(calls)} chiamate, {unique_services} servizi unici")
    
    # Scrittura file CSV
    write_service_csv(results, OUTPUT_CSV)
    print(f"\nDettagli salvati in: {OUTPUT_CSV}")
    
    # Scrittura mappatura
    write_service_mapping(results, OUTPUT_MAPPING)
    print(f"Mappatura salvata in: {OUTPUT_MAPPING}")
    
    print("\nEstrazione completata!")


if __name__ == '__main__':
    main()
