# PRODOTTI ASSICURATIVI E DIFFERENZIAZIONI DI LOGICA

## Panoramica

Il sistema NAW gestisce diversi tipi di prodotti assicurativi con logiche di business specifiche per ogni categoria. Le differenziazioni sono implementate attraverso controlli condizionali, helper classes e configurazioni specifiche.

## Tipologie di Prodotti Identificate

### 🏦 **Categorie Principali**

#### **1. Prodotti per Categoria Contrattuale**
- **INDIVIDUALI** (`"individuale"`) - Polizze individuali
- **COLLETTIVE** (`"collettiva"`) - Polizze collettive

#### **2. Prodotti per Tipologia Tecnica**
- **MULTIGARANZIA** (`"M"`) - Prodotti multi-garanzia
- **UNIT LINKED** (`"U"`) - Prodotti unit linked
- **GESTIONE SEPARATA** - Prodotti a gestione separata
- **TRADIZIONALI** - Prodotti tradizionali

#### **3. Prodotti Specializzati**
- **PIP** (`"PIP"`) - Piani Individuali Pensionistici
- **FIP** (`"FIP"`) - Fondi Individuali Pensionistici
- **TRANCHING** (`"SI"`) - Prodotti con tranching
- **VIPENSIONO** - Prodotti ViPensiono

### 🔍 **Struttura di Identificazione Prodotto**

<augment_code_snippet path="Source/Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/prodotto/types/ProdottoTipoDettaglio.java" mode="EXCERPT">
````java
public class ProdottoTipoDettaglio {
    protected String prodotto;        // Codice prodotto
    protected String multigaranzia;   // "M" = Multigaranzia, "U" = Unit Linked
    protected String vipensiono;      // "PIP" = PIP, "FIP" = FIP
    protected String tranching;       // "SI" = Con tranching, "NO" = Senza
    protected String modelloCosti;    // Modello costi applicato
    protected String mipac;           // Flag MIPAC
    protected String pianoPens;       // Piano pensionistico
}
````
</augment_code_snippet>

## Differenziazioni di Logica per Prodotto

### 🎯 **1. Routing API per Tipo Prodotto**

<augment_code_snippet path="Source/REST.Services.Utility/src/it/sistinf/rest/model/polizza/utility/PolizzaUtility.java" mode="EXCERPT">
````java
public static Map<String, Boolean> getMappa(ProdottoTipoDettaglio prodottoTD) {
    Map<String, Boolean> aMap = new HashMap<String, Boolean>();
    
    // Controllo Tranching
    String tranching = GenericUtils.stringToString(prodottoTD.getTranching());
    boolean isTranching = SrvConstants.SI.equals(tranching);
    aMap.put("isTranching", isTranching);
    
    // Controllo ViPensiono (PIP Multigaranzia)
    String tipoProdotto = GenericUtils.stringToString(prodottoTD.getMultigaranzia());
    String pip = GenericUtils.stringToString(prodottoTD.getVipensiono());
    boolean isViPensiono = SrvConstants.PIP.equals(pip) && "M".equals(tipoProdotto);
    aMap.put("isViPensiono", isViPensiono);
    
    // Controllo PIP Unit Linked
    boolean isPip = SrvConstants.PIP.equals(pip) && "U".equals(tipoProdotto);
    aMap.put("isPip", isPip);
    
    return aMap;
}
````
</augment_code_snippet>

### 🔄 **2. Logica Condizionale per Catalogo Prodotti**

<augment_code_snippet path="Source/Services.Common.Impl/src/it/sistinf/albedoweb/services/flusso/prometeia/service/impl/FlussoPrometeiaImpl.java" mode="EXCERPT">
````java
public CatalogoProdottoResponse catalogoProdotto(CatalogoProdottoRequest request) {
    CatalogoProdottoResponse res = new CatalogoProdottoResponse();
    List<CatalogoProdotto> listaCatalogoProdotto = new ArrayList<>();
    
    String tipoProdotto = WsdlMapUtils.getString(request.getCatalogoInfoMap(), SrvConstants.TIPO_PRODOTTO);
    
    if (SrvConstants.COLLETTIVE.equals(tipoProdotto)) {
        listaCatalogoProdotto = prometeiaDao.selectListaCatalogoProdottoCollettive(request);
    } else if (SrvConstants.INDIVIDUALI.equals(tipoProdotto)) {
        listaCatalogoProdotto = prometeiaDao.selectListaCatalogoProdottoIndividuali(request);
    } else if (SrvConstants.COLLETTIVE_NV.equals(tipoProdotto)) {
        listaCatalogoProdotto = prometeiaDao.selectListaCatalogoProdottoCollettivaNoVita(request);
    }
    
    res.getCatalogoProdottoSelect().addAll(listaCatalogoProdotto);
    return res;
}
````
</augment_code_snippet>

### 🏗️ **3. Gestione Differenziata per Unit Linked vs Multigaranzia**

<augment_code_snippet path="Source/albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java" mode="EXCERPT">
````java
private PolizzaInfoSimpleEstesa valorizzaUnitLinked(PolizzaInfoSimpleEstesa pise, 
    Boolean isMultigaranzia, Boolean isPip, String tipoLiquidaz) {
    
    Boolean isUnitLinked = Boolean.FALSE;
    
    if (isMultigaranzia) {
        if (!isPip) {
            // Le Polizze su prodotti Multigaranzia NO-PIP [ovvero MultiInvest] 
            // hanno sempre la componente UNIT
            isUnitLinked = Boolean.TRUE;
        } else {
            // Le Polizze su prodotti Multigaranzia PIP 
            // Sono obbligato a leggere le posizioni sulla T024 
            List<PosizioneInfo> listaPosizioni = selectPosizioni(pise);
            if (!listaPosizioni.isEmpty()) {
                listaPosizioni = filtraPosizioni(listaPosizioni, tipoLiquidaz);
                if(!listaPosizioni.isEmpty()) {
                    isUnitLinked = isPolizzaUnitLinked(listaPosizioni);
                }
            }
        }
    }
    
    pise.setUnitLinked(isUnitLinked);
    return pise;
}
````
</augment_code_snippet>

### 🔍 **4. Helper per Identificazione Prodotti PIP/FIP**

<augment_code_snippet path="Source/Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/prodotto/helper/ProdottoProdottoHelper.java" mode="EXCERPT">
````java
public class ProdottoProdottoHelper {
    
    public boolean isProdottoPip(ProdottoDatiComuni130Info prodottoInfo) {
        String codProd = GenericUtils.stringToString(prodottoInfo.getCodProd());
        return (SrvConstants.TIPO_PRODOTTO_PIP.equals(codProd) || 
                SrvConstants.TIPO_PRODOTTO_FIP.equals(codProd));
    }
    
    public boolean isProdottoFatca(ProdottoDati130Info prodottoInfo) {
        return SrvConstants.SI.equals(GenericUtils.stringToString(prodottoInfo.getFatca()));
    }
}
````
</augment_code_snippet>

### 📊 **5. Differenziazione per Categoria Individuale vs Collettiva**

<augment_code_snippet path="Source/Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/prodotto/service/helpers/ProductBuilderProdottoHelper.java" mode="EXCERPT">
````java
public void setDatiProdotto_daTabella004(ProdottoDettaglio prodotto, DatiProdotto datiProdotto) {
    prodotto.setCodSoc(datiProdotto.getCodsoc());
    prodotto.setCodProd(datiProdotto.getCodprod());
    
    // Differenziazione Individuale vs Collettiva
    if (datiProdotto.getPolizza() == null || 
        datiProdotto.getPolizza().equals("" + SrvConstants.INDIVIDUALEN)) {
        prodotto.setCategoria(SrvConstants.INDIVIDUALE);
    } else {
        prodotto.setCategoria(SrvConstants.COLLETTIVA);
    }
    
    // Differenziazione Prodotto Standard
    if (datiProdotto.getTipprod() != null && datiProdotto.getTipprod().equals("98")) {
        prodotto.setFlStandard(SrvConstants.SI);
    } else {
        prodotto.setFlStandard(SrvConstants.NO);
    }
}
````
</augment_code_snippet>

## Costanti di Sistema per Prodotti

### 📋 **Costanti Principali (da SrvConstants)**

```java
// Tipi di Prodotto
public static final String TIPO_PRODOTTO_PIP = "PIP";
public static final String TIPO_PRODOTTO_FIP = "FIP";
public static final String MULTIGARANZIA = "M";
public static final String UNITLINKED = "U";
public static final String TRANCHING = "SI";

// Categorie
public static final String INDIVIDUALE = "individuale";
public static final String COLLETTIVA = "collettiva";
public static final String INDIVIDUALI = "individuali";
public static final String COLLETTIVE = "collettive";

// Valori Booleani
public static final String SI = "SI";
public static final String NO = "NO";
public static final String PIP = "PIP";
public static final String FIP = "FIP";

// Categorie Numeriche
public static final String INDIVIDUALE_11 = "11";
public static final String INDIVIDUALE_12 = "12";
public static final String COLLETTIVA_21 = "21";
public static final String COLLETTIVA_22 = "22";
```

## Programmi COBOL Specifici per Prodotto

### 🎯 **Routing COBOL per Tipo Prodotto**

#### **Prodotti Tranching**
- **VWLSE560** - Controllo dati tecnici multi-investimento
- **VWLSE192** - Registra fondi switch (SBEX)
- **VWLSE544** - Annulla fondi switch (SBEX)

#### **Altri Prodotti**
- **VWLSE461** - Controllo dati tecnici standard
- **VWLSE513** - Elenco unità tecniche con prevalente
- **VWLSE618** - Elenco altri dati polizza

#### **Prodotti PIP/FIP**
- **VWLSE387** - Carica parametri gestione anagrafiche ruolo
- **VWLSE521** - Carica figure ToBe gestione anagrafiche ruolo
- **VWLSE388** - Registra gestione anagrafiche ruolo (SBEX)

#### **Prodotti Multigaranzia**
- **VWLSE507** - Carica dati multigaranzia
- **DWLSE154** - Nuovo gestione speciale prodotto
- **DWLSE155** - Salva gestione speciale prodotto

## Query Database Differenziate

### 🗄️ **Selezioni Specifiche per Tipo Prodotto**

#### **Prodotti Multi-Invest senza FIP/PIP**
```sql
-- selectProdottiMultinvestNoFipNoPip
SELECT * FROM PRODOTTI 
WHERE TIPO_PRODOTTO IN ('M','U') 
AND CODPROD NOT IN ('PIP','FIP')
```

#### **Prodotti Unit Linked senza FIP/PIP**
```sql
-- selectProdottiMultinvestULNoFipNoPip  
SELECT * FROM PRODOTTI 
WHERE TIPO_PRODOTTO = 'U'
AND CODPROD NOT IN ('PIP','FIP')
```

#### **Prodotti con Tranching**
```sql
-- selectProdottiConTranching
SELECT * FROM PRODOTTI 
WHERE TIPO_PRODOTTO IN ('M','U')
AND TRANCHING = 'SI'
```

## Esempi di Utilizzo delle Differenziazioni

### 🔄 **1. Switch di Fondi per Prodotti Tranching**

```java
if (isTranching) {
    // Usa VWLSE560 per controllo dati tecnici multi-invest
    response = controllaVAEmessoTranching(request);
} else {
    // Usa VWLSE461 per controllo dati tecnici standard
    response = controllaVAEmessoAltro(request);
}
```

### 📊 **2. Gestione Posizioni per Multigaranzia vs Unit Linked**

```java
if (isMultigaranzia && !isPip) {
    // Multigaranzia NO-PIP: sempre Unit Linked
    polizza.setUnitLinked(true);
} else if (isMultigaranzia && isPip) {
    // Multigaranzia PIP: verifica posizioni T024
    boolean hasUnitLinked = verificaPosizioniUnitLinked(polizza);
    polizza.setUnitLinked(hasUnitLinked);
}
```

### 🏦 **3. Configurazione Stampe per Prodotti Specifici**

```java
if (codProdotto.equals(SrvConstants.TIPO_PRODOTTO_TABOO_CUBE)) {
    parametri.put(SrvConstants.SITUAZIONE_SINTETICA_TABOO_CUBE, "SI");
} else if (codProdotto.equals(SrvConstants.TIPO_PRODOTTO_EASY_TO_SELL_603)) {
    parametri.put(SrvConstants.SITUAZIONE_SINTETICA_EASY_TO_SELL, "SI");
}
```

## Architettura di Gestione Prodotti

### 🏗️ **Flusso di Identificazione Prodotto**

```
Request → SelectTipoProdottoDettaglioGenerico → ProdottoTipoDettaglio
    ↓
PolizzaUtility.getMappa() → Map<String, Boolean>
    ↓
Routing Condizionale:
├─ isTranching → Flusso Tranching (VWLSE560)
├─ isViPensiono → Flusso ViPensiono  
├─ isPip → Flusso PIP (VWLSE387)
└─ default → Flusso Standard (VWLSE461)
```

### 📋 **Metodi Helper Principali**

- `ProdottoProdottoHelper.isProdottoPip()` - Identifica prodotti PIP/FIP
- `PolizzaHelper.isMultigaranzia()` - Identifica prodotti multigaranzia
- `PolizzaHelper.isUnitLinked()` - Identifica prodotti unit linked
- `ServicesUtils.isRapportoCollettiva()` - Identifica rapporti collettivi
- `ServicesUtils.isRapportoIndividuale()` - Identifica rapporti individuali

## Considerazioni Architetturali

### ✅ **Vantaggi del Sistema Attuale**
1. **Separazione chiara** tra logiche di prodotto
2. **Riutilizzo** di helper classes comuni
3. **Configurabilità** tramite costanti centralizzate
4. **Estensibilità** per nuovi tipi di prodotto

### ⚠️ **Aree di Miglioramento**
1. **Centralizzazione** delle logiche di routing
2. **Documentazione** delle regole di business
3. **Testing** delle combinazioni di prodotto
4. **Refactoring** di codice duplicato

## Conclusioni

Il sistema NAW implementa un'architettura sofisticata per la gestione di diversi tipi di prodotti assicurativi, con differenziazioni di logica implementate a più livelli:

1. **Livello API**: Routing condizionale basato su tipo prodotto
2. **Livello Business**: Helper classes per identificazione prodotti
3. **Livello COBOL**: Programmi specifici per ogni tipologia
4. **Livello Database**: Query ottimizzate per categoria prodotto

Questa struttura garantisce flessibilità e manutenibilità del sistema pur gestendo la complessità del dominio assicurativo.
