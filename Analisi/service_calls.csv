Category,Service_Name,File_Path,Line_Number,Pattern,Full_Line,Context
EJB_PROXY_DEFINITION,PortafoglioRapporto,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,8,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PortafoglioRapportoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","       6: 	     http://www.springframework.org/schema/aop/spring-aop.xsd"" default-lazy-init=""true""> |        7:  | >>>    8: 	<bean id=""PortafoglioRapportoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |        9: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PortafoglioRapporto""/> |       10: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoInterface""/>"
EJB_PROXY_DEFINITION,PropostaProposta,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,14,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PropostaPropostaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      12: 	</bean> |       13:  | >>>   14: 	<bean id=""PropostaPropostaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       15: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PropostaProposta""/> |       16: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/>"
EJB_PROXY_DEFINITION,AbilitazioneUtente,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,20,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""AbilitazioneUtenteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      18: 	</bean> |       19:  | >>>   20: 	<bean id=""AbilitazioneUtenteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       21: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneUtente""/> |       22: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteInterface""/>"
EJB_PROXY_DEFINITION,AbilitazioneAzione,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,26,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""AbilitazioneAzioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      24: 	</bean> |       25:  | >>>   26: 	<bean id=""AbilitazioneAzioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       27: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneAzione""/> |       28: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneInterface""/>"
EJB_PROXY_DEFINITION,StrutturaRete,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,32,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""StrutturaReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      30: 	</bean> |       31:  | >>>   32:     <bean id=""StrutturaReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       33: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}StrutturaRete""/> |       34:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteInterface""/>"
EJB_PROXY_DEFINITION,ProdottoProdotto,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,38,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""ProdottoProdottoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      36:     </bean> |       37:  | >>>   38: 	<bean id=""ProdottoProdottoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       39: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoProdotto""/> |       40: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoInterface""/>"
EJB_PROXY_DEFINITION,Customer,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,44,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""CustomerProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      42: 	</bean> |       43:  | >>>   44: 	<bean id=""CustomerProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       45: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Customer""/> |       46: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerInterface""/>"
EJB_PROXY_DEFINITION,Domini,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,50,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""DominiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      48: 	</bean> |       49:  | >>>   50: 	<bean id=""DominiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       51: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Domini""/> |       52: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/>"
EJB_PROXY_DEFINITION,AnagraficaAnagrafica,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,56,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""AnagraficaAnagraficaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      54: 	</bean> |       55:  | >>>   56: 	<bean id=""AnagraficaAnagraficaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       57: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AnagraficaAnagrafica""/> |       58:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaInterface""/>"
EJB_PROXY_DEFINITION,CoeffCostoMgmFee,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,62,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""CoeffCostoMgmFeeProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      60:     </bean> |       61:  | >>>   62:     <bean id=""CoeffCostoMgmFeeProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       63: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CoeffCostoMgmFee""/> |       64: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeInterface""/>"
EJB_PROXY_DEFINITION,CustomerAccessori,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,68,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""CustomerAccessoriProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      66: 	</bean> |       67:  | >>>   68: 	<bean id=""CustomerAccessoriProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       69: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CustomerAccessori""/> |       70: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriInterface""/>"
EJB_PROXY_DEFINITION,WorkFlowMgr,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,74,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""WorkFlowMgrProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      72: 	</bean> |       73:  | >>>   74: 	<bean id=""WorkFlowMgrProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       75: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}WorkFlowMgr""/> |       76: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.workflow.mgr.WorkFlowMgrInterface""/>"
EJB_PROXY_DEFINITION,LocalitaStradario,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,80,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""LocalitaStradarioProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      78: 	</bean> |       79:  | >>>   80: 	<bean id=""LocalitaStradarioProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       81: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}LocalitaStradario""/> |       82: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioInterface""/>"
EJB_PROXY_DEFINITION,ProdottoTariffa,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,86,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""ProdottoTariffaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      84: 	</bean> |       85:  | >>>   86: 	<bean id=""ProdottoTariffaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       87: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoTariffa""/> |       88: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaInterface""/>"
EJB_PROXY_DEFINITION,PianiSpostamento,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,92,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PianiSpostamentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      90: 	</bean> |       91:  | >>>   92: 	<bean id=""PianiSpostamentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       93: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PianiSpostamento""/> |       94: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoInterface""/>"
EJB_PROXY_DEFINITION,ModelloMatematicoFormula,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,98,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""ModelloMatematicoFormulaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","      96: 	</bean> |       97:  | >>>   98: 	<bean id=""ModelloMatematicoFormulaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |       99: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ModelloMatematicoFormula""/> |      100: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaInterface""/>"
EJB_PROXY_DEFINITION,TabellaTracciato,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,104,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""TabellaTracciatoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     102: 	</bean> |      103:  | >>>  104: 	<bean id=""TabellaTracciatoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      105: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}TabellaTracciato""/> |      106: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoInterface""/>"
EJB_PROXY_DEFINITION,DerogheService,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,110,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""DerogheServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     108: 	</bean> |      109:  | >>>  110: 	<bean id=""DerogheServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      111: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}DerogheService""/> |      112: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.DerogheServiceInterface""/>"
EJB_PROXY_DEFINITION,QuestionarioAura,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,116,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""QuestionarioAuraProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     114: 	</bean> |      115:  | >>>  116: 	<bean id=""QuestionarioAuraProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      117: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioAura""/> |      118: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraInterface""/>"
EJB_PROXY_DEFINITION,PolizzaGestioneRid,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,122,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PolizzaGestioneRidProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     120: 	</bean> |      121:  | >>>  122: 	<bean id=""PolizzaGestioneRidProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      123: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaGestioneRid""/> |      124: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidInterface""/>"
EJB_PROXY_DEFINITION,FondoTrasferimento,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,128,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""FondoTrasferimentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     126: 	</bean> |      127:  | >>>  128: 	<bean id=""FondoTrasferimentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      129: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}FondoTrasferimento""/> |      130: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoInterface""/>"
EJB_PROXY_DEFINITION,QuestionarioStruttura,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,134,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""QuestionarioStrutturaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     132: 	</bean> |      133:  | >>>  134: 	<bean id=""QuestionarioStrutturaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      135: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioStruttura""/> |      136: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaInterface""/>"
EJB_PROXY_DEFINITION,ZbRete,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,140,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""ZbReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     138: 	</bean> |      139:  | >>>  140: 	<bean id=""ZbReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      141: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ZbRete""/> |      142: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteInterface""/>"
EJB_PROXY_DEFINITION,PolizzaPolizza,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,146,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PolizzaPolizzaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     144: 	</bean> |      145:  | >>>  146: 	<bean id=""PolizzaPolizzaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      147: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPolizza""/> |      148: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaInterface""/>"
EJB_PROXY_DEFINITION,PolizzaPosizione,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,152,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PolizzaPosizioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     150: 	</bean> |      151:  | >>>  152: 	<bean id=""PolizzaPosizioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      153: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPosizione""/> |      154: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneInterface""/>"
EJB_PROXY_DEFINITION,SyncroPolicy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,158,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""SyncroPolicyProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     156: 	</bean> |      157:  | >>>  158: 	<bean id=""SyncroPolicyProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      159: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}SyncroPolicy""/> |      160: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyInterface""/>"
EJB_PROXY_DEFINITION,PrenotazionePostVendita,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,164,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PrenotazionePostVenditaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     162: 	</bean> |      163:  | >>>  164: 	<bean id=""PrenotazionePostVenditaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      165: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PrenotazionePostVendita""/> |      166: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaInterface""/>"
EJB_PROXY_DEFINITION,GestioneTabelleTabelleVita,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,170,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""GestioneTabelleTabelleVitaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     168: 	</bean> |      169:  | >>>  170: 	<bean id=""GestioneTabelleTabelleVitaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      171: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita""/> |      172: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaInterface""/>"
EJB_PROXY_DEFINITION,PolizzaStorni,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,176,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PolizzaStorniProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     174: 	</bean> |      175:  | >>>  176: 	<bean id=""PolizzaStorniProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      177: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaStorni""/> |      178: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniInterface""/>"
EJB_PROXY_DEFINITION,PolizzaOpzioniContrattuali,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,182,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PolizzaOpzioniContrattualiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     180: 	</bean> |      181:  | >>>  182: 	<bean id=""PolizzaOpzioniContrattualiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      183: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali""/> |      184: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiInterface""/>"
EJB_PROXY_DEFINITION,Test,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,9,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Test"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","       7:  |        8: 	<!-- Local EJB Services INIZIO --> | >>>    9: 	<bean id=""Test"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       10: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Test""/> |       11: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.test.service.TestInterface""/>"
EJB_PROXY_DEFINITION,Abilitazioni,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,14,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Abilitazioni"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      12: 	</bean> |       13:  | >>>   14: 	<bean id=""Abilitazioni"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       15: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Abilitazioni""/> |       16: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.abilitazione.service.AbilitazioniInterface""/>"
EJB_PROXY_DEFINITION,Prodotto,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,19,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Prodotto"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      17: 	</bean> |       18:  | >>>   19: 	<bean id=""Prodotto"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       20: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Prodotto""/> |       21: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.prodotto.service.ProdottoInterface""/>"
EJB_PROXY_DEFINITION,CustAccessori,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,24,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""CustAccessori"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      22: 	</bean> |       23:  | >>>   24: 	<bean id=""CustAccessori"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       25: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CustAccessori""/> |       26: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.customer.service.CustAccessoriInterface""/>"
EJB_PROXY_DEFINITION,Cust,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,29,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Cust"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      27: 	</bean> |       28:  | >>>   29: 	<bean id=""Cust"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       30: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Cust""/> |       31: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.customer.service.CustInterface""/>"
EJB_PROXY_DEFINITION,Anagrafica,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,34,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Anagrafica"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      32: 	</bean> |       33:  | >>>   34: 	<bean id=""Anagrafica"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       35: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Anagrafica""/> |       36: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.anagrafica.service.AnagraficaInterface""/>"
EJB_PROXY_DEFINITION,CoeffMgmFee,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,39,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""CoeffMgmFee"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      37: 	</bean> |       38:  | >>>   39: 	<bean id=""CoeffMgmFee"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       40: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee""/> |       41: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.coeffCostoMgmFee.service.CoeffMgmFeeInterface""/>"
EJB_PROXY_DEFINITION,Portafoglio,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,44,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Portafoglio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      42: 	</bean> |       43:  | >>>   44: 	<bean id=""Portafoglio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       45: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Portafoglio""/> |       46: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.portafoglio.service.PortafoglioInterface""/>"
EJB_PROXY_DEFINITION,Rete,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,49,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Rete"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      47: 	</bean> |       48:  | >>>   49: 	<bean id=""Rete"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       50: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Rete""/> |       51: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.rete.service.ReteInterface""/>"
EJB_PROXY_DEFINITION,Dominio,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,54,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Dominio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      52: 	</bean> |       53:  | >>>   54: 	<bean id=""Dominio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       55: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Dominio""/> |       56: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.domini.service.DominioInterface""/>"
EJB_PROXY_DEFINITION,Proposta,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,59,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Proposta"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      57: 	</bean> |       58:  | >>>   59: 	<bean id=""Proposta"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       60: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Proposta""/> |       61: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.proposta.service.PropostaInterface""/>"
EJB_PROXY_DEFINITION,WorkflowAutorizzativo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,64,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""WorkflowAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      62: 	</bean> |       63:  | >>>   64: 	<bean id=""WorkflowAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       65: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo""/> |       66: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflow.service.WorkflowAutorizzativoInterface""/>"
EJB_PROXY_DEFINITION,Viario,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,69,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Viario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      67: 	</bean> |       68:  | >>>   69: 	<bean id=""Viario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       70: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Viario""/> |       71: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.viario.service.service.ViarioInterface""/>"
EJB_PROXY_DEFINITION,Tariffa,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,74,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Tariffa"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      72: 	</bean> |       73:  | >>>   74: 	<bean id=""Tariffa"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       75: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tariffa""/> |       76: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.tariffa.service.TariffaInterface""/>"
EJB_PROXY_DEFINITION,Formula,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,79,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Formula"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      77: 	</bean> |       78:  | >>>   79: 	<bean id=""Formula"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       80: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Formula""/> |       81: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.formula.service.FormulaInterface""/>"
EJB_PROXY_DEFINITION,Deroga,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,84,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Deroga"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      82: 	</bean> |       83:  | >>>   84: 	<bean id=""Deroga"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       85: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Deroga""/> |       86: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.deroga.service.DerogaInterface""/>"
EJB_PROXY_DEFINITION,Questionario,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,89,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Questionario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      87: 	</bean> |       88:  | >>>   89: 	<bean id=""Questionario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       90: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Questionario""/> |       91: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.questionario.service.QuestionarioInterface""/>"
EJB_PROXY_DEFINITION,ReteZb,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,94,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""ReteZb"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      92: 	</bean> |       93:  | >>>   94: 	<bean id=""ReteZb"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |       95: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}ReteZb""/> |       96: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.retezb.service.ReteZbInterface""/>"
EJB_PROXY_DEFINITION,Tracciato,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,99,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Tracciato"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","      97: 	</bean> |       98:  | >>>   99: 	<bean id=""Tracciato"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |      100: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tracciato""/> |      101: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.tracciato.service.TracciatoInterface""/>"
EJB_PROXY_DEFINITION,BloccoAutorizzativo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,104,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""BloccoAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","     102: 	</bean> |      103:  | >>>  104: 	<bean id=""BloccoAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |      105: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo""/> |      106: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.blocco.autorizzativo.service.BloccoAutorizzativoInterface""/>"
EJB_PROXY_DEFINITION,Polizza,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,109,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""Polizza"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","     107: 	</bean> |      108:  | >>>  109: 	<bean id=""Polizza"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |      110: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Polizza""/> |      111: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.polizza.service.PolizzaInterface""/>"
EJB_PROXY_DEFINITION,WorkflowEmissivo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,114,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""WorkflowEmissivo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","     112: 	</bean> |      113:  | >>>  114: 	<bean id=""WorkflowEmissivo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |      115: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo""/> |      116: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflowEmissivo.service.WorkflowEmissivoInterface""/>"
EJB_PROXY_DEFINITION,WorkflowOpzContrattuali,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,119,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.ejb\.access\.LocalStatelessSessionProxyFactoryBean"">","<bean id=""WorkflowOpzContrattuali"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean"">","     117: 	</bean> |      118:  | >>>  119: 	<bean id=""WorkflowOpzContrattuali"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> |      120: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali""/> |      121: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflowOpzContrattuali.service.WorkflowOpzContrattualiInterface""/>"
EJB_PROXY_DEFINITION,Aegis,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,240,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""AegisProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     238: 	</bean> |      239:  | >>>  240: 	<bean id=""AegisProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      241:         <property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Aegis""/> |      242:         <property name=""businessInterface"" value=""it.sistinf.albedoweb.services.function.aegis.service.AegisInterface""/>"
EJB_PROXY_DEFINITION,Print,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,246,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<bean id=""PrintProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean"">","     244: 	</bean> |      245:  | >>>  246:     <bean id=""PrintProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> |      247: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Print""/> |      248: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.eventi.print.service.PrintInterface""/>"
EJB_PROXY_DEFINITION,PropostaService,Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml,45,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<!--     <bean id=""PropostaServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> -->","      43: <!-- 	</bean> --> |       44:  | >>>   45: <!--     <bean id=""PropostaServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> --> |       46: <!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04PropostaProposta""/>                                    --> |       47: <!-- 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/> -->"
EJB_PROXY_DEFINITION,DominiService,Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml,59,"<bean\s+id=""([^""]+)Proxy""\s+class=""org\.springframework\.ejb\.access\.SimpleRemoteStatelessSessionProxyFactoryBean"">","<!-- 	<bean id=""DominiServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> -->","      57: <!-- 	</bean> --> |       58:  | >>>   59: <!-- 	<bean id=""DominiServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> --> |       60: <!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04Domini""/>                                    --> |       61: <!-- 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/> -->"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PortafoglioRapporto,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,9,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PortafoglioRapporto""/>","       7:  |        8: 	<bean id=""PortafoglioRapportoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>    9: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PortafoglioRapporto""/> |       10: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoInterface""/> |       11:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.portafoglio.rapporto.service.ejb.RemoteEJBPortafoglioRapportoInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PropostaProposta,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,15,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PropostaProposta""/>","      13:  |       14: 	<bean id=""PropostaPropostaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   15: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PropostaProposta""/> |       16: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/> |       17:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.ejb.RemoteEJBPropostaPropostaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}AbilitazioneUtente,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,21,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneUtente""/>","      19:  |       20: 	<bean id=""AbilitazioneUtenteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   21: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneUtente""/> |       22: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteInterface""/> |       23: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.abilitazione.utente.service.ejb.RemoteEJBAbilitazioneUtenteInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}AbilitazioneAzione,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,27,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneAzione""/>","      25:  |       26: 	<bean id=""AbilitazioneAzioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   27: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneAzione""/> |       28: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneInterface""/> |       29:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.abilitazione.azione.service.ejb.RemoteEJBAbilitazioneAzioneInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}StrutturaRete,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,33,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}StrutturaRete""/>","      31:  |       32:     <bean id=""StrutturaReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   33: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}StrutturaRete""/> |       34:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteInterface""/> |       35:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.struttura.rete.service.ejb.RemoteEJBStrutturaReteInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}ProdottoProdotto,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,39,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoProdotto""/>","      37:  |       38: 	<bean id=""ProdottoProdottoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   39: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoProdotto""/> |       40: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoInterface""/> |       41: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prodotto.prodotto.service.ejb.RemoteEJBProdottoProdottoInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}Customer,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,45,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Customer""/>","      43:  |       44: 	<bean id=""CustomerProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   45: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Customer""/> |       46: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerInterface""/> |       47: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customer.service.ejb.RemoteEJBCustomerInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}Domini,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,51,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Domini""/>","      49:  |       50: 	<bean id=""DominiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   51: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Domini""/> |       52: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/> |       53: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.ejb.RemoteEJBDominiInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}AnagraficaAnagrafica,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,57,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AnagraficaAnagrafica""/>","      55:  |       56: 	<bean id=""AnagraficaAnagraficaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   57: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AnagraficaAnagrafica""/> |       58:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaInterface""/> |       59:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.anagrafica.service.ejb.RemoteEJBAnagraficaAnagraficaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}CoeffCostoMgmFee,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,63,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CoeffCostoMgmFee""/>","      61:  |       62:     <bean id=""CoeffCostoMgmFeeProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   63: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CoeffCostoMgmFee""/> |       64: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeInterface""/> |       65: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.ejb.RemoteEJBCoeffCostoMgmFeeInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}CustomerAccessori,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,69,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CustomerAccessori""/>","      67:  |       68: 	<bean id=""CustomerAccessoriProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   69: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CustomerAccessori""/> |       70: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriInterface""/> |       71: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.ejb.RemoteEJBCustomerAccessoriInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}WorkFlowMgr,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,75,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}WorkFlowMgr""/>","      73:  |       74: 	<bean id=""WorkFlowMgrProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   75: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}WorkFlowMgr""/> |       76: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.workflow.mgr.WorkFlowMgrInterface""/> |       77: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.workflow.mgr.WorkFlowMgrRemoteInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}LocalitaStradario,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,81,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}LocalitaStradario""/>","      79:  |       80: 	<bean id=""LocalitaStradarioProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   81: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}LocalitaStradario""/> |       82: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioInterface""/> |       83: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.localita.stradario.service.ejb.RemoteEJBLocalitaStradarioInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}ProdottoTariffa,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,87,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoTariffa""/>","      85:  |       86: 	<bean id=""ProdottoTariffaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   87: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoTariffa""/> |       88: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaInterface""/> |       89: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prodotto.tariffa.service.ejb.RemoteEJBProdottoTariffaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PianiSpostamento,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,93,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PianiSpostamento""/>","      91:  |       92: 	<bean id=""PianiSpostamentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   93: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PianiSpostamento""/> |       94: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoInterface""/> |       95: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.fondo.pianispostamento.service.ejb.RemoteEJBPianiSpostamentoInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}ModelloMatematicoFormula,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,99,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ModelloMatematicoFormula""/>","      97:  |       98: 	<bean id=""ModelloMatematicoFormulaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   99: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ModelloMatematicoFormula""/> |      100: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaInterface""/> |      101: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.modellomatematico.formula.service.ejb.RemoteEJBModelloMatematicoFormulaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}TabellaTracciato,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,105,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}TabellaTracciato""/>","     103:  |      104: 	<bean id=""TabellaTracciatoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  105: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}TabellaTracciato""/> |      106: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoInterface""/> |      107: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.tabella.tracciato.service.ejb.RemoteEJBTabellaTracciatoInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}DerogheService,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,111,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}DerogheService""/>","     109:  |      110: 	<bean id=""DerogheServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  111: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}DerogheService""/> |      112: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.DerogheServiceInterface""/> |      113: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.deroghe.RemoteDerogheServiceInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}QuestionarioAura,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,117,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioAura""/>","     115:  |      116: 	<bean id=""QuestionarioAuraProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  117: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioAura""/> |      118: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraInterface""/> |      119:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.questionario.aura.service.ejb.RemoteEJBQuestionarioAuraInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PolizzaGestioneRid,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,123,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaGestioneRid""/>","     121:  |      122: 	<bean id=""PolizzaGestioneRidProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  123: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaGestioneRid""/> |      124: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidInterface""/> |      125: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.gestionerid.service.ejb.RemoteEJBPolizzaGestioneRidInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}FondoTrasferimento,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,129,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}FondoTrasferimento""/>","     127:  |      128: 	<bean id=""FondoTrasferimentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  129: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}FondoTrasferimento""/> |      130: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoInterface""/> |      131: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.fondo.trasferimento.service.ejb.RemoteEJBFondoTrasferimentoInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}QuestionarioStruttura,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,135,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioStruttura""/>","     133:  |      134: 	<bean id=""QuestionarioStrutturaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  135: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioStruttura""/> |      136: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaInterface""/> |      137: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.questionario.struttura.service.ejb.RemoteEJBQuestionarioStrutturaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}ZbRete,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,141,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ZbRete""/>","     139:  |      140: 	<bean id=""ZbReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  141: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ZbRete""/> |      142: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteInterface""/> |      143:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ejb.RemoteEJBZbReteInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PolizzaPolizza,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,147,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPolizza""/>","     145:  |      146: 	<bean id=""PolizzaPolizzaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  147: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPolizza""/> |      148: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaInterface""/> |      149: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.polizza.service.ejb.RemoteEJBPolizzaPolizzaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PolizzaPosizione,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,153,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPosizione""/>","     151:  |      152: 	<bean id=""PolizzaPosizioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  153: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPosizione""/> |      154: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneInterface""/> |      155:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.posizione.service.ejb.RemoteEJBPolizzaPosizioneInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}SyncroPolicy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,159,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}SyncroPolicy""/>","     157:  |      158: 	<bean id=""SyncroPolicyProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  159: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}SyncroPolicy""/> |      160: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyInterface""/> |      161:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.elis.syncropolicy.service.ejb.RemoteEJBSyncroPolicyInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PrenotazionePostVendita,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,165,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PrenotazionePostVendita""/>","     163:  |      164: 	<bean id=""PrenotazionePostVenditaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  165: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PrenotazionePostVendita""/> |      166: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaInterface""/> |      167: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prenotazione.postvendita.service.ejb.RemoteEJBPrenotazionePostVenditaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,171,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita""/>","     169:  |      170: 	<bean id=""GestioneTabelleTabelleVitaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  171: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita""/> |      172: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaInterface""/> |      173: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.ejb.RemoteEJBGestioneTabelleTabelleVitaInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PolizzaStorni,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,177,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaStorni""/>","     175:  |      176: 	<bean id=""PolizzaStorniProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  177: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaStorni""/> |      178: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniInterface""/> |      179: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.storni.service.ejb.RemoteEJBPolizzaStorniInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,183,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali""/>","     181:  |      182: 	<bean id=""PolizzaOpzioniContrattualiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  183: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali""/> |      184: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiInterface""/> |      185: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.ejb.RemoteEJBPolizzaOpzioniContrattualiInterface""/>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Test,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,10,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Test""/>","       8: 	<!-- Local EJB Services INIZIO --> |        9: 	<bean id=""Test"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   10: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Test""/> |       11: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.test.service.TestInterface""/> |       12: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Abilitazioni,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,15,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Abilitazioni""/>","      13:  |       14: 	<bean id=""Abilitazioni"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   15: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Abilitazioni""/> |       16: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.abilitazione.service.AbilitazioniInterface""/> |       17: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Prodotto,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,20,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Prodotto""/>","      18:  |       19: 	<bean id=""Prodotto"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   20: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Prodotto""/> |       21: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.prodotto.service.ProdottoInterface""/> |       22: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}CustAccessori,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,25,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CustAccessori""/>","      23:  |       24: 	<bean id=""CustAccessori"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   25: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CustAccessori""/> |       26: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.customer.service.CustAccessoriInterface""/> |       27: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Cust,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,30,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Cust""/>","      28:  |       29: 	<bean id=""Cust"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   30: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Cust""/> |       31: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.customer.service.CustInterface""/> |       32: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Anagrafica,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,35,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Anagrafica""/>","      33:  |       34: 	<bean id=""Anagrafica"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   35: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Anagrafica""/> |       36: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.anagrafica.service.AnagraficaInterface""/> |       37: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,40,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee""/>","      38:  |       39: 	<bean id=""CoeffMgmFee"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   40: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}CoeffMgmFee""/> |       41: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.coeffCostoMgmFee.service.CoeffMgmFeeInterface""/> |       42: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Portafoglio,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,45,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Portafoglio""/>","      43:  |       44: 	<bean id=""Portafoglio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   45: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Portafoglio""/> |       46: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.portafoglio.service.PortafoglioInterface""/> |       47: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Rete,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,50,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Rete""/>","      48:  |       49: 	<bean id=""Rete"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   50: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Rete""/> |       51: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.rete.service.ReteInterface""/> |       52: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Dominio,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,55,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Dominio""/>","      53:  |       54: 	<bean id=""Dominio"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   55: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Dominio""/> |       56: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.domini.service.DominioInterface""/> |       57: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Proposta,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,60,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Proposta""/>","      58:  |       59: 	<bean id=""Proposta"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   60: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Proposta""/> |       61: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.proposta.service.PropostaInterface""/> |       62: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,65,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo""/>","      63:  |       64: 	<bean id=""WorkflowAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   65: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowAutorizzativo""/> |       66: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflow.service.WorkflowAutorizzativoInterface""/> |       67: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Viario,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,70,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Viario""/>","      68:  |       69: 	<bean id=""Viario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   70: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Viario""/> |       71: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.viario.service.service.ViarioInterface""/> |       72: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Tariffa,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,75,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tariffa""/>","      73:  |       74: 	<bean id=""Tariffa"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   75: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tariffa""/> |       76: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.tariffa.service.TariffaInterface""/> |       77: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Formula,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,80,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Formula""/>","      78:  |       79: 	<bean id=""Formula"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   80: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Formula""/> |       81: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.formula.service.FormulaInterface""/> |       82: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Deroga,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,85,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Deroga""/>","      83:  |       84: 	<bean id=""Deroga"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   85: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Deroga""/> |       86: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.deroga.service.DerogaInterface""/> |       87: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Questionario,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,90,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Questionario""/>","      88:  |       89: 	<bean id=""Questionario"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   90: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Questionario""/> |       91: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.questionario.service.QuestionarioInterface""/> |       92: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}ReteZb,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,95,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}ReteZb""/>","      93:  |       94: 	<bean id=""ReteZb"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>   95: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}ReteZb""/> |       96: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.retezb.service.ReteZbInterface""/> |       97: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Tracciato,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,100,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tracciato""/>","      98:  |       99: 	<bean id=""Tracciato"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>  100: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Tracciato""/> |      101: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.tracciato.service.TracciatoInterface""/> |      102: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,105,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo""/>","     103:  |      104: 	<bean id=""BloccoAutorizzativo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>  105: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}BloccoAutorizzativo""/> |      106: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.blocco.autorizzativo.service.BloccoAutorizzativoInterface""/> |      107: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}Polizza,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,110,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Polizza""/>","     108:  |      109: 	<bean id=""Polizza"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>  110: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}Polizza""/> |      111: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.polizza.service.PolizzaInterface""/> |      112: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,115,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo""/>","     113:  |      114: 	<bean id=""WorkflowEmissivo"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>  115: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowEmissivo""/> |      116: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflowEmissivo.service.WorkflowEmissivoInterface""/> |      117: 	</bean>"
JNDI_LOOKUP,ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali,REST.Services.Cobol.Web/src/spring-conf/internal-services.xml,120,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali""/>","     118:  |      119: 	<bean id=""WorkflowOpzContrattuali"" class=""org.springframework.ejb.access.LocalStatelessSessionProxyFactoryBean""> | >>>  120: 	  	<property name=""jndiName"" value=""ejblocal:${prefixJNDINameEJBProxy}WorkflowOpzContrattuali""/> |      121: 	  	<property name=""businessInterface"" value=""it.sistinf.rest.singleton.workflowOpzContrattuali.service.WorkflowOpzContrattualiInterface""/> |      122: 	</bean>"
JNDI_LOOKUP,java:comp/env/service/SyncroCustomerService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/syncrocustomer/service/SyncroCustomerInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elis.syncrocustomer.service.SyncroCustomerService)ctx.lookup(""java:comp/env/service/SyncroCustomerService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.elis.syncrocustomer.service.SyncroCustomerService)ctx.lookup(""java:comp/env/service/SyncroCustomerService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ZbReteService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/zbank/zbrete/service/ZbReteInterfacePortProxy.java,67,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteService)ctx.lookup(""java:comp/env/service/ZbReteService"");","      65:             { |       66:                 InitialContext ctx = new InitialContext(); | >>>   67:                 _service = (it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteService)ctx.lookup(""java:comp/env/service/ZbReteService""); |       68:             } |       69:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/SyncroPolicyService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/syncropolicy/service/SyncroPolicyInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyService)ctx.lookup(""java:comp/env/service/SyncroPolicyService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyService)ctx.lookup(""java:comp/env/service/SyncroPolicyService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PaperlessService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elis/paperless/service/PaperlessInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elis.paperless.service.PaperlessService)ctx.lookup(""java:comp/env/service/PaperlessService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.elis.paperless.service.PaperlessService)ctx.lookup(""java:comp/env/service/PaperlessService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ProdottoProdottoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/prodotto/service/ProdottoProdottoInterfacePortProxy.java,153,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoService)ctx.lookup(""java:comp/env/service/ProdottoProdottoService"");","     151:             { |      152:                 InitialContext ctx = new InitialContext(); | >>>  153:                 _service = (it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoService)ctx.lookup(""java:comp/env/service/ProdottoProdottoService""); |      154:             } |      155:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ProdottoTariffaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/tariffa/service/ProdottoTariffaInterfacePortProxy.java,71,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaService)ctx.lookup(""java:comp/env/service/ProdottoTariffaService"");","      69:             { |       70:                 InitialContext ctx = new InitialContext(); | >>>   71:                 _service = (it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaService)ctx.lookup(""java:comp/env/service/ProdottoTariffaService""); |       72:             } |       73:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ProdottoFormulaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/formula/service/ProdottoFormulaInterfacePortProxy.java,59,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prodotto.formula.service.ProdottoFormulaService)ctx.lookup(""java:comp/env/service/ProdottoFormulaService"");","      57:             { |       58:                 InitialContext ctx = new InitialContext(); | >>>   59:                 _service = (it.sistinf.albedoweb.services.prodotto.formula.service.ProdottoFormulaService)ctx.lookup(""java:comp/env/service/ProdottoFormulaService""); |       60:             } |       61:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ProdottoVettoreService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prodotto/vettore/service/ProdottoVettoreInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prodotto.vettore.service.ProdottoVettoreService)ctx.lookup(""java:comp/env/service/ProdottoVettoreService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.prodotto.vettore.service.ProdottoVettoreService)ctx.lookup(""java:comp/env/service/ProdottoVettoreService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/TrascodificaDominiEasyService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/easy/trascodifica/service/TrascodificaDominiEasyInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.easy.trascodifica.service.TrascodificaDominiEasyService)ctx.lookup(""java:comp/env/service/TrascodificaDominiEasyService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.easy.trascodifica.service.TrascodificaDominiEasyService)ctx.lookup(""java:comp/env/service/TrascodificaDominiEasyService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AnagrafeEasyService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/easy/anagrafe/service/AnagrafeEasyInterfacePortProxy.java,67,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.easy.anagrafe.service.AnagrafeEasyService)ctx.lookup(""java:comp/env/service/AnagrafeEasyService"");","      65:             { |       66:                 InitialContext ctx = new InitialContext(); | >>>   67:                 _service = (it.sistinf.albedoweb.services.easy.anagrafe.service.AnagrafeEasyService)ctx.lookup(""java:comp/env/service/AnagrafeEasyService""); |       68:             } |       69:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/DominiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/domini/naw/service/DominiInterfacePortProxy.java,81,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.domini.naw.service.DominiService)ctx.lookup(""java:comp/env/service/DominiService"");","      79:             { |       80:                 InitialContext ctx = new InitialContext(); | >>>   81:                 _service = (it.sistinf.albedoweb.services.domini.naw.service.DominiService)ctx.lookup(""java:comp/env/service/DominiService""); |       82:             } |       83:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/OrchestratoreConfigurazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/orchestratore/configurazione/service/OrchestratoreConfigurazioneInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.orchestratore.configurazione.service.OrchestratoreConfigurazioneService)ctx.lookup(""java:comp/env/service/OrchestratoreConfigurazioneService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.orchestratore.configurazione.service.OrchestratoreConfigurazioneService)ctx.lookup(""java:comp/env/service/OrchestratoreConfigurazioneService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ProfiloInvestimentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/profiloinvestimento/service/ProfiloInvestimentoInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.profiloinvestimento.service.ProfiloInvestimentoService)ctx.lookup(""java:comp/env/service/ProfiloInvestimentoService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.fondo.profiloinvestimento.service.ProfiloInvestimentoService)ctx.lookup(""java:comp/env/service/ProfiloInvestimentoService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PianiSpostamentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/pianispostamento/service/PianiSpostamentoInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoService)ctx.lookup(""java:comp/env/service/PianiSpostamentoService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoService)ctx.lookup(""java:comp/env/service/PianiSpostamentoService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FondoUtilitaRetrocesseService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/utilitaretrocesse/service/FondoUtilitaRetrocesseInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.utilitaretrocesse.service.FondoUtilitaRetrocesseService)ctx.lookup(""java:comp/env/service/FondoUtilitaRetrocesseService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.fondo.utilitaretrocesse.service.FondoUtilitaRetrocesseService)ctx.lookup(""java:comp/env/service/FondoUtilitaRetrocesseService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RaggruppamentoFondiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/raggruppamento/service/RaggruppamentoFondiInterfacePortProxy.java,83,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.raggruppamento.service.RaggruppamentoFondiService)ctx.lookup(""java:comp/env/service/RaggruppamentoFondiService"");","      81:             { |       82:                 InitialContext ctx = new InitialContext(); | >>>   83:                 _service = (it.sistinf.albedoweb.services.fondo.raggruppamento.service.RaggruppamentoFondiService)ctx.lookup(""java:comp/env/service/RaggruppamentoFondiService""); |       84:             } |       85:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CorporateEventsService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/corporateevents/service/CorporateEventsInterfacePortProxy.java,73,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.corporateevents.service.CorporateEventsService)ctx.lookup(""java:comp/env/service/CorporateEventsService"");","      71:             { |       72:                 InitialContext ctx = new InitialContext(); | >>>   73:                 _service = (it.sistinf.albedoweb.services.fondo.corporateevents.service.CorporateEventsService)ctx.lookup(""java:comp/env/service/CorporateEventsService""); |       74:             } |       75:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RibilanciamentoFondiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/service/RibilanciamentoFondiInterfacePortProxy.java,115,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.ribilanciamento.service.RibilanciamentoFondiService)ctx.lookup(""java:comp/env/service/RibilanciamentoFondiService"");","     113:             { |      114:                 InitialContext ctx = new InitialContext(); | >>>  115:                 _service = (it.sistinf.albedoweb.services.fondo.ribilanciamento.service.RibilanciamentoFondiService)ctx.lookup(""java:comp/env/service/RibilanciamentoFondiService""); |      116:             } |      117:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/GestioneSeparataService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/gestioneseparata/service/GestioneSeparataInterfacePortProxy.java,65,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.gestioneseparata.service.GestioneSeparataService)ctx.lookup(""java:comp/env/service/GestioneSeparataService"");","      63:             { |       64:                 InitialContext ctx = new InitialContext(); | >>>   65:                 _service = (it.sistinf.albedoweb.services.fondo.gestioneseparata.service.GestioneSeparataService)ctx.lookup(""java:comp/env/service/GestioneSeparataService""); |       66:             } |       67:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FondoTrasferimentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/trasferimento/service/FondoTrasferimentoInterfacePortProxy.java,65,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoService)ctx.lookup(""java:comp/env/service/FondoTrasferimentoService"");","      63:             { |       64:                 InitialContext ctx = new InitialContext(); | >>>   65:                 _service = (it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoService)ctx.lookup(""java:comp/env/service/FondoTrasferimentoService""); |       66:             } |       67:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ModelloMatematicoFormulaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/modellomatematico/formula/service/ModelloMatematicoFormulaInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaService)ctx.lookup(""java:comp/env/service/ModelloMatematicoFormulaService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaService)ctx.lookup(""java:comp/env/service/ModelloMatematicoFormulaService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/StampaEsternaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/stampa/esterna/service/StampaEsternaInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.stampa.esterna.service.StampaEsternaService)ctx.lookup(""java:comp/env/service/StampaEsternaService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.stampa.esterna.service.StampaEsternaService)ctx.lookup(""java:comp/env/service/StampaEsternaService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/StampaVariabileService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/stampa/variabile/service/StampaVariabileInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.stampa.variabile.service.StampaVariabileService)ctx.lookup(""java:comp/env/service/StampaVariabileService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.stampa.variabile.service.StampaVariabileService)ctx.lookup(""java:comp/env/service/StampaVariabileService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AbilitazioneAzioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/azione/service/AbilitazioneAzioneInterfacePortProxy.java,69,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneService)ctx.lookup(""java:comp/env/service/AbilitazioneAzioneService"");","      67:             { |       68:                 InitialContext ctx = new InitialContext(); | >>>   69:                 _service = (it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneService)ctx.lookup(""java:comp/env/service/AbilitazioneAzioneService""); |       70:             } |       71:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AbilitazioneUtenteService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utente/service/AbilitazioneUtenteInterfacePortProxy.java,81,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteService)ctx.lookup(""java:comp/env/service/AbilitazioneUtenteService"");","      79:             { |       80:                 InitialContext ctx = new InitialContext(); | >>>   81:                 _service = (it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteService)ctx.lookup(""java:comp/env/service/AbilitazioneUtenteService""); |       82:             } |       83:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/MonitorGidService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/monitor/gid/service/MonitorGidInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.monitor.gid.service.MonitorGidService)ctx.lookup(""java:comp/env/service/MonitorGidService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.monitor.gid.service.MonitorGidService)ctx.lookup(""java:comp/env/service/MonitorGidService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/NumeratoriService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/numeratori/numeratori/service/NumeratoriInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.numeratori.numeratori.service.NumeratoriService)ctx.lookup(""java:comp/env/service/NumeratoriService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.numeratori.numeratori.service.NumeratoriService)ctx.lookup(""java:comp/env/service/NumeratoriService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CodaMqService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/osb/codamq/service/CodaMqInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.osb.codamq.service.CodaMqService)ctx.lookup(""java:comp/env/service/CodaMqService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.osb.codamq.service.CodaMqService)ctx.lookup(""java:comp/env/service/CodaMqService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CoordinatedServicesService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/core/coordinatedservices/service/CoordinatedServicesInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.core.coordinatedservices.service.CoordinatedServicesService)ctx.lookup(""java:comp/env/service/CoordinatedServicesService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.core.coordinatedservices.service.CoordinatedServicesService)ctx.lookup(""java:comp/env/service/CoordinatedServicesService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/StampaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/core/stampe/service/StampaInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.core.stampe.service.StampaService)ctx.lookup(""java:comp/env/service/StampaService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.core.stampe.service.StampaService)ctx.lookup(""java:comp/env/service/StampaService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PropostaPropostaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/proposta/proposta/service/PropostaPropostaInterfacePortProxy.java,155,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaService)ctx.lookup(""java:comp/env/service/PropostaPropostaService"");","     153:             { |      154:                 InitialContext ctx = new InitialContext(); | >>>  155:                 _service = (it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaService)ctx.lookup(""java:comp/env/service/PropostaPropostaService""); |      156:             } |      157:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/UnderwritingService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/proposta/underwriting/service/UnderwritingInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.proposta.underwriting.service.UnderwritingService)ctx.lookup(""java:comp/env/service/UnderwritingService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.proposta.underwriting.service.UnderwritingService)ctx.lookup(""java:comp/env/service/UnderwritingService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaAccountingService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/accounting/service/PolizzaAccountingInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.accounting.service.PolizzaAccountingService)ctx.lookup(""java:comp/env/service/PolizzaAccountingService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.polizza.accounting.service.PolizzaAccountingService)ctx.lookup(""java:comp/env/service/PolizzaAccountingService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaMovimentiContabiliService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/movimenticontabili/service/PolizzaMovimentiContabiliInterfacePortProxy.java,57,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.movimenticontabili.service.PolizzaMovimentiContabiliService)ctx.lookup(""java:comp/env/service/PolizzaMovimentiContabiliService"");","      55:             { |       56:                 InitialContext ctx = new InitialContext(); | >>>   57:                 _service = (it.sistinf.albedoweb.services.polizza.movimenticontabili.service.PolizzaMovimentiContabiliService)ctx.lookup(""java:comp/env/service/PolizzaMovimentiContabiliService""); |       58:             } |       59:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaLiquizioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/liquidazione/service/PolizzaLiquizioneInterfacePortProxy.java,89,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.liquidazione.service.PolizzaLiquizioneService)ctx.lookup(""java:comp/env/service/PolizzaLiquizioneService"");","      87:             { |       88:                 InitialContext ctx = new InitialContext(); | >>>   89:                 _service = (it.sistinf.albedoweb.services.polizza.liquidazione.service.PolizzaLiquizioneService)ctx.lookup(""java:comp/env/service/PolizzaLiquizioneService""); |       90:             } |       91:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaCambioRuoloService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/cambioruolo/service/PolizzaCambioRuoloInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.cambioruolo.service.PolizzaCambioRuoloService)ctx.lookup(""java:comp/env/service/PolizzaCambioRuoloService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.polizza.cambioruolo.service.PolizzaCambioRuoloService)ctx.lookup(""java:comp/env/service/PolizzaCambioRuoloService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/VariazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/variazione/service/VariazioneInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.variazione.service.VariazioneService)ctx.lookup(""java:comp/env/service/VariazioneService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.polizza.variazione.service.VariazioneService)ctx.lookup(""java:comp/env/service/VariazioneService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/BonificaMultiInvestService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/bonificamulti/service/BonificaMultiInvestInterfacePortProxy.java,73,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.bonificamulti.service.BonificaMultiInvestService)ctx.lookup(""java:comp/env/service/BonificaMultiInvestService"");","      71:             { |       72:                 InitialContext ctx = new InitialContext(); | >>>   73:                 _service = (it.sistinf.albedoweb.services.polizza.bonificamulti.service.BonificaMultiInvestService)ctx.lookup(""java:comp/env/service/BonificaMultiInvestService""); |       74:             } |       75:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaOpzioniContrattualiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/opzionicontrattuali/service/PolizzaOpzioniContrattualiInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiService)ctx.lookup(""java:comp/env/service/PolizzaOpzioniContrattualiService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiService)ctx.lookup(""java:comp/env/service/PolizzaOpzioniContrattualiService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaPolizzaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/polizza/service/PolizzaPolizzaInterfacePortProxy.java,283,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaService)ctx.lookup(""java:comp/env/service/PolizzaPolizzaService"");","     281:             { |      282:                 InitialContext ctx = new InitialContext(); | >>>  283:                 _service = (it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaService)ctx.lookup(""java:comp/env/service/PolizzaPolizzaService""); |      284:             } |      285:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaCoassicurazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/coassicurazione/service/PolizzaCoassicurazioneInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.coassicurazione.service.PolizzaCoassicurazioneService)ctx.lookup(""java:comp/env/service/PolizzaCoassicurazioneService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.polizza.coassicurazione.service.PolizzaCoassicurazioneService)ctx.lookup(""java:comp/env/service/PolizzaCoassicurazioneService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaTassazioneDatiCristallizzatiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/tassazione/daticristallizzati/service/PolizzaTassazioneDatiCristallizzatiInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.tassazione.daticristallizzati.service.PolizzaTassazioneDatiCristallizzatiService)ctx.lookup(""java:comp/env/service/PolizzaTassazioneDatiCristallizzatiService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.polizza.tassazione.daticristallizzati.service.PolizzaTassazioneDatiCristallizzatiService)ctx.lookup(""java:comp/env/service/PolizzaTassazioneDatiCristallizzatiService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RelazioneFigureRapportoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/relazionefigurerapporto/service/RelazioneFigureRapportoInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.relazionefigurerapporto.service.RelazioneFigureRapportoService)ctx.lookup(""java:comp/env/service/RelazioneFigureRapportoService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.polizza.relazionefigurerapporto.service.RelazioneFigureRapportoService)ctx.lookup(""java:comp/env/service/RelazioneFigureRapportoService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaSwitchService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/_switch/service/PolizzaSwitchInterfacePortProxy.java,63,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza._switch.service.PolizzaSwitchService)ctx.lookup(""java:comp/env/service/PolizzaSwitchService"");","      61:             { |       62:                 InitialContext ctx = new InitialContext(); | >>>   63:                 _service = (it.sistinf.albedoweb.services.polizza._switch.service.PolizzaSwitchService)ctx.lookup(""java:comp/env/service/PolizzaSwitchService""); |       64:             } |       65:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/VariazioneRischioAssicuratoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/variazionerischioassicurato/service/VariazioneRischioAssicuratoInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.variazionerischioassicurato.service.VariazioneRischioAssicuratoService)ctx.lookup(""java:comp/env/service/VariazioneRischioAssicuratoService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.polizza.variazionerischioassicurato.service.VariazioneRischioAssicuratoService)ctx.lookup(""java:comp/env/service/VariazioneRischioAssicuratoService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaStorniService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/service/PolizzaStorniInterfacePortProxy.java,83,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniService)ctx.lookup(""java:comp/env/service/PolizzaStorniService"");","      81:             { |       82:                 InitialContext ctx = new InitialContext(); | >>>   83:                 _service = (it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniService)ctx.lookup(""java:comp/env/service/PolizzaStorniService""); |       84:             } |       85:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaMovimentazioniULService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/movimentazioniul/service/PolizzaMovimentazioniULInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.movimentazioniul.service.PolizzaMovimentazioniULService)ctx.lookup(""java:comp/env/service/PolizzaMovimentazioniULService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.polizza.movimentazioniul.service.PolizzaMovimentazioniULService)ctx.lookup(""java:comp/env/service/PolizzaMovimentazioniULService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ParametriCollettivaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/ParametriCollettivaInterfacePortProxy.java,65,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup(""java:comp/env/service/ParametriCollettivaService"");","      63:             { |       64:                 InitialContext ctx = new InitialContext(); | >>>   65:                 _service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup(""java:comp/env/service/ParametriCollettivaService""); |       66:             } |       67:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ParametriCollettivaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/PercipientiInterfacePortProxy.java,59,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup(""java:comp/env/service/ParametriCollettivaService"");","      57:             { |       58:                 InitialContext ctx = new InitialContext(); | >>>   59:                 _service = (it.sistinf.albedoweb.services.polizza.parametricollettiva.service.ParametriCollettivaService)ctx.lookup(""java:comp/env/service/ParametriCollettivaService""); |       60:             } |       61:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaCollettoreService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/collettore/service/PolizzaCollettoreInterfacePortProxy.java,61,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.collettore.service.PolizzaCollettoreService)ctx.lookup(""java:comp/env/service/PolizzaCollettoreService"");","      59:             { |       60:                 InitialContext ctx = new InitialContext(); | >>>   61:                 _service = (it.sistinf.albedoweb.services.polizza.collettore.service.PolizzaCollettoreService)ctx.lookup(""java:comp/env/service/PolizzaCollettoreService""); |       62:             } |       63:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/OpzioniConversioneODifferimentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/opzioniconversioneodifferimento/service/OpzioniConversioneODifferimentoInterfacePortProxy.java,48,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.opzioniconversioneodifferimento.service.OpzioniConversioneODifferimentoService)ctx.lookup(""java:comp/env/service/OpzioniConversioneODifferimentoService"");","      46:             { |       47:                 InitialContext ctx = new InitialContext(); | >>>   48:                 _service = (it.sistinf.albedoweb.services.polizza.opzioniconversioneodifferimento.service.OpzioniConversioneODifferimentoService)ctx.lookup(""java:comp/env/service/OpzioniConversioneODifferimentoService""); |       49:             } |       50:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaCostiAnnuiRendicontatiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/costiannuirendicontati/service/PolizzaCostiAnnuiRendicontatiInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.costiannuirendicontati.service.PolizzaCostiAnnuiRendicontatiService)ctx.lookup(""java:comp/env/service/PolizzaCostiAnnuiRendicontatiService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.polizza.costiannuirendicontati.service.PolizzaCostiAnnuiRendicontatiService)ctx.lookup(""java:comp/env/service/PolizzaCostiAnnuiRendicontatiService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaGestioneRidService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/gestionerid/service/PolizzaGestioneRidInterfacePortProxy.java,76,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidService)ctx.lookup(""java:comp/env/service/PolizzaGestioneRidService"");","      74:             { |       75:                 InitialContext ctx = new InitialContext(); | >>>   76:                 _service = (it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidService)ctx.lookup(""java:comp/env/service/PolizzaGestioneRidService""); |       77:             } |       78:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaCambioFrazionamentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/cambiofrazionamento/service/PolizzaCambioFrazionamentoInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.cambiofrazionamento.service.PolizzaCambioFrazionamentoService)ctx.lookup(""java:comp/env/service/PolizzaCambioFrazionamentoService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.polizza.cambiofrazionamento.service.PolizzaCambioFrazionamentoService)ctx.lookup(""java:comp/env/service/PolizzaCambioFrazionamentoService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaMgmFeeService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/mgmfee/service/PolizzaMgmFeeInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.mgmfee.service.PolizzaMgmFeeService)ctx.lookup(""java:comp/env/service/PolizzaMgmFeeService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.polizza.mgmfee.service.PolizzaMgmFeeService)ctx.lookup(""java:comp/env/service/PolizzaMgmFeeService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/EventiPolizzaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/eventi/service/EventiPolizzaInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.eventi.service.EventiPolizzaService)ctx.lookup(""java:comp/env/service/EventiPolizzaService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.polizza.eventi.service.EventiPolizzaService)ctx.lookup(""java:comp/env/service/EventiPolizzaService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PremiumTranchingService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/premiumtranching/service/PremiumTranchingInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.premiumtranching.service.PremiumTranchingService)ctx.lookup(""java:comp/env/service/PremiumTranchingService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.polizza.premiumtranching.service.PremiumTranchingService)ctx.lookup(""java:comp/env/service/PremiumTranchingService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PercipientiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/percipienti/service/PercipientiInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.percipienti.service.PercipientiService)ctx.lookup(""java:comp/env/service/PercipientiService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.polizza.percipienti.service.PercipientiService)ctx.lookup(""java:comp/env/service/PercipientiService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaRiattivazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/riattivazione/service/PolizzaRiattivazioneInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.riattivazione.service.PolizzaRiattivazioneService)ctx.lookup(""java:comp/env/service/PolizzaRiattivazioneService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.polizza.riattivazione.service.PolizzaRiattivazioneService)ctx.lookup(""java:comp/env/service/PolizzaRiattivazioneService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaPosizioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/posizione/service/PolizzaPosizioneInterfacePortProxy.java,127,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneService)ctx.lookup(""java:comp/env/service/PolizzaPosizioneService"");","     125:             { |      126:                 InitialContext ctx = new InitialContext(); | >>>  127:                 _service = (it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneService)ctx.lookup(""java:comp/env/service/PolizzaPosizioneService""); |      128:             } |      129:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RiallocazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/riallocazione/service/RiallocazioneInterfacePortProxy.java,67,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.riallocazione.service.RiallocazioneService)ctx.lookup(""java:comp/env/service/RiallocazioneService"");","      65:             { |       66:                 InitialContext ctx = new InitialContext(); | >>>   67:                 _service = (it.sistinf.albedoweb.services.polizza.riallocazione.service.RiallocazioneService)ctx.lookup(""java:comp/env/service/RiallocazioneService""); |       68:             } |       69:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaDurService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/dur/service/PolizzaDurInterfacePortProxy.java,61,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.dur.service.PolizzaDurService)ctx.lookup(""java:comp/env/service/PolizzaDurService"");","      59:             { |       60:                 InitialContext ctx = new InitialContext(); | >>>   61:                 _service = (it.sistinf.albedoweb.services.polizza.dur.service.PolizzaDurService)ctx.lookup(""java:comp/env/service/PolizzaDurService""); |       62:             } |       63:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaTrasferimentoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/trasferimento/service/PolizzaTrasferimentoInterfacePortProxy.java,75,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.trasferimento.service.PolizzaTrasferimentoService)ctx.lookup(""java:comp/env/service/PolizzaTrasferimentoService"");","      73:             { |       74:                 InitialContext ctx = new InitialContext(); | >>>   75:                 _service = (it.sistinf.albedoweb.services.polizza.trasferimento.service.PolizzaTrasferimentoService)ctx.lookup(""java:comp/env/service/PolizzaTrasferimentoService""); |       76:             } |       77:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaPianoVersamentiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/pianoversamenti/service/PolizzaPianoVersamentiInterfacePortProxy.java,95,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.pianoversamenti.service.PolizzaPianoVersamentiService)ctx.lookup(""java:comp/env/service/PolizzaPianoVersamentiService"");","      93:             { |       94:                 InitialContext ctx = new InitialContext(); | >>>   95:                 _service = (it.sistinf.albedoweb.services.polizza.pianoversamenti.service.PolizzaPianoVersamentiService)ctx.lookup(""java:comp/env/service/PolizzaPianoVersamentiService""); |       96:             } |       97:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PolizzaLimitazioneOperativitaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/limitazioneoperativita/service/PolizzaLimitazioneOperativitaInterfacePortProxy.java,65,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.polizza.limitazioneoperativita.service.PolizzaLimitazioneOperativitaService)ctx.lookup(""java:comp/env/service/PolizzaLimitazioneOperativitaService"");","      63:             { |       64:                 InitialContext ctx = new InitialContext(); | >>>   65:                 _service = (it.sistinf.albedoweb.services.polizza.limitazioneoperativita.service.PolizzaLimitazioneOperativitaService)ctx.lookup(""java:comp/env/service/PolizzaLimitazioneOperativitaService""); |       66:             } |       67:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/GestioneEventiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/gestioneeventi/eventi/service/GestioneEventiInterfacePortProxy.java,69,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.gestioneeventi.eventi.service.GestioneEventiService)ctx.lookup(""java:comp/env/service/GestioneEventiService"");","      67:             { |       68:                 InitialContext ctx = new InitialContext(); | >>>   69:                 _service = (it.sistinf.albedoweb.services.gestioneeventi.eventi.service.GestioneEventiService)ctx.lookup(""java:comp/env/service/GestioneEventiService""); |       70:             } |       71:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/BonificaAnagraficaAmletoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/bonifica/anagrafica/amleto/service/BonificaAnagraficaAmletoInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.bonifica.anagrafica.amleto.service.BonificaAnagraficaAmletoService)ctx.lookup(""java:comp/env/service/BonificaAnagraficaAmletoService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.bonifica.anagrafica.amleto.service.BonificaAnagraficaAmletoService)ctx.lookup(""java:comp/env/service/BonificaAnagraficaAmletoService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/StrutturaReteService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/struttura/rete/service/StrutturaReteInterfacePortProxy.java,89,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteService)ctx.lookup(""java:comp/env/service/StrutturaReteService"");","      87:             { |       88:                 InitialContext ctx = new InitialContext(); | >>>   89:                 _service = (it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteService)ctx.lookup(""java:comp/env/service/StrutturaReteService""); |       90:             } |       91:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RemoteCommandService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/utils/remotecommand/service/RemoteCommandInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.utils.remotecommand.service.RemoteCommandService)ctx.lookup(""java:comp/env/service/RemoteCommandService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.utils.remotecommand.service.RemoteCommandService)ctx.lookup(""java:comp/env/service/RemoteCommandService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CatalogoProdottiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/catalogoprodotti/catalogoprodotti/service/CatalogoProdottiInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.catalogoprodotti.catalogoprodotti.service.CatalogoProdottiService)ctx.lookup(""java:comp/env/service/CatalogoProdottiService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.catalogoprodotti.catalogoprodotti.service.CatalogoProdottiService)ctx.lookup(""java:comp/env/service/CatalogoProdottiService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/SyncroNawSyncroRelService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/syncro/nawsyncrorel/service/SyncroNawSyncroRelInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.syncro.nawsyncrorel.service.SyncroNawSyncroRelService)ctx.lookup(""java:comp/env/service/SyncroNawSyncroRelService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.syncro.nawsyncrorel.service.SyncroNawSyncroRelService)ctx.lookup(""java:comp/env/service/SyncroNawSyncroRelService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AegisService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/aegis/service/AegisInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.function.aegis.service.AegisService)ctx.lookup(""java:comp/env/service/AegisService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.function.aegis.service.AegisService)ctx.lookup(""java:comp/env/service/AegisService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/GdprService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/gdpr/service/GdprInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.function.gdpr.service.GdprService)ctx.lookup(""java:comp/env/service/GdprService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.function.gdpr.service.GdprService)ctx.lookup(""java:comp/env/service/GdprService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FunctionGenericService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/function/generic/service/FunctionGenericInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.function.generic.service.FunctionGenericService)ctx.lookup(""java:comp/env/service/FunctionGenericService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.function.generic.service.FunctionGenericService)ctx.lookup(""java:comp/env/service/FunctionGenericService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/LocalitaStradarioService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/localita/stradario/service/LocalitaStradarioInterfacePortProxy.java,75,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioService)ctx.lookup(""java:comp/env/service/LocalitaStradarioService"");","      73:             { |       74:                 InitialContext ctx = new InitialContext(); | >>>   75:                 _service = (it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioService)ctx.lookup(""java:comp/env/service/LocalitaStradarioService""); |       76:             } |       77:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/LocalitaViarioService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/localita/viario/service/LocalitaViarioInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.localita.viario.service.LocalitaViarioService)ctx.lookup(""java:comp/env/service/LocalitaViarioService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.localita.viario.service.LocalitaViarioService)ctx.lookup(""java:comp/env/service/LocalitaViarioService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AssociazioneCampagnaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/campagnaprovvigionale/associazione/service/AssociazioneCampagnaInterfacePortProxy.java,75,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.campagnaprovvigionale.associazione.service.AssociazioneCampagnaService)ctx.lookup(""java:comp/env/service/AssociazioneCampagnaService"");","      73:             { |       74:                 InitialContext ctx = new InitialContext(); | >>>   75:                 _service = (it.sistinf.albedoweb.services.campagnaprovvigionale.associazione.service.AssociazioneCampagnaService)ctx.lookup(""java:comp/env/service/AssociazioneCampagnaService""); |       76:             } |       77:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CampagnaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/campagnaprovvigionale/campagna/service/CampagnaInterfacePortProxy.java,65,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.campagnaprovvigionale.campagna.service.CampagnaService)ctx.lookup(""java:comp/env/service/CampagnaService"");","      63:             { |       64:                 InitialContext ctx = new InitialContext(); | >>>   65:                 _service = (it.sistinf.albedoweb.services.campagnaprovvigionale.campagna.service.CampagnaService)ctx.lookup(""java:comp/env/service/CampagnaService""); |       66:             } |       67:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/GestioneTabelleTabelleVitaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/gestionetabelle/tabellevita/service/GestioneTabelleTabelleVitaInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaService)ctx.lookup(""java:comp/env/service/GestioneTabelleTabelleVitaService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaService)ctx.lookup(""java:comp/env/service/GestioneTabelleTabelleVitaService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/EleCodeImgcodeService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/imgcode/service/EleCodeImgcodeInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elecode.imgcode.service.EleCodeImgcodeService)ctx.lookup(""java:comp/env/service/EleCodeImgcodeService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.elecode.imgcode.service.EleCodeImgcodeService)ctx.lookup(""java:comp/env/service/EleCodeImgcodeService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/EleCodeBarcodeService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/barcode/service/EleCodeBarcodeInterfacePortProxy.java,47,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.elecode.barcode.service.EleCodeBarcodeService)ctx.lookup(""java:comp/env/service/EleCodeBarcodeService"");","      45:             { |       46:                 InitialContext ctx = new InitialContext(); | >>>   47:                 _service = (it.sistinf.albedoweb.services.elecode.barcode.service.EleCodeBarcodeService)ctx.lookup(""java:comp/env/service/EleCodeBarcodeService""); |       48:             } |       49:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/DashboardDatamartService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/dashboard/datamart/service/DashboardDatamartInterfacePortProxy.java,115,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.dashboard.datamart.service.DashboardDatamartService)ctx.lookup(""java:comp/env/service/DashboardDatamartService"");","     113:             { |      114:                 InitialContext ctx = new InitialContext(); | >>>  115:                 _service = (it.sistinf.albedoweb.services.dashboard.datamart.service.DashboardDatamartService)ctx.lookup(""java:comp/env/service/DashboardDatamartService""); |      116:             } |      117:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/DashboardEventiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/dashboard/eventi/service/DashboardEventiInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.dashboard.eventi.service.DashboardEventiService)ctx.lookup(""java:comp/env/service/DashboardEventiService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.dashboard.eventi.service.DashboardEventiService)ctx.lookup(""java:comp/env/service/DashboardEventiService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CustomerAccessoriService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customeraccessori/service/CustomerAccessoriInterfacePortProxy.java,57,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriService)ctx.lookup(""java:comp/env/service/CustomerAccessoriService"");","      55:             { |       56:                 InitialContext ctx = new InitialContext(); | >>>   57:                 _service = (it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriService)ctx.lookup(""java:comp/env/service/CustomerAccessoriService""); |       58:             } |       59:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AnagraficaEsternaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/esterna/service/AnagraficaEsternaInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.anagrafica.esterna.service.AnagraficaEsternaService)ctx.lookup(""java:comp/env/service/AnagraficaEsternaService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.anagrafica.esterna.service.AnagraficaEsternaService)ctx.lookup(""java:comp/env/service/AnagraficaEsternaService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AnagraficaAnagraficaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/AnagraficaAnagraficaInterfacePortProxy.java,165,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaService)ctx.lookup(""java:comp/env/service/AnagraficaAnagraficaService"");","     163:             { |      164:                 InitialContext ctx = new InitialContext(); | >>>  165:                 _service = (it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaService)ctx.lookup(""java:comp/env/service/AnagraficaAnagraficaService""); |      166:             } |      167:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PDNDService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/pdnd/service/PDNDInterfacePortProxy.java,69,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.anagrafica.pdnd.service.PDNDService)ctx.lookup(""java:comp/env/service/PDNDService"");","      67:             { |       68:                 InitialContext ctx = new InitialContext(); | >>>   69:                 _service = (it.sistinf.albedoweb.services.anagrafica.pdnd.service.PDNDService)ctx.lookup(""java:comp/env/service/PDNDService""); |       70:             } |       71:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CustomerService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/service/CustomerInterfacePortProxy.java,99,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerService)ctx.lookup(""java:comp/env/service/CustomerService"");","      97:             { |       98:                 InitialContext ctx = new InitialContext(); | >>>   99:                 _service = (it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerService)ctx.lookup(""java:comp/env/service/CustomerService""); |      100:             } |      101:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/AuditTracingService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/audit/tracing/service/AuditTracingInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.audit.tracing.service.AuditTracingService)ctx.lookup(""java:comp/env/service/AuditTracingService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.audit.tracing.service.AuditTracingService)ctx.lookup(""java:comp/env/service/AuditTracingService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RestrizioneAccessoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/restrizioneaccesso/service/RestrizioneAccessoInterfacePortProxy.java,59,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.portafoglio.restrizioneaccesso.service.RestrizioneAccessoService)ctx.lookup(""java:comp/env/service/RestrizioneAccessoService"");","      57:             { |       58:                 InitialContext ctx = new InitialContext(); | >>>   59:                 _service = (it.sistinf.albedoweb.services.portafoglio.restrizioneaccesso.service.RestrizioneAccessoService)ctx.lookup(""java:comp/env/service/RestrizioneAccessoService""); |       60:             } |       61:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PortafoglioRapportoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/rapporto/service/PortafoglioRapportoInterfacePortProxy.java,103,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoService)ctx.lookup(""java:comp/env/service/PortafoglioRapportoService"");","     101:             { |      102:                 InitialContext ctx = new InitialContext(); | >>>  103:                 _service = (it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoService)ctx.lookup(""java:comp/env/service/PortafoglioRapportoService""); |      104:             } |      105:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PortafoglioBonificiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/bonifici/service/PortafoglioBonificiInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.portafoglio.bonifici.service.PortafoglioBonificiService)ctx.lookup(""java:comp/env/service/PortafoglioBonificiService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.portafoglio.bonifici.service.PortafoglioBonificiService)ctx.lookup(""java:comp/env/service/PortafoglioBonificiService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PortafoglioEstrazionePolizzaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/portafoglio/estrazione/service/PortafoglioEstrazionePolizzaInterfacePortProxy.java,55,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.portafoglio.estrazione.service.PortafoglioEstrazionePolizzaService)ctx.lookup(""java:comp/env/service/PortafoglioEstrazionePolizzaService"");","      53:             { |       54:                 InitialContext ctx = new InitialContext(); | >>>   55:                 _service = (it.sistinf.albedoweb.services.portafoglio.estrazione.service.PortafoglioEstrazionePolizzaService)ctx.lookup(""java:comp/env/service/PortafoglioEstrazionePolizzaService""); |       56:             } |       57:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/TabellaTracciatoService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/service/TabellaTracciatoInterfacePortProxy.java,169,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoService)ctx.lookup(""java:comp/env/service/TabellaTracciatoService"");","     167:             { |      168:                 InitialContext ctx = new InitialContext(); | >>>  169:                 _service = (it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoService)ctx.lookup(""java:comp/env/service/TabellaTracciatoService""); |      170:             } |      171:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/QuestionarioStrutturaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/questionario/struttura/service/QuestionarioStrutturaInterfacePortProxy.java,81,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaService)ctx.lookup(""java:comp/env/service/QuestionarioStrutturaService"");","      79:             { |       80:                 InitialContext ctx = new InitialContext(); | >>>   81:                 _service = (it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaService)ctx.lookup(""java:comp/env/service/QuestionarioStrutturaService""); |       82:             } |       83:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/QuestionarioAuraService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/questionario/aura/service/QuestionarioAuraInterfacePortProxy.java,59,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraService)ctx.lookup(""java:comp/env/service/QuestionarioAuraService"");","      57:             { |       58:                 InitialContext ctx = new InitialContext(); | >>>   59:                 _service = (it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraService)ctx.lookup(""java:comp/env/service/QuestionarioAuraService""); |       60:             } |       61:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/ReportPrenotazioniService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/report/prenotazioni/service/ReportPrenotazioniInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.report.prenotazioni.service.ReportPrenotazioniService)ctx.lookup(""java:comp/env/service/ReportPrenotazioniService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.report.prenotazioni.service.ReportPrenotazioniService)ctx.lookup(""java:comp/env/service/ReportPrenotazioniService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CollettivaNonGestitaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/collettiva/collettivanongestita/service/CollettivaNonGestitaInterfacePortProxy.java,69,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.collettiva.collettivanongestita.service.CollettivaNonGestitaService)ctx.lookup(""java:comp/env/service/CollettivaNonGestitaService"");","      67:             { |       68:                 InitialContext ctx = new InitialContext(); | >>>   69:                 _service = (it.sistinf.albedoweb.services.collettiva.collettivanongestita.service.CollettivaNonGestitaService)ctx.lookup(""java:comp/env/service/CollettivaNonGestitaService""); |       70:             } |       71:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PrintDataService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/eventi/printdata/service/PrintDataServiceInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.eventi.printdata.service.PrintDataService)ctx.lookup(""java:comp/env/service/PrintDataService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.eventi.printdata.service.PrintDataService)ctx.lookup(""java:comp/env/service/PrintDataService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FlussoEbaasFlussoEbaasService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/flussoebaas/flussoebaas/service/FlussoEbaasFlussoEbaasInterfacePortProxy.java,59,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.flussoebaas.flussoebaas.service.FlussoEbaasFlussoEbaasService)ctx.lookup(""java:comp/env/service/FlussoEbaasFlussoEbaasService"");","      57:             { |       58:                 InitialContext ctx = new InitialContext(); | >>>   59:                 _service = (it.sistinf.albedoweb.services.flussoebaas.flussoebaas.service.FlussoEbaasFlussoEbaasService)ctx.lookup(""java:comp/env/service/FlussoEbaasFlussoEbaasService""); |       60:             } |       61:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FlussoPrometeiaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/flusso/prometeia/service/FlussoPrometeiaInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.flusso.prometeia.service.FlussoPrometeiaService)ctx.lookup(""java:comp/env/service/FlussoPrometeiaService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.flusso.prometeia.service.FlussoPrometeiaService)ctx.lookup(""java:comp/env/service/FlussoPrometeiaService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/MessaggioReteVenditaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/retevendita/messaggio/service/MessaggioReteVenditaInterfacePortProxy.java,53,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.retevendita.messaggio.service.MessaggioReteVenditaService)ctx.lookup(""java:comp/env/service/MessaggioReteVenditaService"");","      51:             { |       52:                 InitialContext ctx = new InitialContext(); | >>>   53:                 _service = (it.sistinf.albedoweb.services.retevendita.messaggio.service.MessaggioReteVenditaService)ctx.lookup(""java:comp/env/service/MessaggioReteVenditaService""); |       54:             } |       55:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/FlussoReteVenditaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/retevendita/flusso/service/FlussoReteVenditaInterfacePortProxy.java,49,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.retevendita.flusso.service.FlussoReteVenditaService)ctx.lookup(""java:comp/env/service/FlussoReteVenditaService"");","      47:             { |       48:                 InitialContext ctx = new InitialContext(); | >>>   49:                 _service = (it.sistinf.albedoweb.services.retevendita.flusso.service.FlussoReteVenditaService)ctx.lookup(""java:comp/env/service/FlussoReteVenditaService""); |       50:             } |       51:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/MovimentiContabiliOasiService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/oasi/movimenticontabili/service/MovimentiContabiliOasiInterfacePortProxy.java,63,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.oasi.movimenticontabili.service.MovimentiContabiliOasiService)ctx.lookup(""java:comp/env/service/MovimentiContabiliOasiService"");","      61:             { |       62:                 InitialContext ctx = new InitialContext(); | >>>   63:                 _service = (it.sistinf.albedoweb.services.oasi.movimenticontabili.service.MovimentiContabiliOasiService)ctx.lookup(""java:comp/env/service/MovimentiContabiliOasiService""); |       64:             } |       65:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PrenotazioneLiquidazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/liquidazione/service/PrenotazioneLiquidazioneInterfacePortProxy.java,45,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prenotazione.liquidazione.service.PrenotazioneLiquidazioneService)ctx.lookup(""java:comp/env/service/PrenotazioneLiquidazioneService"");","      43:             { |       44:                 InitialContext ctx = new InitialContext(); | >>>   45:                 _service = (it.sistinf.albedoweb.services.prenotazione.liquidazione.service.PrenotazioneLiquidazioneService)ctx.lookup(""java:comp/env/service/PrenotazioneLiquidazioneService""); |       46:             } |       47:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PrenotazionePostVenditaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/postvendita/service/PrenotazionePostVenditanterfacePortProxy.java,87,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaService)ctx.lookup(""java:comp/env/service/PrenotazionePostVenditaService"");","      85:             { |       86:                 InitialContext ctx = new InitialContext(); | >>>   87:                 _service = (it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaService)ctx.lookup(""java:comp/env/service/PrenotazionePostVenditaService""); |       88:             } |       89:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PrenotazioneTrasfAgenziaService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/trasfagenzia/service/PrenotazioneTrasfAgenziaInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prenotazione.trasfagenzia.service.PrenotazioneTrasfAgenziaService)ctx.lookup(""java:comp/env/service/PrenotazioneTrasfAgenziaService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.prenotazione.trasfagenzia.service.PrenotazioneTrasfAgenziaService)ctx.lookup(""java:comp/env/service/PrenotazioneTrasfAgenziaService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/PrenotazioneDocumentazioneService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/prenotazione/documentazione/service/PrenotazioneDocumentazioneInterfacePortProxy.java,51,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.prenotazione.documentazione.service.PrenotazioneDocumentazioneService)ctx.lookup(""java:comp/env/service/PrenotazioneDocumentazioneService"");","      49:             { |       50:                 InitialContext ctx = new InitialContext(); | >>>   51:                 _service = (it.sistinf.albedoweb.services.prenotazione.documentazione.service.PrenotazioneDocumentazioneService)ctx.lookup(""java:comp/env/service/PrenotazioneDocumentazioneService""); |       52:             } |       53:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/RctService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/interfacce/contabilita/rct/service/RctInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.interfacce.contabilita.rct.service.RctService)ctx.lookup(""java:comp/env/service/RctService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.interfacce.contabilita.rct.service.RctService)ctx.lookup(""java:comp/env/service/RctService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CollettoreService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/interfacce/collettore/service/CollettoreInterfacePortProxy.java,43,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.interfacce.collettore.service.CollettoreService)ctx.lookup(""java:comp/env/service/CollettoreService"");","      41:             { |       42:                 InitialContext ctx = new InitialContext(); | >>>   43:                 _service = (it.sistinf.albedoweb.services.interfacce.collettore.service.CollettoreService)ctx.lookup(""java:comp/env/service/CollettoreService""); |       44:             } |       45:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/CoeffCostoMgmFeeService,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/deroghe/coeffcostomgmfee/service/CoeffCostoMgmFeeInterfacePortProxy.java,79,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeService)ctx.lookup(""java:comp/env/service/CoeffCostoMgmFeeService"");","      77:             { |       78:                 InitialContext ctx = new InitialContext(); | >>>   79:                 _service = (it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeService)ctx.lookup(""java:comp/env/service/CoeffCostoMgmFeeService""); |       80:             } |       81:             catch (NamingException e)"
JNDI_LOOKUP,java:comp/env/service/DataDisposal,Services.Common.Interfaces/src/com/zurich/it/oil/externalservices/datadisposal/DataDisposalPortProxy.java,46,"lookup\s*\(\s*[""\']([^""\']+)[""\']","_service = (com.zurich.it.oil.externalservices.datadisposal.DataDisposal)ctx.lookup(""java:comp/env/service/DataDisposal"");","      44:             { |       45:                 InitialContext ctx = new InitialContext(); | >>>   46:                 _service = (com.zurich.it.oil.externalservices.datadisposal.DataDisposal)ctx.lookup(""java:comp/env/service/DataDisposal""); |       47:             } |       48:             catch (NamingException e)"
JNDI_LOOKUP,${jndiDatasourceName},Services.Common.Persistency/src/spring-conf/datasource.xml,10,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${jndiDatasourceName}"" />","       8:  |        9: 	<bean id=""applicationDatasourceOnLine"" class=""org.springframework.jndi.JndiObjectFactoryBean""> | >>>   10: 		<property name=""jndiName"" value=""${jndiDatasourceName}"" /> |       11: 	</bean> |       12: "
JNDI_LOOKUP,${prefixJNDINameEJBProxy}Aegis,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,241,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Aegis""/>","     239:  |      240: 	<bean id=""AegisProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  241:         <property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Aegis""/> |      242:         <property name=""businessInterface"" value=""it.sistinf.albedoweb.services.function.aegis.service.AegisInterface""/> |      243:         <property name=""homeInterface"" value=""it.sistinf.albedoweb.services.function.aegis.service.ejb.RemoteEJBAegisInterface""/>"
JNDI_LOOKUP,${prefixJNDINameEJBProxy}Print,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,247,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Print""/>","     245:  |      246:     <bean id=""PrintProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  247: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Print""/> |      248: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.eventi.print.service.PrintInterface""/> |      249: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.eventi.print.service.ejb.RemoteEJBPrintInterface""/>"
JNDI_LOOKUP,albedoWebSIALBW04PropostaProposta,Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml,46,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04PropostaProposta""/>                                    -->","      44:  |       45: <!--     <bean id=""PropostaServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> --> | >>>   46: <!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04PropostaProposta""/>                                    --> |       47: <!-- 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/> --> |       48: <!-- 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/> -->"
JNDI_LOOKUP,albedoWebSIALBW04Domini,Services.Common.EJB/ejbModule/spring-conf/real-env/PropostaPropostaImpl.xml,60,"jndiName[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04Domini""/>                                    -->","      58:  |       59: <!-- 	<bean id=""DominiServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> --> | >>>   60: <!-- 	  	<property name=""jndiName"" value=""albedoWebSIALBW04Domini""/>                                    --> |       61: <!-- 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/> --> |       62: <!-- 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/> -->"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,9,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PortafoglioRapporto""/>","       7:  |        8: 	<bean id=""PortafoglioRapportoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>    9: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PortafoglioRapporto""/> |       10: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.portafoglio.rapporto.service.PortafoglioRapportoInterface""/> |       11:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.portafoglio.rapporto.service.ejb.RemoteEJBPortafoglioRapportoInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,15,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PropostaProposta""/>","      13:  |       14: 	<bean id=""PropostaPropostaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   15: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PropostaProposta""/> |       16: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.PropostaPropostaInterface""/> |       17:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.proposta.proposta.service.ejb.RemoteEJBPropostaPropostaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,21,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneUtente""/>","      19:  |       20: 	<bean id=""AbilitazioneUtenteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   21: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneUtente""/> |       22: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.utente.service.AbilitazioneUtenteInterface""/> |       23: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.abilitazione.utente.service.ejb.RemoteEJBAbilitazioneUtenteInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,27,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneAzione""/>","      25:  |       26: 	<bean id=""AbilitazioneAzioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   27: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AbilitazioneAzione""/> |       28: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.abilitazione.azione.service.AbilitazioneAzioneInterface""/> |       29:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.abilitazione.azione.service.ejb.RemoteEJBAbilitazioneAzioneInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,33,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}StrutturaRete""/>","      31:  |       32:     <bean id=""StrutturaReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   33: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}StrutturaRete""/> |       34:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.struttura.rete.service.StrutturaReteInterface""/> |       35:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.struttura.rete.service.ejb.RemoteEJBStrutturaReteInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,39,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoProdotto""/>","      37:  |       38: 	<bean id=""ProdottoProdottoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   39: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoProdotto""/> |       40: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.prodotto.service.ProdottoProdottoInterface""/> |       41: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prodotto.prodotto.service.ejb.RemoteEJBProdottoProdottoInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,45,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Customer""/>","      43:  |       44: 	<bean id=""CustomerProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   45: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Customer""/> |       46: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customer.service.CustomerInterface""/> |       47: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customer.service.ejb.RemoteEJBCustomerInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,51,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Domini""/>","      49:  |       50: 	<bean id=""DominiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   51: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Domini""/> |       52: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.DominiInterface""/> |       53: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.domini.naw.service.ejb.RemoteEJBDominiInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,57,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AnagraficaAnagrafica""/>","      55:  |       56: 	<bean id=""AnagraficaAnagraficaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   57: 		<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}AnagraficaAnagrafica""/> |       58:     	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.anagrafica.service.AnagraficaAnagraficaInterface""/> |       59:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.anagrafica.service.ejb.RemoteEJBAnagraficaAnagraficaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,63,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CoeffCostoMgmFee""/>","      61:  |       62:     <bean id=""CoeffCostoMgmFeeProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   63: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CoeffCostoMgmFee""/> |       64: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.CoeffCostoMgmFeeInterface""/> |       65: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.deroghe.coeffcostomgmfee.service.ejb.RemoteEJBCoeffCostoMgmFeeInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,69,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CustomerAccessori""/>","      67:  |       68: 	<bean id=""CustomerAccessoriProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   69: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}CustomerAccessori""/> |       70: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.CustomerAccessoriInterface""/> |       71: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.anagrafica.customeraccessori.service.ejb.RemoteEJBCustomerAccessoriInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,75,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}WorkFlowMgr""/>","      73:  |       74: 	<bean id=""WorkFlowMgrProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   75: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}WorkFlowMgr""/> |       76: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.workflow.mgr.WorkFlowMgrInterface""/> |       77: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.workflow.mgr.WorkFlowMgrRemoteInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,81,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}LocalitaStradario""/>","      79:  |       80: 	<bean id=""LocalitaStradarioProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   81: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}LocalitaStradario""/> |       82: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.localita.stradario.service.LocalitaStradarioInterface""/> |       83: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.localita.stradario.service.ejb.RemoteEJBLocalitaStradarioInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,87,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoTariffa""/>","      85:  |       86: 	<bean id=""ProdottoTariffaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   87: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ProdottoTariffa""/> |       88: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prodotto.tariffa.service.ProdottoTariffaInterface""/> |       89: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prodotto.tariffa.service.ejb.RemoteEJBProdottoTariffaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,93,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PianiSpostamento""/>","      91:  |       92: 	<bean id=""PianiSpostamentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   93: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PianiSpostamento""/> |       94: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.pianispostamento.service.PianiSpostamentoInterface""/> |       95: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.fondo.pianispostamento.service.ejb.RemoteEJBPianiSpostamentoInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,99,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ModelloMatematicoFormula""/>","      97:  |       98: 	<bean id=""ModelloMatematicoFormulaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>   99: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ModelloMatematicoFormula""/> |      100: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.modellomatematico.formula.service.ModelloMatematicoFormulaInterface""/> |      101: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.modellomatematico.formula.service.ejb.RemoteEJBModelloMatematicoFormulaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,105,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}TabellaTracciato""/>","     103:  |      104: 	<bean id=""TabellaTracciatoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  105: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}TabellaTracciato""/> |      106: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.tabella.tracciato.service.TabellaTracciatoInterface""/> |      107: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.tabella.tracciato.service.ejb.RemoteEJBTabellaTracciatoInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,111,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}DerogheService""/>","     109:  |      110: 	<bean id=""DerogheServiceProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  111: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}DerogheService""/> |      112: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.deroghe.DerogheServiceInterface""/> |      113: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.deroghe.RemoteDerogheServiceInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,117,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioAura""/>","     115:  |      116: 	<bean id=""QuestionarioAuraProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  117: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioAura""/> |      118: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.aura.service.QuestionarioAuraInterface""/> |      119:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.questionario.aura.service.ejb.RemoteEJBQuestionarioAuraInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,123,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaGestioneRid""/>","     121:  |      122: 	<bean id=""PolizzaGestioneRidProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  123: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaGestioneRid""/> |      124: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.gestionerid.service.PolizzaGestioneRidInterface""/> |      125: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.gestionerid.service.ejb.RemoteEJBPolizzaGestioneRidInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,129,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}FondoTrasferimento""/>","     127:  |      128: 	<bean id=""FondoTrasferimentoProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  129: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}FondoTrasferimento""/> |      130: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.fondo.trasferimento.service.FondoTrasferimentoInterface""/> |      131: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.fondo.trasferimento.service.ejb.RemoteEJBFondoTrasferimentoInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,135,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioStruttura""/>","     133:  |      134: 	<bean id=""QuestionarioStrutturaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  135: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}QuestionarioStruttura""/> |      136: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.questionario.struttura.service.QuestionarioStrutturaInterface""/> |      137: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.questionario.struttura.service.ejb.RemoteEJBQuestionarioStrutturaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,141,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ZbRete""/>","     139:  |      140: 	<bean id=""ZbReteProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  141: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}ZbRete""/> |      142: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ZbReteInterface""/> |      143:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.elis.zbank.zbrete.service.ejb.RemoteEJBZbReteInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,147,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPolizza""/>","     145:  |      146: 	<bean id=""PolizzaPolizzaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  147: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPolizza""/> |      148: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.polizza.service.PolizzaPolizzaInterface""/> |      149: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.polizza.service.ejb.RemoteEJBPolizzaPolizzaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,153,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPosizione""/>","     151:  |      152: 	<bean id=""PolizzaPosizioneProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  153: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaPosizione""/> |      154: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.posizione.service.PolizzaPosizioneInterface""/> |      155:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.posizione.service.ejb.RemoteEJBPolizzaPosizioneInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,159,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}SyncroPolicy""/>","     157:  |      158: 	<bean id=""SyncroPolicyProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  159: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}SyncroPolicy""/> |      160: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.elis.syncropolicy.service.SyncroPolicyInterface""/> |      161:     	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.elis.syncropolicy.service.ejb.RemoteEJBSyncroPolicyInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,165,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PrenotazionePostVendita""/>","     163:  |      164: 	<bean id=""PrenotazionePostVenditaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  165: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PrenotazionePostVendita""/> |      166: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.prenotazione.postvendita.service.PrenotazionePostVenditaInterface""/> |      167: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.prenotazione.postvendita.service.ejb.RemoteEJBPrenotazionePostVenditaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,171,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita""/>","     169:  |      170: 	<bean id=""GestioneTabelleTabelleVitaProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  171: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}GestioneTabelleTabelleVita""/> |      172: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.GestioneTabelleTabelleVitaInterface""/> |      173: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.gestionetabelle.tabellevita.service.ejb.RemoteEJBGestioneTabelleTabelleVitaInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,177,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaStorni""/>","     175:  |      176: 	<bean id=""PolizzaStorniProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  177: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaStorni""/> |      178: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.storni.service.PolizzaStorniInterface""/> |      179: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.storni.service.ejb.RemoteEJBPolizzaStorniInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,REST.Services.Cobol.Web/src/spring-conf/external-services.xml,183,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali""/>","     181:  |      182: 	<bean id=""PolizzaOpzioniContrattualiProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  183: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}PolizzaOpzioniContrattuali""/> |      184: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.PolizzaOpzioniContrattualiInterface""/> |      185: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.polizza.opzionicontrattuali.service.ejb.RemoteEJBPolizzaOpzioniContrattualiInterface""/>"
DATABASE_CONFIG,${applicationDatasource},Services.Common.Persistency/src/spring-conf/spring-ibatis.xml,13,"dataSource[""\']?\s*ref\s*=\s*[""\']([^""\']+)[""\']","<property name=""dataSource"" ref=""${applicationDatasource}"" />","      11: 	<bean id=""blueLifeSqlMapConfig"" class=""org.springframework.orm.ibatis.SqlMapClientFactoryBean""> |       12: 		<property name=""configLocation"" value=""persistency/resources/sqlMapConfig.xml"" /> | >>>   13: 		<property name=""dataSource"" ref=""${applicationDatasource}"" /> |       14: 	</bean> |       15: "
DATABASE_CONFIG,jndiDatasourceName,Services.Common.Persistency/src/spring-conf/datasource.xml,10,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${jndiDatasourceName}"" />","       8:  |        9: 	<bean id=""applicationDatasourceOnLine"" class=""org.springframework.jndi.JndiObjectFactoryBean""> | >>>   10: 		<property name=""jndiName"" value=""${jndiDatasourceName}"" /> |       11: 	</bean> |       12: "
DATABASE_CONFIG,prefixJNDINameEJBProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,241,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Aegis""/>","     239:  |      240: 	<bean id=""AegisProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  241:         <property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Aegis""/> |      242:         <property name=""businessInterface"" value=""it.sistinf.albedoweb.services.function.aegis.service.AegisInterface""/> |      243:         <property name=""homeInterface"" value=""it.sistinf.albedoweb.services.function.aegis.service.ejb.RemoteEJBAegisInterface""/>"
DATABASE_CONFIG,prefixJNDINameEJBProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,247,"<property\s+name=""jndiName""\s+value=""\$\{([^}]+)\}","<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Print""/>","     245:  |      246:     <bean id=""PrintProxy"" class=""org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean""> | >>>  247: 	  	<property name=""jndiName"" value=""${prefixJNDINameEJBProxy}Print""/> |      248: 	  	<property name=""businessInterface"" value=""it.sistinf.albedoweb.services.eventi.print.service.PrintInterface""/> |      249: 	  	<property name=""homeInterface"" value=""it.sistinf.albedoweb.services.eventi.print.service.ejb.RemoteEJBPrintInterface""/>"
DATABASE_CONFIG,${applicationDatasource},Services.Common.Impl/src/spring-conf/PolizzaTrasferimentoDao.xml,16,"dataSource[""\']?\s*ref\s*=\s*[""\']([^""\']+)[""\']","<property name=""dataSource"" ref=""${applicationDatasource}"" />","      14:  |       15:  	<bean id=""PolizzaTrasferimentoCustomDao"" class=""it.sistinf.albedoweb.services.polizza.trasferimento.service.persistency.PolizzaTrasferimentoCustomDao"" init-method=""init"" > | >>>   16: 		<property name=""dataSource"" ref=""${applicationDatasource}"" /> |       17: 		<property name=""blueLifeLogger"" ref=""BlueLifeLogger"" /> |       18: 	</bean>"
EJB_PROXY_USAGE,ProdottoProdottoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java,72,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy"");","      70:     @PostConstruct |       71:     private void init() { | >>>   72:     	prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |       73: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); |       74:     }"
EJB_PROXY_USAGE,TabellaTracciatoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/prodotto/Prodotto.java,73,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"");","      71:     private void init() { |       72:     	prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); | >>>   73: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); |       74:     } |       75: "
EJB_PROXY_USAGE,DominiProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java,61,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");","      59:     @PostConstruct |       60:     private void init() { | >>>   61:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |       62:     	fondo = (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoProxy""); |       63:     }"
EJB_PROXY_USAGE,FondoTrasferimentoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/domini/Dominio.java,62,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","fondo = (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoProxy"");","      60:     private void init() { |       61:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); | >>>   62:     	fondo = (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoProxy""); |       63:     } |       64: "
EJB_PROXY_USAGE,AbilitazioneUtenteProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/abilitazione/Abilitazioni.java,62,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioneUtente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy"");","      60:     @PostConstruct |       61:     private void init() { | >>>   62:     	abilitazioneUtente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); |       63:     	abilitazioneAzione = (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneProxy""); |       64:     }"
EJB_PROXY_USAGE,AbilitazioneAzioneProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/abilitazione/Abilitazioni.java,63,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioneAzione = (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneProxy"");","      61:     private void init() { |       62:     	abilitazioneUtente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); | >>>   63:     	abilitazioneAzione = (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneProxy""); |       64:     } |       65: "
EJB_PROXY_USAGE,PolizzaPolizzaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,390,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");","     388: 	@PostConstruct |      389:     private void init() { | >>>  390: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      391: 		proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      392: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"");"
EJB_PROXY_USAGE,PropostaPropostaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,391,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy"");","     389:     private void init() { |      390: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); | >>>  391: 		proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      392: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); |      393: 		posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy"");"
EJB_PROXY_USAGE,TabellaTracciatoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,392,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"");","     390: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      391: 		proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); | >>>  392: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); |      393: 		posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy""); |      394: 		deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy"");"
EJB_PROXY_USAGE,PolizzaPosizioneProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,393,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy"");","     391: 		proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      392: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); | >>>  393: 		posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy""); |      394: 		deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy""); |      395: 		utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy"");"
EJB_PROXY_USAGE,DerogheServiceProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,394,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy"");","     392: 		tracciato = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy""); |      393: 		posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy""); | >>>  394: 		deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy""); |      395: 		utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); |      396: 		stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy"");"
EJB_PROXY_USAGE,AbilitazioneUtenteProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,395,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy"");","     393: 		posizione = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy""); |      394: 		deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy""); | >>>  395: 		utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); |      396: 		stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy""); |      397: 		anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy"");"
EJB_PROXY_USAGE,SyncroPolicyProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,396,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy"");","     394: 		deroga = (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceProxy""); |      395: 		utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); | >>>  396: 		stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy""); |      397: 		anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); |      398: 		postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy"");"
EJB_PROXY_USAGE,AnagraficaAnagraficaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,397,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy"");","     395: 		utente = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteProxy""); |      396: 		stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy""); | >>>  397: 		anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); |      398: 		postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy""); |      399: 		domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");"
EJB_PROXY_USAGE,PrenotazionePostVenditaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,398,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy"");","     396: 		stampa = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyProxy""); |      397: 		anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); | >>>  398: 		postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy""); |      399: 		domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      400: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy"");"
EJB_PROXY_USAGE,DominiProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,399,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");","     397: 		anagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); |      398: 		postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy""); | >>>  399: 		domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      400: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      401: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy"");"
EJB_PROXY_USAGE,ProdottoProdottoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,400,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy"");","     398: 		postVendita = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaProxy""); |      399: 		domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); | >>>  400: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      401: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      402: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");"
EJB_PROXY_USAGE,WorkFlowMgrProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,401,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy"");","     399: 		domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      400: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); | >>>  401: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      402: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      403: 		strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"");"
EJB_PROXY_USAGE,Abilitazioni,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,402,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");","     400: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      401: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); | >>>  402: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      403: 		strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy""); |      404: 		storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy"");"
EJB_PROXY_USAGE,StrutturaReteProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,403,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"");","     401: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      402: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); | >>>  403: 		strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy""); |      404: 		storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy""); |      405: 		polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy"");"
EJB_PROXY_USAGE,PolizzaStorniProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,404,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy"");","     402: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      403: 		strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy""); | >>>  404: 		storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy""); |      405: 		polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy""); |      406:     	anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica"");"
EJB_PROXY_USAGE,PolizzaOpzioniContrattualiProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,405,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy"");","     403: 		strutturaRete = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy""); |      404: 		storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy""); | >>>  405: 		polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy""); |      406:     	anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica""); |      407:     	portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio"");"
EJB_PROXY_USAGE,Anagrafica,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,406,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica"");","     404: 		storni = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniProxy""); |      405: 		polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy""); | >>>  406:     	anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica""); |      407:     	portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio""); |      408:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio"");"
EJB_PROXY_USAGE,Portafoglio,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,407,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio"");","     405: 		polizzaOpzContrattuali = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiProxy""); |      406:     	anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica""); | >>>  407:     	portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio""); |      408:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); |      409:     	bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo"");"
EJB_PROXY_USAGE,Dominio,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,408,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio"");","     406:     	anagraficaRest = (AnagraficaInterface) FactoryBean.getBean(""Anagrafica""); |      407:     	portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio""); | >>>  408:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); |      409:     	bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo""); |      410:     	prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto"");"
EJB_PROXY_USAGE,BloccoAutorizzativo,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,409,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo"");","     407:     	portafoglioRest = (PortafoglioInterface) FactoryBean.getBean(""Portafoglio""); |      408:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); | >>>  409:     	bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo""); |      410:     	prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |      411:     	customer = (CustInterface) FactoryBean.getBean(""Cust"");"
EJB_PROXY_USAGE,Prodotto,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,410,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto"");","     408:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); |      409:     	bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo""); | >>>  410:     	prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |      411:     	customer = (CustInterface) FactoryBean.getBean(""Cust""); |      412:     	customerSoap = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");"
EJB_PROXY_USAGE,Cust,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,411,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customer = (CustInterface) FactoryBean.getBean(""Cust"");","     409:     	bloccoAutorizzativo = (BloccoAutorizzativoInterface) FactoryBean.getBean(""BloccoAutorizzativo""); |      410:     	prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); | >>>  411:     	customer = (CustInterface) FactoryBean.getBean(""Cust""); |      412:     	customerSoap = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |      413: "
EJB_PROXY_USAGE,CustomerProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java,412,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customerSoap = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");","     410:     	prodottoRest = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |      411:     	customer = (CustInterface) FactoryBean.getBean(""Cust""); | >>>  412:     	customerSoap = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |      413:  |      414: 	}"
EJB_PROXY_USAGE,AnagraficaAnagraficaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/anagrafica/Anagrafica.java,66,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","anagraficaAnagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy"");","      64:     @PostConstruct |       65:     private void init() { | >>>   66:     	anagraficaAnagrafica = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); |       67:     } |       68: "
EJB_PROXY_USAGE,PortafoglioRapportoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,117,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","portafoglio = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy"");","     115:     @PostConstruct |      116:     private void init() { | >>>  117:     	portafoglio = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy""); |      118:     	proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      119: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");"
EJB_PROXY_USAGE,PropostaPropostaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,118,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy"");","     116:     private void init() { |      117:     	portafoglio = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy""); | >>>  118:     	proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      119: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      120: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");"
EJB_PROXY_USAGE,Abilitazioni,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,119,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");","     117:     	portafoglio = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy""); |      118:     	proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); | >>>  119: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      120: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      121: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy"");"
EJB_PROXY_USAGE,PolizzaPolizzaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,120,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");","     118:     	proposta = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaProxy""); |      119: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); | >>>  120: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      121: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      122: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy"");"
EJB_PROXY_USAGE,WorkFlowMgrProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,121,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy"");","     119: 		abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |      120: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); | >>>  121: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      122: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      123:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");"
EJB_PROXY_USAGE,ProdottoProdottoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,122,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy"");","     120: 		polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      121: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); | >>>  122: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      123:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |      124:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy"");"
EJB_PROXY_USAGE,CustomerProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,123,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");","     121: 		workFlowMgr = (WorkFlowMgrInterface) FactoryBean.getBean(""WorkFlowMgrProxy""); |      122: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); | >>>  123:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |      124:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |      125:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio"");"
EJB_PROXY_USAGE,PepCrimeProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,124,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy"");","     122: 		prodotto = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoProxy""); |      123:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); | >>>  124:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |      125:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); |      126:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");"
EJB_PROXY_USAGE,Dominio,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,125,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio"");","     123:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |      124:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); | >>>  125:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); |      126:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      127:     }"
EJB_PROXY_USAGE,DominiProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/portafoglio/Portafoglio.java,126,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");","     124:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |      125:     	dominioRest = (DominioInterface) FactoryBean.getBean(""Dominio""); | >>>  126:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      127:     } |      128: "
EJB_PROXY_USAGE,Abilitazioni,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,76,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");","      74:     @PostConstruct |       75:     private void init() { | >>>   76:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |       77:     	polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |       78:     	cust = (CustInterface) FactoryBean.getBean(""Cust"");"
EJB_PROXY_USAGE,PolizzaPolizzaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,77,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");","      75:     private void init() { |       76:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); | >>>   77:     	polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |       78:     	cust = (CustInterface) FactoryBean.getBean(""Cust""); |       79:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto"");"
EJB_PROXY_USAGE,Cust,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,78,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","cust = (CustInterface) FactoryBean.getBean(""Cust"");","      76:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |       77:     	polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); | >>>   78:     	cust = (CustInterface) FactoryBean.getBean(""Cust""); |       79:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |       80:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");"
EJB_PROXY_USAGE,Prodotto,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,79,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto"");","      77:     	polizza = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |       78:     	cust = (CustInterface) FactoryBean.getBean(""Cust""); | >>>   79:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |       80:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       81:     	dominio = (DominioInterface) FactoryBean.getBean(""Dominio"");"
EJB_PROXY_USAGE,CustomerProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,80,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");","      78:     	cust = (CustInterface) FactoryBean.getBean(""Cust""); |       79:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); | >>>   80:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       81:     	dominio = (DominioInterface) FactoryBean.getBean(""Dominio""); |       82:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy"");"
EJB_PROXY_USAGE,Dominio,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,81,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","dominio = (DominioInterface) FactoryBean.getBean(""Dominio"");","      79:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |       80:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); | >>>   81:     	dominio = (DominioInterface) FactoryBean.getBean(""Dominio""); |       82:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |       83:     	portafoglioRapporto = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy"");"
EJB_PROXY_USAGE,PepCrimeProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,82,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy"");","      80:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       81:     	dominio = (DominioInterface) FactoryBean.getBean(""Dominio""); | >>>   82:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |       83:     	portafoglioRapporto = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy""); |       84:     }"
EJB_PROXY_USAGE,PortafoglioRapportoProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/blocco/autorizzativo/BloccoAutorizzativo.java,83,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","portafoglioRapporto = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy"");","      81:     	dominio = (DominioInterface) FactoryBean.getBean(""Dominio""); |       82:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); | >>>   83:     	portafoglioRapporto = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoProxy""); |       84:     } |       85: "
EJB_PROXY_USAGE,PepCrimeProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/Cust.java,93,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy"");","      91:     @PostConstruct |       92:     private void init() { | >>>   93:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |       94:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       95:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");"
EJB_PROXY_USAGE,CustomerProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/Cust.java,94,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");","      92:     private void init() { |       93:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); | >>>   94:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       95:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |       96:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");"
EJB_PROXY_USAGE,DominiProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/Cust.java,95,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","domini = (DominiInterface) FactoryBean.getBean(""DominiProxy"");","      93:     	pepCrimeService = (PepCrimeService) FactoryBean.getBean(""PepCrimeProxy""); |       94:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); | >>>   95:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); |       96:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |       97:     }"
EJB_PROXY_USAGE,Abilitazioni,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/Cust.java,96,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni"");","      94:     	customer = (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       95:     	domini = (DominiInterface) FactoryBean.getBean(""DominiProxy""); | >>>   96:     	abilitazioni = (AbilitazioniInterface) FactoryBean.getBean(""Abilitazioni""); |       97:     } |       98: "
EJB_PROXY_USAGE,CustomerAccessoriProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/customer/CustAccessori.java,31,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","customerAccessori = (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy"");","      29:     @PostConstruct |       30:     private void init() { | >>>   31:     	customerAccessori = (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy""); |       32:     } |       33: "
EJB_PROXY_USAGE,BlueLifeLogger,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,128,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","BluelifeLogger bluelifeLogger = (BluelifeLogger) FactoryBean.getBean(""BlueLifeLogger"");","     126: 	@PostConstruct |      127:     private void init() { | >>>  128:     	BluelifeLogger bluelifeLogger = (BluelifeLogger) FactoryBean.getBean(""BlueLifeLogger""); |      129:     	log = bluelifeLogger.getLogger(RestSrvConstants.POLIZZA_API); |      130:     	polizza = (PolizzaInterface) FactoryBean.getBean(""Polizza"");"
EJB_PROXY_USAGE,Polizza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,130,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizza = (PolizzaInterface) FactoryBean.getBean(""Polizza"");","     128:     	BluelifeLogger bluelifeLogger = (BluelifeLogger) FactoryBean.getBean(""BlueLifeLogger""); |      129:     	log = bluelifeLogger.getLogger(RestSrvConstants.POLIZZA_API); | >>>  130:     	polizza = (PolizzaInterface) FactoryBean.getBean(""Polizza""); |      131:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |      132:     	polizzaInterface = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");"
EJB_PROXY_USAGE,Prodotto,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,131,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto"");","     129:     	log = bluelifeLogger.getLogger(RestSrvConstants.POLIZZA_API); |      130:     	polizza = (PolizzaInterface) FactoryBean.getBean(""Polizza""); | >>>  131:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); |      132:     	polizzaInterface = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      133:     }"
EJB_PROXY_USAGE,PolizzaPolizzaProxy,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,132,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","polizzaInterface = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy"");","     130:     	polizza = (PolizzaInterface) FactoryBean.getBean(""Polizza""); |      131:     	prodotto = (ProdottoInterface) FactoryBean.getBean(""Prodotto""); | >>>  132:     	polizzaInterface = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaProxy""); |      133:     } |      134: "
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/helper/RibilanciamentoFondiHelper.java,74,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface domini = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","      72: 	public static Date calcolaDataEffetto(Date dataRichiesta) throws Exception { |       73:  | >>>   74: 		DominiInterface domini = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |       75: 		Locale locale = Locale.ITALIAN; // Le DATE sulla TSTAB088 sono in formato ITALIANO. |       76: 		Date dataEffetto = DateUtils.getDate(""31/12/9999"", locale); // Data Massima"
EJB_PROXY_USAGE,RibilanciamentoFondiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/fondo/ribilanciamento/helper/RibilanciamentoFondiHelper.java,182,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiBusinessDelegate"");","     180:  |      181: 	protected static RibilanciamentoFondiInterface getRibilanciamentoFondiDelegate() { | >>>  182: 	 	return (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiBusinessDelegate""); |      183: 	} |      184: }"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java,38,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface domini = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","      36: 		String segregazione = cmpExt; |       37:  | >>>   38: 		DominiInterface domini = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |       39: 		DominiCorrelatiInfo dci = new DominiCorrelatiInfo(); |       40: 		dci.setSiglaVersione(SrvConstants.ZURI);"
EJB_PROXY_USAGE,AbilitazioneUtenteBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java,65,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteBusinessDelegate"");","      63: 		AbilitazioneUtenteInterface abilitazioneUtenteInterface = null; |       64: 		if (SrvConstants.WAR.equals(ambito)) { | >>>   65: 			abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteBusinessDelegate""); |       66: 		} else { |       67: 			abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteImpl"");"
EJB_PROXY_USAGE,AbilitazioneUtenteImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/abilitazione/utility/AbilitazioneUtility.java,67,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteImpl"");","      65: 			abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteBusinessDelegate""); |       66: 		} else { | >>>   67: 			abilitazioneUtenteInterface = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteImpl""); |       68: 		} |       69: 		UtenteLoginInfoResponse utenteLoginInfoResponse = abilitazioneUtenteInterface.getUtenteInfoEsteso(utenteInfoReq);"
EJB_PROXY_USAGE,DominiProxy,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java,16,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DominiInterface) FactoryBean.getBean(""DominiProxy"");","      14:  |       15: 	protected DominiInterface getDominiInterface() { | >>>   16: 		return (DominiInterface) FactoryBean.getBean(""DominiProxy""); |       17: 	} |       18: "
EJB_PROXY_USAGE,CustomerProxy,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerInterface) FactoryBean.getBean(""CustomerProxy"");","      18:  |       19: 	protected CustomerInterface getCustomerInterface() { | >>>   20: 		return (CustomerInterface) FactoryBean.getBean(""CustomerProxy""); |       21: 	} |       22: "
EJB_PROXY_USAGE,AnagraficaAnagraficaProxy,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java,24,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy"");","      22:  |       23: 	protected AnagraficaAnagraficaInterface getAnagraficaInterface() { | >>>   24: 		return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaProxy""); |       25: 	} |       26: "
EJB_PROXY_USAGE,CustomerAccessoriProxy,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromProxy.java,28,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy"");","      26:  |       27: 	protected CustomerAccessoriInterface getCustomerAccessoriInterface() { | >>>   28: 		return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy""); |       29: 	} |       30: }"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java,14,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","      12:  |       13: 	protected DominiInterface getDominiInterface() { | >>>   14: 		return (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |       15: 	} |       16: "
EJB_PROXY_USAGE,CustomerBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java,18,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate"");","      16:  |       17: 	protected CustomerInterface getCustomerInterface() { | >>>   18: 		return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate""); |       19: 	} |       20: "
EJB_PROXY_USAGE,AnagraficaAnagraficaBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate"");","      20:  |       21: 	protected AnagraficaAnagraficaInterface getAnagraficaInterface() { | >>>   22: 		return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate""); |       23: 	} |       24: "
EJB_PROXY_USAGE,CustomerAccessoriBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitor.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriBusinessDelegate"");","      24:  |       25: 	protected CustomerAccessoriInterface getCustomerAccessoriInterface() { | >>>   26: 		return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriBusinessDelegate""); |       27: 	} |       28: }"
EJB_PROXY_USAGE,DominiImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java,16,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DominiInterface) FactoryBean.getBean(""DominiImpl"");","      14:  |       15: 	protected DominiInterface getDominiInterface() { | >>>   16: 		return (DominiInterface) FactoryBean.getBean(""DominiImpl""); |       17: 	} |       18: "
EJB_PROXY_USAGE,CustomerImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerInterface) FactoryBean.getBean(""CustomerImpl"");","      18:  |       19: 	protected CustomerInterface getCustomerInterface() { | >>>   20: 		return (CustomerInterface) FactoryBean.getBean(""CustomerImpl""); |       21: 	} |       22: "
EJB_PROXY_USAGE,AnagraficaAnagraficaImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java,24,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaImpl"");","      22:  |       23: 	protected AnagraficaAnagraficaInterface getAnagraficaInterface() { | >>>   24: 		return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaImpl""); |       25: 	} |       26: "
EJB_PROXY_USAGE,CustomerAccessoriImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/continuous_monitoring/CmMonitorFromImpl.java,28,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriImpl"");","      26:  |       27: 	protected CustomerAccessoriInterface getCustomerAccessoriInterface() { | >>>   28: 		return (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriImpl""); |       29: 	} |       30: "
EJB_PROXY_USAGE,CustomerBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagraficaRapporto/helpers/AnagraficaRapportoHelper.java,3020,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate"");","    3018:  |     3019: 	private static CustomerInterface getCustomerBusinessDelegate() { | >>> 3020: 		return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate""); |     3021: 	} |     3022: "
EJB_PROXY_USAGE,EleCodeBarcodeBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/elecode/barcode/types/BarcodeHelper.java,492,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","EleCodeBarcodeInterface eleCodeBarcodeInterface = (EleCodeBarcodeInterface) FactoryBean.getBean(""EleCodeBarcodeBusinessDelegate"");","     490: 			eRequest.setInfoEncode(infoEncode); |      491: 			System.out.println(""###Info to Encode: #"" + infoToEncode + ""#""); | >>>  492: 			EleCodeBarcodeInterface eleCodeBarcodeInterface = (EleCodeBarcodeInterface) FactoryBean.getBean(""EleCodeBarcodeBusinessDelegate""); |      493: 			EncodeResponse eResponse = eleCodeBarcodeInterface.encode(eRequest); |      494: 			nomeFile = eResponse.getNomeFile();"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java,160,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     158:  |      159: 		try { | >>>  160: 			DominiInterface bd = (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      161: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |      162: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java,210,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     208:  |      209: 		try { | >>>  210: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      211: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |      212: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,AnagraficaAnagraficaBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java,346,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","AnagraficaAnagraficaInterface anagraficaImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate"");","     344: 						controlloIbanRequest.setCodiceIban(iban); |      345: 						try { | >>>  346: 							AnagraficaAnagraficaInterface anagraficaImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate""); |      347: 							ControlloIbanResponse ibanResponse = anagraficaImpl.controlloIban(controlloIbanRequest); |      348: 							if (!ibanResponse.isIbanValido()) {"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java,491,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     489: 		di.setMapValori(WsdlMapUtils.valorizzaWsdlMapFor(valoriInfoMap)); |      490: 		dreq.setRequestInfo(di); | >>>  491: 		DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      492: 		DominiCorrelatiResponse dres = bd.searchDominiCorrelatati(dreq); |      493: "
EJB_PROXY_USAGE,DominiImpl,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/customer/util/CustomerUtils.java,988,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface impl =(DominiInterface) FactoryBean.getBean(""DominiImpl"");","     986: 		dominio.setCodiceRegola(SrvConstants.LAST_DT_ULTVAR); |      987: 		request.setRequestInfo(dominio); | >>>  988: 		DominiInterface impl =(DominiInterface) FactoryBean.getBean(""DominiImpl""); |      989: 		try { |      990: 			response = impl.searchDominiCorrelatati(request);"
EJB_PROXY_USAGE,DominiBusinessDelegate,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/batch/stampe/helpers/StampeBatchHelpers.java,420,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiDao = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","     418:  |      419: 	private ElementoDominiCorrelatiInfo getRegolaFromTSTAB988(String proxy, String regola, String myFunzionalita, String unCodProd) throws PolicyFault, ServiceFault { | >>>  420: 		DominiInterface dominiDao = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |      421: 		DominiCorrelatiRequest reqDaRegola = new DominiCorrelatiRequest(); |      422: 		DominiCorrelatiInfo myDomini= new DominiCorrelatiInfo();"
EJB_PROXY_USAGE,SyncroCustomerImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/syncrocustomer/service/SyncroCustomerInterfaceBindingImpl.java,25,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (SyncroCustomerInterface) FactoryBean.getBean(""SyncroCustomerImpl"");","      23: 	@PostConstruct |       24: 	private void init() { | >>>   25: 		serviceImpl = (SyncroCustomerInterface) FactoryBean.getBean(""SyncroCustomerImpl""); |       26: 	} |       27: "
EJB_PROXY_USAGE,ZbReteImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/zbank/zbrete/service/ZbReteInterfaceBindingImpl.java,44,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ZbReteInterface) FactoryBean.getBean(""ZbReteImpl"");","      42: 	@PostConstruct |       43: 	private void init() { | >>>   44: 		serviceImpl = (ZbReteInterface) FactoryBean.getBean(""ZbReteImpl""); |       45: 	} |       46: "
EJB_PROXY_USAGE,SyncroPolicyImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/syncropolicy/service/SyncroPolicyInterfaceBindingImpl.java,30,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyImpl"");","      28: 	private void init() { |       29: //		serviceImpl = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyImpl_1""); | >>>   30: 		serviceImpl = (SyncroPolicyInterface) FactoryBean.getBean(""SyncroPolicyImpl""); |       31: 	} |       32: "
EJB_PROXY_USAGE,PaperlessImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elis/paperless/service/PaperlessInterfaceBindingImpl.java,25,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PaperlessInterface) FactoryBean.getBean(""PaperlessImpl"");","      23: 	@PostConstruct |       24: 	private void init() { | >>>   25: 		serviceImpl = (PaperlessInterface) FactoryBean.getBean(""PaperlessImpl""); |       26: 	} |       27: "
EJB_PROXY_USAGE,ProdottoProdottoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/prodotto/service/ProdottoProdottoInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,ProdottoTariffaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/tariffa/service/ProdottoTariffaInterfaceBindingImpl.java,49,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ProdottoTariffaInterface) FactoryBean.getBean(""ProdottoTariffaImpl"");","      47: 	@PostConstruct |       48: 	private void init() { | >>>   49:   		serviceImpl = (ProdottoTariffaInterface) FactoryBean.getBean(""ProdottoTariffaImpl""); |       50: 	} |       51: "
EJB_PROXY_USAGE,ProdottoFormulaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/formula/service/ProdottoFormulaInterfaceBindingImpl.java,37,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ProdottoFormulaInterface) FactoryBean.getBean(""ProdottoFormulaImpl"");","      35: 	private void init() { |       36:  | >>>   37:   		serviceImpl = (ProdottoFormulaInterface) FactoryBean.getBean(""ProdottoFormulaImpl""); |       38: 	} |       39: "
EJB_PROXY_USAGE,ProdottoVettoreImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prodotto/vettore/service/ProdottoVettoreInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ProdottoVettoreInterface) FactoryBean.getBean(""ProdottoVettoreImpl"");","      19: 	private void init() { |       20:  | >>>   21:   		serviceImpl = (ProdottoVettoreInterface) FactoryBean.getBean(""ProdottoVettoreImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,TrascodificaDominiEasyImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/easy/trascodifica/service/TrascodificaDominiEasyInterfaceBindingImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (TrascodificaDominiEasyInterface) FactoryBean.getBean(""TrascodificaDominiEasyImpl"");","      18: 	@PostConstruct |       19: 	private void init() { | >>>   20:   		serviceImpl = (TrascodificaDominiEasyInterface) FactoryBean.getBean(""TrascodificaDominiEasyImpl""); |       21: 	} |       22: "
EJB_PROXY_USAGE,AnagrafeEasyImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/easy/anagrafe/service/AnagrafeEasyInterfaceBindingImpl.java,44,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AnagrafeEasyInterface) FactoryBean.getBean(""AnagrafeEasyImpl"");","      42: 	@PostConstruct |       43: 	private void init() { | >>>   44:   		serviceImpl = (AnagrafeEasyInterface) FactoryBean.getBean(""AnagrafeEasyImpl""); |       45: 	} |       46: "
EJB_PROXY_USAGE,DominiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/domini/naw/service/DominiInterfaceBindingImpl.java,61,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (DominiInterface) FactoryBean.getBean(""DominiImpl"");","      59: 	private void init() { |       60:  | >>>   61:   		serviceImpl = (DominiInterface) FactoryBean.getBean(""DominiImpl""); |       62: 	} |       63: "
EJB_PROXY_USAGE,ParametriImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/parametri/ejb/ParametriServiceEJB.java,30,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ParametriServiceInterface) FactoryBean.getBean(""ParametriImpl"");","      28: 	private void init() { |       29:  | >>>   30:   		serviceImpl = (ParametriServiceInterface) FactoryBean.getBean(""ParametriImpl""); |       31: 	} |       32: "
EJB_PROXY_USAGE,OrchestratoreConfigurazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/orchestratore/configurazione/service/OrchestratoreConfigurazioneInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (OrchestratoreConfigurazioneInterface) FactoryBean.getBean(""OrchestratoreConfigurazioneImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (OrchestratoreConfigurazioneInterface) FactoryBean.getBean(""OrchestratoreConfigurazioneImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,ProfiloInvestimentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/profiloinvestimento/service/ProfiloInvestimentoInterfaceBindingImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ProfiloInvestimentoInterface) FactoryBean.getBean(""ProfiloInvestimentoImpl"");","      18: 	@PostConstruct |       19: 	private void init() { | >>>   20: 		serviceImpl = (ProfiloInvestimentoInterface) FactoryBean.getBean(""ProfiloInvestimentoImpl""); |       21: 	} |       22:     public DeleteProfiloInvestimentoResponse deleteProfiloInvestimento(DeleteProfiloInvestimentoRequest request) throws PolicyFault, ServiceFault {"
EJB_PROXY_USAGE,PianiSpostamentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/pianispostamento/service/PianiSpostamentoInterfaceBindingImpl.java,30,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PianiSpostamentoInterface) FactoryBean.getBean(""PianiSpostamentoImpl"");","      28: 	@PostConstruct |       29: 	private void init() { | >>>   30: 		serviceImpl = (PianiSpostamentoInterface) FactoryBean.getBean(""PianiSpostamentoImpl""); |       31: 	} |       32: "
EJB_PROXY_USAGE,FondoUtilitaRetrocesseImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/utilitaretrocesse/service/FondoUtilitaRetrocesseInterfaceBindingImpl.java,27,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FondoUtilitaRetrocesseInterface) FactoryBean.getBean(""FondoUtilitaRetrocesseImpl"");","      25: 	@PostConstruct |       26: 	private void init() { | >>>   27:   		serviceImpl = (FondoUtilitaRetrocesseInterface) FactoryBean.getBean(""FondoUtilitaRetrocesseImpl""); |       28: 	} |       29: "
EJB_PROXY_USAGE,RaggruppamentoFondiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/raggruppamento/service/RaggruppamentoFondiInterfaceBindingImpl.java,61,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RaggruppamentoFondiInterface) FactoryBean.getBean(""RaggruppamentoFondiImpl"");","      59: 	@PostConstruct |       60: 	private void init() { | >>>   61: 		serviceImpl = (RaggruppamentoFondiInterface) FactoryBean.getBean(""RaggruppamentoFondiImpl""); |       62: 	} |       63: "
EJB_PROXY_USAGE,CorporateEventsImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/corporateevents/service/CorporateEventsInterfaceBindingImpl.java,51,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CorporateEventsInterface) FactoryBean.getBean(""CorporateEventsImpl"");","      49: 	@PostConstruct |       50: 	private void init() { | >>>   51: 		serviceImpl = (CorporateEventsInterface) FactoryBean.getBean(""CorporateEventsImpl""); |       52: 	} |       53: "
EJB_PROXY_USAGE,RibilanciamentoFondiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/ribilanciamento/service/RibilanciamentoFondiInterfaceBindingImpl.java,94,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiImpl"");","      92: 	@PostConstruct |       93: 	private void init() { | >>>   94: 		serviceImpl = (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiImpl""); |       95: 	} |       96: "
EJB_PROXY_USAGE,GestioneSeparataImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/gestioneseparata/service/GestioneSeparataInterfaceBindingImpl.java,42,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (GestioneSeparataInterface) FactoryBean.getBean(""GestioneSeparataImpl"");","      40: 	@PostConstruct |       41: 	private void init() { | >>>   42: 		serviceImpl = (GestioneSeparataInterface) FactoryBean.getBean(""GestioneSeparataImpl""); |       43: 	} |       44:     public SelectGestioneSeparataResponse selectGestioneSeparata(SelectGestioneSeparataRequest request) throws PolicyFault, ServiceFault {"
EJB_PROXY_USAGE,FondoTrasferimentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/fondo/trasferimento/service/FondoTrasferimentoInterfaceBindingImpl.java,43,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoImpl"");","      41: 	@PostConstruct |       42: 	private void init() { | >>>   43: 		serviceImpl = (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoImpl""); |       44: 	} |       45: "
EJB_PROXY_USAGE,ModelloMatematicoFormulaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/modelloMatematico/formula/service/ModelloMatematicoFormulaInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ModelloMatematicoFormulaInterface) FactoryBean.getBean(""ModelloMatematicoFormulaImpl"");","      30: 	@PostConstruct |       31: 	private void init() { | >>>   32:   		serviceImpl = (ModelloMatematicoFormulaInterface) FactoryBean.getBean(""ModelloMatematicoFormulaImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,StampaEsternaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/stampa/esterna/service/StampaEsternaInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (StampaEsternaInterface) FactoryBean.getBean(""StampaEsternaImpl"");","      20: 	@PostConstruct |       21: 	private void init() { | >>>   22:   		serviceImpl = (StampaEsternaInterface) FactoryBean.getBean(""StampaEsternaImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,StampaVariabileImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/stampa/variabile/service/StampaVariabileInterfaceBindingImpl.java,25,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (StampaVariabileInterface) FactoryBean.getBean(""StampaVariabileImpl"");","      23: 	private void init() { |       24:  | >>>   25:   		serviceImpl = (StampaVariabileInterface) FactoryBean.getBean(""StampaVariabileImpl""); |       26: 	} |       27: "
EJB_PROXY_USAGE,AbilitazioneAzioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/abilitazione/azione/service/AbilitazioneAzioneInterfaceBindingImpl.java,48,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneImpl"");","      46: 	@PostConstruct |       47: 	private void init() { | >>>   48:   		serviceImpl = (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneImpl""); |       49: 	} |       50: "
EJB_PROXY_USAGE,AbilitazioneUtenteImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/abilitazione/utente/service/AbilitazioneUtenteInterfaceBindingImpl.java,64,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteImpl"");","      62: 	@PostConstruct |       63: 	private void init() { | >>>   64: 		serviceImpl = (AbilitazioneUtenteInterface) FactoryBean.getBean(""AbilitazioneUtenteImpl""); |       65: 	} |       66: "
EJB_PROXY_USAGE,MonitorGidImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/monitor/gid/service/MonitorGidInterfaceBindingImpl.java,23,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (MonitorGidInterface) FactoryBean.getBean(""MonitorGidImpl"");","      21: 	private void init() { |       22:  | >>>   23:   		serviceImpl = (MonitorGidInterface) FactoryBean.getBean(""MonitorGidImpl""); |       24: 	} |       25: "
EJB_PROXY_USAGE,NumeratoriImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/numeratori/numeratori/service/NumeratoriInterfaceBindingImpl.java,30,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (NumeratoriInterface) FactoryBean.getBean(""NumeratoriImpl"");","      28: 	private void init() { |       29:  | >>>   30:   		serviceImpl = (NumeratoriInterface) FactoryBean.getBean(""NumeratoriImpl""); |       31: 	} |       32: "
EJB_PROXY_USAGE,CodaMqImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/osb/codamq/service/CodaMqInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CodaMqInterface) FactoryBean.getBean(""CodaMqImpl"");","      19: 	private void init() { |       20:  | >>>   21:   		serviceImpl = (CodaMqInterface) FactoryBean.getBean(""CodaMqImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,PropostaPropostaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/proposta/proposta/service/PropostaPropostaInterfaceBindingImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaImpl"");","      18: 	private void init() { |       19:  | >>>   20:   		serviceImpl = (PropostaPropostaInterface) FactoryBean.getBean(""PropostaPropostaImpl""); |       21: 	} |       22: "
EJB_PROXY_USAGE,UnderwritingImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/proposta/underwriting/service/UnderwritingInterfaceBindingImpl.java,29,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (UnderwritingInterface) FactoryBean.getBean(""UnderwritingImpl"");","      27: 	@PostConstruct |       28: 	private void init() { | >>>   29:   		serviceImpl = (UnderwritingInterface) FactoryBean.getBean(""UnderwritingImpl""); |       30: 	} |       31: "
EJB_PROXY_USAGE,PolizzaAccountingImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/accounting/service/PolizzaAccountingInterfaceBindingImpl.java,31,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaAccountingInterface) FactoryBean.getBean(""PolizzaAccountingImpl"");","      29: 	private void init() { |       30:  | >>>   31:   		serviceImpl = (PolizzaAccountingInterface) FactoryBean.getBean(""PolizzaAccountingImpl""); |       32: 	} |       33: "
EJB_PROXY_USAGE,PolizzaMovimentiContabiliImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/movimenticontabili/service/PolizzaMovimentiContabiliInterfaceBindingImpl.java,38,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaMovimentiContabiliInterface) FactoryBean.getBean(""PolizzaMovimentiContabiliImpl"");","      36: 	@PostConstruct |       37: 	public void init() { | >>>   38: 		serviceImpl = (PolizzaMovimentiContabiliInterface) FactoryBean.getBean(""PolizzaMovimentiContabiliImpl""); |       39: 	} |       40: "
EJB_PROXY_USAGE,PolizzaLiquidazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/liquidazione/service/PolizzaLiquidazioneInterfaceBindingImpl.java,67,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaLiquidazioneInterface) FactoryBean.getBean(""PolizzaLiquidazioneImpl"");","      65: 	private void init() { |       66:  | >>>   67:   		serviceImpl = (PolizzaLiquidazioneInterface) FactoryBean.getBean(""PolizzaLiquidazioneImpl""); |       68:   	} |       69:     public LiquidazioneResponse elencoLiquidazioni(LiquidazioneRequest request) throws PolicyFault, ServiceFault {"
EJB_PROXY_USAGE,PolizzaCambioRuoloImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/cambioruolo/service/PolizzaCambioRuoloInterfaceBindingImpl.java,28,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaCambioRuoloInterface) FactoryBean.getBean(""PolizzaCambioRuoloImpl"");","      26: 	private void init() { |       27:  | >>>   28:   		serviceImpl = (PolizzaCambioRuoloInterface) FactoryBean.getBean(""PolizzaCambioRuoloImpl""); |       29: 	} |       30: "
EJB_PROXY_USAGE,PolizzaVariazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/variazione/service/VariazioneInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (VariazioneInterface) FactoryBean.getBean(""PolizzaVariazioneImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (VariazioneInterface) FactoryBean.getBean(""PolizzaVariazioneImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,BonificaMultiInvestImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/bonificamulti/service/BonificaMultiInvestInterfaceBindingImpl.java,53,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (BonificaMultiInvestInterface) FactoryBean.getBean(""BonificaMultiInvestImpl"");","      51: 	private void init() { |       52:  | >>>   53:   		serviceImpl = (BonificaMultiInvestInterface) FactoryBean.getBean(""BonificaMultiInvestImpl""); |       54: 	} |       55: "
EJB_PROXY_USAGE,PolizzaOpzioniContrattualiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/opzionicontrattuali/service/PolizzaOpzioniContrattualiInterfaceBindingImpl.java,27,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiImpl"");","      25: 	@PostConstruct |       26: 	private void init() { | >>>   27:   		serviceImpl = (PolizzaOpzioniContrattualiInterface) FactoryBean.getBean(""PolizzaOpzioniContrattualiImpl""); |       28: 	} |       29: "
EJB_PROXY_USAGE,PolizzaPolizzaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/polizza/service/PolizzaPolizzaInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaImpl"");","      19: 	@PostConstruct |       20: 	private void init() { | >>>   21:   		serviceImpl = (PolizzaPolizzaInterface) FactoryBean.getBean(""PolizzaPolizzaImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,PolizzaCoassicurazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/coassicurazione/service/PolizzaCoassicurazioneInterfaceBindingImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaCoassicurazioneInterface) FactoryBean.getBean(""PolizzaCoassicurazioneImpl"");","      18: 	@PostConstruct |       19: 	private void init() { | >>>   20:   		serviceImpl = (PolizzaCoassicurazioneInterface) FactoryBean.getBean(""PolizzaCoassicurazioneImpl""); |       21: 	} |       22: "
EJB_PROXY_USAGE,PolizzaTassazioneDatiCristallizzatiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/tassazione/daticristallizzati/service/PolizzaTassazioneDatiCristallizzatiInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaTassazioneDatiCristallizzatiInterface) FactoryBean.getBean(""PolizzaTassazioneDatiCristallizzatiImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (PolizzaTassazioneDatiCristallizzatiInterface) FactoryBean.getBean(""PolizzaTassazioneDatiCristallizzatiImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,RelazioneFigureRapportoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/relazionefigurerapporto/service/RelazioneFigureRapportoInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RelazioneFigureRapportoInterface) FactoryBean.getBean(""RelazioneFigureRapportoImpl"");","      30: 	private void init() { |       31:  | >>>   32:   		serviceImpl = (RelazioneFigureRapportoInterface) FactoryBean.getBean(""RelazioneFigureRapportoImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,PolizzaSwitchImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/_switch/service/PolizzaSwitchInterfaceBindingImpl.java,43,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaSwitchInterface) FactoryBean.getBean(""PolizzaSwitchImpl"");","      41: 	private void init() { |       42:  | >>>   43:   		serviceImpl = (PolizzaSwitchInterface) FactoryBean.getBean(""PolizzaSwitchImpl""); |       44: 	} |       45: "
EJB_PROXY_USAGE,VariazioneRischioAssicuratoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/variazionerischioassicurato/service/VariazioneRischioAssicuratoInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (VariazioneRischioAssicuratoInterface) FactoryBean.getBean(""VariazioneRischioAssicuratoImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (VariazioneRischioAssicuratoInterface) FactoryBean.getBean(""VariazioneRischioAssicuratoImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,PolizzaStorniImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/storni/service/PolizzaStorniInterfaceBindingImpl.java,64,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniImpl"");","      62: 	private void init() { |       63:  | >>>   64: 		serviceImpl = (PolizzaStorniInterface) FactoryBean.getBean(""PolizzaStorniImpl""); |       65: 	} |       66: "
EJB_PROXY_USAGE,PolizzaMovimentazioniULImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/movimentazioniul/service/PolizzaMovimentazioniULInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaMovimentazioniULInterface) FactoryBean.getBean(""PolizzaMovimentazioniULImpl"");","      24: 	@PostConstruct |       25: 	private void init() { | >>>   26:   		serviceImpl = (PolizzaMovimentazioniULInterface) FactoryBean.getBean(""PolizzaMovimentazioniULImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,ParametriCollettivaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/parametricollettiva/service/ParametriCollettivaInterfaceBindingImpl.java,44,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ParametriCollettivaInterface) FactoryBean.getBean(""ParametriCollettivaImpl"");","      42: 	private void init() { |       43:  | >>>   44:   		serviceImpl = (ParametriCollettivaInterface) FactoryBean.getBean(""ParametriCollettivaImpl""); |       45: 	} |       46: "
EJB_PROXY_USAGE,PolizzaCollettoreImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/collettore/service/PolizzaCollettoreInterfaceBindingImpl.java,40,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaCollettoreInterface) FactoryBean.getBean(""PolizzaCollettoreImpl"");","      38: 	private void init() { |       39:  | >>>   40:   		serviceImpl = (PolizzaCollettoreInterface) FactoryBean.getBean(""PolizzaCollettoreImpl""); |       41: 	} |       42: "
EJB_PROXY_USAGE,OpzioniConversioneODifferimentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/opzioniconversioneodifferimento/service/OpzioniConversioneODifferimentoInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (OpzioniConversioneODifferimentoInterface) FactoryBean.getBean(""OpzioniConversioneODifferimentoImpl"");","      24: 	private void init() { |       25:  | >>>   26:   		serviceImpl = (OpzioniConversioneODifferimentoInterface) FactoryBean.getBean(""OpzioniConversioneODifferimentoImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,PolizzaCostiAnnuiRendicontatiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/costiannuirendicontati/service/PolizzaCostiAnnuiRendicontatiInterfaceBindingImpl.java,23,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaCostiAnnuiRendicontatiInterface) FactoryBean.getBean(""PolizzaCostiAnnuiRendicontatiImpl"");","      21: 	@PostConstruct |       22: 	public void init() { | >>>   23: 		serviceImpl = (PolizzaCostiAnnuiRendicontatiInterface) FactoryBean.getBean(""PolizzaCostiAnnuiRendicontatiImpl""); |       24: 	} |       25: "
EJB_PROXY_USAGE,PolizzaGestioneRidImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/gestionerid/service/PolizzaGestioneRidInterfaceBindingImpl.java,57,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaGestioneRidInterface) FactoryBean.getBean(""PolizzaGestioneRidImpl"");","      55: 	private void init() { |       56:  | >>>   57:   		serviceImpl = (PolizzaGestioneRidInterface) FactoryBean.getBean(""PolizzaGestioneRidImpl""); |       58: 	} |       59: "
EJB_PROXY_USAGE,PolizzaCambioFrazionamentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/cambiofrazionamento/service/PolizzaCambioFrazionamentoInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaCambioFrazionamentoInterface) FactoryBean.getBean(""PolizzaCambioFrazionamentoImpl"");","      19: 	private void init() { |       20:  | >>>   21:   		serviceImpl = (PolizzaCambioFrazionamentoInterface) FactoryBean.getBean(""PolizzaCambioFrazionamentoImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,PolizzaMgmFeeImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/mgmfee/service/PolizzaMgmFeeInterfaceBindingImpl.java,34,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaMgmFeeInterface) FactoryBean.getBean(""PolizzaMgmFeeImpl"");","      32: 	@PostConstruct |       33: 	private void init() { | >>>   34:   		serviceImpl = (PolizzaMgmFeeInterface) FactoryBean.getBean(""PolizzaMgmFeeImpl""); |       35: 	} |       36: "
EJB_PROXY_USAGE,EventiPolizzaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/eventi/service/EventiPolizzaInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (EventiPolizzaInterface) FactoryBean.getBean(""EventiPolizzaImpl"");","      30: 	@PostConstruct |       31: 	private void init() { | >>>   32:   		serviceImpl = (EventiPolizzaInterface) FactoryBean.getBean(""EventiPolizzaImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,PremiumTranchingImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/premiumtranching/service/PremiumTranchingInterfaceBindingImpl.java,24,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PremiumTranchingInterface) FactoryBean.getBean(""PremiumTranchingImpl"");","      22: 	@PostConstruct |       23: 	private void init() { | >>>   24:   		serviceImpl = (PremiumTranchingInterface) FactoryBean.getBean(""PremiumTranchingImpl""); |       25: 	} |       26: "
EJB_PROXY_USAGE,PercipientiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/percipienti/service/PercipientiInterfaceBindingImpl.java,28,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PercipientiInterface) FactoryBean.getBean(""PercipientiImpl"");","      26: 	private void init() { |       27:  | >>>   28:   		serviceImpl = (PercipientiInterface) FactoryBean.getBean(""PercipientiImpl""); |       29: 	} |       30: "
EJB_PROXY_USAGE,PolizzaRiattivazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/riattivazione/service/PolizzaRiattivazioneInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaRiattivazioneInterface) FactoryBean.getBean(""PolizzaRiattivazioneImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (PolizzaRiattivazioneInterface) FactoryBean.getBean(""PolizzaRiattivazioneImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,PolizzaPosizioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/posizione/service/PolizzaPosizioneInterfaceBindingImpl.java,105,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneImpl"");","     103: 	private void init() { |      104:  | >>>  105:   		serviceImpl = (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneImpl""); |      106: 	} |      107: "
EJB_PROXY_USAGE,RiallocazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/riallocazione/service/RiallocazioneInterfaceBindingImpl.java,48,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","riallocazioneImpl = (RiallocazioneInterface) FactoryBean.getBean(""RiallocazioneImpl"");","      46: 	private void init() { |       47:  | >>>   48: 		riallocazioneImpl = (RiallocazioneInterface) FactoryBean.getBean(""RiallocazioneImpl""); |       49: 	} |       50: "
EJB_PROXY_USAGE,PolizzaDurImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/dur/service/PolizzaDurInterfaceBindingImpl.java,40,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaDurInterface) FactoryBean.getBean(""PolizzaDurImpl"");","      38: 	private void init() { |       39:  | >>>   40:   		serviceImpl = (PolizzaDurInterface) FactoryBean.getBean(""PolizzaDurImpl""); |       41: 	} |       42: "
EJB_PROXY_USAGE,PolizzaTrasferimentoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/trasferimento/service/PolizzaTrasferimentoInterfaceBindingImpl.java,66,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaTrasferimentoInterface) FactoryBean.getBean(""PolizzaTrasferimentoImpl"");","      64: 	private void init() { |       65:  | >>>   66:   		serviceImpl = (PolizzaTrasferimentoInterface) FactoryBean.getBean(""PolizzaTrasferimentoImpl""); |       67: 	} |       68: "
EJB_PROXY_USAGE,PolizzaPianoVersamentiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/pianoversamenti/service/PolizzaPianoVersamentiInterfaceBindingImpl.java,72,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaPianoVersamentiInterface) FactoryBean.getBean(""PolizzaPianoVersamentiImpl"");","      70: 	@PostConstruct |       71: 	private void init() { | >>>   72:   		serviceImpl = (PolizzaPianoVersamentiInterface) FactoryBean.getBean(""PolizzaPianoVersamentiImpl""); |       73: 	} |       74: "
EJB_PROXY_USAGE,PolizzaLimitazioneOperativitaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/polizza/limitazioneoperativita/service/PolizzaLimitazioneOperativitaInterfaceBindingImpl.java,44,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PolizzaLimitazioneOperativitaInterface) FactoryBean.getBean(""PolizzaLimitazioneOperativitaImpl"");","      42: 	@PostConstruct |       43: 	private void init() { | >>>   44:   		serviceImpl = (PolizzaLimitazioneOperativitaInterface) FactoryBean.getBean(""PolizzaLimitazioneOperativitaImpl""); |       45: 	} |       46: "
EJB_PROXY_USAGE,GestioneEventiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/gestioneeventi/eventi/service/GestioneEventiInterfaceBindingImpl.java,54,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (GestioneEventiInterface) FactoryBean.getBean(""GestioneEventiImpl"");","      52: 	@PostConstruct |       53: 	private void init() { | >>>   54:   		serviceImpl = (GestioneEventiInterface) FactoryBean.getBean(""GestioneEventiImpl""); |       55: 	} |       56: "
EJB_PROXY_USAGE,BonificaAnagraficaAmletoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/bonifica/anagrafica/amleto/service/BonificaAnagraficaAmletoInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (BonificaAnagraficaAmletoInterface) FactoryBean.getBean(""BonificaAnagraficaAmletoImpl"");","      24: 	private void init() { |       25:  | >>>   26:   		serviceImpl = (BonificaAnagraficaAmletoInterface) FactoryBean.getBean(""BonificaAnagraficaAmletoImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,StrutturaReteImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/struttura/rete/service/StrutturaReteInterfaceBindingImpl.java,69,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteImpl"");","      67: 	private void init() { |       68:  | >>>   69:   		serviceImpl = (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteImpl""); |       70: 	} |       71: "
EJB_PROXY_USAGE,RemoteCommandImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/utils/remotecommand/service/RemoteCommandInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RemoteCommandInterface) FactoryBean.getBean(""RemoteCommandImpl"");","      19: 	@PostConstruct |       20: 	private void init() { | >>>   21:   		serviceImpl = (RemoteCommandInterface) FactoryBean.getBean(""RemoteCommandImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,CatalogoProdottiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/catalogoprodotti/catalogoprodotti/service/CatalogoProdottiInterfaceBindingImpl.java,31,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CatalogoProdottiInterface) FactoryBean.getBean(""CatalogoProdottiImpl"");","      29: 	@PostConstruct |       30: 	private void init() { | >>>   31:   		serviceImpl = (CatalogoProdottiInterface) FactoryBean.getBean(""CatalogoProdottiImpl""); |       32: 	} |       33: "
EJB_PROXY_USAGE,NawSyncroRelImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/syncro/nawsyncrorel/service/SyncroNawSyncroRelInterfaceBindingImpl.java,31,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (SyncroNawSyncroRelInterface) FactoryBean.getBean(""NawSyncroRelImpl"");","      29:     @PostConstruct |       30: 	private void init() { | >>>   31:   		serviceImpl = (SyncroNawSyncroRelInterface) FactoryBean.getBean(""NawSyncroRelImpl""); |       32: 	} |       33: "
EJB_PROXY_USAGE,AegisImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/aegis/service/AegisInterfaceBindingImpl.java,29,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AegisInterface) FactoryBean.getBean(""AegisImpl"");","      27: 	private void init() { |       28:  | >>>   29:   		serviceImpl = (AegisInterface) FactoryBean.getBean(""AegisImpl""); |       30: 	} |       31: "
EJB_PROXY_USAGE,GdprImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/gdpr/service/GdprInterfaceBindingImpl.java,24,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (GdprInterface) FactoryBean.getBean(""GdprImpl"");","      22: 	private void init() { |       23:  | >>>   24:   		serviceImpl = (GdprInterface) FactoryBean.getBean(""GdprImpl""); |       25: 	} |       26: "
EJB_PROXY_USAGE,FunctionGenericImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/function/generic/service/FunctionGenericInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FunctionGenericInterface) FactoryBean.getBean(""FunctionGenericImpl"");","      30: 	@PostConstruct |       31: 	private void init() { | >>>   32:   		serviceImpl = (FunctionGenericInterface) FactoryBean.getBean(""FunctionGenericImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,LocalitaStradarioImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/localita/stradario/service/LocalitaStradarioInterfaceBindingImpl.java,54,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (LocalitaStradarioInterface) FactoryBean.getBean(""LocalitaStradarioImpl"");","      52: 	@PostConstruct |       53: 	private void init() { | >>>   54:   		serviceImpl = (LocalitaStradarioInterface) FactoryBean.getBean(""LocalitaStradarioImpl""); |       55: 	} |       56: "
EJB_PROXY_USAGE,LocalitaViarioImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/localita/viario/service/LocalitaViarioInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (LocalitaViarioInterface) FactoryBean.getBean(""LocalitaViarioImpl"");","      24: 	@PostConstruct |       25: 	private void init() { | >>>   26:   		serviceImpl = (LocalitaViarioInterface) FactoryBean.getBean(""LocalitaViarioImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,AssociazioneCampagnaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/campagnaprovvigionale/associazione/service/AssociazioneCampagnaInterfaceBindingImpl.java,54,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AssociazioneCampagnaInterface) FactoryBean.getBean(""AssociazioneCampagnaImpl"");","      52: 	private void init() { |       53:  | >>>   54:   		serviceImpl = (AssociazioneCampagnaInterface) FactoryBean.getBean(""AssociazioneCampagnaImpl""); |       55: 	} |       56: "
EJB_PROXY_USAGE,CampagnaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/campagnaprovvigionale/campagna/service/CampagnaInterfaceBindingImpl.java,44,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CampagnaInterface) FactoryBean.getBean(""CampagnaImpl"");","      42: 	private void init() { |       43:  | >>>   44:   		serviceImpl = (CampagnaInterface) FactoryBean.getBean(""CampagnaImpl""); |       45: 	} |       46: "
EJB_PROXY_USAGE,ContiZPlatformImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/gestionetabelle/tabellevita/service/GestioneTabelleTabelleVitaInterfaceBindingImpl.java,31,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (GestioneTabelleTabelleVitaInterface) FactoryBean.getBean(""ContiZPlatformImpl"");","      29: 	@PostConstruct |       30: 	private void init() { | >>>   31: 		serviceImpl = (GestioneTabelleTabelleVitaInterface) FactoryBean.getBean(""ContiZPlatformImpl""); |       32: 	} |       33: "
EJB_PROXY_USAGE,EleCodeImgcodeImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elecode/imgcode/service/EleCodeImgcodeInterfaceBindingImpl.java,20,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (EleCodeImgcodeInterface) FactoryBean.getBean(""EleCodeImgcodeImpl"");","      18: 	@PostConstruct |       19: 	private void init() { | >>>   20: 		serviceImpl = (EleCodeImgcodeInterface) FactoryBean.getBean(""EleCodeImgcodeImpl""); |       21: 	} |       22: "
EJB_PROXY_USAGE,EleCodeBarcodeImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/elecode/barcode/service/EleCodeBarcodeInterfaceBindingImpl.java,24,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (EleCodeBarcodeInterface) FactoryBean.getBean(""EleCodeBarcodeImpl"");","      22: 	@PostConstruct |       23: 	private void init() { | >>>   24: 		serviceImpl = (EleCodeBarcodeInterface) FactoryBean.getBean(""EleCodeBarcodeImpl""); |       25: 	} |       26: "
EJB_PROXY_USAGE,DashboardDatamartImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/dashboard/datamart/service/DashboardDatamartInterfaceBindingImpl.java,95,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (DashboardDatamartInterface) FactoryBean.getBean(""DashboardDatamartImpl"");","      93: 	@PostConstruct |       94: 	private void init() { | >>>   95:   		serviceImpl = (DashboardDatamartInterface) FactoryBean.getBean(""DashboardDatamartImpl""); |       96: 	} |       97: "
EJB_PROXY_USAGE,DashboardEventiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/dashboard/eventi/service/DashboardEventiInterfaceBindingImpl.java,37,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (DashboardEventiInterface) FactoryBean.getBean(""DashboardEventiImpl"");","      35: 	@PostConstruct |       36: 	private void init() { | >>>   37:   		serviceImpl = (DashboardEventiInterface) FactoryBean.getBean(""DashboardEventiImpl""); |       38: 	} |       39: 	public InserisciEventiResponse inserisciEventi(InserisciEventiRequest request) throws PolicyFault, ServiceFault {"
EJB_PROXY_USAGE,CustomerAccessoriImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/customeraccessori/service/CustomerAccessoriInterfaceBindingImpl.java,35,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriImpl"");","      33: 	private void init() { |       34:  | >>>   35:   		serviceImpl = (CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriImpl""); |       36: 	} |       37: "
EJB_PROXY_USAGE,AnagraficaEsternaEasyImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/esterna/service/AnagraficaEsternaInterfaceBindingImpl.java,30,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AnagraficaEsternaInterface) FactoryBean.getBean(""AnagraficaEsternaEasyImpl"");","      28: 	private void init() { |       29:  | >>>   30:   		serviceImpl = (AnagraficaEsternaInterface) FactoryBean.getBean(""AnagraficaEsternaEasyImpl""); |       31: 	} |       32: "
EJB_PROXY_USAGE,AnagraficaAnagraficaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/AnagraficaAnagraficaInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaImpl"");","      24: 	private void init() { |       25:  | >>>   26:   		serviceImpl = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,PDNDImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/pdnd/service/PDNDInterfaceBindingImpl.java,46,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PDNDInterface) FactoryBean.getBean(""PDNDImpl"");","      44: 	@PostConstruct |       45: 	private void init() { | >>>   46:   		serviceImpl = (PDNDInterface) FactoryBean.getBean(""PDNDImpl""); |       47: 	} |       48: "
EJB_PROXY_USAGE,CustomerImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/anagrafica/customer/service/CustomerInterfaceBindingImpl.java,79,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CustomerInterface) FactoryBean.getBean(""CustomerImpl"");","      77: 	private void init() { |       78:  | >>>   79:   		serviceImpl = (CustomerInterface) FactoryBean.getBean(""CustomerImpl""); |       80: 	} |       81: "
EJB_PROXY_USAGE,AuditTracingImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/audit/tracing/service/AuditTracingInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (AuditTracingInterface) FactoryBean.getBean(""AuditTracingImpl"");","      20: 	private void init() { |       21:  | >>>   22:   		serviceImpl = (AuditTracingInterface) FactoryBean.getBean(""AuditTracingImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,RestrizioneAccessoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/restrizioneaccesso/service/RestrizioneAccessoInterfaceBindingImpl.java,38,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RestrizioneAccessoInterface) FactoryBean.getBean(""RestrizioneAccessoImpl"");","      36: 	private void init() { |       37:  | >>>   38:   		serviceImpl = (RestrizioneAccessoInterface) FactoryBean.getBean(""RestrizioneAccessoImpl""); |       39: 	} |       40: "
EJB_PROXY_USAGE,PortafoglioRapportoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/rapporto/service/PortafoglioRapportoInterfaceBindingImpl.java,83,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoImpl"");","      81: 	private void init() { |       82:  | >>>   83:   		serviceImpl = (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoImpl""); |       84: 	} |       85: "
EJB_PROXY_USAGE,PortafoglioBonificiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/bonifici/service/PortafoglioBonificiInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PortafoglioBonificiInterface) FactoryBean.getBean(""PortafoglioBonificiImpl"");","      30: 	private void init() { |       31:  | >>>   32:   		serviceImpl = (PortafoglioBonificiInterface) FactoryBean.getBean(""PortafoglioBonificiImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,PortafoglioEstrazionePolizzaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/portafoglio/estrazione/service/PortafoglioEstrazionePolizzaInterfaceBindingImpl.java,34,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PortafoglioEstrazionePolizzaInterface) FactoryBean.getBean(""PortafoglioEstrazionePolizzaImpl"");","      32: 	private void init() { |       33:  | >>>   34:   		serviceImpl = (PortafoglioEstrazionePolizzaInterface) FactoryBean.getBean(""PortafoglioEstrazionePolizzaImpl""); |       35: 	} |       36: "
EJB_PROXY_USAGE,TabellaTracciatoImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/tabella/tracciato/service/TabellaTracciatoInterfaceBindingImpl.java,19,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoImpl"");","      17: 	@PostConstruct |       18: 	private void init() { | >>>   19:   		serviceImpl = (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoImpl""); |       20: 	} |       21: "
EJB_PROXY_USAGE,QuestionarioStrutturaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/questionario/struttura/service/QuestionarioStrutturaInterfaceBindingImpl.java,58,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (QuestionarioStrutturaInterface) FactoryBean.getBean(""QuestionarioStrutturaImpl"");","      56: 	@PostConstruct |       57: 	private void init() { | >>>   58:   		serviceImpl = (QuestionarioStrutturaInterface) FactoryBean.getBean(""QuestionarioStrutturaImpl""); |       59: 	} |       60: "
EJB_PROXY_USAGE,QuestionarioAuraImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/questionario/aura/service/QuestionarioAuraInterfaceBindingImpl.java,38,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (QuestionarioAuraInterface) FactoryBean.getBean(""QuestionarioAuraImpl"");","      36: 	@PostConstruct |       37: 	private void init() { | >>>   38:   		serviceImpl = (QuestionarioAuraInterface) FactoryBean.getBean(""QuestionarioAuraImpl""); |       39: 	} |       40: "
EJB_PROXY_USAGE,ReportPrenotazioniImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/report/prenotazioni/service/ReportPrenotazioniInterfaceBindingImpl.java,29,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (ReportPrenotazioniInterface) FactoryBean.getBean(""ReportPrenotazioniImpl"");","      27: 	private void init() { |       28:  | >>>   29:   		serviceImpl = (ReportPrenotazioniInterface) FactoryBean.getBean(""ReportPrenotazioniImpl""); |       30: 	} |       31: "
EJB_PROXY_USAGE,CollettivaNonGestitaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/collettiva/collettivanongestita/service/CollettivaNonGestitaInterfaceBindingImpl.java,49,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CollettivaNonGestitaInterface) FactoryBean.getBean(""CollettivaNonGestitaImpl"");","      47: 	private void init() { |       48:  | >>>   49:   		serviceImpl = (CollettivaNonGestitaInterface) FactoryBean.getBean(""CollettivaNonGestitaImpl""); |       50: 	} |       51: "
EJB_PROXY_USAGE,PrintDataImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/eventi/printdata/service/PrintDataServiceInterfaceBindingImpl.java,22,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PrintDataInterface) FactoryBean.getBean(""PrintDataImpl"");","      20: 	@PostConstruct |       21: 	private void init() { | >>>   22:   		serviceImpl = (PrintDataInterface) FactoryBean.getBean(""PrintDataImpl""); |       23: 	} |       24: "
EJB_PROXY_USAGE,FlussoEbaasImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/flussoebaas/flussoebaas/service/FlussoEbaasFlussoEbaasInterfaceBindingImpl.java,39,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FlussoEbaasFlussoEbaasInterface) FactoryBean.getBean(""FlussoEbaasImpl"");","      37: 	@PostConstruct |       38: 	private void init() { | >>>   39:   		serviceImpl = (FlussoEbaasFlussoEbaasInterface) FactoryBean.getBean(""FlussoEbaasImpl""); |       40: 	} |       41: "
EJB_PROXY_USAGE,FlussoPrometeiaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/flusso/prometeia/service/FlussoPrometeiaInterfaceBindingImpl.java,28,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FlussoPrometeiaInterface) FactoryBean.getBean(""FlussoPrometeiaImpl"");","      26: 	@PostConstruct |       27: 	private void init() { | >>>   28:   		serviceImpl = (FlussoPrometeiaInterface) FactoryBean.getBean(""FlussoPrometeiaImpl""); |       29: 	} |       30: "
EJB_PROXY_USAGE,MessaggioReteVenditaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/retevendita/messaggio/service/MessaggioReteVenditaInterfaceBindingImpl.java,32,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (MessaggioReteVenditaInterface) FactoryBean.getBean(""MessaggioReteVenditaImpl"");","      30: 	@PostConstruct |       31: 	private void init() { | >>>   32: 		serviceImpl = (MessaggioReteVenditaInterface) FactoryBean.getBean(""MessaggioReteVenditaImpl""); |       33: 	} |       34: "
EJB_PROXY_USAGE,FlussoReteVenditaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/retevendita/flusso/service/FlussoReteVenditaInterfaceBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (FlussoReteVenditaInterface) FactoryBean.getBean(""FlussoReteVenditaImpl"");","      24: 	@PostConstruct |       25: 	private void init() { | >>>   26: 		serviceImpl = (FlussoReteVenditaInterface) FactoryBean.getBean(""FlussoReteVenditaImpl""); |       27: 	} |       28:     public SelectAssFlussiDistributoriResponse selectAssFlussiDistributori(SelectAssFlussiDistributoriRequest request) throws PolicyFault, ServiceFault {"
EJB_PROXY_USAGE,MovimentiContabiliOasiImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/oasi/movimenticontabili/service/MovimentiContabiliOasiInterfaceBindingImpl.java,41,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (MovimentiContabiliOasiInterface) FactoryBean.getBean(""MovimentiContabiliOasiImpl"");","      39: 	private void init() { |       40:  | >>>   41:   		serviceImpl = (MovimentiContabiliOasiInterface) FactoryBean.getBean(""MovimentiContabiliOasiImpl""); |       42: 	} |       43: "
EJB_PROXY_USAGE,PrenotazioneLiquidazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/liquidazione/service/PrenotazioneLiquidazioneInterfaceBindingImpl.java,23,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PrenotazioneLiquidazioneInterface) FactoryBean.getBean(""PrenotazioneLiquidazioneImpl"");","      21: 	@PostConstruct |       22: 	private void init() { | >>>   23: 		serviceImpl = (PrenotazioneLiquidazioneInterface) FactoryBean.getBean(""PrenotazioneLiquidazioneImpl""); |       24: 	} |       25: "
EJB_PROXY_USAGE,PrenotazionePostVenditaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/postvendita/service/PrenotazionePostVenditaInterfaceBindingImpl.java,65,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaImpl"");","      63: 	@PostConstruct |       64: 	private void init() { | >>>   65: 		serviceImpl = (PrenotazionePostVenditaInterface) FactoryBean.getBean(""PrenotazionePostVenditaImpl""); |       66: 	} |       67: "
EJB_PROXY_USAGE,PrenotazioneTrasfAgenziaImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/trasfagenzia/service/PrenotazioneTrasfAgenziaInterfaceBindingImpl.java,29,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PrenotazioneTrasfAgenziaInterface) FactoryBean.getBean(""PrenotazioneTrasfAgenziaImpl"");","      27: 	private void init() { |       28:  | >>>   29:   		serviceImpl = (PrenotazioneTrasfAgenziaInterface) FactoryBean.getBean(""PrenotazioneTrasfAgenziaImpl""); |       30: 	} |       31: "
EJB_PROXY_USAGE,PrenotazioneDocumentazioneImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/prenotazione/documentazione/service/PrenotazioneDocumentazioneInterfaceBindingImpl.java,29,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (PrenotazioneDocumentazioneInterface) FactoryBean.getBean(""PrenotazioneDocumentazioneImpl"");","      27: 	@PostConstruct |       28: 	private void init() { | >>>   29:   		serviceImpl = (PrenotazioneDocumentazioneInterface) FactoryBean.getBean(""PrenotazioneDocumentazioneImpl""); |       30: 	} |       31: "
EJB_PROXY_USAGE,RctImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/interfacce/contabilita/rct/service/RctInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (RctInterface) FactoryBean.getBean(""RctImpl"");","      19: 	@PostConstruct |       20: 	private void init() { | >>>   21:   		serviceImpl = (RctInterface) FactoryBean.getBean(""RctImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,CollettoreImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/interfacce/collettore/service/CollettoreInterfaceBindingImpl.java,21,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CollettoreInterface) FactoryBean.getBean(""CollettoreImpl"");","      19: 	@PostConstruct |       20: 	private void init() { | >>>   21:   		serviceImpl = (CollettoreInterface) FactoryBean.getBean(""CollettoreImpl""); |       22: 	} |       23: "
EJB_PROXY_USAGE,CoeffCostoMgmFeeImpl,Services.Common.EJB/ejbModule/it/sistinf/albedoweb/services/deroghe/coeffcostomgmfee/service/CoeffCostoMgmFeeInterfaceBindingImpl.java,57,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (CoeffCostoMgmFeeInterface) FactoryBean.getBean(""CoeffCostoMgmFeeImpl"");","      55: 	@PostConstruct |       56: 	private void init() { | >>>   57:   		serviceImpl = (CoeffCostoMgmFeeInterface) FactoryBean.getBean(""CoeffCostoMgmFeeImpl""); |       58: 	} |       59: "
EJB_PROXY_USAGE,DataDisposalImpl,Services.Common.EJB/ejbModule/com/zurich/it/oil/externalservices/datadisposal/DataDisposalBindingImpl.java,26,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","serviceImpl = (DataDisposalPortType) FactoryBean.getBean(""DataDisposalImpl"");","      24: 	@PostConstruct |       25: 	private void init() { | >>>   26:   		serviceImpl = (DataDisposalPortType) FactoryBean.getBean(""DataDisposalImpl""); |       27: 	} |       28: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/bo/DominiHelper.java,157,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface domini = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     155: 		request.setRequestInfo(dominio); |      156: 		try { | >>>  157: 			it.sistinf.albedoweb.services.domini.naw.service.DominiInterface domini = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      158: 			List<ElementoDominiCorrelatiInfo> regoleInfo = domini.searchDominiCorrelatati(request).getResponseInfo(); |      159: 			for(ElementoDominiCorrelatiInfo regola : regoleInfo) {"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/bo/DominiHelper.java,364,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominiBD = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","     362: 		List<ElementoDominioInfo> listDominiCorrelati = null; |      363:  | >>>  364: 		it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominiBD = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |      365: 		DominiCorrelatiPrsRequest requestDom = getRequestDominiCorrelati(sigla, |      366: 																	     regola ,"
EJB_PROXY_USAGE,ProdottoProdottoBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3133,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoBusinessDelegate"");","    3131:  |     3132:     protected ProdottoProdottoInterface getProdottoProdottoServiceBusinessDelegate() { | >>> 3133: 		return (ProdottoProdottoInterface) FactoryBean.getBean(""ProdottoProdottoBusinessDelegate""); |     3134: 	} |     3135: "
EJB_PROXY_USAGE,PortafoglioRapportoBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3137,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoBusinessDelegate"");","    3135:  |     3136: 	public PortafoglioRapportoInterface getRapportoBusinessDelegate() { | >>> 3137: 		return (PortafoglioRapportoInterface) FactoryBean.getBean(""PortafoglioRapportoBusinessDelegate""); |     3138: 	} |     3139: "
EJB_PROXY_USAGE,AbilitazioneAzioneBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3141,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneBusinessDelegate"");","    3139:  |     3140:     protected AbilitazioneAzioneInterface getAbilitazioneAzioneBusinessDelegate() { | >>> 3141: 		return (AbilitazioneAzioneInterface) FactoryBean.getBean(""AbilitazioneAzioneBusinessDelegate""); |     3142: 	} |     3143: "
EJB_PROXY_USAGE,ModelloMatematicoFormulaBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3145,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (ModelloMatematicoFormulaInterface) FactoryBean.getBean(""ModelloMatematicoFormulaBusinessDelegate"");","    3143:  |     3144:     protected ModelloMatematicoFormulaInterface getModelloMatematicoBusinessDelegate() { | >>> 3145: 		return (ModelloMatematicoFormulaInterface) FactoryBean.getBean(""ModelloMatematicoFormulaBusinessDelegate""); |     3146: 	} |     3147: "
EJB_PROXY_USAGE,FondoTrasferimentoBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3149,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoBusinessDelegate"");","    3147:  |     3148:     protected FondoTrasferimentoInterface getFondoTrasferimentoBusinessDelegate() { | >>> 3149: 		return (FondoTrasferimentoInterface) FactoryBean.getBean(""FondoTrasferimentoBusinessDelegate""); |     3150: 	} |     3151: "
EJB_PROXY_USAGE,DefCondizioniBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3153,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (GestioneTabelleDefCondizioniInterface) FactoryBean.getBean(""DefCondizioniBusinessDelegate"");","    3151:  |     3152:     protected GestioneTabelleDefCondizioniInterface  getDefCondizioniBusinessDelegate() { | >>> 3153: 		return (GestioneTabelleDefCondizioniInterface) FactoryBean.getBean(""DefCondizioniBusinessDelegate""); |     3154: 	} |     3155: "
EJB_PROXY_USAGE,GestioneEventiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3157,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (GestioneEventiInterface) FactoryBean.getBean(""GestioneEventiBusinessDelegate"");","    3155:  |     3156: 	protected GestioneEventiInterface getGestioneEventiBusinessDelegate() { | >>> 3157: 		return (GestioneEventiInterface) FactoryBean.getBean(""GestioneEventiBusinessDelegate""); |     3158: 	} |     3159: "
EJB_PROXY_USAGE,DerogheServiceBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3820,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceBusinessDelegate"");","    3818: 	protected DerogheServiceInterface getDerogheServiceBusinessDelegate() { |     3819:  | >>> 3820: 		return (DerogheServiceInterface) FactoryBean.getBean(""DerogheServiceBusinessDelegate""); |     3821: 	} |     3822: "
EJB_PROXY_USAGE,ParametriBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3824,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (ParametriServiceInterface) FactoryBean.getBean(""ParametriBusinessDelegate"");","    3822:  |     3823: 	protected ParametriServiceInterface getParametriServicesBusinessDelegate(){ | >>> 3824: 		return (ParametriServiceInterface) FactoryBean.getBean(""ParametriBusinessDelegate""); |     3825:  |     3826: 	}"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3970,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface domini = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    3968: 		request.setRequestInfo(dominio); |     3969: 		try { | >>> 3970: 			it.sistinf.albedoweb.services.domini.naw.service.DominiInterface domini = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     3971: 			List<ElementoDominiCorrelatiInfo> regoleInfo = domini.searchDominiCorrelatati(request).getResponseInfo(); |     3972: 			for(ElementoDominiCorrelatiInfo regola : regoleInfo) {"
EJB_PROXY_USAGE,StrutturaReteBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3985,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate"");","    3983:  |     3984: 	protected StrutturaReteInterface getStrutturaReteBusinessDelegate() { | >>> 3985: 		return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate""); |     3986: 	} |     3987: "
EJB_PROXY_USAGE,CatalogoProdottiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3989,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CatalogoProdottiInterface) FactoryBean.getBean(""CatalogoProdottiBusinessDelegate"");","    3987:  |     3988: 	protected CatalogoProdottiInterface getCatalogoProdottiBusinessDelegate() { | >>> 3989: 		return (CatalogoProdottiInterface) FactoryBean.getBean(""CatalogoProdottiBusinessDelegate""); |     3990: 	} |     3991: "
EJB_PROXY_USAGE,RibilanciamentoFondiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3993,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiBusinessDelegate"");","    3991:  |     3992: 	protected RibilanciamentoFondiInterface getRibilanciamentoFondiDelegate() { | >>> 3993: 		return (RibilanciamentoFondiInterface) FactoryBean.getBean(""RibilanciamentoFondiBusinessDelegate""); |     3994: 	} |     3995: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,3997,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    3995:  |     3996: 	protected static it.sistinf.albedoweb.services.domini.naw.service.DominiInterface getDominiBusinessDelegate() { | >>> 3997: 		return (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     3998: 	} |     3999: "
EJB_PROXY_USAGE,AnagraficaAnagraficaBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4001,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate"");","    3999:  |     4000: 	protected AnagraficaAnagraficaInterface getAnagraficaAnagraficaBusinessDelegate() { | >>> 4001: 		return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate""); |     4002: 	} |     4003: "
EJB_PROXY_USAGE,ProdottoTariffaBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4005,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (ProdottoTariffaInterface) FactoryBean.getBean(""ProdottoTariffaBusinessDelegate"");","    4003:  |     4004: 	protected ProdottoTariffaInterface getProdottoTariffaBusinessDelegate() { | >>> 4005: 		return (ProdottoTariffaInterface) FactoryBean.getBean(""ProdottoTariffaBusinessDelegate""); |     4006: 	} |     4007: "
EJB_PROXY_USAGE,CoeffCostoMgmFeeBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4009,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CoeffCostoMgmFeeInterface) FactoryBean.getBean(""CoeffCostoMgmFeeBusinessDelegate"");","    4007:  |     4008: 	protected CoeffCostoMgmFeeInterface getCoeffCostoMgmFeeBusinessDelegate() { | >>> 4009: 		return (CoeffCostoMgmFeeInterface) FactoryBean.getBean(""CoeffCostoMgmFeeBusinessDelegate""); |     4010: 	} |     4011: "
EJB_PROXY_USAGE,PianiSpostamentoBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4013,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (PianiSpostamentoInterface) FactoryBean.getBean(""PianiSpostamentoBusinessDelegate"");","    4011:  |     4012: 	protected PianiSpostamentoInterface getPianiSpostamentoBusinessDelegate() { | >>> 4013: 		return (PianiSpostamentoInterface) FactoryBean.getBean(""PianiSpostamentoBusinessDelegate""); |     4014: 	} |     4015: "
EJB_PROXY_USAGE,LocalitaStradarioBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4017,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (LocalitaStradarioInterface) FactoryBean.getBean(""LocalitaStradarioBusinessDelegate"");","    4015:  |     4016: 	protected LocalitaStradarioInterface getLocalitaStradarioBusinessDelegate() { | >>> 4017: 		return (LocalitaStradarioInterface) FactoryBean.getBean(""LocalitaStradarioBusinessDelegate""); |     4018: 	} |     4019: "
EJB_PROXY_USAGE,CampagnaBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4021,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CampagnaInterface) FactoryBean.getBean(""CampagnaBusinessDelegate"");","    4019:  |     4020: 	protected CampagnaInterface getCampagnaBusinessDelegate() { | >>> 4021: 		return (CampagnaInterface) FactoryBean.getBean(""CampagnaBusinessDelegate""); |     4022: 	} |     4023: 	protected GestioneSeparataInterface getGestioneSeparataBusinessDelegate() {"
EJB_PROXY_USAGE,GestioneSeparataBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4025,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (GestioneSeparataInterface) FactoryBean.getBean(""GestioneSeparataBusinessDelegate"");","    4023: 	protected GestioneSeparataInterface getGestioneSeparataBusinessDelegate() { |     4024:  | >>> 4025: 		return (GestioneSeparataInterface) FactoryBean.getBean(""GestioneSeparataBusinessDelegate""); |     4026: 	} |     4027: 	protected PolizzaMgmFeeInterface getPolizzaMgmFeeBusinessDelegate() {"
EJB_PROXY_USAGE,PolizzaMgmFeeBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4028,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (PolizzaMgmFeeInterface) FactoryBean.getBean(""PolizzaMgmFeeBusinessDelegate"");","    4026: 	} |     4027: 	protected PolizzaMgmFeeInterface getPolizzaMgmFeeBusinessDelegate() { | >>> 4028: 		return (PolizzaMgmFeeInterface) FactoryBean.getBean(""PolizzaMgmFeeBusinessDelegate""); |     4029: 	} |     4030: "
EJB_PROXY_USAGE,TabellaTracciatoBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/DominiFullJavaHelper.java,4032,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoBusinessDelegate"");","    4030:  |     4031: 	protected TabellaTracciatoInterface getTabellaTracciatoDelegate() { | >>> 4032: 	 	return (TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoBusinessDelegate""); |     4033: 	} |     4034: }"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/custom/CaricaStrutturaReteDiVenditaLivello1Action.java,197,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     195: 	public static DominiCorrelatiResponse getTrasferimentiTraLivelli1AbilitatiNew() throws it.sistinf.albedoweb.services.domini.naw.service.PolicyFault, it.sistinf.albedoweb.services.domini.naw.service.ServiceFault{ |      196:  | >>>  197: 		it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      198: 		DominiCorrelatiRequest dominiReq = new DominiCorrelatiRequest(); |      199: 		dominiReq.setRequestInfo(buildRequestInfo(""""));"
EJB_PROXY_USAGE,StrutturaReteBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/custom/DominioReteVenditaLivello1Custom.java,161,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate"");","     159:  |      160: 	protected StrutturaReteInterface getStrutturaReteBusinessDelegate() { | >>>  161: 		return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate""); |      162: 	} |      163: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/custom/CaricaStrutturaReteDiVenditaLivello0Action.java,178,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     176: 		String trasferimentiTraLivelli0Abilitati = """"; |      177:  | >>>  178: 		it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      179: 		DominiCorrelatiRequest dominiReq = new DominiCorrelatiRequest(); |      180: 		dominiReq.setRequestInfo(buildRequestInfo(codGruppoFilter));"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/domini/implementazione/custom/CaricaStrutturaReteDiVenditaLivello0Action.java,192,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     190: 	public static DominiCorrelatiResponse getTrasferimentiTraLivelli0AbilitatiNew() throws it.sistinf.albedoweb.services.domini.naw.service.PolicyFault, it.sistinf.albedoweb.services.domini.naw.service.ServiceFault{ |      191:  | >>>  192: 		it.sistinf.albedoweb.services.domini.naw.service.DominiInterface dominio = (it.sistinf.albedoweb.services.domini.naw.service.DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      193: 		DominiCorrelatiRequest dominiReq = new DominiCorrelatiRequest(); |      194: 		dominiReq.setRequestInfo(buildRequestInfo(""""));"
EJB_PROXY_USAGE,PolizzaPosizioneProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,101,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectPosizioneSimpleT024Response posizResponse = ((PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy"")).selectPosizioneSimple(posizRequest);","      99: 	private  List<PosizioneInfo> selectPosizioni(PolizzaInfoSimpleEstesa polizzaInfoSimpleEstesa) throws it.sistinf.albedoweb.services.polizza.posizione.service.PolicyFault, it.sistinf.albedoweb.services.polizza.posizione.service.ServiceFault { |      100: 		SelectPosizioneSimpleT024Request posizRequest = buildSelectPosizioneSimpleT024Request(polizzaInfoSimpleEstesa); | >>>  101: 		SelectPosizioneSimpleT024Response posizResponse = ((PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy"")).selectPosizioneSimple(posizRequest); |      102: 		List<PosizioneInfo> listaPosizioni = posizResponse.getPosizioneInfoSelect(); |      103: 		return listaPosizioni;"
EJB_PROXY_USAGE,TabellaTracciatoProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,468,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectProdottoDatiComuni130Response selectProdottoComuni130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDatiComuni130(selectProdottoComuni130Request);","     466: 					it.sistinf.albedoweb.services.tabella.tracciato.service.ServiceFault { |      467: 		SelectProdottoDatiComuni130Request selectProdottoComuni130Request = buildSelectProdottiComuni130Request(pise, codProdotto); | >>>  468: 		SelectProdottoDatiComuni130Response selectProdottoComuni130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDatiComuni130(selectProdottoComuni130Request); |      469: 		List<ProdottoDatiComuni130Info> listaProdotti_130 = selectProdottoComuni130Response.getListaProdotti(); |      470: 		return listaProdotti_130;"
EJB_PROXY_USAGE,TabellaTracciatoProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,490,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectProdottoDati130Response selectProdottoDati130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDati130(selectProdottoDati130Request);","     488: 					it.sistinf.albedoweb.services.tabella.tracciato.service.ServiceFault { |      489: 		SelectProdottoDati130Request selectProdottoDati130Request = buildSelectProdottiDati130Request(pise, codProdotto); | >>>  490: 		SelectProdottoDati130Response selectProdottoDati130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDati130(selectProdottoDati130Request); |      491: 		List<ProdottoDati130Info> listaProdotti_130 = selectProdottoDati130Response.getProdottoDati130InfoList(); |      492: 		return listaProdotti_130;"
EJB_PROXY_USAGE,TabellaTracciatoProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,523,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectProdottoDatiComuni130Response selectProdottoComuni130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDatiComuni130(selectProdottoComuni130Request);","     521: 		PolizzaInfoSimpleEstesa pise = buildPolizzaInfoSimpleEstesaBaseFrom(polizzaEstesa); |      522: 		SelectProdottoDatiComuni130Request selectProdottoComuni130Request = buildSelectProdottiComuni130Request(pise, polizzaEstesa.getProdotto().getCodice()); | >>>  523: 		SelectProdottoDatiComuni130Response selectProdottoComuni130Response = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectProdottoDatiComuni130(selectProdottoComuni130Request); |      524: 		List<ProdottoDatiComuni130Info> listaProdotti_130 = selectProdottoComuni130Response.getListaProdotti(); |      525: 		if (!listaProdotti_130.isEmpty()) {"
EJB_PROXY_USAGE,TabellaTracciatoProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,544,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectRaggruppamentoUnitLinkResponse raggrResponse = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectRaggruppamentoUnitLink(raggrUL);","     542: 		vttab190Info.setChiave1(codiceUT); |      543: 		raggrUL.setVttab190(vttab190Info); | >>>  544: 		SelectRaggruppamentoUnitLinkResponse raggrResponse = ((TabellaTracciatoInterface) FactoryBean.getBean(""TabellaTracciatoProxy"")).selectRaggruppamentoUnitLink(raggrUL); |      545: 		if (!raggrResponse.getRaggruUnitLink410Info().isEmpty()){ |      546: 			raggr = raggrResponse.getRaggruUnitLink410Info().get(0);"
EJB_PROXY_USAGE,StrutturaReteProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,694,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectLivello1Response livello1Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello1(agenziaRequest);","     692:  |      693: 		agenziaRequest.setLiv1InfoMap(WsdlMapUtils.valorizzaWsdlMapFor(mapAgenzia)); | >>>  694: 		SelectLivello1Response livello1Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello1(agenziaRequest); |      695: 		if (!livello1Response.getLiv1Info().isEmpty()) { |      696: 			descrAgenzia = livello1Response.getLiv1Info().get(0).getDescrMandato().trim();"
EJB_PROXY_USAGE,StrutturaReteProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,711,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectLivello2Response livello2Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello2(subAgenziaRequest);","     709:  |      710: 		subAgenziaRequest.setLiv2InfoMap(WsdlMapUtils.valorizzaWsdlMapFor(mapSubAgenzia)); | >>>  711: 		SelectLivello2Response livello2Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello2(subAgenziaRequest); |      712: 		if (!livello2Response.getLiv2Info().isEmpty()) { |      713: 			descrSubAgenzia = livello2Response.getLiv2Info().get(0).getDescrSubAge().trim();"
EJB_PROXY_USAGE,StrutturaReteProxy,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,729,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectLivello3Response livello3Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello3(collRequest);","     727:  |      728: 		collRequest.setLiv3InfoMap(WsdlMapUtils.valorizzaWsdlMapFor(mapCollocatore)); | >>>  729: 		SelectLivello3Response livello3Response = ((StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"")).selectLivello3(collRequest); |      730: 		if (!livello3Response.getLiv3Info().isEmpty()) { |      731: 			descrCollocatore = livello3Response.getLiv3Info().get(0).getDescrCollocatore().trim();"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/polizza/helper/PolizzaHelper.java,878,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     876: 		List<ElementoDominiCorrelatiInfo> lista = new ArrayList<ElementoDominiCorrelatiInfo>(); |      877: 		try { | >>>  878: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      879: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |      880: 			lista = response.getResponseInfo();"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,1312,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    1310: 	public static String getProfessioneDaAttivitaPrevalente(String attivitaPrevalente, String codSoc) throws PolicyFault, ServiceFault { |     1311:  | >>> 1312: 		DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     1313: 		String rc = """"; |     1314: 		String codiceRegola = ""ATTPRE_PF"";"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,1707,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    1705: 		try { |     1706: 			// richiamo il servizio dei domini | >>> 1707: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     1708: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1709: 			if(response != null){"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,1764,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    1762: 		try { |     1763: 			// richiamo il servizio dei domini | >>> 1764: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     1765: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1766: 			if(response != null){"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,1842,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    1840: 		try { |     1841: 			// richiamo il servizio dei domini | >>> 1842: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     1843: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1844: 			if(response != null){"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2016,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    2014:  |     2015: 		try { | >>> 2016: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     2017: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2018: 			if (response != null && !response.getResponseInfo().isEmpty()) {"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2051,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    2049:  |     2050: 		try { | >>> 2051: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     2052: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2053: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2080,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy"");","    2078:  |     2079: 		try { | >>> 2080: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiProxy""); |     2081: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2082: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2447,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy"");","    2445: 	private static DominiCorrelatiResponse ricercaRegolaDbkBpa988(DominiCorrelatiInfo di, String codSoc, String livello1) throws PolicyFault, ServiceFault { |     2446:  | >>> 2447: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy""); |     2448: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2449: "
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2487,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy"");","    2485: 	private static DominiCorrelatiResponse ricercaRegolaTcm988(DominiCorrelatiInfo di, Integer codSoc, String codiceProdotto, String livello0, String livello1) throws PolicyFault, ServiceFault { |     2486:  | >>> 2487: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy""); |     2488: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2489: 		String codiceCompagnia = codSoc.toString();"
EJB_PROXY_USAGE,PolizzaPosizioneProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2597,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy"");","    2595:  |     2596: 	private static PolizzaPosizioneInterface getPolizzaPosizioneBusinessDelegate() { | >>> 2597: 		return (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneProxy""); |     2598: 	} |     2599: "
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2628,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy"");","    2626: 	public static boolean isAgenziaNonAdeguata(Integer codiceCompagnia, String livello1) throws PolicyFault, ServiceFault { |     2627:  | >>> 2628: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy""); |     2629: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2630: 		DominiCorrelatiInfo di = new DominiCorrelatiInfo();"
EJB_PROXY_USAGE,StrutturaReteProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2697,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy"");","    2695:  |     2696: 	private static StrutturaReteInterface getStrutturaReteDelegate() { | >>> 2697: 		return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteProxy""); |     2698: 	} |     2699: "
EJB_PROXY_USAGE,CustomerAccessoriProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2798,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy"")).selectClasseDiRischio(classeDiRischioRequest);","    2796:  |     2797: 				// Lettura della tabella TSTABPRI per reperire il profilo di rischio | >>> 2798: 				SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriProxy"")).selectClasseDiRischio(classeDiRischioRequest); |     2799:  |     2800: 				if (selectClasseDiRichioResponse != null && !selectClasseDiRichioResponse.getClasseDiRischioInfoList().isEmpty()) {"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,2887,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy"");","    2885: 		Double premioLimite = new Double(0); |     2886: 		List<ElementoDominioInfo> listaPremi = null; | >>> 2887: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy""); |     2888: 		DominiRequest dominiRequest = new DominiRequest(); |     2889: 		DominiResponse oggDominiResponse;"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,3031,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","oggDominiResponse = ((DominiInterface)FactoryBean.getBean(""DominiProxy"")).searchDomini(dominiRequest);","    3029: 		DominiResponse oggDominiResponse; |     3030:  | >>> 3031: 			oggDominiResponse = ((DominiInterface)FactoryBean.getBean(""DominiProxy"")).searchDomini(dominiRequest); |     3032:  |     3033: 			if(oggDominiResponse.getResponseInfo()!=null && oggDominiResponse.getResponseInfo().size()>0){"
EJB_PROXY_USAGE,DominiProxy,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelperProxy.java,3048,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy"");","    3046: 	public static boolean isAgenziaDbk(Integer codiceCompagnia, String livello1) throws PolicyFault, ServiceFault { |     3047:  | >>> 3048: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiProxy""); |     3049: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     3050: 		DominiCorrelatiInfo di = new DominiCorrelatiInfo();"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,1312,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    1310: 	public static String getProfessioneDaAttivitaPrevalente(String attivitaPrevalente, String codSoc) throws PolicyFault, ServiceFault { |     1311:  | >>> 1312: 		DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     1313: 		String rc = """"; |     1314: 		String codiceRegola = ""ATTPRE_PF"";"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,1707,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    1705: 		try { |     1706: 			// richiamo il servizio dei domini | >>> 1707: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     1708: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1709: 			if(response != null){"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,1764,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    1762: 		try { |     1763: 			// richiamo il servizio dei domini | >>> 1764: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     1765: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1766: 			if(response != null){"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,1842,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    1840: 		try { |     1841: 			// richiamo il servizio dei domini | >>> 1842: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     1843: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     1844: 			if(response != null){"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2016,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    2014:  |     2015: 		try { | >>> 2016: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     2017: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2018: 			if (response != null && !response.getResponseInfo().isEmpty()) {"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2051,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    2049:  |     2050: 		try { | >>> 2051: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     2052: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2053: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2080,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","    2078:  |     2079: 		try { | >>> 2080: 			DominiInterface bd =(DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |     2081: 			DominiCorrelatiResponse response = bd.searchDominiCorrelatati(reqDomCorr); |     2082: 			return response.getResponseInfo();"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2447,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","    2445: 	private static DominiCorrelatiResponse ricercaRegolaDbkBpa988(DominiCorrelatiInfo di, String codSoc, String livello1) throws PolicyFault, ServiceFault { |     2446:  | >>> 2447: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |     2448: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2449: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2487,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","    2485: 	private static DominiCorrelatiResponse ricercaRegolaTcm988(DominiCorrelatiInfo di, Integer codSoc, String codiceProdotto, String livello0, String livello1) throws PolicyFault, ServiceFault { |     2486:  | >>> 2487: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |     2488: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2489: 		String codiceCompagnia = codSoc.toString();"
EJB_PROXY_USAGE,PolizzaPosizioneBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2597,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneBusinessDelegate"");","    2595:  |     2596: 	private static PolizzaPosizioneInterface getPolizzaPosizioneBusinessDelegate() { | >>> 2597: 		return (PolizzaPosizioneInterface) FactoryBean.getBean(""PolizzaPosizioneBusinessDelegate""); |     2598: 	} |     2599: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2628,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","    2626: 	public static boolean isAgenziaNonAdeguata(Integer codiceCompagnia, String livello1) throws PolicyFault, ServiceFault { |     2627:  | >>> 2628: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |     2629: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     2630: 		DominiCorrelatiInfo di = new DominiCorrelatiInfo();"
EJB_PROXY_USAGE,StrutturaReteBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2697,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate"");","    2695:  |     2696: 	private static StrutturaReteInterface getStrutturaReteDelegate() { | >>> 2697: 		return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate""); |     2698: 	} |     2699: "
EJB_PROXY_USAGE,CustomerAccessoriBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2798,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriBusinessDelegate"")).selectClasseDiRischio(classeDiRischioRequest);","    2796:  |     2797: 				// Lettura della tabella TSTABPRI per reperire il profilo di rischio | >>> 2798: 				SelectClasseDiRischioInfoResponse selectClasseDiRichioResponse = ((CustomerAccessoriInterface) FactoryBean.getBean(""CustomerAccessoriBusinessDelegate"")).selectClasseDiRischio(classeDiRischioRequest); |     2799:  |     2800: 				if (selectClasseDiRichioResponse != null && !selectClasseDiRichioResponse.getClasseDiRischioInfoList().isEmpty()) {"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,2887,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","    2885: 		Double premioLimite = new Double(0); |     2886: 		List<ElementoDominioInfo> listaPremi = null; | >>> 2887: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |     2888: 		DominiRequest dominiRequest = new DominiRequest(); |     2889: 		DominiResponse oggDominiResponse;"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,3031,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","oggDominiResponse = ((DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"")).searchDomini(dominiRequest);","    3029: 		DominiResponse oggDominiResponse; |     3030:  | >>> 3031: 			oggDominiResponse = ((DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"")).searchDomini(dominiRequest); |     3032:  |     3033: 			if(oggDominiResponse.getResponseInfo()!=null && oggDominiResponse.getResponseInfo().size()>0){"
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/customer/util/CustomerHelper.java,3048,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate"");","    3046: 	public static boolean isAgenziaDbk(Integer codiceCompagnia, String livello1) throws PolicyFault, ServiceFault { |     3047:  | >>> 3048: 		DominiInterface dominiBD = (DominiInterface)FactoryBean.getBean(""DominiBusinessDelegate""); |     3049: 		DominiCorrelatiRequest dreq = new DominiCorrelatiRequest(); |     3050: 		DominiCorrelatiInfo di = new DominiCorrelatiInfo();"
EJB_PROXY_USAGE,MonitorGidBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/CommonServicesUtils.java,18,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (MonitorGidInterface) FactoryBean.getBean(""MonitorGidBusinessDelegate"");","      16:     protected static MonitorGidInterface getMonitorGidBusinessDelegate() { |       17:  | >>>   18: 		return (MonitorGidInterface) FactoryBean.getBean(""MonitorGidBusinessDelegate""); |       19: 	} |       20: "
EJB_PROXY_USAGE,AnagraficaAnagraficaBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java,1723,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","AnagraficaAnagraficaInterface aai = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate"");","    1721: 			SelectFiguraAnagraficaPolizzaRequest selectFiguraAnagraficaPolizzaRequest = new SelectFiguraAnagraficaPolizzaRequest(); |     1722: 			selectFiguraAnagraficaPolizzaRequest.setFiguraAnagraficaInfoMap(wsdlMap); | >>> 1723: 			AnagraficaAnagraficaInterface aai = (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate""); |     1724: 			selectFiguraAnagraficaPolizzaResponse = aai.selectFiguraAnagraficaPolizza(selectFiguraAnagraficaPolizzaRequest); |     1725: 		} catch (it.sistinf.albedoweb.services.anagrafica.anagrafica.service.PolicyFault pf) {"
EJB_PROXY_USAGE,AnagrafeEasyBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java,2042,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagrafeEasyInterface) FactoryBean.getBean(""AnagrafeEasyBusinessDelegate"");","    2040: 	private static AnagrafeEasyInterface getAnagrafeEasyBusinessDelegate() { |     2041:  | >>> 2042: 		return (AnagrafeEasyInterface) FactoryBean.getBean(""AnagrafeEasyBusinessDelegate""); |     2043: 	} |     2044: "
EJB_PROXY_USAGE,StrutturaReteBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java,2047,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate"");","    2045: 	private static StrutturaReteInterface getStrutturaReteBusinessDelegate() { |     2046:  | >>> 2047: 		return (StrutturaReteInterface) FactoryBean.getBean(""StrutturaReteBusinessDelegate""); |     2048: 	} |     2049: "
EJB_PROXY_USAGE,CustomerBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/AnagrafeUtils.java,2052,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate"");","    2050: 	private static CustomerInterface getCustomerBusinessDelegate() { |     2051:  | >>> 2052: 		return (CustomerInterface) FactoryBean.getBean(""CustomerBusinessDelegate""); |     2053: 	} |     2054: "
EJB_PROXY_USAGE,DominiBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/ControlloUtils.java,250,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate"");","     248:  |      249: 	private static DominiInterface getDominiBusinessDelegate() { | >>>  250: 		return (DominiInterface) FactoryBean.getBean(""DominiBusinessDelegate""); |      251: 	} |      252: "
EJB_PROXY_USAGE,AnagraficaAnagraficaBusinessDelegate,albedoBase/it/sistinf/albedoweb/utility/ControlloUtils.java,254,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate"");","     252:  |      253: 	private static AnagraficaAnagraficaInterface getAnagraficaBusinessDelegate() { | >>>  254: 		return (AnagraficaAnagraficaInterface) FactoryBean.getBean(""AnagraficaAnagraficaBusinessDelegate""); |      255: 	} |      256: "
EJB_PROXY_USAGE,DominiProxy,REST.Services.Utility/src/it/sistinf/rest/model/portafoglio/utility/PortafoglioUtility.java,200,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (DominiInterface) FactoryBean.getBean(""DominiProxy"");","     198:  |      199: 	protected static DominiInterface getDominiInterfaceProxy() { | >>>  200: 		return (DominiInterface) FactoryBean.getBean(""DominiProxy""); |      201: 	} |      202: "
EJB_PROXY_USAGE,DominiImpl,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/service/impl/PropostaPropostaImpl.java,508,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","dominiImpl = (DominiInterface) FactoryBean.getBean(""DominiImpl"");*/","     506:  |      507: 		/*System.err.println(this + ""*************************** INIT ******************""); | >>>  508: 		dominiImpl = (DominiInterface) FactoryBean.getBean(""DominiImpl"");*/ |      509: 		log = blueLifeLogger.getLogger(this.getClass()); |      510: 	}"
EJB_PROXY_USAGE,OpenSecProxy,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/service/impl/PropostaPropostaImpl.java,7824,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurich(modPagamentoPrimaRata, modPagamentoPrimaRata.getUserVariaz());","    7822: 					modPagamentoPrimaRata.setIban(iban); |     7823: //					OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurichMock(""OK"", """", """"); | >>> 7824: 					OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurich(modPagamentoPrimaRata, modPagamentoPrimaRata.getUserVariaz()); |     7825: 					if(openSecControlloIbanResponse.getStatus().equals(SrvConstants.HTTP_STATUS_OK)){ |     7826: 						if(GenericUtils.stringToStringNoNull(openSecControlloIbanResponse.getResponse().getControlloIBANAddebitoCFSP_Response().getEsito()).equals(SrvConstants.ESITO_KO)){"
EJB_PROXY_USAGE,StampaVariabileBusinessDelegate,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/service/impl/GestioneEventiImpl.java,525,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","return (StampaVariabileInterface) FactoryBean.getBean(""StampaVariabileBusinessDelegate"");","     523:  |      524: 	protected StampaVariabileInterface getStampaVariabileServiceBusinessDelegate() { | >>>  525: 		return (StampaVariabileInterface) FactoryBean.getBean(""StampaVariabileBusinessDelegate""); |      526: 	} |      527: "
EJB_PROXY_USAGE,OpenSecProxy,Services.Common.Impl/src/it/sistinf/albedoweb/services/anagrafica/anagrafica/service/impl/AnagraficaAnagraficaImpl.java,1659,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurich(modPagamentoInfo, request.getUser());","    1657: 					modPagamentoInfo.setIban(request.getCodiceIban()); |     1658: //					OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurichMock(""OK"", """", """"); | >>> 1659: 					OpenSecControlloIbanResponse openSecControlloIbanResponse = ((OpenSecService) FactoryBean.getBean(""OpenSecProxy"")).controlloIbanIncassoZurich(modPagamentoInfo, request.getUser()); |     1660: 					if(openSecControlloIbanResponse.getStatus().equals(SrvConstants.HTTP_STATUS_OK)){ |     1661: 						if(GenericUtils.stringToStringNoNull(openSecControlloIbanResponse.getResponse().getControlloIBANAddebitoCFSP_Response().getEsito()).equals(SrvConstants.ESITO_KO)){"
EJB_PROXY_USAGE,AegisProxy,Services.Common.Impl/src/com/zurich/it/oil/externalservices/datadisposal/impl/DataDisposalImpl.java,52,"FactoryBean\.getBean\s*\(\s*[""\']([^""\']+)[""\']","aegisImpl = (AegisInterface) FactoryBean.getBean(""AegisProxy"");","      50:  |       51: 		log = blueLifeLogger.getLogger(SrvConstants.DATA_DISPOSAL); | >>>   52: 		aegisImpl = (AegisInterface) FactoryBean.getBean(""AegisProxy""); |       53: 	} |       54: "
REST_ENDPOINT,/polizza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,112,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/polizza"")","     110: import org.apache.log4j.Logger; |      111:  | >>>  112: @Path(""/polizza"") |      113:  |      114: @javax.annotation.Generated(value = ""io.swagger.codegen.v3.generators.java.JavaJAXRSSpecServerCodegen"", date = ""2025-04-18T08:39:53.815883189Z[GMT]"")"
REST_ENDPOINT,/funzioniPolizza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,136,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/funzioniPolizza"")","     134:  |      135:     @PUT | >>>  136:     @Path(""/funzioniPolizza"") |      137:     @Consumes({ ""application/json"" }) |      138:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/grigliaCompatibilita,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,155,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/grigliaCompatibilita"")","     153:  |      154:     @PUT | >>>  155:     @Path(""/grigliaCompatibilita"") |      156:     @Consumes({ ""application/json"" }) |      157:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/continuousMonitoring,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,174,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/continuousMonitoring"")","     172:  |      173:     @PUT | >>>  174:     @Path(""/continuousMonitoring"") |      175:     @Consumes({ ""application/json"" }) |      176:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/continuousMonitoringLiquidazioni,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,193,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/continuousMonitoringLiquidazioni"")","     191:  |      192:     @PUT | >>>  193:     @Path(""/continuousMonitoringLiquidazioni"") |      194:     @Consumes({ ""application/json"" }) |      195:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/opzContrattuali/salva,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,212,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/opzContrattuali/salva"")","     210:  |      211:     @PUT | >>>  212:     @Path(""/opzContrattuali/salva"") |      213:     @Consumes({ ""application/json"" }) |      214:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/opzContrattuali/inoltra,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,231,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/opzContrattuali/inoltra"")","     229:  |      230:     @PUT | >>>  231:     @Path(""/opzContrattuali/inoltra"") |      232:     @Consumes({ ""application/json"" }) |      233:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/controllaOpzContrattuali,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,250,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/controllaOpzContrattuali"")","     248:  |      249:     @PUT | >>>  250:     @Path(""/controllaOpzContrattuali"") |      251:     @Consumes({ ""application/json"" }) |      252:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/opzioniContrattuali,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,269,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/opzioniContrattuali"")","     267:  |      268:     @PUT | >>>  269:     @Path(""/opzioniContrattuali"") |      270:     @Consumes({ ""application/json"" }) |      271:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/inizializza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,288,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/inizializza"")","     286:  |      287:     @PUT | >>>  288:     @Path(""/inizializza"") |      289:     @Consumes({ ""application/json"" }) |      290:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/storicoOpzioniContrattuali,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,311,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/storicoOpzioniContrattuali"")","     309:  |      310:     @PUT | >>>  311:     @Path(""/storicoOpzioniContrattuali"") |      312:     @Consumes({ ""application/json"" }) |      313:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/inizializzaProposta,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,330,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/inizializzaProposta"")","     328:  |      329:     @PUT | >>>  330:     @Path(""/inizializzaProposta"") |      331:     @Consumes({ ""application/json"" }) |      332:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/inizializzaVAEmesso,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,351,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/inizializzaVAEmesso"")","     349:  |      350:     @PUT | >>>  351:     @Path(""/inizializzaVAEmesso"") |      352:     @Consumes({ ""application/json"" }) |      353:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/disimpegna,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,412,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/disimpegna"")","     410:  |      411: 	@PUT | >>>  412:     @Path(""/disimpegna"") |      413:     @Consumes({ ""application/json"" }) |      414:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/vaEmesso/salva,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,431,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/vaEmesso/salva"")","     429:  |      430: 	@PUT | >>>  431:     @Path(""/vaEmesso/salva"") |      432:     @Consumes({ ""application/json"" }) |      433:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/anagrafica/salvaSoggettoTerzo,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,451,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/anagrafica/salvaSoggettoTerzo"")","     449:  |      450: 	@PUT | >>>  451:     @Path(""/anagrafica/salvaSoggettoTerzo"") |      452:     @Consumes({ ""application/json"" }) |      453:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/vaEmesso/inoltra,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,470,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/vaEmesso/inoltra"")","     468:  |      469:     @PUT | >>>  470:     @Path(""/vaEmesso/inoltra"") |      471:     @Consumes({ ""application/json"" }) |      472:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/riscatto/controlla,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,490,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/riscatto/controlla"")","     488:  |      489:     @PUT | >>>  490:     @Path(""/riscatto/controlla"") |      491:     @Consumes({ ""application/json"" }) |      492:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/riscatto/inizializza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,532,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/riscatto/inizializza"")","     530:  |      531:     @PUT | >>>  532:     @Path(""/riscatto/inizializza"") |      533:     @Consumes({ ""application/json"" }) |      534:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/riscatto/calcola,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,577,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/riscatto/calcola"")","     575:  |      576:     @PUT | >>>  577:     @Path(""/riscatto/calcola"") |      578:     @Consumes({ ""application/json"" }) |      579:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/vaEmesso/controlla,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,661,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/vaEmesso/controlla"")","     659:  |      660:     @PUT | >>>  661:     @Path(""/vaEmesso/controlla"") |      662:     @Consumes({ ""application/json"" }) |      663:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/percipienti/elenco,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,707,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/percipienti/elenco"")","     705:  |      706:     @PUT | >>>  707:     @Path(""/percipienti/elenco"") |      708:     @Consumes({ ""application/json"" }) |      709:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/percipienti/inserisci,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,730,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/percipienti/inserisci"")","     728:  |      729:     @PUT | >>>  730:     @Path(""/percipienti/inserisci"") |      731:     @Consumes({ ""application/json"" }) |      732:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/recesso/inizializza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,753,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/recesso/inizializza"")","     751:  |      752:     @PUT | >>>  753:     @Path(""/recesso/inizializza"") |      754:     @Consumes({ ""application/json"" }) |      755:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/recesso/controlla,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,772,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/recesso/controlla"")","     770:  |      771:     @PUT | >>>  772:     @Path(""/recesso/controlla"") |      773:     @Consumes({ ""application/json"" }) |      774:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/scadenza/inizializza,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,791,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/scadenza/inizializza"")","     789:  |      790:     @PUT | >>>  791:     @Path(""/scadenza/inizializza"") |      792:     @Consumes({ ""application/json"" }) |      793:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/scadenza/controlla,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,813,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/scadenza/controlla"")","     811:  |      812:     @PUT | >>>  813:     @Path(""/scadenza/controlla"") |      814:     @Consumes({ ""application/json"" }) |      815:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/riscatto/salva,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,835,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/riscatto/salva"")","     833:  |      834:     @PUT | >>>  835:     @Path(""/riscatto/salva"") |      836:     @Consumes({ ""application/json"" }) |      837:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/recesso/controllaPerInserimento,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,880,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/recesso/controllaPerInserimento"")","     878:  |      879:     @PUT | >>>  880:     @Path(""/recesso/controllaPerInserimento"") |      881:     @Consumes({ ""application/json"" }) |      882:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/riscatto/inoltra,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,899,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/riscatto/inoltra"")","     897:  |      898:     @PUT | >>>  899:     @Path(""/riscatto/inoltra"") |      900:     @Consumes({ ""application/json"" }) |      901:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/recesso/salva,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,918,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/recesso/salva"")","     916:  |      917:     @PUT | >>>  918:     @Path(""/recesso/salva"") |      919:     @Consumes({ ""application/json"" }) |      920:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/vaEmesso/controllaPerInserimento,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,937,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/vaEmesso/controllaPerInserimento"")","     935:  |      936:     @PUT | >>>  937:     @Path(""/vaEmesso/controllaPerInserimento"") |      938:     @Consumes({ ""application/json"" }) |      939:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/recesso/inoltra,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,983,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/recesso/inoltra"")","     981:  |      982:     @PUT | >>>  983:     @Path(""/recesso/inoltra"") |      984:     @Consumes({ ""application/json"" }) |      985:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/anagrafe/aggiorna,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,1002,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/anagrafe/aggiorna"")","    1000:  |     1001:     @PUT | >>> 1002:     @Path(""/anagrafe/aggiorna"") |     1003:     @Consumes({ ""application/json"" }) |     1004:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/scadenza/salva,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,1021,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/scadenza/salva"")","    1019:  |     1020:     @PUT | >>> 1021:     @Path(""/scadenza/salva"") |     1022:     @Consumes({ ""application/json"" }) |     1023:     @Produces({ ""application/json"" })"
REST_ENDPOINT,/scadenza/inoltra,REST.Services.Cobol.Web/src/it/sistinf/rest/cobol/api/PolizzaApi.java,1041,"@Path\s*\(\s*[""\']([^""\']+)[""\']","@Path(""/scadenza/inoltra"")","    1039:  |     1040:     @PUT | >>> 1041:     @Path(""/scadenza/inoltra"") |     1042:     @Consumes({ ""application/json"" }) |     1043:     @Produces({ ""application/json"" })"
REST_ENDPOINT,${Oasi.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,14,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Oasi.endpoint}""/>","      12: 	    <property name=""wsdlDocumentUrl"" value=""${Oasi.url.local}""/> |       13: 	    <property name=""namespaceUri"" value=""http://next.zurich.it/mergeSplit/""/> | >>>   14: 	    <property name=""endpointAddress"" value=""${Oasi.endpoint}""/> |       15: 	    <property name=""serviceName"" value=""mergeSplitPortTypeSoap12QSService""/> |       16: 	    <property name=""username"" value=""${Oasi.basic.auth.username}""/>"
REST_ENDPOINT,${Oasi.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,26,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Oasi.endpoint}""/>","      24: 	    <property name=""wsdlDocumentUrl"" value=""${Oasi.url.local}""/> |       25: 	    <property name=""namespaceUri"" value=""http://next.zurich.it/mergeSplit/""/> | >>>   26: 	    <property name=""endpointAddress"" value=""${Oasi.endpoint}""/> |       27: <!-- BASIC AUTHENTICATION --> |       28:  	    <!-- <property name=""serviceName"" value=""mergeSplitPortTypeSoap12QSService""/>"
REST_ENDPOINT,${Easy.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,48,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Easy.endpoint}""/>","      46: 	    <property name=""wsdlDocumentUrl"" value=""${Easy.url.local}""/> |       47: 	    <property name=""namespaceUri"" value=""http://registry.zurich.it/registry/""/> | >>>   48: 	    <property name=""endpointAddress"" value=""${Easy.endpoint}""/> |       49: 	    <property name=""serviceName"" value=""AnagraphPortTypeSoap12QSService""/> |       50: 		<property name=""username"" value=""${Easy.basic.auth.username}""/>"
REST_ENDPOINT,${Easy.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,59,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Easy.endpoint}""/>","      57: 	    <property name=""wsdlDocumentUrl"" value=""${Easy.url.local}""/> |       58: 	    <property name=""namespaceUri"" value=""http://registry.zurich.it/registry/""/> | >>>   59: 	    <property name=""endpointAddress"" value=""${Easy.endpoint}""/> |       60: 	    <property name=""serviceName"" value=""AnagraphicServices""/> |       61: 	    <property name=""portName"" value=""AnagraphicPortTypeSoap12""/>"
REST_ENDPOINT,${MovOasi.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,78,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${MovOasi.endpoint}""/>","      76: 	    <property name=""wsdlDocumentUrl"" value=""${MovOasi.url.local}""/> |       77: 	    <property name=""namespaceUri"" value=""http://nawoasi.zurich.it/nawoasi/""/> | >>>   78: 	    <property name=""endpointAddress"" value=""${MovOasi.endpoint}""/> |       79: 	    <property name=""serviceName"" value=""nawOasiPortTypeSoap12QSService""/> |       80: 		<property name=""username"" value=""${MovOasi.basic.auth.username}""/>"
REST_ENDPOINT,${Syncro.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,89,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Syncro.endpoint}""/>","      87: 	    <property name=""wsdlDocumentUrl"" value=""${Syncro.url.local}""/> |       88: 	    <property name=""namespaceUri"" value=""http://nap.zurich.it/messages/""/> | >>>   89: 	    <property name=""endpointAddress"" value=""${Syncro.endpoint}""/> |       90: 	    <property name=""serviceName"" value=""AccountingService""/> |       91: 	    <property name=""portName"" value=""AccountingServicePortSoap12""/>"
REST_ENDPOINT,${Cheope.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,109,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${Cheope.endpoint}""/>","     107: 	    <!-- property name=""namespaceUri"" value=""urn://www.cheopeonline.it/wscheope/""/  --> |      108:         <property name=""namespaceUri"" value=""urn://cheopeonline.it/wscheope""/> | >>>  109: 	    <property name=""endpointAddress"" value=""${Cheope.endpoint}""/> |      110: 	    <property name=""serviceName"" value=""DataService""/> |      111: 	    <property name=""portName"" value=""DataServiceSoap12""/>"
REST_ENDPOINT,${CodeMq.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,124,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${CodeMq.endpoint}""/>","     122:         <property name=""wsdlDocumentUrl"" value=""${CodeMq.url.local}""/> |      123:         <property name=""namespaceUri"" value=""http://zurich.com/it/osb/services/messagesDispatcherUtility/pubTextMessage""/> | >>>  124:         <property name=""endpointAddress"" value=""${CodeMq.endpoint}""/> |      125:         <property name=""serviceName"" value=""PubTextMessage""/> |      126:         <property name=""portName"" value=""PubTextMessageSOAP12Port""/>"
REST_ENDPOINT,${DataAnonymize.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,143,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${DataAnonymize.endpoint}""/>","     141: 	    <property name=""wsdlDocumentUrl"" value=""${DataAnonymize.url.local}""/> |      142: 	    <property name=""namespaceUri"" value=""http://zurich.com/it/osb/services/dataAnonymize""/> | >>>  143: 	    <property name=""endpointAddress"" value=""${DataAnonymize.endpoint}""/> |      144: 	    <property name=""serviceName"" value=""DataAnonymize""/> |      145: 	    <property name=""portName"" value=""dataAnonymizePort""/>"
REST_ENDPOINT,${BEYONDOC.endpoint},Services.Common.EJB/ejbModule/spring-conf/external-services.xml,162,"endpointAddress[""\']?\s*value\s*=\s*[""\']([^""\']+)[""\']","<property name=""endpointAddress"" value=""${BEYONDOC.endpoint}""/>","     160: 	    <property name=""wsdlDocumentUrl"" value=""${BEYONDOC.url.local}""/> |      161: 	    <property name=""namespaceUri"" value=""http://web.formunix""/> | >>>  162: 	    <property name=""endpointAddress"" value=""${BEYONDOC.endpoint}""/> |      163: 	    <property name=""serviceName"" value=""Form2WebService""/> |      164: 	    <property name=""portName"" value=""Form2WebServicePort""/>"
WEB_SERVICE_DEFINITION,AnagrafeEasyWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,44,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""AnagrafeEasyWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","      42:  |       43: <!-- PRE AMLETO | >>>   44:  	<bean id=""AnagrafeEasyWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |       45: 	    <property name=""serviceInterface"" value=""it.zurich.registry.registry.AnagraphPortType""/> |       46: 	    <property name=""wsdlDocumentUrl"" value=""${Easy.url.local}""/>"
WEB_SERVICE_DEFINITION,AnagrafeEasyWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,55,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""AnagrafeEasyWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","      53: 	</bean> |       54: 	 --> | >>>   55: 	<bean id=""AnagrafeEasyWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |       56: 	    <property name=""serviceInterface"" value=""it.zurich.registry.registry.AnagraphicPortType""/> |       57: 	    <property name=""wsdlDocumentUrl"" value=""${Easy.url.local}""/>"
WEB_SERVICE_DEFINITION,MovimentiOasiWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,74,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""MovimentiOasiWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","      72: 	</bean> |       73:  | >>>   74: 	<bean id=""MovimentiOasiWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |       75: 	    <property name=""serviceInterface"" value=""it.zurich.nawoasi.nawoasi.NawOasiPortType""/> |       76: 	    <property name=""wsdlDocumentUrl"" value=""${MovOasi.url.local}""/>"
WEB_SERVICE_DEFINITION,SyncroWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,85,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""SyncroWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","      83: 	</bean> |       84:  | >>>   85: 	<bean id=""SyncroWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |       86: 	    <property name=""serviceInterface"" value=""it.zurich.nap.messages.AccountingServicePort""/> |       87: 	    <property name=""wsdlDocumentUrl"" value=""${Syncro.url.local}""/>"
WEB_SERVICE_DEFINITION,CheopeWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,104,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""CheopeWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","     102: 	</bean> |      103:  | >>>  104: 	<bean id=""CheopeWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |      105: 	    <property name=""serviceInterface"" value=""it.cheopeonline.wscheope.DataServiceSoap""/> |      106: 	    <property name=""wsdlDocumentUrl"" value=""${Cheope.url.local}""/>"
WEB_SERVICE_DEFINITION,CodeMqWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,120,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""CodeMqWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","     118: 	</bean> |      119:  | >>>  120:  	<bean id=""CodeMqWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |      121:         <property name=""serviceInterface"" value=""com.zurich.it.osb.services.messagesdispatcherutility.pubtextmessage.PubTextMessagePortType""/> |      122:         <property name=""wsdlDocumentUrl"" value=""${CodeMq.url.local}""/>"
WEB_SERVICE_DEFINITION,DataAnonymizeWSProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,139,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""DataAnonymizeWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","     137: 	</bean> |      138:  | >>>  139:  	<bean id=""DataAnonymizeWSProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |      140: 	    <property name=""serviceInterface"" value=""com.zurich.it.osb.services.dataanonymize.DataAnonymizePortType""/> |      141: 	    <property name=""wsdlDocumentUrl"" value=""${DataAnonymize.url.local}""/>"
WEB_SERVICE_DEFINITION,Form2WebServiceProxy,Services.Common.EJB/ejbModule/spring-conf/external-services.xml,158,"<bean\s+id=""([^""]+)""\s+class=""org\.springframework\.remoting\.jaxws\.JaxWsPortProxyFactoryBean""","<bean id=""Form2WebServiceProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true"">","     156: 	</bean> |      157:  | >>>  158: 	<bean id=""Form2WebServiceProxy"" class=""org.springframework.remoting.jaxws.JaxWsPortProxyFactoryBean"" lazy-init=""true""> |      159: 	    <property name=""serviceInterface"" value=""formunix.web.Form2WebService""/> |      160: 	    <property name=""wsdlDocumentUrl"" value=""${BEYONDOC.url.local}""/>"
