# RIASSUNTO COMPLETO DELLE CHIAMATE ESTERNE - SISTEMA NAW

## Panoramica Generale

Il sistema NAW (Network Application Web) è un'applicazione enterprise Java che gestisce servizi assicurativi con un'architettura a più livelli che include:

- **23.507 chiamate esterne totali** identificate
- **591 chiamate ai servizi** specifiche
- **6.129 chiamate ai metodi** dei servizi
- **444 servizi unici** configurati
- **110 servizi attivi** con chiamate ai metodi

## Categorie di Chiamate Esterne

### 1. CHIAMATE A SISTEMI LEGACY COBOL (189 chiamate)
**Tipo**: Integrazione con mainframe tramite JCA (Java Connector Architecture)

**Tecnologie utilizzate**:
- `javax.resource.cci.*` (104 occorrenze) - Java Connector Architecture
- `com.ibm.etools.marshall.*` (84 occorrenze) - IBM marshalling tools per COBOL
- Connettori J2C per serializzazione dati verso mainframe

**Servizi principali che utilizzano COBOL**:
- Gestione polizze (switch, storni, liquidazioni)
- Anagrafica clienti (variazioni, refresh)
- Prodotti assicurativi (dati comuni, formule)
- Tabelle di sistema (calendari, parametri)

### 2. API ESTERNE E WEB SERVICES (13.903 chiamate)
**Sistemi esterni integrati**:

#### Servizi Zurich
- **OASI** (${Oasi.endpoint}) - Sistema di gestione polizze
- **Easy** (${Easy.endpoint}) - Anagrafe clienti
- **MovOasi** (${MovOasi.endpoint}) - Movimenti contabili
- **Syncro** (${Syncro.endpoint}) - Sincronizzazione dati

#### Servizi Terze Parti
- **Cheope** (${Cheope.endpoint}) - Servizi esterni
- **CodeMQ** (${CodeMq.endpoint}) - Messaging
- **DataAnonymize** (${DataAnonymize.endpoint}) - Anonimizzazione dati
- **BEYONDOC** (${BEYONDOC.endpoint}) - Gestione documenti

### 3. SERVIZI EJB INTERNI (527 chiamate)
**Architettura EJB**:
- **303 chiamate** tramite `FactoryBean.getBean()`
- **119 servizi** annotati con `@Stateless`
- **46 binding** EJB locali (`ejblocal:`)
- **34 proxy** remoti (`SimpleRemoteStatelessSessionProxyFactoryBean`)

**Servizi EJB principali**:
- Customer (gestione clienti)
- Polizza (gestione polizze)
- Portafoglio (gestione portafoglio)
- Prodotto (gestione prodotti)
- Domini (tabelle di sistema)
- Abilitazioni (autorizzazioni)

### 4. CHIAMATE DATABASE (2.892 chiamate)
**Tecnologie di persistenza**:
- **SqlMapClient** (2.882 occorrenze) - iBATIS/MyBatis
- **DataSource** (8 occorrenze) - Connection pooling
- **JNDI** per lookup datasource

### 5. SERVIZI REST (333 chiamate)
**Endpoint REST esposti**:
- `@POST` (120 occorrenze)
- `@DELETE` (93 occorrenze)
- `@PUT` (35 occorrenze)
- `@GET` (14 occorrenze)
- `Response.ok()` (35 occorrenze)

### 6. CONFIGURAZIONI SPRING (1.950 chiamate)
**Dependency Injection**:
- **1.389** proprietà configurate (`<property name=`)
- **459** bean definiti (`<bean id=`)
- **94** riferimenti (`ref=`)

## Top 10 Servizi Più Chiamati

1. **serviceImpl** - 1.335 chiamate
2. **_service** - 672 chiamate  
3. **prodotto** - 581 chiamate
4. **customer** - 345 chiamate
5. **dominiImpl** - 330 chiamate
6. **anagraficaImpl** - 291 chiamate
7. **domini** - 267 chiamate
8. **polizza** - 254 chiamate
9. **service** - 222 chiamate
10. **tabellaTracciatoImpl** - 100 chiamate

## Top 10 Metodi Più Chiamati

1. **createDispatch()** - 336 chiamate
2. **searchDomini()** - 263 chiamate
3. **searchDominiCorrelatati()** - 254 chiamate
4. **selectPolizza()** - 76 chiamate
5. **selectModPagamento()** - 52 chiamate
6. **getUtenteInfo()** - 47 chiamate
7. **searchDominiRelazionati()** - 45 chiamate
8. **selectOrigineFondi()** - 43 chiamate
9. **selectAllCustomerInfo()** - 40 chiamate
10. **selectValutazioneIntermediario()** - 40 chiamate

## Architettura di Integrazione

### Livello 1: REST API
- Espone servizi REST per client esterni
- Gestisce autenticazione e autorizzazione
- Valida input e formatta output

### Livello 2: EJB Business Logic
- Implementa logica di business
- Gestisce transazioni
- Coordina chiamate a servizi esterni

### Livello 3: Integrazione Sistemi Legacy
- **JCA/J2C** per comunicazione con mainframe COBOL
- **Web Services** per sistemi Zurich
- **Database** per persistenza dati

### Livello 4: Sistemi Esterni
- **Mainframe COBOL** (sistemi legacy assicurativi)
- **Sistemi Zurich** (OASI, Easy, Syncro)
- **Servizi terze parti** (Cheope, BEYONDOC)

## File con Maggiori Chiamate

1. **PolizzaPolizzaImpl.java** - 484 chiamate
2. **ProdottoProdottoImpl.java** - 418 chiamate
3. **Polizza.java** (REST) - 256 chiamate
4. **PropostaPropostaImpl.java** - 181 chiamate
5. **PolizzaSwitchImpl.java** - 169 chiamate

## Conclusioni

Il sistema NAW implementa un'architettura enterprise complessa che:

1. **Integra sistemi legacy COBOL** tramite connettori JCA standardizzati
2. **Espone API REST moderne** per applicazioni client
3. **Utilizza EJB** per la logica di business transazionale
4. **Si integra con ecosistema Zurich** tramite Web Services
5. **Gestisce persistenza** tramite iBATIS/MyBatis
6. **Mantiene separazione delle responsabilità** tra livelli architetturali

La presenza di 189 chiamate specifiche a sistemi COBOL conferma che il sistema è progettato per mantenere l'integrazione con sistemi mainframe legacy, utilizzando tecnologie standard Java Enterprise per garantire scalabilità e manutenibilità.

## File di Output Generati

- `all_calls.csv` - Tutte le chiamate categorizzate
- `calls_summary.txt` - Riassunto per categoria
- `service_calls.csv` - Chiamate ai servizi specifiche
- `service_mapping.txt` - Mappatura servizi
- `method_calls.csv` - Chiamate ai metodi
- `method_summary.txt` - Riassunto metodi
- `RIASSUNTO_COMPLETO.md` - Questo documento
