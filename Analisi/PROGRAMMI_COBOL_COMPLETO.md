# PROGRAMMI COBOL IDENTIFICATI NEL SISTEMA NAW

## Panoramica Generale

Il sistema NAW utilizza **857 programmi COBOL unici** con **1.759 chiamate totali** configurate nei file XML di regole. Tutti i programmi vengono eseguiti tramite:

- **Transazioni CICS**: SB00 (1.623 programmi) e SBEX (136 programmi)
- **Connettori**: A05TARE (1.625 programmi) e A05TAREEX (134 programmi)
- **Programma iniziale**: WNDISPC0 (standard) e WNDISPC1 (extended)

## Relazione tra Classi J2C e Programmi COBOL

Le **189 classi J2C** identificate precedentemente (come `RichiestaSwitch`, `RichiestaAnacus`, `DBSFLCDATISWITCH`, ecc.) sono **strutture dati** che vengono utilizzate per comunicare con i programmi COBOL, ma **NON corrispondono direttamente ai nomi dei programmi**.

### Architettura di Comunicazione

```
Java Application
        ↓
Classi J2C (es. RichiestaSwitch.java)
        ↓
JCA Connector (A05TARE/A05TAREEX)
        ↓
Transazione CICS (SB00/SBEX)
        ↓
Programma COBOL (es. VWLSE534)
```

## Categorie di Programmi COBOL

### 🏦 **GESTIONE POLIZZE**

#### Switch e Riallocazioni
- **VWLSE534** - Carica fondi switch
- **VWLSE538** - Aggiorna linea investimento IN
- **VWLSE192** - Registra fondi switch (SBEX)
- **VWLSE544** - Annulla fondi switch (SBEX)
- **WSERD039** - Elenco fondi destinazione
- **VWLSE616** - Exit switch

#### Gestione Polizze Core
- **WSER0068** - Disimpegno polizza
- **VWLSE513** - Elenco unità tecniche con prevalente
- **VWLSE618** - Elenco altri dati polizza
- **VWLSE187** - Elenco eventi polizza
- **WSER0043** - Elenco unità tecniche
- **WSER0033** - Dettaglio polizza (new)
- **WSER0040** - Dettaglio posizione UT (new)

### 👥 **GESTIONE ANAGRAFICA**

#### Anagrafica Base
- **WSER0003** - Ricerca anagrafica
- **VWLSE510** - Salva anagrafica rapporto (SBEX)

#### Gestione Anagrafiche Ruolo
- **VWLSE387** - Carica parametri gestione anagrafiche ruolo
- **VWLSE521** - Carica figure ToBe gestione anagrafiche ruolo
- **VWLSE388** - Registra gestione anagrafiche ruolo (SBEX)
- **VWLSE389** - Controllo singola anagrafica gestione ruolo

### 💼 **GESTIONE PRODOTTI**

#### Prodotti Base
- **WSER0005** - Elenco prodotti
- **WSER0034** - Prodotto selezionato

#### Prodotti Index
- **DWLSLPRO** - Elenco prodotti index
- **DWLSE140** - Salva nuovo prodotto index
- **DWLSE141** - Modifica prodotto index
- **DWLSE142** - Elimina prodotto index
- **DWLSLPDT** - Elenco date prodotti index
- **DWLSE145** - Inserisci date prodotto index
- **DWLSE146** - Elimina date prodotto index

#### Gestione Speciale Prodotto
- **DWLSE154** - Nuovo gestione speciale prodotto
- **DWLSE155** - Salva gestione speciale prodotto
- **DWLSE156** - Elimina gestione speciale prodotto
- **DWLSLGS1** - Elenco gestione speciale prodotto

### 📊 **TABELLE DI SISTEMA**

#### Parametri Generali
- **DWLSE011** - Salva parametri DIBA
- **DWLSE048** - Carica date elaborazione
- **DWLSLNPP** - Elenco numeri proposte polizze

#### Management Fee
- **DWLSE163** - Nuovo management fee
- **DWLSE164** - Dettaglio management fee
- **DWLSE165** - Modifica management fee
- **DWLSE166** - Elimina management fee
- **DWLSE167** - Salva management fee
- **DWLSLFEE** - Elenco management fee

#### Gestione Speciale Polizza
- **DWLSE157** - Nuovo gestione speciale polizza
- **DWLSE158** - Salva gestione speciale polizza
- **DWLSE159** - Elimina gestione speciale polizza
- **DWLSLGS2** - Elenco gestione speciale polizza

### 💰 **GESTIONE FINANZIARIA**

#### Fondi e Investimenti
- **WSER0044** - Elenco fondi
- **WSERD008** - Andamento fondi
- **DWLSE181** - Dettaglio profilo investimento

#### Liquidazioni e Prestiti
- **VWLSE302** - Carica modalità pagamento prestiti
- **VWLSE266** - Concessione prestiti conferma (SBEX)
- **WSER0133** - Variazione prestiti conferma

### 🔐 **ABILITAZIONI E SICUREZZA**

#### Gestione Utenti
- **WSDB0041** - Salva utente
- **WSDB0044** - Carica utente
- **WSDB0043** - Modifica utente
- **WSDB0042** - Elimina utente
- **WSDB0040** - Elenco utenti

#### Autorizzazioni
- **WSAUTH02** - Abilitazione apri polizza
- **WSAUTH04** - Abilitazione apri polizza collettiva
- **WSAUTH03** - Abilitazione apri UT
- **WSDB0030** - Inizializzazione azione
- **WSDB0000** - Carica nuovo profilo

### 📋 **PROPOSTE E WORKFLOW**

#### Gestione Proposte
- **VWLSE652** - Carica palette riattivazione
- **WSER0224** - Controlla parametri opzioni differimento
- **VWLSE327** - Registra opzioni differimento new

#### Product Builder
- **VWLSE240** - Esegui formula per posizioni
- **VWLSE241** - Esegui formula per posizioni SB00
- **VWLSE242** - Esegui prova formula

### 🔄 **GESTIONI BACK OFFICE**

#### Limitazione Operatività
- **VWLSE312** - Dettaglio FUG limitazione operatività
- **VWLSE313** - Salva FUG limitazione operatività
- **VWLSE329** - Dettaglio SQC limitazione operatività
- **VWLSE332** - Salva CQS limitazione operatività
- **VWLSE333** - Dettaglio PVT limitazione operatività
- **VWLSE334** - Salva PVT limitazione operatività

#### Variazioni Contratto
- **VWLSE375** - Controllo variazioni contratto dati anagrafica (SBEX)
- **WSER0103** - Carica parametri variazione PUR
- **WSER0186** - Carica parametri stabilizzazione premio

### 📄 **STAMPE E REPORT**

#### Stampe Back Office
- **VWLSE399** - Sinistro prodotti gestione separata
- **VWLSE508** - Carica tipo prodotto
- **VWLSE511** - Preventivo a scadenza

### 🔍 **INQUIRY E CONSULTAZIONI**

#### Inquiry Polizza
- **VWLSE543** - Elenco fondi rivalutazione e UL
- **VWLSE273** - Dettaglio storico posizione UT
- **VWLSE274** - Dettaglio posizione riassicurazione

### 🌐 **INTEGRAZIONE SISTEMI ESTERNI**

#### Syncro (Antiriciclaggio)
- **WSYN0008** - Carica dettaglio movimento AR Syncro
- **WSYN0009** - Registra movimento AR Syncro
- **WSYN0010** - Controllo incasso diretto movimento AR Syncro
- **WSYN0011** - Elenco movimenti AR Syncro

#### Incassi e Antiriciclaggio
- **AWLSE012** - Controlla incasso titolo AR
- **AWLSE013** - Riporta incasso titolo AR
- **AWLSE014** - Incasso proposta controlla dati
- **AWLSE015** - Incasso proposta riporta dati
- **AWLSE016** - Incasso diretto controlla dati
- **AWLSE017** - Incasso diretto riporta dati

## Convenzioni di Naming

### Prefissi Programmi
- **WSER** - Servizi Web standard
- **VWLSE** - Servizi Vita Web Life Standard/Extended
- **DWLSE** - Data Warehouse Life Standard Extended
- **WSAUTH** - Web Services Authorization
- **WSDB** - Web Services Database
- **AWLSE** - Anti-Washing Life Standard Extended
- **WSYN** - Web Syncro
- **RWLSE** - Rete Web Life Standard Extended

### Suffissi Numerici
- I programmi sono numerati progressivamente all'interno di ogni categoria
- Numeri bassi (001-099) spesso indicano funzioni base
- Numeri alti (500+) spesso indicano funzioni specializzate

## Architettura di Esecuzione

### Transazioni CICS
- **SB00**: Transazione standard per la maggior parte dei programmi (1.623)
- **SBEX**: Transazione extended per operazioni complesse (136)

### Connettori
- **A05TARE**: Connector standard per ambiente di test/sviluppo (1.625)
- **A05TAREEX**: Connector extended per operazioni speciali (134)

### Programmi Iniziali
- **WNDISPC0**: Dispatcher standard
- **WNDISPC1**: Dispatcher extended per transazioni SBEX

## Conclusioni

Il sistema NAW implementa un'architettura enterprise complessa con:

1. **857 programmi COBOL** che coprono tutti gli aspetti del business assicurativo
2. **Separazione chiara** tra operazioni standard (SB00) e extended (SBEX)
3. **Naming convention** strutturata per identificare facilmente le funzionalità
4. **Integrazione completa** con sistemi legacy tramite JCA e CICS
5. **Copertura funzionale** di polizze, anagrafica, prodotti, tabelle sistema, e gestioni back office

Le classi J2C Java fungono da **ponte di comunicazione** tra l'applicazione Java e questi programmi COBOL, garantendo la serializzazione corretta dei dati e la gestione delle transazioni mainframe.
