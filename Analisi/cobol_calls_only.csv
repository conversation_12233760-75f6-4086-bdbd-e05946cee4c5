COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java,20,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__a.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/RichiestaSwitch.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/RichiestaSwitch.java,20,"public class RichiestaSwitch implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/RichiestaSwitch.java,21,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__da.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__da.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza_switch/rich/mapping/Richiestaswitch_dbsflc__dati__funzione_dbsflc__switch__da.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/liquidazione/types/Vttab074Recesso.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/liquidazione/types/Vttab074Recesso.java,18,"public class Vttab074Recesso implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/liquidazione/types/Vttab074Recesso.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/mapping/RichiestaPac.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/mapping/RichiestaPac.java,18,"public class RichiestaPac implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/mapping/RichiestaPac.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRidir.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRidir.java,18,"public class RichiestaRidir implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRidir.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRecesso.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRecesso.java,17,"public class RichiestaRecesso implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaRecesso.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaPac.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaPac.java,18,"public class RichiestaPac implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/polizza/storni/rich/mapping/RichiestaPac.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/RichiestaNewcol.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/RichiestaNewcol.java,18,"public class RichiestaNewcol implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/RichiestaNewcol.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaAnacus.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaAnacus.java,18,"public class RichiestaAnacus implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaAnacus.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestanewbusveraggaumrat_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestanewbusveraggaumrat_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,19,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestanewbusveraggaumrat_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaRefana.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaRefana.java,18,"public class RichiestaRefana implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaRefana.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestavtcdbass_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestavtcdbass_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,19,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/Richiestavtcdbass_l2_l2__dati__trasformazione_l2__polizza__trasformata.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaNewbusVeraggAumrat.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaNewbusVeraggAumrat.java,19,"public class RichiestaNewbusVeraggAumrat implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica/refrehsAnagrafica/rich/mapping/RichiestaNewbusVeraggAumrat.java,20,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica_variazioneAnagrafica/rich/mapping/RichiestaVarana.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica_variazioneAnagrafica/rich/mapping/RichiestaVarana.java,18,"public class RichiestaVarana implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/anagrafica_variazioneAnagrafica/rich/mapping/RichiestaVarana.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/prodotto/ProdottoDatiComuniJ2C.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/prodotto/ProdottoDatiComuniJ2C.java,17,"public class ProdottoDatiComuniJ2C implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/prodotto/ProdottoDatiComuniJ2C.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/DateInvestDisinvestJ2C.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/DateInvestDisinvestJ2C.java,19,"public class DateInvestDisinvestJ2C implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/DateInvestDisinvestJ2C.java,20,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/SceltaModalitaPagamento.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/SceltaModalitaPagamento.java,18,"public class SceltaModalitaPagamento implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/SceltaModalitaPagamento.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/ProcessDateInvDisinvJ2C.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/ProcessDateInvDisinvJ2C.java,17,"public class ProcessDateInvDisinvJ2C implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/dateInvDisinv/ProcessDateInvDisinvJ2C.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/calendarioRibil/CalendarioRibilJ2C.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/calendarioRibil/CalendarioRibilJ2C.java,17,"public class CalendarioRibilJ2C implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/tabella/tracciato/calendarioRibil/CalendarioRibilJ2C.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Interfaces/src/it/sistinf/albedoweb/services/flussoebaas/helper/RichiestaHelper.java,14,import javax.resource.cci.Streamable;,import\s+javax\.resource(\.|\w)*
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec2.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec2.java,17,"public class FormulaInfo_Rec2 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec2.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec1.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec1.java,17,"public class FormulaInfo_Rec1 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/prodotto/formula/helpers/FormulaInfo_Rec1.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/DATI017DBV.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/DATI017DBV.java,19,"public class DATI017DBV implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/DATI017DBV.java,20,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/PropostaPropostaHelper.java,17,import com.ibm.etools.marshall.util.MarshallPackedDecimalUtils;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/Dati017dbv_dati017__dbv_vt17__dati__unit__linked_vt17__fondi__ul.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/Dati017dbv_dati017__dbv_vt17__dati__unit__linked_vt17__fondi__ul.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/proposta/proposta/helpers/Dati017dbv_dati017__dbv_vt17__dati__unit__linked_vt17__fondi__ul.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Sinistro.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Sinistro.java,18,"public class Vttab074Sinistro implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Sinistro.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Cedola.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Cedola.java,18,"public class Vttab074Cedola implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Cedola.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Prestiti.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Prestiti.java,18,"public class Vttab074Prestiti implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Prestiti.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Rendita.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Rendita.java,18,"public class Vttab074Rendita implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Rendita.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Scadenza.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Scadenza.java,18,"public class Vttab074Scadenza implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Scadenza.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Riscatto.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Riscatto.java,18,"public class Vttab074Riscatto implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/liquidazione/helpers/Vttab074Riscatto.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/polizza/helpers/IO022RECORD.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/polizza/helpers/IO022RECORD.java,18,"public class IO022RECORD implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/polizza/helpers/IO022RECORD.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__a.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__a.java,20,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__a.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__da.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__da.java,20,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/Dbsflcdatiswitch_dbsflc__switch__da.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/DBSFLCDATISWITCH.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/DBSFLCDATISWITCH.java,20,"public class DBSFLCDATISWITCH implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/switchservice/helpers/DBSFLCDATISWITCH.java,21,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/DATI027.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/DATI027.java,18,"public class DATI027 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/DATI027.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,10,import com.ibm.etools.marshall.util.ConversionUtils;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,11,import com.ibm.etools.marshall.util.MarshallIntegerUtils;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,12,import com.ibm.etools.marshall.util.MarshallPackedDecimalUtils;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,13,import com.ibm.etools.marshall.util.MarshallResource;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,14,import com.ibm.etools.marshall.util.MarshallStringUtils;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,23,"public class VTTAB024 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/polizza/posizione/helpers/VTTAB024.java,24,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiTassazionePip.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiTassazionePip.java,17,"public class EventiTassazionePip implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiTassazionePip.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrAll.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrAll.java,18,"public class EventiOpzContrAll implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrAll.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiAddebitoZurich.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiAddebitoZurich.java,18,"public class EventiAddebitoZurich implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiAddebitoZurich.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EstrattoConto.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EstrattoConto.java,17,"public class EstrattoConto implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EstrattoConto.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiStampeCollettive.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiStampeCollettive.java,17,"public class EventiStampeCollettive implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiStampeCollettive.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiVaProgrammati.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiVaProgrammati.java,18,"public class EventiVaProgrammati implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiVaProgrammati.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrArpp.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrArpp.java,18,"public class EventiOpzContrArpp implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/gestioneEventi/eventi/helpers/EventiOpzContrArpp.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EventiTassazionePip.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EventiTassazionePip.java,17,"public class EventiTassazionePip implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EventiTassazionePip.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EstrattoConto.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EstrattoConto.java,17,"public class EstrattoConto implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/dashboard/eventi/helpers/EstrattoConto.java,18,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkJ2C_KO.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkJ2C_KO.java,21,"public class RaggruppamentoUnitLinkJ2C_KO implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkJ2C_KO.java,22,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller22J2C_KO.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller22J2C_KO.java,19,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller22J2C_KO.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller23J2C_KO.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller23J2C_KO.java,19,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/RaggruppamentoUnitLinkFiller23J2C_KO.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/SceltaModalitaPagamentoJ2C_KO.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/SceltaModalitaPagamentoJ2C_KO.java,19,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/raggruppamento/SceltaModalitaPagamentoJ2C_KO.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190prodotto130_dati__prodotto_vt190__premio__min__base.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190prodotto130_dati__prodotto_vt190__premio__min__base.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190prodotto130_dati__prodotto_vt190__premio__min__base.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__oneri.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__oneri.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__oneri.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190parametrigenerali999_dati__generali_vt190__irpef_vt190__scaglioni.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190parametrigenerali999_dati__generali_vt190__irpef_vt190__scaglioni.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190parametrigenerali999_dati__generali_vt190__irpef_vt190__scaglioni.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/OpzioneGestionale.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/OpzioneGestionale.java,18,"public class OpzioneGestionale implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/OpzioneGestionale.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__fondo__multiplo.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__fondo__multiplo.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__fondo__multiplo.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Calendario420.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Calendario420.java,18,"public class Vttab190Calendario420 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Calendario420.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Prodotto130.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Prodotto130.java,18,"public class Vttab190Prodotto130 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190Prodotto130.java,19,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190ParametriGenerali999.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190ParametriGenerali999.java,19,"public class Vttab190ParametriGenerali999 implements javax.resource.cci.Record,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190ParametriGenerali999.java,20,"javax.resource.cci.Streamable, com.ibm.etools.marshall.RecordBytes {",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190calendario420_tabella__nav_dati__calendario.java,8,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190calendario420_tabella__nav_dati__calendario.java,19,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190calendario420_tabella__nav_dati__calendario.java,20,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__proventi.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__proventi.java,20,"implements javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190fondirivalutazione030_dati__fondi__rivalutazione_vt190__rendiconto__arc_vt190__dati__proventi.java,21,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190FondiRivalutazione030.java,9,import com.ibm.etools.marshall.util.*;,com\.ibm\.etools\.marshall\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190FondiRivalutazione030.java,23,"javax.resource.cci.Record, javax.resource.cci.Streamable,",javax\.resource\.cci\.
COBOL_LEGACY,Services.Common.Impl/src/it/sistinf/albedoweb/services/tabella/tracciato/helpers/Vttab190FondiRivalutazione030.java,24,com.ibm.etools.marshall.RecordBytes {,com\.ibm\.etools\.marshall\.
