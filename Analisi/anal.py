#!/usr/bin/env python3
"""
comprehensive_call_extractor.py

Estrae tutte le tipologie di chiamate esterne da una codebase Java.
Analizza chiamate a servizi, database, sistemi esterni, API REST, Web Services, ecc.

Output:
  - **Console**: elenco categorizzato delle chiamate trovate
  - **File CSV**: `all_calls.csv` con dettagli delle chiamate
  - **File di testo**: `calls_summary.txt` con riassunto per categoria

Prerequisiti:
  - Python 3.6+

Uso:
  chmod +x comprehensive_call_extractor.py
  ./comprehensive_call_extractor.py
"""
import os
import re
import csv
from collections import defaultdict

# Configurazione fissa
SRC_PATH = "/Users/<USER>/Desktop/Conding/NAW/Source"
OUTPUT_DIR = "/Users/<USER>/Desktop/Conding/NAW/Analisi"
OUTPUT_CSV = os.path.join(OUTPUT_DIR, 'all_calls.csv')
OUTPUT_SUMMARY = os.path.join(OUTPUT_DIR, 'calls_summary.txt')

# Pattern per diversi tipi di chiamate
CALL_PATTERNS = {
    'COBOL_LEGACY': [
        r"Runtime\.getRuntime\(\)\.exec\s*\(",
        r"System\.loadLibrary\s*\(",
        r"import\s+com\.ibm\.cics(\.|\w)*",
        r"CICSRequest",
        r"CICSReply",
        r"import\s+javax\.resource(\.|\w)*",
        r"ProgramCall",
        r"import\s+com\.sap\.conn(\.|\w)*",
        r"CALL\s+'[A-Z0-9]+'",
        r"PROCEDURE\s+NAME\s+\b[\w$]+",
        r"javax\.resource\.cci\.",
        r"com\.ibm\.etools\.marshall\.",
    ],
    'EJB_CALLS': [
        r"@EJB\s+",
        r"\.lookup\s*\(\s*[\"'].*EJB.*[\"']\s*\)",
        r"InitialContext\(\)\.lookup\s*\(",
        r"FactoryBean\.getBean\s*\(",
        r"SimpleRemoteStatelessSessionProxyFactoryBean",
        r"LocalStatelessSessionProxyFactoryBean",
        r"@Stateless",
        r"@Singleton",
        r"ejblocal:",
        r"java:comp/env/ejb/",
    ],
    'WEB_SERVICES': [
        r"@WebService",
        r"@WebMethod",
        r"JAX-WS",
        r"JaxWsPortProxyFactoryBean",
        r"\.wsdl",
        r"SOAPBinding",
        r"@SOAPBinding",
        r"endpointAddress",
        r"serviceInterface",
        r"wsdlDocumentUrl",
    ],
    'REST_CALLS': [
        r"@RestController",
        r"@RequestMapping",
        r"@GetMapping",
        r"@PostMapping",
        r"@PutMapping",
        r"@DeleteMapping",
        r"@Path\s*\(",
        r"@GET",
        r"@POST",
        r"@PUT",
        r"@DELETE",
        r"Response\.ok\(\)",
        r"RestTemplate",
        r"HttpClient",
        r"CloseableHttpClient",
    ],
    'DATABASE_CALLS': [
        r"@Repository",
        r"@Transactional",
        r"DataSource",
        r"JdbcTemplate",
        r"EntityManager",
        r"@PersistenceContext",
        r"@Query",
        r"@NamedQuery",
        r"SqlMapClient",
        r"SqlSession",
        r"Connection\.prepareStatement",
        r"Statement\.execute",
        r"ResultSet",
        r"jndiDatasourceName",
    ],
    'JMS_MQ': [
        r"@JmsListener",
        r"JmsTemplate",
        r"MessageProducer",
        r"MessageConsumer",
        r"Queue",
        r"Topic",
        r"ConnectionFactory",
        r"javax\.jms\.",
        r"MQQueue",
        r"MQConnectionFactory",
    ],
    'EXTERNAL_APIS': [
        r"http://",
        r"https://",
        r"URL\s*\(",
        r"URLConnection",
        r"HttpURLConnection",
        r"endpoint",
        r"\.endpoint",
        r"basic\.auth\.",
        r"client_id",
        r"client_secret",
    ],
    'SPRING_BEANS': [
        r"@Autowired",
        r"@Component",
        r"@Service",
        r"@Bean",
        r"ApplicationContext",
        r"BeanFactory",
        r"<bean\s+id=",
        r"<property\s+name=",
        r"ref=",
    ],
}

# File estensioni da scansionare
SCAN_EXT = ('.java', '.xml', '.properties', '.jsp', '.js', '.sql')

# File estensioni da scansionare
SCAN_EXT = ('.java', '.xml', '.properties')


def detect_all_calls(src_path):
    """
    Scansiona ricorsivamente i file per tutti i tipi di chiamate.
    Restituisce dizionario con categoria -> lista di tuple (path, line_number, line_text, pattern_matched).
    """
    results = defaultdict(list)

    for root, _, files in os.walk(src_path):
        for fname in files:
            if not fname.lower().endswith(SCAN_EXT):
                continue
            path = os.path.join(root, fname)
            try:
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    for lineno, line in enumerate(f, 1):
                        line_stripped = line.strip()
                        if not line_stripped or line_stripped.startswith('//') or line_stripped.startswith('*'):
                            continue

                        for category, patterns in CALL_PATTERNS.items():
                            for pattern in patterns:
                                if re.search(pattern, line, re.IGNORECASE):
                                    results[category].append((path, lineno, line_stripped, pattern))
                                    break  # Solo il primo match per linea per categoria
            except Exception as e:
                print(f"Errore leggendo {path}: {e}")
                continue

    return results


def write_csv_results(results, output_file):
    """Scrive i risultati in formato CSV."""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Category', 'File_Path', 'Line_Number', 'Line_Content', 'Pattern_Matched'])

        for category, calls in results.items():
            for path, lineno, text, pattern in calls:
                # Rimuovi il path base per leggibilità
                relative_path = path.replace(SRC_PATH, '').lstrip('/')
                writer.writerow([category, relative_path, lineno, text, pattern])


def write_summary(results, output_file):
    """Scrive un riassunto delle chiamate trovate."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("RIASSUNTO CHIAMATE ESTERNE TROVATE\n")
        f.write("=" * 50 + "\n\n")

        total_calls = sum(len(calls) for calls in results.values())
        f.write(f"Totale chiamate trovate: {total_calls}\n\n")

        for category, calls in sorted(results.items()):
            f.write(f"\n{category}: {len(calls)} chiamate\n")
            f.write("-" * (len(category) + 20) + "\n")

            # Raggruppa per pattern
            pattern_counts = defaultdict(int)
            for _, _, _, pattern in calls:
                pattern_counts[pattern] += 1

            for pattern, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True):
                f.write(f"  {pattern}: {count} occorrenze\n")

            # Mostra alcuni esempi
            f.write("\nEsempi:\n")
            for i, (path, lineno, text, pattern) in enumerate(calls[:5]):
                relative_path = path.replace(SRC_PATH, '').lstrip('/')
                f.write(f"  {relative_path}:{lineno} -> {text[:100]}...\n")

            if len(calls) > 5:
                f.write(f"  ... e altri {len(calls) - 5} esempi\n")
            f.write("\n")


def main():
    # Verifica directories
    if not os.path.isdir(SRC_PATH):
        print(f"Errore: '{SRC_PATH}' non è una directory valida.")
        return
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("Scansione in corso...")
    print(f"Directory sorgenti: {SRC_PATH}")
    print(f"Directory output: {OUTPUT_DIR}")
    print()

    # Estrazione
    results = detect_all_calls(SRC_PATH)

    if not results:
        print("Nessuna chiamata rilevata.")
        return

    # Calcola totali
    total_calls = sum(len(calls) for calls in results.values())
    print(f"Trovate {total_calls} chiamate totali in {len(results)} categorie:\n")

    # Stampa riassunto per categoria
    for category, calls in sorted(results.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{category}: {len(calls)} chiamate")

    # Scrittura file CSV
    write_csv_results(results, OUTPUT_CSV)
    print(f"\nDettagli salvati in: {OUTPUT_CSV}")

    # Scrittura riassunto
    write_summary(results, OUTPUT_SUMMARY)
    print(f"Riassunto salvato in: {OUTPUT_SUMMARY}")

    print("\nAnalisi completata!")


if __name__ == '__main__':
    main()
