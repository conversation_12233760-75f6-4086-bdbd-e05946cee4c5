# DOCUMENTAZIONE API `/vaEmesso/Controlla`

## Panoramica

L'API `/vaEmesso/Controlla` è un endpoint REST che gestisce il controllo dei versamenti aggiuntivi emessi per polizze assicurative. L'API implementa una logica di business complessa che coinvolge diversi programmi COBOL per la validazione e il calcolo dei dati tecnici.

## Endpoint REST

### URL
```
POST /vaEmesso/Controlla
```

### Controller
**Classe**: `it.sistinf.rest.singleton.polizza.Polizza`  
**Metodo**: `controllaVAEmesso()`

### Request/Response Objects
- **Request**: `ControllaVAEmessoRequest`
- **Response**: `ControllaVAEmessoResponse`

## Flusso di Esecuzione

### 1. **Livello REST Controller**

<augment_code_snippet path="Source/REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java" mode="EXCERPT">
```java
@Override
@TransactionAttribute(TransactionAttributeType.NEVER)
public ControllaVAEmessoResponse controllaVA(ControllaVAEmessoRequest body, 
    List<String> validatorViolations, 
    SelectTipoProdottoDettaglioGenericoResponse selectTipoProdottoDettaglioGenericoResponse) {
    
    ControllaVAEmessoResponse controllaVAEmessoResponse = new ControllaVAEmessoResponse();
    Map<String, Boolean> mapTipoProdotto = PolizzaUtility.getMappa(selectTipoProdottoDettaglioGenericoResponse);
    boolean isTranching = mapTipoProdotto.get("isTranching");

    if(isTranching) {
        // VWLSE560 + VWLSE459
        controllaVAEmessoResponse = (validatorViolations.isEmpty()) ? 
            controllaVAEmessoTranching(body) : PolizzaUtility.valorizzaViolation(body, validatorViolations);
    } else {
        // VWLSE461 + VWLSE459
        controllaVAEmessoResponse = (validatorViolations.isEmpty()) ? 
            controllaVAEmessoAltro(body) : PolizzaUtility.valorizzaViolation(body, validatorViolations);
    }
    return controllaVAEmessoResponse;
}
```
</augment_code_snippet>

### 2. **Logica di Routing per Tipo Prodotto**

L'API implementa due flussi distinti basati sul tipo di prodotto:

#### **A. Prodotti Tranching**
- **Metodo**: `controllaVAEmessoTranching()`
- **Programmi COBOL**: `VWLSE560` + `VWLSE459`

#### **B. Altri Prodotti**
- **Metodo**: `controllaVAEmessoAltro()`
- **Programmi COBOL**: `VWLSE461` + `VWLSE459`

## Implementazione Dettagliata

### **Flusso A: Prodotti Tranching**

<augment_code_snippet path="Source/REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java" mode="EXCERPT">
```java
@TransactionAttribute(TransactionAttributeType.NEVER)
private ControllaVAEmessoResponse controllaVAEmessoTranching(ControllaVAEmessoRequest body) {
    ControllaVAEmessoResponse controllaVAResponse = new ControllaVAEmessoResponse();
    try {
        // 1. Verifica esistenza modalità pagamento
        String esisteModPagamento = getEsisteModPag(body.getPolizzaInfo(), 
            body.getModalitaPagamentoVA(), body.getHeaderCobolSrv().getIdUtente());
        
        // 2. Preparazione request per COBOL
        DatiTecniciUTRequestDTO datiTecniciRequest = 
            PolizzaUtility.valorizzaControllaVAEmessoTranchingRequest(body, esisteModPagamento);
        
        // 3. Chiamata a VWLSE560 - Controllo dati tecnici multi-invest
        PropostaDatiTecniciBOInterface propostaDatiTecniciInterface = new PropostaDatiTecniciBO();
        DatiTecniciUTResponseDTO datiTecniciResponse = 
            propostaDatiTecniciInterface.controllaPropostaDatiTecniciMainMultiInvest(datiTecniciRequest);
        
        if(datiTecniciResponse.isErroreBloccante()){
            // Gestione errori
            return controllaVAResponse;
        }

        // 4. Preparazione per seconda chiamata COBOL
        PropostaRiepilogoRequestDTO propostaRiepilogoRequest = 
            PolizzaUtility.valorizzaPropostaRiepilogoRequestDTO(datiTecniciResponse.getInteractionDTO());
        
        // 5. Chiamata a VWLSE459 - Carica lista UT garanzie
        PropostaBOInterface propostaInterface = new PropostaBO();
        PropostaRiepilogoResponseDTO propostaRiepilogoResponse = 
            propostaInterface.caricaListaUTGaranzie(propostaRiepilogoRequest);
        
        // 6. Valorizzazione response finale
        controllaVAResponse = PolizzaUtility.valorizzaControllaVAEmessoResponse(
            datiTecniciResponse, propostaRiepilogoResponse);
            
    } catch (Exception e) {
        // Gestione eccezioni
    }
    return controllaVAResponse;
}
```
</augment_code_snippet>

### **Flusso B: Altri Prodotti**

<augment_code_snippet path="Source/REST.Services.Cobol.Web/src/it/sistinf/rest/singleton/polizza/Polizza.java" mode="EXCERPT">
```java
@TransactionAttribute(TransactionAttributeType.NEVER)
private ControllaVAEmessoResponse controllaVAEmessoAltro(ControllaVAEmessoRequest body) {
    ControllaVAEmessoResponse controllaVAResponse = new ControllaVAEmessoResponse();
    try {
        // 1. Preparazione request per COBOL
        DatiTecniciUTRequestDTO datiTecniciRequest = 
            PolizzaUtility.valorizzaControllaVAEmessoAltroRequest(body);
        
        // 2. Chiamata a VWLSE461 - Controllo dati tecnici standard
        PropostaDatiTecniciBOInterface propostaDatiTecniciInterface = new PropostaDatiTecniciBO();
        DatiTecniciUTResponseDTO datiTecniciResponse = 
            propostaDatiTecniciInterface.controllaPropostaDatiTecnici(datiTecniciRequest);
        
        if(datiTecniciResponse.isErroreBloccante()){
            // Gestione errori
            return controllaVAResponse;
        }

        // 3. Preparazione per seconda chiamata COBOL
        PropostaRiepilogoRequestDTO propostaRiepilogoRequest = 
            PolizzaUtility.valorizzaPropostaRiepilogoRequestDTO(datiTecniciResponse.getInteractionDTO());
        
        // 4. Chiamata a VWLSE459 - Carica lista UT garanzie
        PropostaBOInterface propostaInterface = new PropostaBO();
        PropostaRiepilogoResponseDTO propostaRiepilogoResponse = 
            propostaInterface.caricaListaUTGaranzie(propostaRiepilogoRequest);
        
        // 5. Valorizzazione response finale
        controllaVAResponse = PolizzaUtility.valorizzaControllaVAEmessoResponse(
            datiTecniciResponse, propostaRiepilogoResponse);
            
    } catch (Exception e) {
        // Gestione eccezioni
    }
    return controllaVAResponse;
}
```
</augment_code_snippet>

## Programmi COBOL Coinvolti

### **VWLSE560 - Controllo Garanzie Dati Tecnici UT Main Multi-Invest**

**Configurazione XML**: `proposta.garanzie.controllaUTMainMultiInvest.xml`

```xml
<rule>
    <id>CONTROLLA-GARANZIE-DATI-TECNICI-UT-MAIN-MULTIINVEST</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE560</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
</rule>
```

**Funzionalità**:
- Controllo dati tecnici per prodotti multi-investimento
- Validazione parametri versamento aggiuntivo
- Calcolo allocazioni gestione separata e unit linked
- Verifica convenzioni e deroghe

### **VWLSE461 - Controllo Garanzie Dati Tecnici UT Standard**

**Configurazione XML**: `proposta.garanzie.controllaUT.xml`

```xml
<rule>
    <id>CONTROLLA-GARANZIE-DATI-TECNICI-UT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE461</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
</rule>
```

**Funzionalità**:
- Controllo dati tecnici per prodotti standard
- Validazione parametri versamento aggiuntivo
- Calcolo premi e allocazioni
- Verifica vincoli contrattuali

### **VWLSE459 - Carica Lista UT Garanzie**

**Configurazione XML**: `proposta.garanzie.caricaListaUT.xml`

```xml
<rule>
    <id>CARICA-GARANZIE-LISTA-UT</id>
    <initialProgram>WNDISPC0</initialProgram>
    <initialTransaction>SB00</initialTransaction>
    <program>VWLSE459</program>
    <transaction>SB00</transaction>
    <connectorId>A05TARE</connectorId>
</rule>
```

**Funzionalità**:
- Caricamento lista unità tecniche
- Dettaglio garanzie per posizione
- Calcolo premi netti e rate
- Gestione errori per UT

## Strutture Dati

### **ControllaVAEmessoRequest**

<augment_code_snippet path="Source/REST.Services.Cobol.Model/src/it/sistinf/rest/cobol/model/polizza/vaEmesso/ControllaVAEmessoRequest.java" mode="EXCERPT">
```java
public class ControllaVAEmessoRequest {
    private HeaderCobolSrv headerCobolSrv;
    private PolizzaInfo polizzaInfo;
    private PrimaRata modalitaPagamentoVA;
    private BigDecimal importoVersamento;
    private String dataVersamento;
    private String tipoVersamento;
    // ... altri campi
}
```
</augment_code_snippet>

### **ControllaVAEmessoResponse**

<augment_code_snippet path="Source/REST.Services.Cobol.Model/src/it/sistinf/rest/cobol/model/polizza/vaEmesso/ControllaVAEmessoResponse.java" mode="EXCERPT">
```java
public class ControllaVAEmessoResponse {
    private HeaderCobolSrv headerCobolSrv;
    private BigDecimal percAllocazioneGS;
    private BigDecimal percAllocazioneUL;
    private String codiceConvenzione;
    private String codiceDeroga;
    private String preRataCompl;
    private BigDecimal unitLinkedPremio;
    private String codLineaInvestimento;
    private BigDecimal gestioneSeparataPremio;
    private String codOpzFacoltativa;
    private String codImportoCedola;
    private String codDurataCedola;
    private List<Errore> errori;
    // ... altri campi
}
```
</augment_code_snippet>

## Architettura di Comunicazione

```
Client REST
    ↓
POST /vaEmesso/Controlla
    ↓
Polizza.controllaVA()
    ↓
┌─────────────────────────────────────┐
│ Routing per Tipo Prodotto          │
├─────────────────────────────────────┤
│ isTranching?                        │
│   ├─ TRUE:  controllaVAEmessoTranching() │
│   └─ FALSE: controllaVAEmessoAltro()     │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ Business Logic Java                 │
├─────────────────────────────────────┤
│ 1. getEsisteModPag()               │
│ 2. PropostaDatiTecniciBO           │
│ 3. PropostaBO                      │
│ 4. PolizzaUtility                  │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ Chiamate COBOL Sequenziali         │
├─────────────────────────────────────┤
│ 1. VWLSE560/VWLSE461 (Controllo)  │
│    ↓                               │
│ 2. VWLSE459 (Lista UT)            │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│ JCA/CICS Integration               │
├─────────────────────────────────────┤
│ Connector: A05TARE                 │
│ Transaction: SB00                  │
│ Dispatcher: WNDISPC0               │
└─────────────────────────────────────┘
    ↓
Mainframe COBOL Programs
```

## Gestione Errori

### **Errori di Validazione**
- Validazione input tramite `BaseValidator.commonValidate()`
- Errori restituiti in `ControllaVAEmessoResponse.errori`

### **Errori COBOL**
- Gestione tramite `isErroreBloccante()`
- Mappatura errori COBOL in oggetti Java
- Propagazione errori al client REST

### **Eccezioni Java**
- `ApplicationException` - Errori applicativi
- `InfrastructureException` - Errori infrastrutturali
- `Exception` - Errori generici

## Transazioni

- **Attributo**: `@TransactionAttribute(TransactionAttributeType.NEVER)`
- **Gestione**: Transazioni gestite a livello COBOL/CICS
- **Rollback**: Gestito dal mainframe

## Logging e Monitoraggio

- **Log applicativo**: Configurato nei file XML delle regole
- **Area funzionale**: "APRI POLIZZA-VERSAMENTI AGGIUNTIVI"
- **Descrizione servizio**: "CONTROLLA VERSAMENTO AGGIUNTIVO EMESSO"

## Considerazioni di Performance

1. **Chiamate sequenziali**: Due chiamate COBOL per ogni richiesta
2. **Caching**: Nessun caching implementato
3. **Timeout**: Gestito a livello connector JCA
4. **Scalabilità**: Limitata dalla capacità del mainframe

## Sicurezza

- **Autenticazione**: Tramite `HeaderCobolSrv.idUtente`
- **Autorizzazione**: Gestita a livello COBOL
- **Validazione input**: Tramite Bean Validation
- **Audit**: Log delle operazioni su mainframe

## Dettagli Tecnici Aggiuntivi

### **Modalità Pagamento**

Il metodo `getEsisteModPag()` verifica l'esistenza della modalità di pagamento:

```java
private String getEsisteModPag(PolizzaInfo polizzaInfo, PrimaRata modPagPRata, String utente) {
    // 1. Recupera info utente
    UtenteInfoResponse utenteInfoResponse = abilitazioni.getUtenteInfo(utente);

    // 2. Carica dati polizza
    SelectPolizzaRequest polizzaRequest = PolizzaUtility.valorizzaSelectPolizzaRequest(polizzaInfo, utenteInfoResponse);
    SelectPolizzaResponse polizzaResponse = polizza.selectPolizza(polizzaRequest);

    // 3. Verifica modalità pagamento tramite domini
    String codSocModPag = codSoc + "_" + modPagPRata.getModPagPrimaRata();
    DominiCorrelatiResponse responseDomini = dominioRest.chiamaDomini988("MPAG_PARAM", valoriInfoMap);

    return esisteModPag;
}
```

### **Utility di Mappatura**

La classe `PolizzaUtility` fornisce metodi di conversione:

- `valorizzaControllaVAEmessoTranchingRequest()` - Mapping per prodotti tranching
- `valorizzaControllaVAEmessoAltroRequest()` - Mapping per altri prodotti
- `valorizzaControllaVAEmessoResponse()` - Mapping response finale
- `valorizzaPropostaRiepilogoRequestDTO()` - Preparazione seconda chiamata COBOL

### **Gestione Tipo Prodotto**

```java
Map<String, Boolean> mapTipoProdotto = PolizzaUtility.getMappa(selectTipoProdottoDettaglioGenericoResponse);
boolean isTranching = mapTipoProdotto.get("isTranching");
boolean isViPensiono = mapTipoProdotto.get("isViPensiono");
```

### **Configurazione JCA**

- **Connector Standard**: `A05TARE`
- **Connector Extended**: `A05TAREEX` (per operazioni complesse)
- **Transaction Standard**: `SB00`
- **Transaction Extended**: `SBEX`
- **Dispatcher**: `WNDISPC0` (standard), `WNDISPC1` (extended)

### **Struttura Commarea COBOL**

#### VWLSE560/VWLSE461 (Input)
```
- Header (150 caratteri)
- Dati polizza (variabile)
- Dati versamento (variabile)
- Parametri controllo (variabile)
```

#### VWLSE459 (Output)
```
- Header (150 caratteri)
- Lista UT (12000 caratteri)
  - 100 iterazioni x 120 caratteri
  - Posizione, codice UT, descrizione
  - Decorrenza, durata, premi
- Area errori (1824 caratteri)
```

### **Campi Response Principali**

- `percAllocazioneGS` - Percentuale allocazione gestione separata
- `percAllocazioneUL` - Percentuale allocazione unit linked
- `codiceConvenzione` - Codice convenzione applicata
- `codiceDeroga` - Codice deroga applicata
- `unitLinkedPremio` - Premio unit linked calcolato
- `gestioneSeparataPremio` - Premio gestione separata calcolato
- `codLineaInvestimento` - Linea di investimento
- `codOpzFacoltativa` - Opzione facoltativa
- `preRataCompl` - Flag pre-rata completamento

## Esempi di Utilizzo

### **Request di Esempio**

```json
{
  "headerCobolSrv": {
    "idUtente": "USER001",
    "codSocieta": 1,
    "dataContabile": "2024-01-15"
  },
  "polizzaInfo": {
    "codSocieta": 1,
    "numeroPolizza": "123456789",
    "numeroCategoria": "01"
  },
  "datiVideoVA": {
    "importoVersamento": 10000.00,
    "dataVersamento": "2024-02-01"
  },
  "modalitaPagamentoVA": {
    "modPagPrimaRata": "01"
  }
}
```

### **Response di Esempio**

```json
{
  "headerCobolSrv": {
    "idUtente": "USER001",
    "returnCode": "00"
  },
  "percAllocazioneGS": 70.00,
  "percAllocazioneUL": 30.00,
  "codiceConvenzione": "CONV001",
  "unitLinkedPremio": 3000.00,
  "gestioneSeparataPremio": 7000.00,
  "codLineaInvestimento": "LI001",
  "errori": []
}
```

## Troubleshooting

### **Errori Comuni**

1. **Errore modalità pagamento non valida**
   - Verificare configurazione domini MPAG_PARAM
   - Controllare abilitazioni utente

2. **Errore tipo prodotto non supportato**
   - Verificare configurazione prodotto
   - Controllare mapping tipo prodotto

3. **Errore calcolo allocazioni**
   - Verificare parametri versamento
   - Controllare configurazione fondi

### **Log di Debug**

Abilitare logging per:
- `it.sistinf.rest.singleton.polizza.Polizza`
- `it.sistinf.albedoweb.proposta.bo.PropostaDatiTecniciBO`
- `it.sistinf.albedoweb.proposta.bo.PropostaBO`

## Dipendenze

### **Servizi EJB**
- `PolizzaPolizzaInterface` - Gestione polizze
- `AbilitazioneUtenteInterface` - Abilitazioni utente
- `StrutturaReteInterface` - Struttura rete vendita

### **Business Objects**
- `PropostaDatiTecniciBO` - Controllo dati tecnici
- `PropostaBO` - Gestione proposte
- `DominioRestInterface` - Gestione domini

### **Utility Classes**
- `PolizzaUtility` - Utility polizze
- `InteractionUtility` - Utility interazione
- `ServiceUtility` - Utility servizi
